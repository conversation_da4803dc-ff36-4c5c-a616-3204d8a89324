/// 希腊字母
#[derive(Default)]
pub struct GreeksField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 合约编码
    pub InsID: String,

    /// 隐含波动率
    pub iv: f64,

    /// Delta
    pub delta: f64,

    /// Gamma
    pub gamma: f64,

    /// Vega
    pub vega: f64,

    /// Theta
    pub theta: f64,

    /// 年度总交易天数
    pub TD: i32,

    /// 剩余交易天数
    pub LD: i32,

    /// 总交易分钟数
    pub TM: i32,

    /// 剩余交易分钟数
    pub LM: i32,

    /// 有效剩余时间
    pub T: f64,

    /// 期权的计算价格
    pub OP: f64,

    /// 现货的计算价格
    pub SP: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
