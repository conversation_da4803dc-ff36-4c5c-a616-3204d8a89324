/// 报撤单信息(按合约统计)
#[derive(Default)]
pub struct OrderInsertCancelFutField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 最大撤单次数
    pub MCancelNum: i32,

    /// 最大大额撤单次数
    pub MLCancelNum: i32,

    /// 大额撤单标准
    pub SLCancelNum: i32,

    /// 引起本条信息变化的业务序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 报单笔数
    pub InsertNum: i32,

    /// 撤单次数
    pub CancelNum: i32,

    /// 大额撤单次数
    pub LCancelNum: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 限持仓信息
#[derive(Default)]
pub struct LimitPositionFutField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 多头最大持仓量
    pub MaxLongVolume: i32,

    /// 空头最大持仓量
    pub MaxShortVolume: i32,

    /// 引起本条信息变化的业务序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 多头持仓量
    pub LongVolume: i32,

    /// 空头持仓量
    pub ShortVolume: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 限开仓统计类型
enum ELimitOpenSTType {
    /// 期货合约
    FutIns,

    /// 期权品种
    OptProduct,

    /// 期权交割月
    OptDeliveryMonth,

    /// 期权深度虚值合约
    OptDeepVirtualIns,
}

/// 限开仓信息
#[derive(Default)]
pub struct LimitOpenFutField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 统计编码(按品种，按合约，按交割月, 不同的统计方式，取相应的值)
    pub STID: String,

    /// 统计类型
    pub STType: i32,

    /// 最大开仓量
    pub MaxOpenVolume: i32,

    /// 引起本条信息变化的业务序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 开仓量
    pub OpenVolume: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 自成交
#[derive(Default)]
pub struct TradeSelfFutField {
    /// 程序编码
    pub AID: i32,

    /// 统计类型. 1:按合约2:按标的(此时InstrumentID填标的品种编码)
    pub TSType: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 引起本条信息变化的业务序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 自成交笔数
    pub TradeNum: i32,

    /// 自成交数量
    pub TradeVol: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
