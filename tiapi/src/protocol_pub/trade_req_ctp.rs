use self::TradeType::*;
use serde::{Deserialize, Serialize};

/// ctp交易协议枚举
pub mod TradeType {

    use num_enum::IntoPrimitive;

    /// 报单价格条件
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum EOrderPriceType {
        /// 市价 - '1'
        AnyPrice = 49,

        /// 限价 - '2'
        LimitPrice = 50,

        /// 最优价 - '3'
        BestPrice = 51,

        /// 最新价 - '4'
        LastPrice = 52,

        /// 卖一价(仅期货) - '5'
        AskPrice1 = 53,

        /// 买一价(仅期货) -'C'
        BidPrice1 = 67,

        /// 五档价 - 'G'
        FiveLevelPrice = 71,

        /// 本方最优价(期货无) - 'H'
        BestPriceThisSide = 72,
    }

    /// 开平标志
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum EOffsetFlag {
        /// 开仓 -'0'
        Open = 48,

        /// 平仓 - '1'
        Close = 49,

        /// 平今(仅期货) - '3'
        CloseToday = 51,

        /// 平昨(仅期货) - '4'
        CloseYesterday = 52,
    }

    /// 投机套保标志
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum EHedgeFlag {
        /// 投机 - '1'
        Speculation = 49,

        /// 套利(仅期货) - '2'
        Arbitrage = 50,

        /// 套保(仅期货) - '3'
        Hedge = 51,

        /// 做市商(仅期货) - '5'
        MarketMaker = 53,

        /// 第一腿投机第二腿套保(仅期货) - '6'
        SpecHedge = 54,

        /// 第一腿套保第二腿投机(仅期货) - '7'
        HedgeSpec = 55,
    }

    /// 买卖方向
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum EDirection {
        /// 买入 - '0'
        Buy = 48,

        /// 卖出 - '1'
        Sell = 49,

        /// 融资买入
        BuyCredit = 3,

        /// 融券卖出
        SellCredit = 4,

        /// 买券还券
        BuyReturn = 5,

        /// 卖券还款
        SellReturn = 6,

        /// 现券还券
        ReturnStock = 7,

        /// 直接还款
        ReturnFund = 8,
    }

    /// 组合指令方向
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum ECombDirection {
        /// 申请组合 - '0'
        Comb = 48,

        /// 申请拆分 - '1'
        UnComb = 49,

        /// 操作员删组合单(仅期货) - '2'
        DelComb = 50,
    }

    /// 有效期类型
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum ETimeCondition {
        /// 立即完成，否则撤销 - '1'
        IOC = 49,

        /// 当日有效 - '3'
        GFD = 51,
    }

    /// 成交量类型
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum EVolumeCondition {
        /// 任何数量 - '1'
        AV = 49,

        /// 最小数量 - '2'
        MV = 50,

        /// 全部数量 - '3'
        CV = 51,
    }

    /// 触发条件
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum EContingentCondition {
        /// 立即 - '1'
        Immediately = 49,
    }

    /// 强平原因
    #[derive(IntoPrimitive)]
    #[repr(i32)]
    pub enum EForceCloseReason {
        /// 非强平 - '0'
        NotForceClose = 48,

        /// 资金不足 - '1'
        LackDeposit = 49,

        /// 客户超仓 - '2'
        ClientOverPositionLimit = 50,

        /// 会员超仓 - '3'
        MemberOverPositionLimit = 51,

        /// 持仓非整数倍 - '4'
        NotMultiple = 52,

        /// 违规 - '5'
        Violation = 53,

        /// 其它 - '6'
        Other = 54,

        /// 自然人临近交割 - '7'
        PersonDeliv = 55,

        /// 风控强平不验证资金 - '8'
        Notverifycapital = 56,
    }
}

/// 请求报单录入
#[derive(Default, Debug, Serialize)]
pub struct CtpReqInputOrderField {
    // CThostFtdcInputOrderField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 报单引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OrderRef: String,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 报单价格条件
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub OrderPriceType: i32,

    /// 买卖方向
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub Direction: i32,

    /// 组合开平标志
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CombOffsetFlag: String,

    /// 组合投机套保标志
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CombHedgeFlag: String,

    /// 价格
    #[serde(skip_serializing_if = "comfun::is_zero_f64")]
    pub LimitPrice: f64,

    /// 数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub VolumeTotalOriginal: i32,

    /// 有效期类型
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub TimeCondition: i32,

    /// GTD日期
    #[serde(skip_serializing_if = "String::is_empty")]
    pub GTDDate: String,

    /// 成交量类型
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub VolumeCondition: i32,

    /// 最小成交量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub MinVolume: i32,

    /// 触发条件
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ContingentCondition: i32,

    /// 止损价
    #[serde(skip_serializing_if = "comfun::is_zero_f64")]
    pub StopPrice: f64,

    /// 强平原因
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ForceCloseReason: i32,

    /// 自动挂起标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub IsAutoSuspend: i32,

    /// 业务单元
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BusinessUnit: String,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 用户强评标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub UserForceClose: i32,

    /// 互换单标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub IsSwapOrder: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// 资金账号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,

    /// 币种代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CurrencyID: String,

    /// 交易编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ClientID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// 组合编号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ComTradeID: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,
}
impl CtpReqInputOrderField {
    /// 设置开平标志
    fn set_offsetglag(&mut self, of: EOffsetFlag) {
        let of: i32 = of.into();
        self.CombOffsetFlag = of.to_string();
    }

    /// 设置投机套保标志
    fn set_hedgeflag(&mut self, hf: EHedgeFlag) {
        let hf: i32 = hf.into();
        self.CombHedgeFlag = hf.to_string();
    }
}

/// 请求报单录入响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputOrderField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 买卖方向
    pub Direction: i32,
}

/// 请求备兑解锁仓
#[derive(Default, Debug)]
pub struct CtpReqInputStocklockField {
    /// 交易所编码
    pub ExchID: String,

    /// 投资者编码
    pub InvestorID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 本地订单编码
    pub LocalOrderNo: i32,

    /// 锁定撤销标志
    pub Locked: i32,

    /// 数量
    pub Volume: i32,
}

/// 请求备兑解锁仓响应(请求共用CtpReqInputOrderField)
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputStockLockField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 交易所编码
    pub ExchID: String,

    /// 投资者编码
    pub InvestorID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 交易所订单编码
    pub OrderSysID: String,

    /// 本地订单编码
    pub LocalOrderNo: i32,

    /// 锁定撤销标志
    pub Locked: i32,

    /// 数量
    pub Volume: i32,
}

/// 请求报单操作
#[derive(Serialize)]
pub struct CtpReqInputOrderActionField {
    // CThostFtdcInputOrderActionField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 报单操作引用
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub OrderActionRef: i32,

    /// 报单引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OrderRef: String,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 前置编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub FrontID: i32,

    /// 会话编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub SessionID: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 报单编号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OrderSysID: String,

    /// 操作标志 **默认值为48(表示撤单),且只支持48**
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ActionFlag: i32,

    /// 价格
    #[serde(skip_serializing_if = "comfun::is_zero_f64")]
    pub LimitPrice: f64,

    /// 数量变化
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub VolumeChange: i32,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// 所有者类型
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OwnerType: String,

    /// 登陆PBU
    #[serde(skip_serializing_if = "String::is_empty")]
    pub LoginPBU: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,
}
impl Default for CtpReqInputOrderActionField {
    fn default() -> Self {
        Self {
            BrokerID: String::default(),
            InvestorID: String::default(),
            OrderActionRef: 0,
            OrderRef: String::default(),
            RequestID: 0,
            FrontID: 0,
            SessionID: 0,
            ExchangeID: String::default(),
            OrderSysID: String::default(),
            ActionFlag: 48, // 撤单,并且只支持值为48
            LimitPrice: 0f64,
            VolumeChange: 0,
            UserID: String::default(),
            InstrumentID: String::default(),
            InvestUnitID: String::default(),
            MacAddress: String::default(),
            OwnerType: String::default(),
            LoginPBU: String::default(),
            IPAddress: String::default(),
        }
    }
}

/// 请求报单操作响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputOrderActionField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 报单编号
    pub OrderSysID: String,
}

/// 请求行权
#[derive(Debug, Serialize)]
pub struct CtpReqInputExecOrderField {
    // CThostFtdcInputExecOrderField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 执行宣告引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExecOrderRef: String,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub Volume: i32,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 业务单元
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BusinessUnit: String,

    /// 开平标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub OffsetFlag: i32,

    /// 投机套保标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub HedgeFlag: i32,

    /// 执行类型(默认值:49-执行)
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ActionType: i32,

    /// 保留头寸申请的持仓方向
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub PosiDirection: i32,

    /// 期权行权后是否保留期货头寸的标记,该字段已废弃
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ReservePositionFlag: i32,

    /// 期权行权后生成的头寸是否自动平仓
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub CloseFlag: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// 资金账号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,

    /// 币种代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CurrencyID: String,

    /// 交易编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ClientID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,
}
impl Default for CtpReqInputExecOrderField {
    fn default() -> Self {
        Self {
            BrokerID: Default::default(),
            InvestorID: Default::default(),
            InstrumentID: Default::default(),
            ExecOrderRef: Default::default(),
            UserID: Default::default(),
            Volume: Default::default(),
            RequestID: Default::default(),
            BusinessUnit: Default::default(),
            OffsetFlag: Default::default(),
            HedgeFlag: Default::default(),
            ActionType: 49,
            PosiDirection: Default::default(),
            ReservePositionFlag: Default::default(),
            CloseFlag: Default::default(),
            ExchangeID: Default::default(),
            InvestUnitID: Default::default(),
            AccountID: Default::default(),
            CurrencyID: Default::default(),
            ClientID: Default::default(),
            MacAddress: Default::default(),
            IPAddress: Default::default(),
        }
    }
}

/// 请求行权响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputExecOrderField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,
}

/// 请求行权操作
#[derive(Debug, Serialize)]
pub struct CtpReqInputExecOrderActionField {
    // CThostFtdcInputExecOrderActionField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 执行宣告操作引用
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ExecOrderActionRef: i32,

    /// 执行宣告引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExecOrderRef: String,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 前置编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub FrontID: i32,

    /// 会话编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub SessionID: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 执行宣告操作编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExecOrderSysID: String,

    /// 操作标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ActionFlag: i32,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// 所有者类型
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OwnerType: String,

    /// 登陆PBU
    #[serde(skip_serializing_if = "String::is_empty")]
    pub LoginPBU: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,
}
impl Default for CtpReqInputExecOrderActionField {
    fn default() -> Self {
        Self {
            BrokerID: Default::default(),
            InvestorID: Default::default(),
            ExecOrderActionRef: Default::default(),
            ExecOrderRef: Default::default(),
            RequestID: Default::default(),
            FrontID: Default::default(),
            SessionID: Default::default(),
            ExchangeID: Default::default(),
            ExecOrderSysID: Default::default(),
            ActionFlag: 48, // 撤单
            UserID: Default::default(),
            InstrumentID: Default::default(),
            InvestUnitID: Default::default(),
            MacAddress: Default::default(),
            OwnerType: Default::default(),
            LoginPBU: Default::default(),
            IPAddress: Default::default(),
        }
    }
}

/// 请求行权操作响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputExecOrderActionField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 执行宣告操作编码
    pub ExecOrderSysID: String,
}

/// 请求申请组合录入
#[derive(Default, Debug, Serialize)]
pub struct CtpReqInputCombActionField {
    // CThostFtdcInputCombActionField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 组合引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CombActionRef: String,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 买卖方向
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub Direction: i32,

    /// 数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub Volume: i32,

    /// 组合指令方向
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub CombDirection: i32,

    /// 投机套保标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub HedgeFlag: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// 拆分组合编号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ComTradeID: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,

    /// 前置编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub FrontID: i32,

    /// 会话编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub SessionID: i32,
}

/// 请求申请组合录入响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputCombActionField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 拆分组合编号
    pub ComTradeID: String,
}

/// 请求行权指令合并录入
#[derive(Default, Debug, Serialize)]
pub struct CtpReqInputExecCombineOrderField {
    // CThostFtdcInputExecCombineOrderField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 看涨合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CallInstrumentID: String,

    /// 看跌合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub PutInstrumentID: String,

    /// 执行宣告合并引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExecCombineOrderRef: String,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub Volume: i32,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 业务单元
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BusinessUnit: String,

    /// 执行类型
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ActionType: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// 交易编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ClientID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,
}

/// 请求行权指令合并录入响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputExecCombineOrderField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,
}

/// 请求行权指令合并操作
#[derive(Debug, Serialize)]
pub struct CtpReqInputExecCombineOrderActionField {
    // CThostFtdcInputExecCombineOrderActionField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 执行宣告合并操作引用
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ExecCombineOrderActionRef: i32,

    /// 执行宣告合并引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExecCombineOrderRef: String,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 前置编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub FrontID: i32,

    /// 会话编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub SessionID: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 执行宣告合并操作编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExecCombineOrderSysID: String,

    /// 操作标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ActionFlag: i32,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,
}
impl Default for CtpReqInputExecCombineOrderActionField {
    fn default() -> Self {
        Self {
            BrokerID: Default::default(),
            InvestorID: Default::default(),
            ExecCombineOrderActionRef: Default::default(),
            ExecCombineOrderRef: Default::default(),
            RequestID: Default::default(),
            FrontID: Default::default(),
            SessionID: Default::default(),
            ExchangeID: Default::default(),
            ExecCombineOrderSysID: Default::default(),
            ActionFlag: 48, // 撤单
            UserID: Default::default(),
            InvestUnitID: Default::default(),
            MacAddress: Default::default(),
            IPAddress: Default::default(),
        }
    }
}

/// 请求行权指令合并操作响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputExecCombineOrderActionField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 执行宣告合并操作编码
    pub ExecCombineOrderSysID: String,
}

/// 请求报价录入
#[derive(Default, Serialize)]
pub struct CtpReqInputQuoteField {
    // CThostFtdcInputQuoteField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 报单引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub QuoteRef: String,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 卖价格
    #[serde(skip_serializing_if = "comfun::is_zero_f64")]
    pub AskPrice: f64,

    /// 买价格
    #[serde(skip_serializing_if = "comfun::is_zero_f64")]
    pub BidPrice: f64,

    /// 卖数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub AskVolume: i32,

    /// 买数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BidVolume: i32,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 业务单元
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BusinessUnit: String,

    /// 卖开平标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub AskOffsetFlag: i32,

    /// 买开平标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BidOffsetFlag: i32,

    /// 卖投机套保标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub AskHedgeFlag: i32,

    /// 买投机套保标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BidHedgeFlag: i32,

    /// 衍生卖报单引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AskOrderRef: String,

    /// 衍生买报单引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BidOrderRef: String,

    /// 应价编号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ForQuoteSysID: String,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// 交易编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ClientID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,

    /// 被顶单编号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ReplaceSysID: String,
}

/// 请求报价录入响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputQuoteField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,
}

/// 请求报价操作
#[derive(Debug, Serialize)]
pub struct CtpReqInputQuoteActionField {
    // CThostFtdcInputQuoteActionField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 报价操作引用
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub QuoteActionRef: i32,

    /// 报价引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub QuoteRef: String,

    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 前置编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub FrontID: i32,

    /// 会话编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub SessionID: i32,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 报价操作编号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub QuoteSysID: String,

    /// 操作标志
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ActionFlag: i32,

    /// 用户代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,

    /// 交易编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ClientID: String,

    /// Mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,

    /// 卖数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub AskVolume: i32,

    /// 买数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BidVolume: i32,

    /// IP地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IPAddress: String,
}
impl Default for CtpReqInputQuoteActionField {
    fn default() -> Self {
        Self {
            BrokerID: Default::default(),
            InvestorID: Default::default(),
            QuoteActionRef: Default::default(),
            QuoteRef: Default::default(),
            RequestID: Default::default(),
            FrontID: Default::default(),
            SessionID: Default::default(),
            ExchangeID: Default::default(),
            QuoteSysID: Default::default(),
            ActionFlag: 48, // 撤单
            UserID: Default::default(),
            InstrumentID: Default::default(),
            InvestUnitID: Default::default(),
            ClientID: Default::default(),
            MacAddress: Default::default(),
            AskVolume: Default::default(),
            BidVolume: Default::default(),
            IPAddress: Default::default(),
        }
    }
}

/// 请求报价操作响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspInputQuoteActionField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 报价操作编号
    pub QuoteSysID: String,
}

/// 非交易业务请求报单录入
#[derive(Default, Debug, Serialize)]
pub struct CtpReqBusinessOrderField {
    // CThostFtdcBusinessInsertField
    /// 请求序号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ReqID: i32,

    /// 业务类型
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BusinessType: i32,

    /// 资金账号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchID: String,

    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InsID: String,

    /// 目标合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub DstInsID: String,

    /// 买卖方向
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub Side: i32,

    /// 数量
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub Volume: i32,

    /// 价格
    #[serde(skip_serializing_if = "comfun::is_zero_f64")]
    pub Price: f64,

    /// 报单引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OrderRef: String,
}

/// 非交易业务请求报单录入响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspBusinessOrderField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,
}

/// 非交易业务请求报单操作
#[derive(Default, Serialize)]
pub struct CtpReqBusinessOrderActionField {
    // CThostFtdcBusinessActionField
    /// 请求编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub RequestID: i32,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 报单操作引用
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub OrderActionRef: i32,

    /// 前置编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub FrontID: i32,

    /// 会话编号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub SessionID: i32,

    /// 报单引用
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OrderRef: String,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 报单编号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OrderSysID: String,
}

/// 非交易业务请求报单操作响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspBusinessOrderActionField {
    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    /// 报单编号
    pub OrderSysID: String,
}
