import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";

export component RightMiddlePage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "其他1" },
        { text: "其他2" },
        { text: "其他3" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.right_middle_selidx;
        }

        if 0 == StkRsk_TabSelIdx.right_middle_selidx: Rectangle { Text {text: "其他1";}}
        if 1 == StkRsk_TabSelIdx.right_middle_selidx: Rectangle { Text {text: "其他2";}}
        if 2 == StkRsk_TabSelIdx.right_middle_selidx: Rectangle { Text {text: "其他3";}}
    }
}
