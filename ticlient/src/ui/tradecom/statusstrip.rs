use crate::slintui::*;
use slint::*;

/// 状态栏
pub(in crate::ui) struct StatusStrip {}

impl StatusStrip {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let ss_login_info = crate::common::global::get_ss_trd_longinfo();
        let app_ss = app.global::<AppStatusStrip>();
        app_ss.set_uid(ss_login_info.0.into());
        app_ss.set_login_status(ss_login_info.1);
        app_ss.set_login_tips(ss_login_info.2.into());
    }
}

impl StatusStrip {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        return;
        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_ss = app.global::<AppStatusStrip>();

            // 本地时间使用东8区时间. 8小时偏移量，即东8区
            let eastern_offset = chrono::FixedOffset::east_opt(8 * 3600).unwrap();
            app_ss.set_time(slint::format!(
                "{}",
                chrono::Utc::now().with_timezone(&eastern_offset).format("%H:%M:%S")
            ));
        });
    }
}
