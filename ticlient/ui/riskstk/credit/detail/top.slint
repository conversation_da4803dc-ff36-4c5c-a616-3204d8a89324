import { Theme, TabView, TabBarItem, Label, ComboBox } from "@zstk/lib.slint";
import { AppCom } from "../../../struct.slint";
import { HorizonSpliterRec } from "../../../widgets/spliterrec.slint";
import { AccountPage } from "../main/account.slint";

import { StkRskCredit_Com,
         StkRskCredit_Account, StkRskCredit_Position,
         StkRskCredit_CreditContract,
         StkRskCredit_CreditLimitAmt, StkRskCredit_CreditLimitPos,
         StkRskCredit_CreditTcAmt, StkRskCredit_CreditTcPos,
         StkRskCredit_CreditConcentration, StkRskCredit_InOrder,
         StkRskCredit_QryCreditReAmtDtl, StkRskCredit_QryWithdrawDeposit, StkRskCredit_QryCreditLimit, StkRskCredit_QryCreditTcAmt,
         StkRskCredit_QryOrder, StkRskCredit_QryTrade, StkRskCredit_QryPosTrans
} from "../struct.slint";

export component TopPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 10px;

        HorizontalLayout {
            padding-top: 5px;
            padding_left: 1px;
            padding_right: 10px;
            spacing: 10px;

            Label {
                text: "资金账户";
                vertical_alignment: center;
            }
            ComboBox {
                width: 130px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                can_scroll: false;
                current_text <=> StkRskCredit_Com.accountid;
                model <=> StkRskCredit_Com.accid_model;

                changed current_text => {
                    StkRskCredit_Account.filter_changed();
                    StkRskCredit_Position.filter_changed();
                    StkRskCredit_CreditContract.filter_changed();
                    StkRskCredit_CreditLimitAmt.filter_changed();
                    StkRskCredit_CreditLimitPos.filter_changed();
                    StkRskCredit_CreditTcAmt.filter_changed();
                    StkRskCredit_CreditTcPos.filter_changed();
                    StkRskCredit_CreditConcentration.filter_changed();
                    StkRskCredit_InOrder.reset_datas();

                    StkRskCredit_QryCreditReAmtDtl.reset_datas();
                    StkRskCredit_QryCreditLimit.reset_datas();
                    StkRskCredit_QryWithdrawDeposit.reset_datas();
                    StkRskCredit_QryCreditTcAmt.reset_datas();
                    StkRskCredit_QryOrder.reset_datas();
                    StkRskCredit_QryTrade.reset_datas();
                    StkRskCredit_QryPosTrans.reset_datas();
                }
            }

            Rectangle {}

            Label {
                text: StkRskCredit_Com.trd_login_tips;
            }
        }

        HorizonSpliterRec {
            height: 2px;
        }

        AccountPage {
            is_used_for_main: false;
        }
    }
}
