import { <PERSON>, Fonts, Label, LineEdit, Button, ComboBox, TableView, TVCellType, Radius, Icons, ListViewItem } from "@zstk/lib.slint";
import { ColWidth, WidgetColor, AppCom, NotifyColor } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { OptTrd_Com, OptTrd_Account } from "struct.slint";

export global OptTrd_QryFundTransDtl {
    in property <[[string]]> row_data;

    in_out property <string> accid;         // 资金账户
    in_out property <string> tradedate;     // 操作日期
    in_out property <string> transstatus;   // 划转状态
    in_out property <string> passwd;        // 密码

    in_out property <string> sel_accid;
    in_out property <string> sel_tradedate;
    in_out property <string> sel_transno;
    callback re_fund_trans();

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clicked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);

    pure callback get_row_data_color(int, int, string) -> brush;
}

export component QryFundTransDtlPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "资金账户";
                vertical_alignment: center;
            }
            i_le_accid := LineEdit {
                width: 120px;
                text: "" != OptTrd_Account.sel_accid ? OptTrd_Account.sel_accid : OptTrd_Com.accountid;
                read_only: true;
            }

            Label {
                text: "操作日期";
                vertical_alignment: center;
            }
            LineEdit {
                width: 120px;
                text <=> OptTrd_QryFundTransDtl.tradedate;
                placeholder_text: "格式 yyyyMMdd";
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "划转状态";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_QryFundTransDtl.transstatus;
                model: [
                    { text: "" },
                    { text: "成功" },
                    { text: "失败" },
                    { text: "未操作" },
                ];
            }

            Label {
                text: "密码";
                vertical_alignment: center;
            }
            LineEdit {
                width: 120px;
                text <=> OptTrd_Com.transpwd;
                input_type: password;
                placeholder_text: "登录密码";
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Button {
                text: "查询";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                    if AppCom.svr_build_date < 20250401 {
                        AppCom.app_ret.msg = @tr("资金划转记录仅在服务端版本日期为 20250401 及之后的版本生效\n\n当前服务端版本日期: {}\n\n请升级服务端 ...", 0 != AppCom.svr_build_date ? AppCom.svr_build_date : "未知");
                        AppCom.app_ret.state = 2;
                        return ;
                    }

                    OptTrd_QryFundTransDtl.accid = i_le_accid.text;
                    OptTrd_QryFundTransDtl.passwd = OptTrd_Com.transpwd;
                    OptTrd_QryFundTransDtl.qry_clicked();
                }
            }

            Rectangle { }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 1,
                fix_tail_count: 1,
                columns: [
                    { title: @tr("序号"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("划转方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("划转对象"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_120 },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    { title: @tr("状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("是否冲正"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("操作日期"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("操作时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("消息"), alignment: TextHorizontalAlignment.left, width: 800px },
                    { title: @tr(""), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80, cell_type: TVCellType.HyperlinkText },
                ]
            };
            row_count: OptTrd_QryFundTransDtl.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_QryFundTransDtl.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                // 划转方向, 金额, 状态, 是否冲正
                if 1 == column_index || 4 == column_index || 5 == column_index || 6 == column_index {
                    return OptTrd_QryFundTransDtl.get_row_data_color(row_index, column_index, data);
                }

                // 继续划转
                if 10 == column_index {
                    return Theme.accent_background;
                }

                NotifyColor.default
            }

            cell_pointer_event(row_index, column_index, event, point) => {
                if PointerEventKind.up == event.kind {
                    self.select_full_row = true;
                    self.set_current_row(row_index);

                    if PointerEventButton.left == event.button {
                        if 10 == column_index && "" != OptTrd_QryFundTransDtl.row_data[row_index][10]{
                            OptTrd_QryFundTransDtl.sel_transno = OptTrd_QryFundTransDtl.row_data[row_index][11];
                            OptTrd_QryFundTransDtl.sel_accid = OptTrd_QryFundTransDtl.row_data[row_index][3];
                            OptTrd_QryFundTransDtl.sel_tradedate = OptTrd_QryFundTransDtl.row_data[row_index][7];
                            OptTrd_QryFundTransDtl.re_fund_trans();
                        }
                    } else if PointerEventButton.right == event.button && PointerEventKind.up == event.kind {
                        if "成功" != OptTrd_QryFundTransDtl.row_data[row_index][5] {
                            OptTrd_QryFundTransDtl.sel_transno = OptTrd_QryFundTransDtl.row_data[row_index][11];
                            OptTrd_QryFundTransDtl.sel_accid = OptTrd_QryFundTransDtl.row_data[row_index][3];
                            OptTrd_QryFundTransDtl.sel_tradedate = OptTrd_QryFundTransDtl.row_data[row_index][7];

                            right_up_point = point;
                            i_pw_menu.show();
                        }
                    }
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_QryFundTransDtl.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_QryFundTransDtl.row_data[row_index]);
                }
            }
        }

        // 服务端实现本功能时是按分页实现的. 此时 admin 对应的版本如果不需要分页字段则会返回全部数据导致分页功能出错
        if OptTrd_QryFundTransDtl.row_data.length > 0 : PageItem {
            height: 28px;
            unit: 1;
            page_index_model <=> OptTrd_QryFundTransDtl.page_index_model;
            item_total <=> OptTrd_QryFundTransDtl.item_total;
            page_total <=> OptTrd_QryFundTransDtl.page_total;
            page_index <=> OptTrd_QryFundTransDtl.page_index;
            page_size <=> OptTrd_QryFundTransDtl.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                OptTrd_QryFundTransDtl.page_index_changed(index);
            }

            page_size_changed(size) => {
                OptTrd_QryFundTransDtl.page_size_changed(size);
            }
        }
    }

    i_pw_menu := PopupWindow {

        x: right_up_point.x - root.absolute_position.x;
        y: right_up_point.y - root.absolute_position.y;

        Rectangle {

            width: 100px;

            border-width: 1px;
            border-color: Theme.border;
            border_radius: Radius.extra_small;

            drop_shadow_blur: 15px;
            drop_shadow_color: Theme.control_background.darker(0.5);

            background: Theme.control_background;

            VerticalLayout {
                padding: 5px;
                spacing: 10px;

                i_menu_item_0 := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 14px;
                            colorize: Theme.foreground;
                            source: Icons.list;
                        }

                        Label {
                            text: "继续划转";
                            horizontal-alignment: left;
                        }
                    }

                    i_menu_fundtrans_ta := TouchArea {
                        clicked => {
                            OptTrd_QryFundTransDtl.re_fund_trans();
                        }
                    }
                }
            }
        }

        states [
            item_0_has_hover when i_menu_fundtrans_ta.has_hover: {
                i_menu_item_0.background: Theme.shadow;
            }
        ]
    }
}
