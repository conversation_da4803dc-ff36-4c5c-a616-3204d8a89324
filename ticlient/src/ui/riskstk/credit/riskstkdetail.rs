use crate::{common::global::TOKIO_RT, slintui::*, ui::IApp};
use slint::*;

use std::{
    sync::{Arc, RwLock},
    thread,
};
use tokio::time::{interval, Duration};

pub struct RiskStkDetailPage {
    run_falg: Arc<RwLock<bool>>,
    thread: Option<thread::<PERSON><PERSON><PERSON><PERSON><PERSON><()>>,

    account: Arc<super::detail::account::Account>,
    position: Arc<super::detail::position::Position>,
    credit_contract: Arc<super::detail::credit_contract::CreditContract>,
    credit_limitamt: Arc<super::detail::credit_limitamt::CreditLimitAmt>,
    credit_limitpos: Arc<super::detail::credit_limitpos::CreditLimitPos>,
    credit_tcamt: Arc<super::detail::credit_tcamt::CreditTcAmt>,
    credit_tcpos: Arc<super::detail::credit_tcpos::CreditTcPos>,
    credit_concentration: Arc<super::detail::credit_concentration::CreditConcentration>,
    qry_credit_reamtdtl: Arc<super::detail::qry_credit_reamt_dtl::QueryCreditReAmtDtl>,
    qry_withdraw_deposit: Arc<super::detail::qry_withdraw_deposit::QueryWithdrawDeposit>,
    qry_credit_limit: Arc<super::detail::qry_credit_limit::QueryCreditLimit>,
    qry_credit_tcamt: Arc<super::detail::qry_credit_tcamt::QueryCreditTcAmt>,
}

impl RiskStkDetailPage {
    pub fn new() -> Self {
        Self {
            run_falg: Arc::new(RwLock::new(true)),
            thread: None,

            account: Arc::new(super::detail::account::Account::new()),
            position: Arc::new(super::detail::position::Position::new()),
            credit_contract: Arc::new(super::detail::credit_contract::CreditContract::new()),
            credit_limitamt: Arc::new(super::detail::credit_limitamt::CreditLimitAmt::new()),
            credit_limitpos: Arc::new(super::detail::credit_limitpos::CreditLimitPos::new()),
            credit_tcamt: Arc::new(super::detail::credit_tcamt::CreditTcAmt::new()),
            credit_tcpos: Arc::new(super::detail::credit_tcpos::CreditTcPos::new()),
            credit_concentration: Arc::new(super::detail::credit_concentration::CreditConcentration::new()),
            qry_credit_reamtdtl: Arc::new(super::detail::qry_credit_reamt_dtl::QueryCreditReAmtDtl::new()),
            qry_withdraw_deposit: Arc::new(super::detail::qry_withdraw_deposit::QueryWithdrawDeposit::new()),
            qry_credit_limit: Arc::new(super::detail::qry_credit_limit::QueryCreditLimit::new()),
            qry_credit_tcamt: Arc::new(super::detail::qry_credit_tcamt::QueryCreditTcAmt::new()),
        }
    }

    /// UI 更新任务
    pub fn start_ui_update_task(&self, app_weak: Weak<RiskStkCreditDetailPage>, run_flag: Arc<RwLock<bool>>) {
        let account_clone = self.account.clone();
        let position_clone = self.position.clone();
        let credit_contract_clone = self.credit_contract.clone();
        let credit_limitamt_clone = self.credit_limitamt.clone();
        let credit_limitpos_clone = self.credit_limitpos.clone();
        let credit_tcamt_clone = self.credit_tcamt.clone();
        let credit_tcpos_clone = self.credit_tcpos.clone();
        let credit_concentration_clone = self.credit_concentration.clone();

        // 启动 tokio 异步任务
        TOKIO_RT.get().unwrap().spawn(async move {
            let mut input_timer = interval(Duration::from_millis(1000));
            let mut other_timer = interval(Duration::from_millis(500));

            loop {
                // 退出条件
                if !*run_flag.read().unwrap() {
                    break;
                }

                tokio::select! {
                    _ = input_timer.tick() => {
                    }
                    _ = other_timer.tick() => {
                        account_clone.update(app_weak.clone()).await;
                        position_clone.update(app_weak.clone()).await;
                        credit_contract_clone.update(app_weak.clone()).await;
                        credit_limitamt_clone.update(app_weak.clone()).await;
                        credit_limitpos_clone.update(app_weak.clone()).await;
                        credit_tcamt_clone.update(app_weak.clone()).await;
                        credit_tcpos_clone.update(app_weak.clone()).await;
                        credit_concentration_clone.update(app_weak.clone()).await;
                    }
                }
            }
            log::info!(
                "run_flag:{}, is_connected:{}. ui update task end....",
                *run_flag.read().unwrap(),
                false /*abnormalstate_clone.is_connected()*/
            );
        });
    }
}

impl IApp for RiskStkDetailPage {
    fn setup_rsk_stk_credit(&mut self, app: &crate::slintui::RiskStkCreditDetailPage) {
        log::info!("RiskStkDetailPage started");

        // 初始化
        {
            log::info!("App init started");

            self.account.init(&app);
            self.position.init(&app);
            self.credit_contract.init(&app);
            self.credit_limitamt.init(&app);
            self.credit_limitpos.init(&app);
            self.credit_tcamt.init(&app);
            self.credit_tcpos.init(&app);
            self.credit_concentration.init(&app);
            self.qry_credit_reamtdtl.init(&app);
            self.qry_withdraw_deposit.init(&app);
            self.qry_credit_limit.init(&app);
            self.qry_credit_tcamt.init(&app);

            log::info!("App init completed");
        }

        // 逻辑更新线程
        {
            log::info!("App start qyery thread started");

            let account_clone = self.account.clone();
            let position_clone = self.position.clone();
            let credit_contract_clone = self.credit_contract.clone();
            let credit_limitamt_clone = self.credit_limitamt.clone();
            let credit_limitpos_clone = self.credit_limitpos.clone();
            let credit_tcamt_clone = self.credit_tcamt.clone();
            let credit_tcpos_clone = self.credit_tcpos.clone();
            let credit_concentration_clone = self.credit_concentration.clone();

            let run_falg: Arc<RwLock<bool>> = self.run_falg.clone();
            self.thread = Some(thread::spawn(move || {
                loop {
                    if !*run_falg.read().unwrap() {
                        break;
                    }

                    account_clone.qry_account();
                    position_clone.qry_position();
                    credit_contract_clone.qry_credit_contract();
                    credit_limitamt_clone.qry_credit_limit();
                    credit_limitpos_clone.qry_credit_limit();
                    credit_tcamt_clone.qry_credit_tcamt();
                    credit_tcpos_clone.qry_credit_tcpos();
                    credit_concentration_clone.qry_credit_concentration();

                    thread::sleep(std::time::Duration::from_secs(1));
                }

                log::info!(
                    "run_falg:{}, is_connected:{}. qry thread is end....",
                    *run_falg.read().unwrap(),
                    false /*abnormalstate_clone.is_connected()*/
                );
            }));

            log::info!("App start qyery thread completed");
        }

        // UI更新线程
        {
            log::info!("App start ui thread started");

            self.start_ui_update_task(app.as_weak(), self.run_falg.clone());

            log::info!("App start ui update thread completed");
        }
    }

    fn teardown(&mut self) {
        log::info!("RiskStkDetailPage stop started");
    }
}
