use tiapi::protocol_pub::app::{AppTDServerErrorField, AppTDSessionTimeoutField};

pub type TdServerErrorMap = dashmap::DashMap<i32, AppTDServerErrorField>;
pub type TdSessionTimeoutMap = dashmap::DashMap<i32, AppTDSessionTimeoutField>;

/// 异常状态回调
pub trait IOnRtnAbnormalState: Send + Sync {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {}

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {}
}