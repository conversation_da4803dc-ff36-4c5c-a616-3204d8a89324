import { <PERSON>, Radius, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TableView, Label, ComboBox } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";
import { OptTrd_TabSelIdx, OptTrd_Position, OptTrd_InputOrder, OptTrd_InputOml, OptTrd_InputExercise } from "struct.slint";

export component PositionPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 80px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_Position.exchid;
                model: CommonModel.exch_opt_model;

                selected => {
                    OptTrd_Position.filter_changed();
                }
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }
            i_cbx_insid := ComboBox {
                width: 110px;
                max_popup_height: root.height - 50px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> OptTrd_Position.insid;
                model <=> OptTrd_Position.insid_model;

                edited(by_selected) => {
                    OptTrd_Position.insid_text_changed();

                    if !by_selected {
                        OptTrd_Position.filter_changed();
                    }
                }

                selected => {
                    OptTrd_Position.filter_changed();
                }
            }

            Label {
                text: "持仓方向";
                vertical_alignment: center;
            }
            i_cbx_poddir := ComboBox {
                width: 80px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_Position.posdir;
                model: [
                    { text: "" },
                    { text: "多头" },
                    { text: "空头" },
                ];

                selected => {
                    OptTrd_Position.posdir_idx = self.current_index;
                    OptTrd_Position.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    OptTrd_Position.export_clicked(type);
                }
            }
        }

        TableView {
            sortable: true;
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 3,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insid },
                    { title: @tr("合约名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insname },

                    { title: @tr("持仓方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_75 },
                    { title: @tr("持仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("实收保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("交易所保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },

                    { title: @tr("组合冻结 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "参与组合的数量" },
                    { title: @tr("在途冻结 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "报单平仓未成交的冻结 + 行权冻结" },
                    { title: @tr("可平仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "持仓量 - 组合冻结 - 在途冻结" },
                    { title: @tr("今日持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "开仓量 - 平仓量" },
                    { title: @tr("上日持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日结算持仓量, 盘中该值不会变动" },
                    { title: @tr("开仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("平仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("转入量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips:"普通仓转备兑仓或备兑仓转普通仓的数量" },
                    { title: @tr("转出量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips:"普通仓转备兑仓或备兑仓转普通仓的数量" },
                    { title: @tr("备兑标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.hedge },
                ]
            };
            row_count: OptTrd_Position.row_data.length;

            sort_canceled => {
                OptTrd_Position.sort_asc_column_index = -1;
                OptTrd_Position.sort_dec_column_index = -1;
            }

            sort_ascending(index) => {
                OptTrd_Position.sort_ascending(index);
                OptTrd_Position.sort_asc_column_index = index;
                OptTrd_Position.sort_dec_column_index = -1;
                true
            }

            sort_descending(index) => {
                OptTrd_Position.sort_descending(index);
                OptTrd_Position.sort_asc_column_index = -1;
                OptTrd_Position.sort_dec_column_index = index;
                true
            }

            get_cell_data(row_index, column_index) => {
                OptTrd_Position.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                OptTrd_Position.get_row_data_color(row_index, column_index, data)
            }

            cell_double_clicked(row_index, column_index) => {
                // 组合录入
                if 1 == OptTrd_TabSelIdx.input_selidx {
                    OptTrd_InputOml.sel_position(OptTrd_Position.row_data[row_index][0],        // accid
                                                 OptTrd_Position.row_data[row_index][1],        // exchid
                                                 OptTrd_Position.row_data[row_index][2],        // insid
                                                 OptTrd_Position.row_data[row_index][4]         // dir
                        );
                    return ;
                }

                // 行权录入
                if 2 == OptTrd_TabSelIdx.input_selidx {
                    if "多" == OptTrd_Position.row_data[row_index][4] {
                        OptTrd_InputExercise.sel_position(OptTrd_Position.row_data[row_index][0],   // accid
                                                          OptTrd_Position.row_data[row_index][1],   // exchid
                                                          OptTrd_Position.row_data[row_index][2],   // insid
                                                          OptTrd_Position.row_data[row_index][10]   // vol
                            );
                    }
                    return;
                }

                // 其他均自动跳转到报单录入
                OptTrd_TabSelIdx.input_selidx = 0;
                OptTrd_InputOrder.sel_position(OptTrd_Position.row_data[row_index][0],      // accid
                    OptTrd_Position.row_data[row_index][1],                                 // exchid
                    OptTrd_Position.row_data[row_index][2],                                 // insid
                    OptTrd_Position.row_data[row_index][4],                                 // dir
                    OptTrd_Position.row_data[row_index][10],                                // vol
                    OptTrd_Position.row_data[row_index][17]                                 // covered
                    );
            }

            cell_pointer_event(row_index, column_index, event, point) => {
                if PointerEventButton.right == event.button || PointerEventButton.left == event.button {
                    if PointerEventButton.right == event.button && PointerEventKind.up == event.kind {
                        self.select_full_row = true;
                        self.set_current_row(row_index);

                        if "空" == OptTrd_Position.row_data[row_index][4] {
                            OptTrd_Position.sel_cc_volume = OptTrd_Position.row_data[row_index][10];
                            OptTrd_Position.sel_cc_accid = OptTrd_Position.row_data[row_index][0];
                            OptTrd_Position.sel_cc_exchid = OptTrd_Position.row_data[row_index][1];
                            OptTrd_Position.sel_cc_insid = OptTrd_Position.row_data[row_index][2];
                            OptTrd_Position.sel_cc_covered = OptTrd_Position.row_data[row_index][17];
                            if OptTrd_Position.pre_deal_covered_convert() {
                                right_up_point = point;
                                i_pw_menu.show();
                            }
                        }
                    }
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_Position.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_Position.row_data[row_index]);
                }
            }
        }
    }

    i_pw_menu := PopupWindow {

        x: right_up_point.x - root.absolute_position.x;
        y: right_up_point.y - root.absolute_position.y;

        Rectangle {

            width: 100px;

            border_width: 1px;
            border_color: Theme.border;
            border_radius: Radius.extra_small;

            drop_shadow_blur: 15px;
            drop_shadow_color: Theme.control_background.darker(0.5);

            background: Theme.control_background;

            VerticalLayout {
                padding: 5px;
                spacing: 10px;

                i_menu_fundtrans := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 14px;
                            colorize: Theme.foreground;
                            source: Icons.list;
                        }

                        Label {
                            text: 1 == OptTrd_Position.show_covered_type ? "转备兑仓" : "转普通仓";
                            horizontal_alignment: left;
                        }
                    }

                    i_menu_fundtrans_ta := TouchArea {
                        clicked => {
                            OptTrd_Position.show_covered_convert = true;
                        }
                    }
                }
            }
        }

        states [
            postrans_has_hover when i_menu_fundtrans_ta.has_hover: {
                i_menu_fundtrans.background: Theme.shadow;
            }
        ]
    }
}
