use crate::serverinfo::MDServerInfo;
use std::{thread, time};

use futures_util::{future, pin_mut, StreamExt};
use tokio::net::TcpStream;
use tokio_tungstenite::{
    connect_async, connect_async_tls_with_config, tungstenite::protocol::Message, MaybeTlsStream, WebSocketStream,
};

/// WS客户端
pub(crate) struct WsClient {
    /// 服务端信息
    svrinfo: MDServerInfo,

    /// 发送通道
    chn_sx: Option<futures_channel::mpsc::UnboundedSender<Message>>,
}

impl WsClient {
    pub fn new() -> Self {
        Self {
            svrinfo: MDServerInfo::default(),
            chn_sx: None,
        }
    }

    /// 释放资源
    pub fn release(&mut self) {
        self.chn_sx = None; // 触发接收线程退出
        thread::sleep(time::Duration::from_secs(1));
    }

    /// 设置服务端信息
    pub fn set_svrinfo(&mut self, si: &MDServerInfo) {
        self.svrinfo = si.clone();
    }

    /// 发送消息
    pub fn send_msg(&self, msg: String) -> crate::Result<()> {
        if self.chn_sx.is_none() {
            return Err("chn_sx is none".to_owned());
        }

        self.chn_sx
            .as_ref()
            .unwrap()
            .unbounded_send(Message::Text(msg.into()))
            .map_err(|err| err.to_string())
    }

    /// 运行
    pub async fn run(&mut self, sender: crossbeam_channel::Sender<tokio_tungstenite::tungstenite::Message>) -> crate::Result<()> {
        // 解析地址
        let hostaddr = self.svrinfo.get_url();
        let url = url::Url::parse(&hostaddr);
        if url.is_err() {
            // 解析URL失败
            return Err(format!("{}", url.err().unwrap().to_string()));
        }

        // 连接服务端
        let ret_cnn: Result<
            (
                WebSocketStream<MaybeTlsStream<TcpStream>>,
                tokio_tungstenite::tungstenite::http::Response<Option<Vec<u8>>>,
            ),
            tokio_tungstenite::tungstenite::Error,
        >;
        if 1 == self.svrinfo.encon {
            let connector = native_tls::TlsConnector::builder()
                .danger_accept_invalid_certs(true)
                .build()
                .unwrap();

            ret_cnn = connect_async_tls_with_config(
                url.unwrap(),
                None,
                false,
                Some(tokio_tungstenite::Connector::NativeTls(connector)),
            )
            .await;
        } else {
            ret_cnn = connect_async(url.unwrap()).await;
        }
        if ret_cnn.is_err() {
            // 连接服务端失败
            return Err(ret_cnn.err().unwrap().to_string());
        }

        let (chn_tx, chn_rx) = futures_channel::mpsc::unbounded();
        tokio::spawn(Self::recv_run(chn_rx, ret_cnn.unwrap().0, sender));
        self.chn_sx = Some(chn_tx); // 保存发送通道

        Ok(())
    }
}

impl WsClient {
    /// 消息接收函数
    async fn recv_run(
        chn_rx: futures_channel::mpsc::UnboundedReceiver<Message>,
        ws_stream: WebSocketStream<MaybeTlsStream<TcpStream>>,
        sender: crossbeam_channel::Sender<Message>,
    ) {
        let (write, read) = ws_stream.split();
        let write_to_ws = chn_rx.map(Ok).forward(write);
        let ws_to_recv = {
            read.for_each(|message| async {
                let mut errglag = false;

                if message.is_ok() {
                    let msg = message.unwrap();
                    if msg.is_close() {
                        log::warn!("[in]: md ws closed. {:?}", msg);
                    }

                    let _ = sender.send(msg);
                } else {
                    log::error!("md ws rec error. {:?}", message.err().unwrap());

                    errglag = true;

                    // 读取失败后添加一条关闭消息到队列
                    let msg = Message::Close(None);
                    let _ = sender.send(msg);
                }

                // 已经发生错误，退出
                if errglag {
                    return;
                }
            })
        };

        pin_mut!(write_to_ws, ws_to_recv);
        future::select(write_to_ws, ws_to_recv).await;
        log::info!("md receive thread closed");
    }
}
