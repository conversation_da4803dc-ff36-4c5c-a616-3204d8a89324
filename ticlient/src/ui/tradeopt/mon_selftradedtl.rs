use std::rc::Rc;

use crate::{
    common::{config::Config, global::TRADE_OPT_BUF, ticonvert::TIConvert}, show_msg_box, slintui::*
};
use slint::*;
use tiapi::protocol_pub::monitor_trade_ctp::CtpReqQrySelfTradeDtlField;

/// 查询 - 自成交详情
pub(super) struct MonSelfTradeDtl {}

impl MonSelfTradeDtl {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let qry = app.global::<OptTrd_MonSelfTradeDtl>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || MonSelfTradeDtl::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| MonSelfTradeDtl::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| MonSelfTradeDtl::on_page_size_changed(&app_weak, size));

        log::trace!("Monitor self trade detail init completed");
    }
}

impl MonSelfTradeDtl {
    /// 查询事件
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qrystd = app.global::<OptTrd_MonSelfTradeDtl>();

        // 清空上一次结果
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qrystd.set_row_data(row_data.into());

        let req = CtpReqQrySelfTradeDtlField {
            ExchID: TIConvert::exch_id(&app_qrystd.get_exchid().as_str()),
            AccountID: "".into(),
            InstrumentID: app_qrystd.get_insid().into(),
            TradeID: app_qrystd.get_tradeid().into(),
            OrderSysID: app_qrystd.get_ordersysid().into(),
            TransTime0: app_qrystd.get_starttime_int(),
            TransTime1: app_qrystd.get_endtime_int(),
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qrystd.get_page_index()
            }
        };
        let page_size = app_qrystd.get_page_size();

        let ret = {
            TRADE_OPT_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_selftradedtl(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qrystd.set_page_index_model(page_index_data.into());
            app_qrystd.set_item_total(ret.0);
            app_qrystd.set_page_index(0);
            app_qrystd.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|trd| {
            let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

            // 资金账号
            items.push(trd.AccountID.clone().into());

            // 交易所
            items.push(TIConvert::exch_name(trd.ExchID).into());

            // 交易编码
            items.push(trd.ClientID.clone().into());

            // 发生时间
            items.push(TIConvert::transtime(trd.TransTime).into());

            // 合约编码
            items.push(trd.InstrumentID.clone().into());

            // 买卖方向
            items.push(TIConvert::side(trd.Side).into());

            // 开平标志
            items.push(TIConvert::offsetflag(trd.OffsetFlag).into());

            // 成交数量
            items.push(slint::format!("{}", trd.TradeVolume));

            // 成交价格
            items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, trd.TradePrice));

            // 成交编号
            items.push(trd.TradeID.clone().into());

            // 交易所报单编号
            items.push(trd.OrderSysID.clone().into());

            // 本地报单编号
            items.push(slint::format!("{}", trd.LocalOrderNo));

            // 备兑标志
            items.push(TIConvert::coverd_flag(trd.Covered).into());

            row_data.push(items.into());
        });

        app_qrystd.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        MonSelfTradeDtl::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        MonSelfTradeDtl::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        MonSelfTradeDtl::on_query(app_weak, true);
    }
}
