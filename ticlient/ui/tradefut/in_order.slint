import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TableView } from "@zstk/lib.slint";
import { AppCom, ColWidth, WidgetColor, AppCom } from "../struct.slint";
import { FutTrd_InOrder } from "struct.slint";

export component InOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <int> select_row_index: -1;
    function req_cancel_order(need_confirm: bool) {
        FutTrd_InOrder.sel_cancel_order.exchid = "";
        if select_row_index >= 0 && FutTrd_InOrder.row_data.length > select_row_index {
            FutTrd_InOrder.sel_cancel_order.exchid = FutTrd_InOrder.row_data[select_row_index][1];
            FutTrd_InOrder.sel_cancel_order.accountid = FutTrd_InOrder.row_data[select_row_index][0];
            FutTrd_InOrder.sel_cancel_order.ordersysid = FutTrd_InOrder.row_data[select_row_index][10];
        }
        FutTrd_InOrder.cancel_order_clieked(FutTrd_InOrder.sel_cancel_order, need_confirm);
    }

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 5,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.fut_insid },
                    { title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    { title: @tr("开平标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.openclose },
                    { title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("报单状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("投保标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.hedge },
                    { title: @tr("报单价格条件"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_pricetype },
                    { title: @tr("有效期类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_timecondition },
                    { title: @tr("成交量类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_95 },
                    { title: @tr("触发条件"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("最小成交量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    { title: @tr("剩余数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("所有者类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_90 },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                ]
            };
            row_count: FutTrd_InOrder.row_data.length;

            get_cell_data(row_index, column_index) => {
                FutTrd_InOrder.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_clicked(row_index, column_index) => {
                self.select_full_row = true;
            }

            cell_double_clicked(row_index, column_index) => {
                req_cancel_order(true);
            }

            current_cell_changed(row_index, column_index) => {
                select_row_index = row_index;
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(FutTrd_InOrder.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(FutTrd_InOrder.row_data[row_index]);
                }
            }
        }

        HorizontalLayout {
            padding: 6px;
            spacing: 30px;
            height: 36px;

            Button {
                text: "撤单";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    req_cancel_order(true);
                }
            }

            Button {
                text: "全撤";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    FutTrd_InOrder.cancel_order_all_clieked(FutTrd_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤买";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    FutTrd_InOrder.cancel_order_bs_clieked(0, FutTrd_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤卖";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    FutTrd_InOrder.cancel_order_bs_clieked(1, FutTrd_InOrder.row_data.length, true);
                }
            }
        }
    }
}
