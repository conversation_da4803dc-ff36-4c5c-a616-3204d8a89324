use crate::{
    common::{
        self,
        tistruct::{FilePathField, TradeSelfMonitorField},
    },
    slintui::*,
};
use slint::*;

/// 设置
pub(super) struct Setting {}

/// 构建与初始化
impl Setting {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_set = app.global::<FutTrd_Setting>();

        // 初始化设置的值
        {
            let cfg = common::global::CFG.get().unwrap().read().unwrap();

            app_set.set_trdreqtip_inputorder(cfg.com.ReqTradeTip.InputOrder);
            app_set.set_trdreqtip_ordercancel(cfg.com.ReqTradeTip.InputOrderAction);

            app_set.set_trdreqtip_inputexec(cfg.com.ReqTradeTip.ExecOrderInsert);
            app_set.set_trdreqtip_execcancel(cfg.com.ReqTradeTip.ExecOrderAction);

            app_set.set_mon_tradeself(MonTradeself {
                trade_vol: cfg.fut_mon.TrdSelfMonitor.TradeVol.to_string().into(),
            });
        }

        // 交易请求确认 - 报单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_inputorder_changed(move || Setting::on_trdreqtip_inputorder_changed(&app_weak));

        // 交易请求确认 - 撤单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_ordercancel_changed(move || Setting::on_trdreqtip_ordercancel_changed(&app_weak));

        // 交易请求确认 - 行权
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_inputexec_changed(move || Setting::on_trdreqtip_inputexec_changed(&app_weak));

        // 交易请求确认 - 撤行权
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_execcancel_changed(move || Setting::on_trdreqtip_execcancel_changed(&app_weak));

        // 异常监控 - 自成交 - 重置
        let app_weak = app.as_weak();
        app_set.on_reset_mon_tradeself(move || Setting::on_reset_mon_tradeself(&app_weak));

        // 异常监控 - 自成交
        let app_weak = app.as_weak();
        app_set.on_mon_tradeself_changed(move || Setting::on_mon_tradeself_changed(&app_weak));

        log::trace!("Setting init completed");
    }
}

// 事件
impl Setting {
    /// 交易请求确认 - 报单
    fn on_trdreqtip_inputorder_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputOrder = app_set.get_trdreqtip_inputorder();
    }

    /// 交易请求确认 - 撤单
    fn on_trdreqtip_ordercancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputOrderAction = app_set.get_trdreqtip_ordercancel();
    }

    /// 交易请求确认 - 行权
    fn on_trdreqtip_inputexec_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.ExecOrderInsert = app_set.get_trdreqtip_inputexec();
    }

    /// 交易请求确认 - 撤行权
    fn on_trdreqtip_execcancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.ExecOrderAction = app_set.get_trdreqtip_execcancel();
    }

    /// 异常监控 - 自成交 - 重置
    fn on_reset_mon_tradeself(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();

        app_set.set_mon_tradeself_err_status(0);
        app_set.set_mon_tradeself_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.fut_mon.TrdSelfMonitor = TradeSelfMonitorField::default();
        app_set.set_mon_tradeself(MonTradeself {
            trade_vol: cfg.fut_mon.TrdSelfMonitor.TradeVol.to_string().into(),
        });

        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.fut_mon.TrdSelfMonitor, false);
    }

    /// 异常监控 - 自成交
    fn on_mon_tradeself_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();

        let tsm = app_set.get_mon_tradeself();

        let ret = tsm.trade_vol.parse::<i32>();
        if let Err(err) = ret {
            app_set.set_mon_tradeself_err_status(1);
            app_set.set_mon_tradeself_err_tips(slint::format!("请输入正确的数量. {}", err.to_string()));
            return;
        }
        let trade_vol = ret.unwrap();
        if trade_vol <= 0 {
            app_set.set_mon_tradeself_err_status(1);
            app_set.set_mon_tradeself_err_tips(slint::format!("数量必需大于0"));
            return;
        }

        app_set.set_mon_tradeself_err_status(0);
        app_set.set_mon_tradeself_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.fut_mon.TrdSelfMonitor.TradeVol = trade_vol;
        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.fut_mon.TrdSelfMonitor, false);
    }
}
