import { Theme, TabBar<PERSON>tem, TabView, MessageBox, MBButtons, MBIcon, MBResult } from "@zstk/lib.slint";

import {
    StkTrd_Com, StkTrd_TabSelIdx, StkTrd_Setting,
    StkTrd_Account, StkTrd_Position, StkTrd_CreditContract, StkTrd_CreditLimitAmt, StkTrd_CreditLimitPos, StkTrd_CreditTcAmt, StkTrd_CreditTcPos,
    StkTrd_CancelTip, StkTrd_InOrder, StkTrd_InFjyOrder,
    StkTrd_InputTip, StkTrd_InputOrder, StkTrd_InputOrderCredit, StkTrd_InputFjyOrder
} from "struct.slint";

import { AccountPage } from "account.slint";
import { FundTransPage } from "fundtrans.slint";
import { PositionTransPage } from "positiontrans.slint";
import { MarketDataPage, StkTrd_MarketData } from "marketdata.slint";
import { PositionPage } from "position.slint";
import { CreditContractPage } from "credit_contract.slint";
import { CreditCreditPage } from "credit_credit.slint";
import { CreditReturnAmtPage } from "credit_return_amt.slint";
import { CreditReturnPosPage } from "credit_return_pos.slint";

import { InPage } from "in.slint";

import { StkTrd_QryOrder } from "qry_order.slint";
import { StkTrd_QryTrade } from "qry_trade.slint";
import { StkTrd_QryFjyOrder } from "qry_fjy_order.slint";
import { StkTrd_QryFjyTrade } from "qry_fjy_trade.slint";
import { StkTrd_QryWithdrawDeposit } from "qry_wd.slint";
import { StkTrd_QryPosTrans } from "qry_postrans.slint";
import { StkTrd_QryFundTransDtl } from "qry_fundtransdtl.slint";
import { StkTrd_PositionTransDtl } from "qry_postransdtl.slint";
import { StkTrd_QryInstrument } from "qry_instrument.slint";
import { StkTrd_QryCreditReAmtDtl } from "qry_credit_reamt_detail.slint";
import { QueryPage } from "qry.slint";

import { StkTrd_MonOrderInsertCancel } from "mon_orderinsertcancel.slint";
import { StkTrd_MonConvertBonds } from "mon_convertbonds.slint";
import { StkTrd_MonSelfTrade } from "mon_selftrade.slint";
import { StkTrd_MonSelfTradeDtl } from "mon_selftradedtl.slint";
import { StkTrd_CreditConcentration } from "credit_concentration.slint";
import { MonitorPage } from "mon.slint";

import { InputPage } from "input.slint";

import { MarketDataDtlPage } from "../md/marketdatadtl.slint";
import { AppInerArgs } from "../struct.slint";

export {
    StkTrd_Com, StkTrd_TabSelIdx, StkTrd_Setting,
    StkTrd_MarketData,
    StkTrd_Account, StkTrd_Position, StkTrd_CreditContract, StkTrd_CreditLimitAmt, StkTrd_CreditLimitPos, StkTrd_CreditTcAmt, StkTrd_CreditTcPos,
    StkTrd_CancelTip, StkTrd_InOrder, StkTrd_InFjyOrder,
    StkTrd_QryOrder, StkTrd_QryTrade, StkTrd_QryFjyOrder, StkTrd_QryFjyTrade, StkTrd_QryWithdrawDeposit, StkTrd_QryPosTrans, StkTrd_QryFundTransDtl, StkTrd_PositionTransDtl, StkTrd_QryInstrument, StkTrd_QryCreditReAmtDtl,
    StkTrd_MonOrderInsertCancel, StkTrd_MonConvertBonds, StkTrd_MonSelfTrade, StkTrd_MonSelfTradeDtl, StkTrd_CreditConcentration,
    StkTrd_InputTip, StkTrd_InputOrder, StkTrd_InputOrderCredit, StkTrd_InputFjyOrder
}

export component TradeStkPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "持仓" },
        { text: "在途订单" },
        { text: "查询" },
        { text: "异常指标监控" },
    ];

    private property <[TabBarItem]> tabview_credit_items: [
        { text: "持仓" },
        { text: "合约" },
        { text: "信用" },
        { text: "在途订单" },
        { text: "查询" },
        { text: "异常指标监控" },
    ];

    VerticalLayout {
        spacing: 1px;

        HorizontalLayout {
            padding_left: 1px;
            padding_right: 1px;

            AccountPage {
                height: StkTrd_Com.iscredit ? ((AppInerArgs.is_all_in_one ? AppInerArgs.trd_detail_amt : true) ? 125px : 65px) : 125px;
            }
        }

        HorizontalLayout {
            padding_left: 1px;
            padding_right: 1px;
            spacing: 1px;
            height: 380px;

            Rectangle {
                border-radius: 2px;
                border-width: 1px;
                border-color: Theme.border;

                MarketDataPage { }
            }

            MarketDataDtlPage {
                width: 285px;
            }
        }

        HorizontalLayout {
            padding_left: 1px;
            padding-right: 1px;
            spacing: 1px;

            VerticalLayout {
                TabView {
                    height: 30px;

                    items: StkTrd_Com.iscredit ? tabview_credit_items : tabview_items;

                    current_index <=> StkTrd_TabSelIdx.selidx;
                }

                if StkTrd_Com.iscredit : Rectangle {
                    if 0 == StkTrd_TabSelIdx.selidx: PositionPage { }
                    if 1 == StkTrd_TabSelIdx.selidx: CreditContractPage { }
                    if 2 == StkTrd_TabSelIdx.selidx: CreditCreditPage { }
                    if 3 == StkTrd_TabSelIdx.selidx: InPage { }
                    if 4 == StkTrd_TabSelIdx.selidx: QueryPage { }
                    if 5 == StkTrd_TabSelIdx.selidx: MonitorPage { }
                }

                if !StkTrd_Com.iscredit : Rectangle {
                    if 0 == StkTrd_TabSelIdx.selidx: PositionPage { }
                    if 1 == StkTrd_TabSelIdx.selidx: InPage { }
                    if 2 == StkTrd_TabSelIdx.selidx: QueryPage { }
                    if 3 == StkTrd_TabSelIdx.selidx: MonitorPage { }
                }
            }

            Rectangle {
                border-radius: 2px;
                border-width: 1px;
                border-color: Theme.border;
                width: 285px;

                InputPage { }
            }
        }
    }

    // 资金划转
    if StkTrd_Account.show_fund_trans : FundTransPage {
        y: 135px;
    }

    // 股份划转
    if StkTrd_Position.show_pos_trans : PositionTransPage {
        y: 415px;
    }

    // 信用交易 - 直接还款
    if StkTrd_Com.iscredit && StkTrd_CreditContract.show_return_amt : CreditReturnAmtPage {
        y: 415px;
    }

    // 信用交易 - 直接还款
    if StkTrd_Com.iscredit && StkTrd_CreditContract.show_return_pos : CreditReturnPosPage {
        y: 415px;
    }

    // 报单提醒
    if StkTrd_InputTip.app_ret.state > 0: Rectangle {
        init => {
            if 100 == StkTrd_InputTip.app_ret.state {
                i_msgbox_input.id = StkTrd_InputTip.app_ret.state;
                i_msgbox_input.show_check = true;
                i_msgbox_input.checked = true;
                i_msgbox_input.open_title(StkTrd_InputTip.app_ret.title, StkTrd_InputTip.app_ret.msg, MBButtons.YesNo, MBIcon.Question);
            } else {
                i_msgbox_input.show_check = false;
                i_msgbox_input.open_title(StkTrd_InputTip.app_ret.title, StkTrd_InputTip.app_ret.msg, MBButtons.Ok, MBIcon.Error);
            }
            StkTrd_InputTip.app_ret = { state: 0, title:"", msg: "" };
        }
    }
    i_msgbox_input := MessageBox {
        width: root.width;
        height: root.height;
        check_txt: 0 == StkTrd_TabSelIdx.input_selidx ? "提醒报单请求" : "提醒非交易业务报单请求";

        clicked(id, ret) => {
            if MBResult.Yes == ret {
                if 100 == id {
                    if 0 == StkTrd_TabSelIdx.input_selidx {
                        if StkTrd-Com.iscredit {
                            StkTrd_InputOrderCredit.ok_clicked(false);
                        } else {
                            StkTrd_InputOrder.ok_clicked(false);
                        }
                    } else if 1 == StkTrd_TabSelIdx.input_selidx {
                        StkTrd_InputFjyOrder.ok_clicked(false);
                    }
                }
            }
            self.close();
        }

        toggled(id, checked) => {
            if 100 == id {
                if 0 == StkTrd_TabSelIdx.input_selidx {
                    if StkTrd_Com.iscredit {
                        StkTrd_InputOrderCredit.req_tip_toggled(checked);
                    }else{
                        StkTrd_InputOrder.req_tip_toggled(checked);
                    }
                } else if 1 == StkTrd_TabSelIdx.input_selidx {
                    StkTrd_InputFjyOrder.req_tip_toggled(checked);
                }
            }
        }
    }

    // 撤单提醒
    if StkTrd_CancelTip.app_ret.state > 0: Rectangle {
        init => {
            if 100 == StkTrd_CancelTip.app_ret.state ||
               101 == StkTrd_CancelTip.app_ret.state ||
               102 == StkTrd_CancelTip.app_ret.state ||
               103 == StkTrd_CancelTip.app_ret.state {
                i_msgbox_cancel.id = StkTrd_CancelTip.app_ret.state;
                i_msgbox_cancel.show_check = true;
                i_msgbox_cancel.checked = true;
                i_msgbox_cancel.open_title(StkTrd_CancelTip.app_ret.title, StkTrd_CancelTip.app_ret.msg, MBButtons.YesNo, MBIcon.Question);
            } else {
                i_msgbox_cancel.show_check = false;
                i_msgbox_cancel.open_title(StkTrd_CancelTip.app_ret.title, StkTrd_CancelTip.app_ret.msg, MBButtons.Ok, MBIcon.Error);
            }
            StkTrd_CancelTip.app_ret = {state: 0, title:"", msg: ""};
        }
    }
    i_msgbox_cancel := MessageBox {
        width: root.width;
        height: root.height;
        check_txt: 0 == StkTrd_TabSelIdx.in_subselidx ? "提醒撤单请求" : "提醒非交易业务撤单请求";

        clicked(id, ret) => {
            if MBResult.Yes == ret {
                if 0 == StkTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        StkTrd_InOrder.cancel_order_clieked(StkTrd_InOrder.sel_cancel_order, false);
                    }
                    if 101 == id {
                        StkTrd_InOrder.cancel_order_all_clieked(StkTrd_InOrder.row_data.length, false);
                    }
                    if 102 == id {
                        StkTrd_InOrder.cancel_order_bs_clieked(0, StkTrd_InOrder.row_data.length, false);
                    }
                    if 103 == id {
                        StkTrd_InOrder.cancel_order_bs_clieked(1, StkTrd_InOrder.row_data.length, false);
                    }
                } else if 1 == StkTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        StkTrd_InFjyOrder.cancel_order_clieked(StkTrd_InFjyOrder.sel_cancel_order, false);
                    }
                    if (101 == id) {
                        StkTrd_InFjyOrder.cancel_order_all_clieked(StkTrd_InFjyOrder.row_data.length, false);
                    }
                }
            }

            self.close();
        }

        toggled(id, checked) => {
            if 0 == StkTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id || 102 == id || 103 == id {
                    StkTrd_InOrder.req_tip_toggled(checked);
                }
            } else if 1 == StkTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id {
                    StkTrd_InFjyOrder.req_tip_toggled(checked);
                }
            }
        }
    }
}
