use serde::Deserialize;

/// 账户资金
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct AccountsStkField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 上次结算准备金
    /// 系统上场时读取到的值,在整个交易日中不会改变
    pub PreBalance: f64,

    /// 本系统分配资金
    /// 初始为上次结算准备金,如果系统设置了本系统冻结且冻结资金小于 PreBalance 则为冻结资金
    pub DistribFund: f64,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 权益
    pub Balance: f64,

    /// 市值权益
    pub ValueBalance: f64,

    /// 可用资金
    pub Available: f64,

    /// 可取资金
    pub WithdrawQuota: f64,

    /// 入金金额
    pub Deposit: f64,

    /// 出金金额
    pub Withdraw: f64,

    /// 手续费
    pub Commision: f64,

    /// 冻结的手续费
    pub FrozenCommission: f64,

    /// 平仓盈亏
    pub CloseProfit: f64,

    /// 持仓盈亏(浮动盈亏)
    pub PositionProfit: f64,

    /// 当日盈亏
    pub Profits: f64,

    /// 证券市值
    pub PosValue: f64,

    /// 冻结的资金
    pub FrozenPremium: f64,

    /// 资金收支
    pub Premium: f64,

    /**********下面字段为信用交易**********/
    /// 总负债
    pub XYAllDebt: f64,

    /// 融资负债
    pub XYRzDebt: f64,

    /// 融券负债
    pub XYRqDebt: f64,

    /// 当日初始负债
    pub XYInitDebt: f64,

    /// 当日已还负债
    pub XYInDebt: f64,

    /// 当日借出负债
    pub XYOutDebt: f64,

    /// 未成交充抵保证金
    pub XYBuyCdMargin: f64,

    /// Sum(可充抵保证金的证券市值 * 折算率)
    pub XYBuyValue: f64,

    /// Sum((融资买入证券市值 - 融资买入金额) * 折算率)
    pub XYBuy: f64,

    /// Sum((融券卖出金额 - 融券卖出证券市值) * 折算率)
    pub XYSell: f64,

    /// Sum(融券卖出金额)
    pub XYSellAmt: f64,

    /// Sum(融资买入证券金额 * 融资保证金比例)
    pub XYBuyMargin: f64,

    /// Sum(融券卖出证券市值 * 融券保证金比例)
    pub XYSellMargin: f64,

    /// Sum(利息)
    pub XYInterest: f64,

    /// Sum(费用)
    pub XYCommi: f64,

    /// 信用证券账户内证券市值总和 --- 用于计算维持担保比例
    pub XYAllValue: f64,

    /// Sum(融资买入金额 + 融券卖出证券数量 * 最新价 + 利息及费用) --- 用于计算维持担保比例
    pub XYValue: f64,

    /// 保证金可用余额
    pub XYAvlMargin: f64,

    /// 维持担保比例
    pub XYMRatio: f64,

    /// 资金收支
    pub XYPremium: f64,

    /// 冻结的保证金
    pub XYFroPrem: f64,

    /// 报单时冻结的资金
    pub XYFroValPrem: f64,

    /// 冻结的费用
    pub XYFroCommi: f64,

    pub XYReDirAmt: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 出入金
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct WithdrawDepositStkField {
    /// 程序编码
    pub AID: i32,

    /// 前置序号
    /// -100:融资授信额度; -101: 融券授信额度; -103:资金头寸; -104:股份头寸
    pub FrontNo: i32,

    /// 交易端 SessionNo
    pub SessionNo: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 交易编码
    pub ClientID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 处理时间
    pub DealTime: i32,

    /// 入金金额
    pub Deposit: f64,

    /// 出金金额
    pub Withdraw: f64,
}

/// 出入金请求查询消息
#[derive(Default)]
pub struct ReqQryWithdrawDepositField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 信用交易 - 资金轧差信息
#[derive(Default, Debug, Clone)]
pub struct CreditGaChaField {
    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 总负债
    pub XYAllDebt: f64,

    /// 融资负债
    pub XYRzDebt: f64,

    /// 融券负债
    pub XYRqDebt: f64,

    /// 当日初始负债
    pub XYInitDebt: f64,

    /// 当日已还负债
    pub XYInDebt: f64,

    /// 当日借出负债
    pub XYOutDebt: f64,
}

/// 信用交易 - 授信额度
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CreditLimitField {
    pub LType: i32,    // 信用类型
    pub AccID: String, // 资金账号
    pub ExchID: i32,   // 交易所编码
    pub Distrib: f64,  // 分配
    pub Used: f64,     // 占用
    pub Frozen: f64,   // 冻结
    pub In: f64,       // 转入
    pub Out: f64,      // 转出
}

/// 信用授权额度请求查询消息
#[derive(Default)]
pub struct ReqQryCreditLimitField {
    /// 授权额度类型
    pub LType: i32,

    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 信用交易 - 资金头寸
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CreditTcAmtField {
    pub TcType: i32,   // 头寸类型
    pub AccID: String, // 资金账号
    pub ExchID: i32,   // 交易所编码
    pub Distrib: f64,  // 分配
    pub Used: f64,     // 占用
    pub Frozen: f64,   // 冻结
    pub In: f64,       // 转入
    pub Out: f64,      // 转出
}

/// 信用资金头寸请求查询消息
#[derive(Default)]
pub struct ReqQryCreditTcAmtField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 信用交易 - 股份头寸
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CreditTcPosField {
    pub TcType: i32,   // 头寸类型
    pub AccID: String, // 资金账号
    pub CliID: String, // 交易编码
    pub ExchID: i32,   // 交易所编码
    pub InsID: String, // 合约编码
    pub Distrib: i32,  // 分配
    pub Used: i32,     // 占用
    pub Frozen: i32,   // 冻结
    pub In: i32,       // 转入
    pub Out: i32,      // 转出
}

/// 信用股份头寸请求查询消息
#[derive(Default)]
pub struct ReqQryCreditTcPosField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}
