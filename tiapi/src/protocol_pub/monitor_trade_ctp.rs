use serde::Deserialize;

/// 限额消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpLimitAmountField {
    pub BizSeqNum: i32,     // 引起本条信息变化的业务序号
    pub ExchID: i32,        // 交易所编码
    pub AccountID: String,  // 资金帐号
    pub AccountType: i32,   // 账号类型
    pub MaxBuyPremium: f64, // 权力仓最大占用的权利金
    pub BuyPremium: f64,    // 权力仓当前占用的权利金

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String,        // 标识消息唯一性
    pub CanBuyPremium: f64, // 剩余额度 = MaxBuyPremium - BuyPremium
    pub Ratio: f64,         // 占比 = BuyPremium / MaxBuyPremium
}

/// 限仓消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpLimitPositionField {
    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub AccountType: i32,  // 账号类型
    pub ClientID: String,  // 交易编码
    pub ProductID: String, // 品种编码

    pub MaxLongVolume: i32,     // 权利仓持仓最大数量
    pub MaxTotalVolume: i32,    // 总持仓最大数量
    pub MaxTodayBuyVolume: i32, // 当日买入开仓最大数量

    pub BizSeqNum: i32,         // 引起本条信息变化的业务序号
    pub TransTime: i32,         // 发生时间
    pub CurLongVolume: i32,     // 当前权利仓持仓数量
    pub CurTotalVolume: i32,    // 当前持仓数量
    pub CurTodayBuyVolume: i32, // 当日买入开仓数量

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}

/// 限仓消息(期货)
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpFutLimitPositionField {
    pub ExchID: i32,          // 交易所编码
    pub AccountID: String,    // 资金帐号
    pub ClientID: String,     // 交易编码
    pub ProductID: String,    // 品种编码
    pub InstrumentID: String, // 合约编码

    pub MaxLongVolume: i32,  // 多头最大持仓量
    pub MaxShortVolume: i32, // 空头最大持仓量

    pub BizSeqNum: i32,   // 引起本条信息变化的业务序号
    pub TransTime: i32,   // 发生时间
    pub LongVolume: i32,  // 多头持仓量
    pub ShortVolume: i32, // 空头持仓量

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}

/// 限开仓消息(期货)
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpFutLimitOpenField {
    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub ClientID: String,  // 交易编码
    pub ProductID: String, // 品种编码

    pub STID: String,       // 统计编码(按品种，按合约，按交割月, 不同的统计方式，取相应的值)
    pub STType: i32,        // 统计类型
    pub MaxOpenVolume: i32, // 最大开仓量

    pub BizSeqNum: i32,  // 引起本条信息变化的业务序号
    pub TransTime: i32,  // 发生时间
    pub OpenVolume: i32, // 开仓量

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}

/// 自成交消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpTradeSelfField {
    pub TSType: i32,          // 统计类型. 1:按合约2:按标的(此时InstrumentID填标的品种编码)
    pub ExchID: i32,          // 交易所编码
    pub AccountID: String,    // 资金帐号
    pub AccountType: i32,     // 账号类型
    pub ClientID: String,     // 交易编码
    pub InstrumentID: String, // 合约编码

    pub BizSeqNum: i32, // 引起本条信息变化的业务序号
    pub TransTime: i32, // 发生时间
    pub TradeNum: i32,  // 自成交笔数
    pub TradeVol: i32,  // 自成交数量

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}

/// 自成交详情消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpTradeSelfDtlField {
    pub FrontNo: i32,   // 交易端前置
    pub SessionNo: i32, // 交易端SessionNO

    pub MsgSeqNum: i32, // 消息序号(按资金账户-消息类型从1递增)
    pub BizSeqNum: i32, // 业务序号(按资金账户从1递增)

    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub AccountType: i32,  // 账号类型
    pub ClientID: String,  // 交易编码

    pub InstrumentID: String, // 合约编码
    pub TradeID: String,      // 成交编号

    pub LocalOrderNo: i32,  // 本地报单编号
    pub OrderSysID: String, // 交易所报单编号

    pub Side: i32,       // 买卖方向
    pub OffsetFlag: i32, // 开平标记
    pub HedgeFlag: i32,  // 投机套保标记
    pub Covered: i32,    // 备兑标签

    pub TradePrice: f64,  // 成交价格
    pub TradeVolume: i32, // 成交数量
    pub TransTime: i32,   // 成交时间
}

/// 自成交详情请求查询消息
#[derive(Default)]
pub struct CtpReqQrySelfTradeDtlField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 成交编号
    pub TradeID: String,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 报撤单消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpOrderInsertCancelField {
    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub AccountType: i32,  // 账号类型
    pub ClientID: String,  // 交易编码
    pub ProductID: String, // 品种编码

    pub BizSeqNum: i32, // 引起本条信息变化的业务序号
    pub TransTime: i32, // 发生时间

    pub InsertNum: i32, // 报单笔数
    pub CancelNum: i32, // 撤单笔数

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}

/// 报撤单消息(期货)
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpFutOrderInsertCancelField {
    pub ExchID: i32,          // 交易所编码
    pub AccountID: String,    // 资金帐号
    pub AccountType: i32,     // 账号类型
    pub ClientID: String,     // 交易编码
    pub ProductID: String,    // 品种编码
    pub InstrumentID: String, // 合约编码
    pub MInsertNum: i32,      // 最大报单次数
    pub MCancelNum: i32,      // 最大撤单次数
    pub MLCancelNum: i32,     // 最大大额撤单次数
    pub SLCancelNum: i32,     // 大额撤单标准

    pub BizSeqNum: i32, // 引起本条信息变化的业务序号
    pub TransTime: i32, // 发生时间

    pub InsertNum: i32,  // 报单笔数
    pub CancelNum: i32,  // 撤单笔数
    pub LCancelNum: i32, // 大额撤单笔数

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}

/// 成交持仓比例消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpTradePositionField {
    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub AccountType: i32,  // 账号类型
    pub ClientID: String,  // 交易编码
    pub ProductID: String, // 品种编码

    pub BizSeqNum: i32,   // 引起本条信息变化的业务序号
    pub TransTime: i32,   // 发生时间
    pub TradeVolume: i32, // 成交量
    pub Position: i32,    // 持仓量
    pub Ratio: f64,       // 成交量/持仓量

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String,      // 标识消息唯一性
    pub IsAbnormal: bool, // 是否异常
}

/// 合并后的成交持仓比和报撤单信息
#[derive(Default)]
pub struct CtpTrdPosOrdInsertCancelField {
    pub Key: String, // 标识消息唯一性

    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub AccountType: i32,  // 账号类型
    pub ClientID: String,  // 交易编码
    pub ProductID: String, // 品种编码

    pub BizSeqNum: i32, // 引起本条信息变化的业务序号
    pub TransTime: i32, // 发生时间

    pub TradeVolume: i32, // 成交量
    pub Position: i32,    // 持仓量
    pub Ratio: f64,       // 成交量/持仓量

    pub InsertNum: i32, // 报单笔数
    pub CancelNum: i32, // 撤单笔数
}

/// 可转债监控信息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpConvertBondsMonField {
    pub ExchID: i32,          // 交易所编码
    pub AccountID: String,    // 资金帐号
    pub AccountType: i32,     // 账号类型
    pub ClientID: String,     // 交易编码
    pub InstrumentID: String, // 合约编码

    pub BizSeqNum: i32, // 引起本条信息变化的业务序号
    pub TransTime: i32, // 发生时间

    pub Position: i32,    // 持仓量
    pub MaxPosition: i32, // 当日最大持仓量

    pub BuyVol: i32,     // 买入数量
    pub BuyAmount: f64,  // 买入金额
    pub SellVol: i32,    // 卖出数量
    pub SellAmount: f64, // 卖出金额

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}

/// 成交监控信息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpTradeMonField {
    pub ExchID: i32,          // 交易所编码
    pub AccountID: String,    // 资金帐号
    pub AccountType: i32,     // 账号类型
    pub ClientID: String,     // 交易编码
    pub InstrumentID: String, // 合约编码

    pub BizSeqNum: i32, // 引起本条信息变化的业务序号
    pub TransTime: i32, // 发生时间

    pub BuyVol: i32,     // 买入数量
    pub BuyAmount: f64,  // 买入金额
    pub SellVol: i32,    // 卖出数量
    pub SellAmount: f64, // 卖出金额

    /**********下面字段为非协议内容用于计算展示等**********/
    pub Key: String, // 标识消息唯一性
}
