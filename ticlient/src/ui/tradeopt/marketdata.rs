use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    apiproc::mdbuf::IOnRtnMarketDataL1,
    common::{
        self,
        config::Config,
        global::{deserialize_user_data, read_user_data_from_file, serialize_write_user_data, INS, MD_API, MD_BUF, TOKIO_RT},
        ticonvert::TIConvert,
        tistruct::FilePathField,
    },
    show_msg_box,
    slintui::*,
};
use mdapi::protocol::ptmd::PTRtnMarketDataL1;
use slint::*;

pub type MdL1Map = dashmap::DashMap<String, PTRtnMarketDataL1>;
pub type SubInsidSet = dashmap::DashSet<String>;

/// 获取价格字字符串
pub fn get_price_str(price: f64, digits: usize) -> SharedString {
    if price >= Config::INVALID_PRICE {
        return TIConvert::format_f(price, digits).into();
    }

    "--".into()
}

/// 获取价格变动标志
/// 返回值: 0: 等于; 1:大于; 其他值:小于
pub fn get_price_up_down_falg(pre_stl_price: f64, price: f64) -> i32 {
    if price <= Config::INVALID_PRICE {
        return 0;
    }

    if price > pre_stl_price {
        1
    } else if pre_stl_price == price {
        0
    } else {
        2
    }
}

/// 行情数据
struct MdDatas {
    /// 错误信息
    errmsg: Arc<Mutex<String>>,

    /// 数据是否有改变
    data_changed: AtomicBool,

    /// 行情数据
    data_map: Arc<MdL1Map>,

    /// 当前手工订阅行情的合约
    sub_insid_set: Arc<SubInsidSet>,

    ///保存订阅行情合约的文件路径
    sub_insid_path: String,

    /// 输入的合约是否有改变
    input_instid_changed: AtomicBool,
}
impl MdDatas {
    pub fn new() -> Self {
        Self {
            errmsg: Arc::new(Mutex::new(String::new())),
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(MdL1Map::new()),
            sub_insid_set: Arc::new(SubInsidSet::new()),
            sub_insid_path: {
                let cfg = common::global::CFG.get().unwrap().read().unwrap();
                std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::SELF_MD_OPT_INS_ID)
            },
            input_instid_changed: AtomicBool::new(false),
        }
    }

    /// 保存已订阅合约的编码
    pub fn save_sub_insid(&self) {
        let sub_ins_vec = self.sub_insid_set.iter().map(|x| x.to_owned()).collect::<Vec<String>>();
        serialize_write_user_data(&self.sub_insid_path, &sub_ins_vec, false);
    }
}
static MD_DATAS: OnceLock<MdDatas> = OnceLock::new();

// L1行情接收
struct MDL1 {}
impl MDL1 {
    pub fn new() -> Self {
        Self {}
    }
}
impl IOnRtnMarketDataL1 for MDL1 {
    fn rtn(&self, md: PTRtnMarketDataL1) {
        let md_datas = MD_DATAS.get().unwrap();

        md_datas.data_map.insert(std::format!("{}{}", md.EID, md.IID.clone()), md);
        md_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 行情
pub struct Marketdata {}

/// 构建与初始化
impl Marketdata {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = MD_DATAS.set(MdDatas::new());

        // 注册接收行情
        {
            let mut md_buf = MD_BUF.get().unwrap().write().unwrap();
            md_buf.register_rtn_md_l1_callback(Box::new(MDL1::new()));
        }
        log::trace!("Marketdata init register_rtn_md_l1_callback");

        // 订阅所有的自定义合约
        {
            let mut ins_vec = Vec::new();
            if let Some(insid_arr) =
                deserialize_user_data::<Vec<String>>(&read_user_data_from_file(&MD_DATAS.get().unwrap().sub_insid_path))
            {
                let ins_g = { INS.get().unwrap().read().unwrap() };
                for insid in insid_arr.iter() {
                    let ins = ins_g.get_ins_by_insid(&insid).unwrap_or_default();
                    if !ins.insid.is_empty() {
                        ins_vec.push(ins);
                    }
                }
            }

            if !ins_vec.is_empty() {
                TOKIO_RT.get().unwrap().spawn(async move {
                    let md_datas = MD_DATAS.get().unwrap();

                    let md_api = MD_API.get().unwrap().read().await;
                    for ins in ins_vec.iter() {
                        // 构建一条订阅合约的行情
                        let md = PTRtnMarketDataL1 {
                            EID: ins.exchname.clone(),
                            IID: ins.insid.clone(),
                            PSP: ins.prestlprice,
                            ..Default::default()
                        };

                        match md_api.req_subscribe(&ins.insid).await {
                            Ok(_) => {
                                md_datas.data_map.insert(std::format!("{}{}", md.EID, md.IID.clone()), md);
                                md_datas.data_changed.store(true, Ordering::Relaxed);

                                md_datas.sub_insid_set.insert(ins.insid.clone());
                            }
                            Err(err) => {
                                log::warn!("init: req_subscribe({}) failed. {}", ins.insid, err);
                            }
                        }
                    }

                    // 保存当前已订阅行情的合约
                    md_datas.save_sub_insid();
                });
            }
        }
        log::trace!("Marketdata init sub saved instrument");

        // UI事件
        {
            let app_md = app.global::<OptTrd_MarketData>();

            // 添加合约
            {
                let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                let all_insid = { common::global::INS.get().unwrap().read().unwrap().get_id_all_with_name() };
                all_insid.iter().for_each(|it| {
                    items.push(crate::slintui::LineItem {
                        text: slint::format!("{}", it.key()),
                        remark: slint::format!("{}", it.value()),
                        ..Default::default()
                    });
                });
                let sort_model = Rc::new(items.sort_by(move |r_a, r_b| r_a.text.cmp(&r_b.text)));
                app_md.set_insid_model(sort_model.clone().into());
                app_md.set_all_insid_model(sort_model.into());
            }
            log::trace!("Marketdata init set instrument datas");

            let app_weak = app.as_weak();
            app_md.on_insid_changed(move |id| Marketdata::on_insid_changed(&app_weak));

            let app_weak = app.as_weak();
            app_md.on_add_clicked(move || Marketdata::on_add_clicked(&app_weak));

            let app_weak = app.as_weak();
            app_md.on_del_clicked(move |exchid, insid| Marketdata::on_del_clicked(&app_weak, exchid, insid));

            let app_weak = app.as_weak();
            app_md.on_del_all_clicked(move |row_cnt| Marketdata::on_del_all_clicked(&app_weak, row_cnt));

            let app_weak = app.as_weak();
            app_md.on_current_cell_changed(move |row_index, column_index| {
                Marketdata::on_current_cell_changed(&app_weak, row_index, column_index)
            });

            log::trace!("Marketdata init ui events");
        }

        log::trace!("Marketdata init completed");
    }
}

/// 与API的交互
impl Marketdata {
    /// 订阅失败信息
    pub fn on_sub_failed(&self, em: String) {
        *MD_DATAS.get().unwrap().errmsg.lock().unwrap() = em;
    }

    /// 退订阅失败信息
    pub fn on_un_sub_failed(&self, em: String) {
        *MD_DATAS.get().unwrap().errmsg.lock().unwrap() = em;
    }
}

/// 事件
impl Marketdata {
    /// 合约有变动
    fn on_insid_changed(app_weak: &Weak<App>) {
        let md_datas = MD_DATAS.get().unwrap();
        md_datas.input_instid_changed.store(true, Ordering::Relaxed);
    }

    /// 添加自选合约事件
    fn on_add_clicked(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_md = app.global::<OptTrd_MarketData>();
        let md_datas = MD_DATAS.get().unwrap();

        let ins = {
            let insid = app_md.get_insid();
            if insid.is_empty() {
                show_msg_box(&app, 3, Default::default(), "请输入要订阅行情的合约".into());
                return;
            }

            INS.get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins_by_insid(&insid)
                .unwrap_or_default()
        };
        if ins.insid.is_empty() {
            show_msg_box(&app, 3, Default::default(), "输入的合约不存在".into());
            return;
        }

        // 已经订阅, 不再重复订阅
        if md_datas.sub_insid_set.contains(&ins.insid) {
            // 清空输入框
            app_md.set_insid("".into());
            app_md.invoke_insid_changed("".into());
            return;
        }

        // 构建一条订阅合约的行情
        let md = PTRtnMarketDataL1 {
            EID: ins.exchname.clone(),
            IID: ins.insid.clone(),
            PSP: ins.prestlprice,
            ..Default::default()
        };

        let app_mddtl = app.global::<MarketDataDtl>();
        let cur_key = std::format!("{}{}", ins.exchname, ins.insid);
        let pre_key = app_mddtl.get_pre_key().to_string();
        if cur_key != pre_key {
            app_mddtl.set_exchid(ins.exchname.clone().into());
            app_mddtl.set_insid(ins.insid.clone().into());
            app_mddtl.set_cur_key(cur_key.into());
        }

        let iid = ins.insid.clone();
        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            // 订阅
            if let Err(err) = MD_API.get().unwrap().read().await.req_subscribe(&iid).await {
                let _ = wapp.upgrade_in_event_loop(move |app| {
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("请求订阅合约[{}]失败.\n{}", iid, err),
                    );
                });

                return;
            }

            let md_datas = MD_DATAS.get().unwrap();

            // 展示构建的行情
            md_datas.data_map.insert(std::format!("{}{}", md.EID, md.IID), md);
            md_datas.data_changed.store(true, Ordering::Relaxed);

            // 保存当前已订阅行情的合约. 即便服务端响应订阅失败也添加, 最多下次初始化时再踢出
            md_datas.sub_insid_set.insert(iid);
            md_datas.save_sub_insid();

            // 清空输入框
            let _ = wapp.upgrade_in_event_loop(move |app| {
                let app_md = app.global::<StkTrd_MarketData>();
                app_md.set_insid("".into());
                app_md.invoke_insid_changed("".into());
            });
        });
    }

    /// 删除选中合约事件
    fn on_del_clicked(app_weak: &Weak<App>, exchid: SharedString, insid: SharedString) {
        let wapp = app_weak.clone();
        TOKIO_RT.get().unwrap().spawn(async move {
            let result = MD_API.get().unwrap().read().await.req_unsubscribe(insid.as_str()).await;
            let err_msg = result.as_ref().err().map(|e| e.to_string());
            let _ = wapp.upgrade_in_event_loop(move |app| {
                if let Some(err) = err_msg {
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("请求退订阅合约[{}]行情失败.\n{}", insid, err),
                    );
                } else {
                    let md_datas = MD_DATAS.get().unwrap();

                    // 删除退订阅合约的行情
                    md_datas
                        .data_map
                        .remove(&std::format!("{}{}", exchid.as_str(), insid.as_str()));
                    md_datas.data_changed.store(true, Ordering::Relaxed);

                    // 删除退订阅合约
                    md_datas.sub_insid_set.remove(insid.as_str());
                    md_datas.save_sub_insid();
                }
            });
        });
    }

    /// 删除所有合约事件
    fn on_del_all_clicked(app_weak: &Weak<App>, row_cnt: i32) {
        let app = app_weak.unwrap();

        let mut ins_vec = Vec::new();
        if 0 != row_cnt {
            let app_md = app.global::<StkTrd_MarketData>();
            for row_index in 0..row_cnt {
                ins_vec.push(app_md.invoke_get_cur_sel_ins(row_index));
            }
        }

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            if !ins_vec.is_empty() {
                let md_api = MD_API.get().unwrap().read().await;

                for ins in ins_vec.iter() {
                    if let Err(err) = md_api.req_unsubscribe(ins.1.as_str()).await {
                        let insid = ins.1.to_owned();
                        let wapp0 = wapp.clone();
                        let _ = wapp0.upgrade_in_event_loop(move |app| {
                            show_msg_box(
                                &app,
                                3,
                                Default::default(),
                                slint::format!("请求退订阅合约[{}]行情失败.\n{}", insid, err),
                            );
                        });
                    }
                }

                tokio::time::sleep(std::time::Duration::from_millis(300)).await;

                let md_datas = MD_DATAS.get().unwrap();

                // 删除行情
                md_datas.data_map.clear();
                md_datas.data_changed.store(true, Ordering::Relaxed);

                // 删除退订阅合约
                md_datas.sub_insid_set.clear();
                md_datas.save_sub_insid();
            }
        });
    }

    /// 所选单元格变动事件
    fn on_current_cell_changed(app_weak: &Weak<App>, row_index: i32, col_index: i32) {
        let app = app_weak.unwrap();
        let app_md = app.global::<OptTrd_MarketData>();
        let app_mddtl = app.global::<MarketDataDtl>();

        let ret = app_md.invoke_get_cur_sel_ins(row_index);
        if ret.0.is_empty() {
            app_mddtl.set_exchid(ret.0);
            app_mddtl.set_insid(ret.1);
            app_mddtl.set_cur_key("".into());
            return;
        }

        let cur_key = std::format!("{}{}", ret.0.as_str(), ret.1.as_str());
        let pre_key = app_mddtl.get_pre_key().to_string();
        if cur_key != pre_key {
            app_mddtl.set_exchid(ret.0);
            app_mddtl.set_insid(ret.1);
            app_mddtl.set_cur_key(cur_key.into());
        }
    }
}

/// 更新UI
impl Marketdata {
    /// 更新先中合约的行情详情
    fn update_sel_md_dtl(app_mddtl: &MarketDataDtl, md: &PTRtnMarketDataL1, price_digits: usize, new_falg: bool) {
        if new_falg {
            let mut insname = "".to_owned();
            let mut upper_price = 0.;
            let mut lower_price = 0.;
            {
                if let Some(ins) = common::global::INS
                    .get()
                    .unwrap()
                    .read()
                    .unwrap()
                    .get_ins(TIConvert::exch_id(&md.EID), &md.IID)
                {
                    insname = if !ins.symbol.is_empty() {
                        ins.symbol.clone()
                    } else {
                        ins.name.clone()
                    };
                    upper_price = ins.uplmtprice;
                    lower_price = ins.lowlmtprice;
                }
            };

            app_mddtl.set_exchid(md.EID.clone().into());
            app_mddtl.set_insid(md.IID.clone().into());
            app_mddtl.set_insname(insname.into());
            app_mddtl.invoke_set_prestlprice(get_price_str(md.PSP, price_digits));
            app_mddtl.set_lower_price(get_price_str(lower_price, price_digits));
            app_mddtl.set_upper_price(get_price_str(upper_price, price_digits));
            app_mddtl.set_open_price(get_price_str(md.OP, price_digits));
            app_mddtl.set_open_price_flag(get_price_up_down_falg(md.PSP, md.OP));
        }

        // 最低/高价格
        app_mddtl.set_lowest_price(get_price_str(md.LTP, price_digits));
        app_mddtl.set_lowest_price_flag(get_price_up_down_falg(md.PSP, md.LTP));
        app_mddtl.set_highest_price(get_price_str(md.HTP, price_digits));
        app_mddtl.set_highest_price_flag(get_price_up_down_falg(md.PSP, md.HTP));

        // 更新最新价
        app_mddtl.set_update_time(TIConvert::transtime2(md.UT as i32).into());
        app_mddtl.set_last_price(get_price_str(md.LP, price_digits));
        app_mddtl.set_last_price_falg(get_price_up_down_falg(md.PSP, md.LP));
        let ret = {
            if md.LP > 0. && 0. != md.PSP {
                (md.LP - md.PSP, 100.0 * (md.LP - md.PSP) / md.PSP)
            } else {
                if 0. == md.LP {
                    (0., 0.)
                } else {
                    (md.LP - md.PSP, 0.)
                }
            }
        };
        if ret.0 > 0. {
            app_mddtl.set_last_price_ud(slint::format!("+{}", TIConvert::format_f(ret.0, price_digits)));
            app_mddtl.set_last_price_rate(slint::format!("+{:.2}", ret.1));
        } else {
            app_mddtl.set_last_price_ud(TIConvert::format_f(ret.0, price_digits).into());
            app_mddtl.set_last_price_rate(slint::format!("{:.2}", ret.1));
        }

        // 更新买5档
        {
            let bid: Rc<VecModel<MdLevelInfo>> = Rc::new(VecModel::default());

            bid.push(MdLevelInfo {
                price: get_price_str(md.BP1, price_digits),
                volume: TIConvert::format_i(md.BV1).into(),
                falg: get_price_up_down_falg(md.PSP, md.BP1),
            });

            bid.push(MdLevelInfo {
                price: get_price_str(md.BP2, price_digits),
                volume: TIConvert::format_i(md.BV2).into(),
                falg: get_price_up_down_falg(md.PSP, md.BP2),
            });

            bid.push(MdLevelInfo {
                price: get_price_str(md.BP3, price_digits),
                volume: TIConvert::format_i(md.BV3).into(),
                falg: get_price_up_down_falg(md.PSP, md.BP3),
            });

            bid.push(MdLevelInfo {
                price: get_price_str(md.BP4, price_digits),
                volume: TIConvert::format_i(md.BV4).into(),
                falg: get_price_up_down_falg(md.PSP, md.BP4),
            });

            bid.push(MdLevelInfo {
                price: get_price_str(md.BP5, price_digits),
                volume: TIConvert::format_i(md.BV5).into(),
                falg: get_price_up_down_falg(md.PSP, md.BP5),
            });

            app_mddtl.set_bid(bid.into());
        }

        // 更新卖5档
        {
            let ask: Rc<VecModel<MdLevelInfo>> = Rc::new(VecModel::default());

            ask.push(MdLevelInfo {
                price: get_price_str(md.AP1, price_digits),
                volume: TIConvert::format_i(md.AV1).into(),
                falg: get_price_up_down_falg(md.PSP, md.AP1),
            });

            ask.push(MdLevelInfo {
                price: get_price_str(md.AP2, price_digits),
                volume: TIConvert::format_i(md.AV2).into(),
                falg: get_price_up_down_falg(md.PSP, md.AP2),
            });

            ask.push(MdLevelInfo {
                price: get_price_str(md.AP3, price_digits),
                volume: TIConvert::format_i(md.AV3).into(),
                falg: get_price_up_down_falg(md.PSP, md.AP3),
            });

            ask.push(MdLevelInfo {
                price: get_price_str(md.AP4, price_digits),
                volume: TIConvert::format_i(md.AV4).into(),
                falg: get_price_up_down_falg(md.PSP, md.AP4),
            });

            ask.push(MdLevelInfo {
                price: get_price_str(md.AP5, price_digits),
                volume: TIConvert::format_i(md.AV5).into(),
                falg: get_price_up_down_falg(md.PSP, md.AP5),
            });

            app_mddtl.set_ask(ask.into());
        }
    }

    // 初始化选中合约
    async fn init_sel_md_dtl(&self, app_weak: &Weak<App>) {
        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mddtl: MarketDataDtl = app.global::<MarketDataDtl>();

            if app_mddtl.invoke_is_sel_ins_changed() {
                app_mddtl.invoke_set_prekey_with_curkey();

                let md = {
                    let key = app_mddtl.get_cur_key();
                    let md_datas = MD_DATAS.get().unwrap();
                    let ret = md_datas.data_map.get(key.as_str());
                    ret
                };
                if let Some(md) = md {
                    let mut digits = Config::PRICE_DIGITS;
                    if let Some(ins) = common::global::INS
                        .get()
                        .unwrap()
                        .read()
                        .unwrap()
                        .get_ins(TIConvert::exch_id(&md.EID), &md.IID)
                    {
                        digits = ins.ptw as usize;
                    }

                    Marketdata::update_sel_md_dtl(&app_mddtl, &md, digits, true);
                }
            }
        });
    }

    // 更新错误信息
    async fn update_aresult(&self, app_weak: &Weak<App>) {
        let md_datas = MD_DATAS.get().unwrap();
        let errmsg = md_datas.errmsg.clone();
        if errmsg.lock().unwrap().is_empty() {
            return;
        }

        let mut errmsg = errmsg.lock().unwrap();
        let em = errmsg.clone();
        *errmsg = "".into();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_msg_box(&app, 3, Default::default(), em.into());
        });
    }

    /// 更新行情
    async fn update_md(&self, app_weak: &Weak<App>) {
        let md_datas = MD_DATAS.get().unwrap();

        // 需要更新的行情
        let mut new_tag = false;
        let data_map_clone = {
            if md_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                new_tag = true;
                md_datas.data_map.clone()
            } else {
                Arc::new(MdL1Map::new())
            }
        };

        if data_map_clone.is_empty() && !new_tag {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mddtl: MarketDataDtl = app.global::<MarketDataDtl>();
            let sel_md_key = app_mddtl.get_cur_key().to_string();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            data_map_clone.iter().for_each(|md| {
                let mut digits = Config::PRICE_DIGITS;
                let mut upper_price = 0.;
                let mut lower_price = 0.;
                let mut insname = String::new();
                {
                    if let Some(ins) = common::global::INS
                        .get()
                        .unwrap()
                        .read()
                        .unwrap()
                        .get_ins(TIConvert::exch_id(&md.EID), &md.IID)
                    {
                        upper_price = ins.uplmtprice;
                        lower_price = ins.lowlmtprice;
                        digits = ins.ptw as usize;
                        insname = ins.symbol.clone();
                    }
                };

                if md.key() == sel_md_key.as_str() {
                    let app_mddtl: MarketDataDtl = app.global::<MarketDataDtl>();
                    Marketdata::update_sel_md_dtl(&app_mddtl, &*md, digits, false);
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 交易所
                items.push(md.EID.clone().into());

                // 合约编码
                items.push(md.IID.clone().into());

                // 合约编码
                items.push(insname.into());

                // 最新价
                items.push(get_price_str(md.LP, digits));

                // 买5量
                items.push(TIConvert::format_i(md.BV5).into());

                // 买5价
                items.push(get_price_str(md.BP5, digits));

                // 买4量
                items.push(TIConvert::format_i(md.BV4).into());

                // 买4价
                items.push(get_price_str(md.BP4, digits));

                // 买3量
                items.push(TIConvert::format_i(md.BV3).into());

                // 买3价
                items.push(get_price_str(md.BP3, digits));

                // 买2量
                items.push(TIConvert::format_i(md.BV2).into());

                // 买2价
                items.push(get_price_str(md.BP2, digits));

                // 买1量
                items.push(TIConvert::format_i(md.BV1).into());

                // 买1价
                items.push(get_price_str(md.BP1, digits));

                // 卖1价
                items.push(get_price_str(md.AP1, digits));

                // 卖1量
                items.push(TIConvert::format_i(md.AV1).into());

                // 卖2价
                items.push(get_price_str(md.AP2, digits));

                // 卖2量
                items.push(TIConvert::format_i(md.AV2).into());

                // 卖3价
                items.push(get_price_str(md.AP3, digits));

                // 卖3量
                items.push(TIConvert::format_i(md.AV3).into());

                // 卖4价
                items.push(get_price_str(md.AP4, digits));

                // 卖4量
                items.push(TIConvert::format_i(md.AV4).into());

                // 卖5价
                items.push(get_price_str(md.AP5, digits));

                // 卖5量
                items.push(TIConvert::format_i(md.AV5).into());

                // 开盘价
                items.push(get_price_str(md.OP, digits));

                // 最低价
                items.push(get_price_str(md.LTP, digits));

                // 最高价
                items.push(get_price_str(md.HTP, digits));

                // 跌停价
                items.push(get_price_str(lower_price, digits));

                // 涨停价
                items.push(get_price_str(upper_price, digits));

                // 昨收价
                items.push(get_price_str(md.PCP, digits));

                // 昨结价
                items.push(get_price_str(md.PSP, digits));

                // 成交量
                items.push(TIConvert::format_i(md.VOL as i64).into());

                // 持仓量
                items.push(TIConvert::format_i(md.OPV).into());

                // 更新时间
                items.push(TIConvert::transtime2(md.UT as i32).into());

                row_data.push(items.into());
            });

            app.global::<OptTrd_MarketData>().set_row_data(row_data.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新错误信息
        self.update_aresult(&app_weak).await;

        // 初始化选中合约
        self.init_sel_md_dtl(&app_weak).await;

        // 更新行情
        self.update_md(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let md_datas = MD_DATAS.get().unwrap();
        if md_datas
            .input_instid_changed
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok()
        {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_md = app.global::<OptTrd_MarketData>();

                let insid = app_md.get_insid();

                if insid.len() > 8 + 5 {
                    // 目前个股期权单合约的编码长度还没有超过8位的
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    app_md.set_insid_model(items.into());
                    return;
                }

                let all_insid_data = app_md.get_all_insid_model();

                if insid.is_empty() {
                    app_md.set_insid_model(all_insid_data);
                    return;
                }

                let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());

                let mut find_flag = false;
                for i in 0..all_insid_data.row_count() {
                    let li = all_insid_data.row_data(i).unwrap();
                    if li.text.starts_with(insid.as_str()) {
                        items.push(li);
                        find_flag = true;
                    } else {
                        if find_flag {
                            break;
                        }
                    }
                }

                app_md.set_insid_model(items.into());
            });
        }
    }
}
