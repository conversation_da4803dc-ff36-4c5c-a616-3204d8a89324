use tiapi::protocol_pub::instrument::{FJYInstrumentField, InstrumentField};

use super::{ticonvert::TIConvert, titype::TIType};

pub type InstrumentMap = dashmap::DashMap<String, InstrumentField>;
pub type FJYInstrumentMap = dashmap::DashMap<String, FJYInstrumentField>;

/// 合约信息
pub struct Instrument {
    /// 是否已经接收完普通合约
    pub is_last: bool,

    /// 是否已经接收完非交易合约
    pub is_fjy_last: bool,

    /// 普通合约
    ins_map: InstrumentMap,

    /// 非交易合约
    fjy_ins_map: FJYInstrumentMap,
}

impl Instrument {
    pub fn new() -> Self {
        Self {
            is_last: false,
            is_fjy_last: false,

            ins_map: InstrumentMap::new(),
            fjy_ins_map: FJYInstrumentMap::new(),
        }
    }
}

impl Instrument {
    /// 添加普通合约
    pub fn push_ins(&mut self, ins: InstrumentField) {
        self.ins_map.insert(format!("{}{}", ins.exchid, ins.insid.clone()), ins);
    }

    /// 添加非交易合约
    pub fn push_fjy_ins(&mut self, ins: FJYInstrumentField) {
        self.fjy_ins_map.insert(format!("{}{}", ins.exchid, ins.insid.clone()), ins);
    }
}

impl Instrument {
    /// 获取所有合约的编码(insid|exchname)
    pub fn get_id_all(&self) -> dashmap::DashSet<String> {
        let ret = dashmap::DashSet::new();
        self.ins_map.iter().for_each(|ins| {
            ret.insert(std::format!("{}|{}", ins.insid, ins.exchname));
        });
        ret
    }

    /// 获取所有合约的编码(insid|exchname, symbol)
    pub fn get_id_all_with_name(&self) -> dashmap::DashMap<String, String> {
        let ret = dashmap::DashMap::new();
        self.ins_map.iter().for_each(|ins| {
            ret.insert(std::format!("{}|{}", ins.insid, ins.exchname), ins.symbol.clone());
        });
        ret
    }

    /// 获取所有期权合约的编码(cp, insid|exchname)
    pub fn get_opt_id_all(&self) -> Vec<(i32, String)> {
        let mut ret = Vec::new();
        self.ins_map.iter().for_each(|ins| {
            if TIType::PRODUCT_OPT == ins.producttype {
                ret.push((ins.optionstype, std::format!("{}|{}", ins.insid, ins.exchname)));
            }
        });
        ret
    }

    /// 获取所有期权合约的编码(insid|exchname, (cp,symbol))
    pub fn get_opt_id_all_with_name(&self) -> dashmap::DashMap<String, (i32, String)> {
        let ret = dashmap::DashMap::new();
        self.ins_map.iter().for_each(|ins| {
            if TIType::PRODUCT_OPT == ins.producttype {
                ret.insert(
                    std::format!("{}|{}", ins.insid, ins.exchname),
                    (ins.optionstype, ins.symbol.clone()),
                );
            }
        });
        ret
    }

    /// 通过交易所编码获取该交易所下所有的合约编码(insid, symbol)
    pub fn get_id_with_name_by_exchid(&self, exchid: i32) -> dashmap::DashMap<String, String> {
        let ret = dashmap::DashMap::new();
        self.ins_map.iter().for_each(|ins| {
            if exchid == ins.exchid {
                ret.insert(ins.insid.clone(), ins.symbol.clone());
            }
        });
        ret
    }

    /// 通过SSE下所有的现货合约(个股期权解锁仓时使用)
    pub fn get_sse_stk_id_for_lock(&self) -> dashmap::DashSet<String> {
        let ret = dashmap::DashSet::new();
        self.ins_map.iter().for_each(|ins| {
            if TIType::EXCH_SSE == ins.exchid && TIType::PRODUCT_STK == ins.producttype {
                ret.insert(std::format!("{}|{}", ins.insid, ins.exchname));
            }
        });
        ret
    }

    /// 获取合约
    pub fn get_ins(&self, exchid: i32, insid: &str) -> Option<InstrumentField> {
        let ret = self.ins_map.try_get(&format!("{}{}", exchid, insid));
        match ret {
            dashmap::try_result::TryResult::Present(ins) => Some(ins.value().clone()),
            _ => None,
        }
    }

    /// 获取合约
    pub fn get_ins_with_exchname(&self, exchname: &str, insid: &str) -> Option<InstrumentField> {
        let ret = self.ins_map.try_get(&format!("{}{}", TIConvert::exch_id(exchname), insid));
        match ret {
            dashmap::try_result::TryResult::Present(ins) => Some(ins.value().clone()),
            _ => None,
        }
    }

    /// 是否存在合约
    pub fn has_ins(&self, exchid: i32, insid: &str) -> bool {
        self.ins_map.contains_key(&format!("{}{}", exchid, insid))
    }

    /// 是否存在合约
    pub fn has_ins_with_exchname(&self, exchname: &str, insid: &str) -> bool {
        self.ins_map
            .contains_key(&format!("{}{}", TIConvert::exch_id(exchname), insid))
    }

    /// 是否存在合约
    pub fn has_ins_only_id(&self, insid: &str) -> bool {
        for ins in &self.ins_map {
            if ins.insid == insid {
                return true;
            }
        }
        false
    }

    /// 获取合约
    ///
    /// 参数`insid` 为合约编码, 共两种格式: insid 与 insid|exchname
    pub fn get_ins_by_insid(&self, insid: &str) -> Option<InstrumentField> {
        let (iid, ename) = Instrument::parse_id(&insid);
        if ename.is_empty() {
            for ins in &self.ins_map {
                if ins.insid == iid {
                    return Some(ins.value().clone());
                }
            }
            None
        } else {
            self.get_ins_with_exchname(&ename, &iid)
        }
    }
}

impl Instrument {
    /// 通过交易编码获取所有的非交易合约编码
    pub fn get_fjy_id_by_exchid(&self, exchid: i32) -> dashmap::DashSet<String> {
        let ret = dashmap::DashSet::new();
        self.fjy_ins_map.iter().for_each(|ins| {
            if exchid == ins.exchid {
                ret.insert(ins.insid.clone());
            }
        });
        ret
    }

    /// 通过交易所编码获取该交易所下所有的非交易合约编码(insid, symbol)
    pub fn get_fjy_id_with_name_by_exchid(&self, exchid: i32) -> dashmap::DashMap<String, String> {
        let ret = dashmap::DashMap::new();
        self.fjy_ins_map.iter().for_each(|ins| {
            if exchid == ins.exchid {
                if ins.sh_fjytype == "CRP" {
                    ret.insert(ins.insid.clone(), ins.name.clone());
                }
            }
        });
        ret
    }

    /// 获取非交易合约信息
    pub fn get_fjy_ins(&self, exchid: i32, insid: &str) -> Option<FJYInstrumentField> {
        let ret = self.fjy_ins_map.try_get(&format!("{}{}", exchid, insid));
        match ret {
            dashmap::try_result::TryResult::Present(ins) => Some(ins.value().clone()),
            _ => None,
        }
    }

    /// 获取非交易合约信息
    pub fn get_fjy_ins_by_insid(&self, insid: &str) -> Option<FJYInstrumentField> {
        for ins in &self.fjy_ins_map {
            if ins.insid == insid {
                return Some(ins.value().clone());
            }
        }
        None
    }
}

impl Instrument {
    /// 分页查询合约
    pub fn qry_ins(&self, exchid: i32, insid: &str, page_index: i32, page_size: i32) -> (i32, Vec<InstrumentField>) {
        log::debug!(
            "qry_ins: exch:{}, insid:{}, pageindex:{}, pagesize:{}",
            exchid,
            insid,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut ins_vec = Vec::<InstrumentField>::new();
        if page_size <= 0 {
            return (cnt, ins_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_ins_vec: Vec<InstrumentField> = self.ins_map.iter().map(|it| it.value().clone()).collect();
        sort_ins_vec.sort_by(|a, b| {
            let aa = std::format!("{}{}", a.exchid, a.insid);
            let bb = std::format!("{}{}", b.exchid, b.insid);
            aa.cmp(&bb)
        });
        sort_ins_vec.into_iter().for_each(|ins| {
            if 0 != exchid {
                if exchid != ins.exchid {
                    return;
                }
            }

            if !insid.is_empty() {
                if !ins.insid.contains(insid) {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                ins_vec.push(ins);
            }

            cnt += 1;
        });

        (cnt, ins_vec)
    }
}

impl Instrument {
    /// 解析合约号
    ///
    /// 合约共两种格式: insid 与 insid|exchname
    ///
    /// 返回值: (insid, exchname)
    pub fn parse_id(id: &str) -> (String, String) {
        if let Some(index) = id.find('|') {
            let insid = id.get(0..index).unwrap();
            let exchname = {
                if let Some(tmp) = id.get(index + 1..id.len()) {
                    tmp
                } else {
                    ""
                }
            };
            return (insid.to_owned(), exchname.to_owned());
        }

        return (id.to_owned(), "".to_owned());
    }
}
