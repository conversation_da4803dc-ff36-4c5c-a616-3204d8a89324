use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    apiproc::tradestkbuf::CtpFjyOrderMap,
    common::{
        config::Config,
        global::{CFG, TOKIO_RT, TRADE_STK_API, TRADE_STK_BUF},
        ticonvert::TIConvert,
    },
    slintui::*,
};
use dashmap::DashMap;
use slint::*;
use tiapi::protocol_pub::{
    order_trade_ctp::CtpBusinessOrderField,
    trade_req_ctp::{CtpReqBusinessOrderActionField, CtpRspBusinessOrderActionField},
};

use super::tradestk::show_cancel_msg_box;

/// 在撤单过程中是否发生错误(用于全撤时,发生错误停止继续撤单)
static IS_ERR_IN_CANCEL: OnceLock<AtomicBool> = OnceLock::new();

/// 在途 - 非交易业务委托
pub(super) struct InFjyOrder {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpFjyOrderMap>,

    /// 撤单响应数据
    rsp_cancel: Arc<Mutex<CtpRspBusinessOrderActionField>>,

    /// 在撤单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_in_cancel: AtomicBool,
}

impl InFjyOrder {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpFjyOrderMap::new()),
            rsp_cancel: Arc::new(Mutex::new(CtpRspBusinessOrderActionField::default())),
            is_err_in_cancel: AtomicBool::new(false),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = IS_ERR_IN_CANCEL.set(AtomicBool::new(false));

        let app_infjyord = app.global::<StkTrd_InFjyOrder>();

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_infjyord.on_req_tip_toggled(move |checked| InFjyOrder::on_req_tip_toggled(&app_weak, checked));

        // 注册撤单笔事件
        let app_weak: Weak<App> = app.as_weak();
        app_infjyord
            .on_cancel_order_clieked(move |ord, need_confirm| InFjyOrder::on_cancel_order_clieked(&app_weak, ord, need_confirm));

        // 注册撤全部事件
        let app_weak = app.as_weak();
        app_infjyord.on_cancel_order_all_clieked(move |row_cnt, need_confirm| {
            InFjyOrder::on_cancel_order_all_clieked(&app_weak, row_cnt, need_confirm)
        });

        log::trace!("Fjy in order init completed");
    }
}

impl InFjyOrder {
    /// 请求查询委托信息
    pub fn qry_in_fjy_order(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_in_fjy_order() };
        if ret.is_empty() {
            return;
        }

        let mut data_changed = false;

        ret.iter().for_each(|it| {
            let key = it.key();
            let ord = it.value();

            if self.data_map.contains_key(key) {
                if 1 != ord.OrdStatus && 2 != ord.OrdStatus {
                    self.data_map.remove(key);
                    data_changed = true;
                } else {
                    let mut pre_order = self.data_map.get_mut(key).unwrap();
                    pre_order.OrdStatus = ord.OrdStatus;
                    pre_order.TransTime = ord.TransTime;
                    data_changed = true;
                }
            } else {
                if 1 == ord.OrdStatus || 2 == ord.OrdStatus {
                    self.data_map.insert(key.clone(), ord.clone());
                    data_changed = true;
                }
            }
        });

        if data_changed {
            self.data_changed.store(true, Ordering::Relaxed);
        }
    }

    /// 非交易业务撤单操作响应
    pub fn on_rsp_inputfjyorderaction(&self, rsp: CtpRspBusinessOrderActionField) {
        IS_ERR_IN_CANCEL.get().unwrap().store(true, Ordering::Relaxed);
        self.is_err_in_cancel.store(true, Ordering::Relaxed);
        *self.rsp_cancel.lock().unwrap() = rsp;
    }
}

impl InFjyOrder {
    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();
        app_set.set_trdreqtip_fjy_ordercancel(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.FjyInputOrderAction = checked;
    }

    /// 撤单笔
    fn on_cancel_order_clieked(app_weak: &Weak<App>, ord: CancelFjyOrderField, need_confirm: bool) {
        let app = app_weak.unwrap();
        let in_ord = app.global::<StkTrd_InFjyOrder>();

        if ord.exchid.is_empty() {
            show_cancel_msg_box(&app, 1, Default::default(), "请先选中要撤的非交易业务报单".into());
            in_ord.set_ctrls_enabled(true);
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.FjyInputOrderAction } {
            show_cancel_msg_box(
                &app,
                100,
                "非交易业务撤单请求确认".into(),
                slint::format!(
                    "资金账户: {}\n\n交易所: {}\n交易所编码: [{}]",
                    ord.accountid,
                    ord.exchid,
                    ord.ordersysid
                ),
            );
            in_ord.set_ctrls_enabled(true);
            return;
        }
        in_ord.set_ctrls_enabled(false);

        let mut req = CtpReqBusinessOrderActionField::default();
        req.ExchangeID = ord.exchid.as_str().into();
        req.InvestorID = ord.accountid.as_str().into();
        req.OrderSysID = ord.ordersysid.as_str().into();

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let api = TRADE_STK_API.get().unwrap().read().await;
            let result = api.req_fjy_orderaction(0, &req).await;
            let err_msg = result.as_ref().err().map(|e| e.to_string());

            let _ = wapp.upgrade_in_event_loop(move |app| {
                if let Some(err) = err_msg {
                    show_cancel_msg_box(
                        &app,
                        1,
                        Default::default(),
                        slint::format!("请求撤单失败\n\n错误信息: {}", err),
                    );
                }
                app.global::<StkTrd_InFjyOrder>().set_ctrls_enabled(true);
            });
        });
    }

    /// 撤全部
    fn on_cancel_order_all_clieked(app_weak: &Weak<App>, row_cnt: i32, need_confirm: bool) {
        let app = app_weak.unwrap();
        let in_ord = app.global::<StkTrd_InFjyOrder>();

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputOrderAction } {
            show_cancel_msg_box(&app, 101, Default::default(), "是否撤销当前所有的非交易业务有效报单?".into());
            in_ord.set_ctrls_enabled(true);
            return;
        }

        if 0 == row_cnt {
            show_cancel_msg_box(&app, 1, Default::default(), "没有非交易业务报单可撤".into());
            in_ord.set_ctrls_enabled(true);
            return;
        }

        in_ord.set_ctrls_enabled(false);

        // 获取当前所有的在途报单
        let mut ord_vec = Vec::new();
        for row_index in 0..row_cnt {
            ord_vec.push(in_ord.invoke_get_cancel_order(row_index))
        }

        // 开始撤单
        let is_err_in_cancel = IS_ERR_IN_CANCEL.get().unwrap();
        is_err_in_cancel.store(false, Ordering::Relaxed);

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let api = TRADE_STK_API.get().unwrap().read().await;

            for ord in ord_vec {
                if is_err_in_cancel.load(Ordering::Relaxed) {
                    break; // 撤单过程中遇到错误, 不再继续撤单
                }

                let mut req = CtpReqBusinessOrderActionField::default();
                req.ExchangeID = ord.exchid.as_str().into();
                req.InvestorID = ord.accountid.as_str().into();
                req.OrderSysID = ord.ordersysid.as_str().into();

                if let Err(err) = api.req_fjy_orderaction(0, &req).await {
                    let _ = wapp.upgrade_in_event_loop(move |app| {
                        show_cancel_msg_box(
                            &app,
                            1,
                            Default::default(),
                            slint::format!(
                                "请求非交易业务撤单失败\n\n资金账户: {}\n\n交易所: {}\n交易所编码: [{}]\n\n错误信息: {}",
                                ord.accountid,
                                ord.exchid,
                                ord.ordersysid,
                                err
                            ),
                        );
                    });

                    break;
                }
            }

            let _ = wapp.upgrade_in_event_loop(move |app| {
                app.global::<StkTrd_InFjyOrder>().set_ctrls_enabled(true);
            });
        });
    }
}

impl InFjyOrder {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let is_err_in_cancel = self
            .is_err_in_cancel
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        let rsp_cancel: CtpRspBusinessOrderActionField;
        let data_map_clone: Arc<DashMap<String, CtpBusinessOrderField>>;

        if !is_err_in_cancel {
            let data_changed = self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok();
            if !data_changed {
                return;
            }

            rsp_cancel = CtpRspBusinessOrderActionField::default();
            data_map_clone = self.data_map.clone();
        } else {
            rsp_cancel = self.rsp_cancel.lock().unwrap().clone();
            data_map_clone = Arc::new(DashMap::new());
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            if is_err_in_cancel {
                show_cancel_msg_box(
                    &app,
                    1,
                    "非交易业务撤单失败".into(),
                    slint::format!(
                        "交易所报单编号:{}\n错误信息:[{}, {}]",
                        rsp_cancel.OrderSysID,
                        rsp_cancel.ec,
                        rsp_cancel.em
                    ),
                );
                return;
            }

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            data_map_clone.iter().for_each(|ord| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 发生时间
                items.push(TIConvert::transtime(ord.TransTime).into());

                // 合约编码
                items.push(ord.InstrumentID.clone().into());

                // 数量
                items.push(slint::format!("{}", ord.Volume));

                // 价格
                items.push(slint::format!("{:.*}", Config::STK_PRICE_DIGITS, ord.Price));

                // 业务类型
                items.push(TIConvert::business_type(ord.BusinessType).into());

                // 买买方向
                items.push(TIConvert::side(ord.Side).into());

                // 报单状态
                items.push(TIConvert::order_status(ord.OrdStatus).into());

                // 交易所报单编号
                items.push(ord.OrderSysID.clone().into());

                // 本地报单编号
                items.push(slint::format!("{}", ord.LocalOrderNo));

                // 剩余数量
                items.push(slint::format!("{}", ord.LeavesVolume));

                // 用户代码
                items.push(ord.UserID.clone().into());

                // 目标合约
                items.push(ord.DstInstrumentID.clone().into());

                // 交易所
                items.push(slint::format!("{}", TIConvert::exch_name(ord.ExchID)));

                // 资金账号
                items.push(ord.AccountID.clone().into());

                // 序号(用于排序)
                items.push(slint::format!("{:>13}", ord.BizSeqNum));

                row_data.push(items.into());
            });

            // 按序号列降序显示
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(14 as usize).unwrap();
                let c_b = r_b.row_data(14 as usize).unwrap();
                c_b.cmp(&c_a)
            }));

            app.global::<StkTrd_InFjyOrder>().set_row_data(sort_model.into());
        });
    }
}
