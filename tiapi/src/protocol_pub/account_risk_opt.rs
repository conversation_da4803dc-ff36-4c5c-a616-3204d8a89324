// 账户资金
#[derive(Default)]
pub struct AccountsField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 上次结算准备金
    /// 系统上场时读取到的值,在整个交易日中不会改变
    pub PreBalance: f64,

    /// 本系统分配资金
    /// 初始为上次结算准备金,如果系统设置了本系统冻结且冻结资金小于 PreBalance 则为冻结资金
    pub DistribFund: f64,

    /// 权利仓最大占用的权利金
    pub MaxBuyPremium: f64,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 权益
    pub Balance: f64,

    /// 市值权益
    pub ValueBalance: f64,

    /// 可用资金
    pub Available: f64,

    /// 可取资金
    pub WithdrawQuota: f64,

    /// 入金金额
    pub Deposit: f64,

    /// 出金金额
    pub Withdraw: f64,

    /// 手续费
    pub Commision: f64,

    /// 冻结的手续费
    pub FrozenCommission: f64,

    /// 平仓盈亏
    pub CloseProfit: f64,

    /// 持仓盈亏(浮动盈亏)
    pub PositionProfit: f64,

    /// 冻结的权利金
    pub FrozenPremium: f64,

    /// 期权权利金收支(如果是现货账户则保存开仓时的持仓金额)
    pub Premium: f64,

    /// 权利仓当前占用的权利金
    pub BuyPremium: f64,

    /// 行权冻结资金
    pub ExFrozen: f64,

    /// 行权冻结费用(买方行权冻结手续费)
    pub ExFrozenCommi: f64,

    /// 冻结保证金
    pub FrozenExchMargin: f64,

    /// 冻结保证金
    pub FrozenMargin: f64,

    /// 交易所占用保证金
    pub ExchMargin: f64,

    /// 占用保证金
    pub UseMargin: f64,

    /// 对冲后占用保证金
    pub ExchNetMargin: f64,

    /// 对冲后占用保证金
    pub UseNetMargin: f64,

    /// 解除当月组合后占用保证金
    pub ExchUCCMMargin: f64,

    /// 解除当月组合后占用保证金
    pub UseUCCMMargin: f64,

    /// 解除当月组合后对冲占用保证金
    pub ExchUCCMNetMargin: f64,

    /// 解除当月组合后对冲占用保证金
    pub UseUCCMNetMargin: f64,

    /// 解除当月组合后需要追加的资金
    pub UCCMAddAmount: f64,

    /// 解除当月组合后需要追加的交易所资金
    pub UCCMAddExchAmount: f64,

    /// 实时风险度
    pub RiskDegree: f64,

    /// 实时交易所风险度
    pub ExchRiskDegree: f64,

    /// 实时对冲风险度
    pub NetRiskDegree: f64,

    /// 实时对冲交易所风险度
    pub NetExchRiskDegree: f64,

    /// 解当月组合后实时风险度
    pub UCCMRiskDegree: f64,

    /// 解当月组合后实时交易所风险度
    pub UCCMExchRiskDegree: f64,

    /// 解当月组合后实时对冲风险度
    pub UCCMNetRiskDegree: f64,

    /// 解当月组合后实时对冲交易所风险度
    pub UCCMNetExchRiskDegree: f64,

    /// 实时市值风险度
    pub VarlueRiskDegree: f64,

    /// 实时交易所市值风险度
    pub VarlueExchRiskDegree: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 资金限额

#[derive(Default)]
pub struct LimitAccountField {
    /// 程序编码
    pub AID: i32,

    // <summary>
    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 权利仓最大占用的权利金
    pub MaxBuyPremium: f64,

    /// 权利仓当前占用的权利金
    pub BuyPremium: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,

    /// 剩余额度 = MaxBuyPremium - BuyPremium
    pub CanBuyPremium: f64,

    /// 占比 = BuyPremium / MaxBuyPremium
    pub Ratio: f64,
}

// 出入金
#[derive(Default)]
pub struct WithdrawDepositField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 处理时间
    pub DealTime: i32,

    /// 入金金额
    pub Deposit: f64,

    /// 出金金额
    pub Withdraw: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
