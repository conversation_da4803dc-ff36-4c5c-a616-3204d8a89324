import { Button, LineEdit, Label, Icons } from "@zstk/lib.slint";
import { WndRec } from "../widgets/wndrec.slint";
import { FundTransField, StkTrd_CreditContract } from "struct.slint";
import { AppCom, WidgetColor, TradeColor, AppInerArgs } from "../struct.slint";

export component CreditReturnAmtPage {

    private property <FundTransField> fund_trans;
    private property <bool> is_show_market: !AppInerArgs.is_all_in_one;

    WndRec {
        title: @tr("直接还款");
        can_close: StkTrd_CreditContract.reamt_ctrls_enabled;

        btn_close_clicked => {
            StkTrd_CreditContract.show_return_amt = false;
        }

        init => {
            StkTrd_CreditContract.reamt_req_ret = "";
        }

        VerticalLayout {
            spacing: 10px;
            padding: 10px;

            HorizontalLayout {
                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "资金账号";
                }

                LineEdit {
                    width: 150px;
                    read_only: true;
                    text: StkTrd_CreditContract.reamt_accid;
                }
            }

            if is_show_market : HorizontalLayout {
                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "交易市场";
                }

                Rectangle {
                    height: 24px;
                    border_color: TradeColor.exchid;
                    border_width: 1px;

                    HorizontalLayout {

                        Rectangle {
                            background: "SSE" == StkTrd_CreditContract.reamt_exchid ? TradeColor.exchid : transparent;

                            Label {
                                text: "上海";
                                vertical_alignment: center;
                                horizontal_alignment: center;
                            }

                            TouchArea {
                                enabled: StkTrd_CreditContract.reamt_ctrls_enabled;
                                mouse_cursor: pointer;
                                clicked => {
                                    StkTrd_CreditContract.reamt_exchid = "SSE";
                                    StkTrd_CreditContract.reamt_insid = "";
                                    StkTrd_CreditContract.reamt_amt = "";
                                    StkTrd_CreditContract.reamt_req_ret = "";
                                }
                            }
                        }

                        Rectangle {
                            width: 1px;
                            border_color: TradeColor.exchid;
                            border_width: 1px;
                        }

                        Rectangle {
                            background: "SZSE" == StkTrd_CreditContract.reamt_exchid ? TradeColor.exchid : transparent;

                            Label {
                                text: "深圳";
                                vertical_alignment: center;
                                horizontal_alignment: center;
                            }

                            TouchArea {
                                enabled: StkTrd_CreditContract.reamt_ctrls_enabled;
                                mouse_cursor: pointer;
                                clicked => {
                                    StkTrd_CreditContract.reamt_exchid = "SZSE";
                                    StkTrd_CreditContract.reamt_insid = "";
                                    StkTrd_CreditContract.reamt_amt = "";
                                    StkTrd_CreditContract.reamt_req_ret = "";
                                }
                            }
                        }

                        Rectangle {
                            width: 1px;
                            border_color: TradeColor.exchid;
                            border_width: 1px;
                        }

                        Rectangle {
                            background: "BSE" == StkTrd_CreditContract.reamt_exchid ? TradeColor.exchid : transparent;

                            Label {
                                text: "北京";
                                vertical_alignment: center;
                                horizontal_alignment: center;
                            }

                            TouchArea {
                                enabled: StkTrd_CreditContract.reamt_ctrls_enabled;
                                mouse_cursor: pointer;
                                clicked => {
                                    StkTrd_CreditContract.reamt_exchid = "BSE";
                                    StkTrd_CreditContract.reamt_insid = "";
                                    StkTrd_CreditContract.reamt_amt = "";
                                    StkTrd_CreditContract.reamt_req_ret = "";
                                }
                            }
                        }
                    }
                }
            }

            // 还指定合约理解成还指定证券代码了, 固暂不支持
            // HorizontalLayout {
            //     Label {
            //         width: 60px;
            //         height: 26px;
            //         vertical_alignment: center;
            //         text: "证券代码";
            //     }

            //     LineEdit {
            //         enabled: StkTrd_CreditContract.reamt_ctrls_enabled;
            //         placeholder_text: "留空为不指定证券代码";
            //         text <=> StkTrd_CreditContract.reamt_insid;
            //         action_icon: Icons.close;

            //         action => {
            //             self.text = "";
            //         }

            //         changed text => {
            //             StkTrd_CreditContract.reamt_req_ret = "";
            //         }
            //     }
            // }

            HorizontalLayout {
                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "还款金额";
                }

                LineEdit {
                    enabled: StkTrd_CreditContract.reamt_ctrls_enabled;
                    placeholder_text: "金额不能小于0.01";
                    text <=> StkTrd_CreditContract.reamt_amt;
                    action_icon: Icons.close;

                    action => {
                        self.text = "";
                    }

                    changed text => {
                        StkTrd_CreditContract.reamt_req_ret = "";
                    }
                }
            }

            Rectangle {
                height: 2px;
            }

            HorizontalLayout {
                Label {
                    height: 26px;
                    vertical_alignment: center;
                    color: TradeColor.dir_sell;
                    text <=> StkTrd_CreditContract.reamt_req_ret;
                }

                Rectangle {
                    horizontal_stretch: 1;
                }

                Button {
                    width: 50px;
                    height: 25px;
                    background: WidgetColor.btn_background;
                    enabled: StkTrd_CreditContract.reamt_ctrls_enabled;
                    text: "还款";
                    clicked => {
                        if AppCom.check_td_session_timeout() {
                            return ;
                        }

                        StkTrd_CreditContract.reamt_ctrls_enabled = false;
                        StkTrd_CreditContract.reamt_clicked();
                    }
                }
            }
        }
    }
}
