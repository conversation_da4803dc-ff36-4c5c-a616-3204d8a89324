import { <PERSON>, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { TimeSelect, TimeItem } from "../widgets/time.slint";
import { ExportButton } from "../widgets/export.slint";

export global FutTrd_QryExercise {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> insid;
    in_out property <string> ordstatus;
    in_out property <string> ordersysid;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);
    callback export_clicked(/* type */ int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

export component QryExercisePage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = FutTrd_QryExercise.exchid;
        i_le_insid.text = FutTrd_QryExercise.insid;
        i_cbx_ordstatus.current_text = FutTrd_QryExercise.ordstatus;
        i_le_ordsysid.text = FutTrd_QryExercise.ordersysid;
        i_ts_start.time = FutTrd_QryExercise.starttime;
        i_ts_end.time = FutTrd_QryExercise.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text: FutTrd_QryExercise.exchid;
                model: CommonModel.exch_fut_model;
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }

            i_le_insid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: FutTrd_QryExercise.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "行权状态";
                vertical_alignment: center;
            }

            i_cbx_ordstatus := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text: FutTrd_QryExercise.ordstatus;
                model: [
                    { text: "" },
                    { text: "未执行" },
                    { text: "已取消" },
                    { text: "已拒绝" },
                ];
            }

            Label {
                text: "行权编码";
                vertical_alignment: center;
            }

            i_le_ordsysid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: FutTrd_QryExercise.ordersysid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "发生时间";
                vertical_alignment: center;
            }

            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: FutTrd_QryExercise.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: FutTrd_QryExercise.endtime;
                }
            }

            Button {
                text: "查询";
                background: WidgetColor.btn_background;
                width: 80px;

                clicked => {
                    FutTrd_QryExercise.exchid = i_cbx_exch.current_text;
                    FutTrd_QryExercise.insid = i_le_insid.text;
                    FutTrd_QryExercise.ordstatus = i_cbx_ordstatus.current_text;
                    FutTrd_QryExercise.ordersysid = i_le_ordsysid.text;
                    FutTrd_QryExercise.starttime = i_ts_start.time;
                    FutTrd_QryExercise.endtime = i_ts_end.time;

                    FutTrd_QryExercise.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    FutTrd_QryExercise.export_clicked(type);
                }
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.fut_insid },
                    { title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("执行结果"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("执行编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地执行编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                ]
            };
            row_count: FutTrd_QryExercise.row_data.length;

            get_cell_data(row_index, column_index) => {
                FutTrd_QryExercise.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(FutTrd_QryExercise.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(FutTrd_QryExercise.row_data[row_index]);
                }
            }
        }

        if FutTrd_QryExercise.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> FutTrd_QryExercise.page_index_model;
            item_total <=> FutTrd_QryExercise.item_total;
            page_total <=> FutTrd_QryExercise.page_total;
            page_index <=> FutTrd_QryExercise.page_index;
            page_size <=> FutTrd_QryExercise.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                FutTrd_QryExercise.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                FutTrd_QryExercise.page_size_changed(size);
            }
        }
    }
}
