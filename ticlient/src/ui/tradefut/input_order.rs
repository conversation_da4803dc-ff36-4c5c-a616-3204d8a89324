use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        config::Config,
        global::{CFG, INS, MD_API, TOKIO_RT, TRADE_FUT_API},
        instrument::Instrument,
        ticonvert::TIConvert,
        titype::TIType,
    },
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{
    CtpReqInputOrderField, CtpRspInputOrderField,
    TradeType::{EContingentCondition, EDirection, EForceCloseReason, EOrderPriceType, ETimeCondition, EVolumeCondition},
};

use super::tradefut::show_input_msg_box;

/// 报单录入数据
struct InputOrderDatas {
    /// 在报单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputorder: AtomicBool,

    /// 报单响应数据
    rsp_inputorder: Arc<Mutex<CtpRspInputOrderField>>,

    /// 输入的合约是否有改变
    input_instid_changed: AtomicBool,
}
impl InputOrderDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputorder: AtomicBool::new(false),
            rsp_inputorder: Arc::new(Mutex::new(CtpRspInputOrderField::default())),
            input_instid_changed: AtomicBool::new(false),
        }
    }
}
static INPUT_ORDER_DATAS: OnceLock<InputOrderDatas> = OnceLock::new();

/// 报单
pub(super) struct InputOrder {}

/// 构建与初始化
impl InputOrder {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化报单录入数据
        let _ = INPUT_ORDER_DATAS.set(InputOrderDatas::new());

        let app_mddtl = app.global::<MarketDataDtl>();

        // 注册点击行情详情的价格时的事件
        let app_weak = app.as_weak();
        app_mddtl.on_price_clicked(move |exchid, insid, price, ptype| {
            InputOrder::on_price_clicked(&app_weak, exchid, insid, price, ptype)
        });

        let app_inputord = app.global::<FutTrd_InputOrder>();

        // 添加合约
        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
        let all_insid = { INS.get().unwrap().read().unwrap().get_id_all_with_name() };
        all_insid.iter().for_each(|it| {
            items.push(crate::slintui::LineItem {
                text: slint::format!("{}", it.key()),
                remark: slint::format!("{}", it.value()),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(items.sort_by(move |r_a, r_b| r_a.text.cmp(&r_b.text)));
        app_inputord.set_insid_model(sort_model.clone().into());
        app_inputord.set_all_insid_model(sort_model.into());

        // 注册合约改变事件
        let app_weak = app.as_weak();
        app_inputord.on_insid_text_changed(move || InputOrder::on_insid_text_changed(&app_weak));

        // 注册价格改变事件
        let app_weak = app.as_weak();
        app_inputord.on_price_text_changed(move || InputOrder::on_price_text_changed(&app_weak));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputord.on_volume_text_changed(move || InputOrder::on_volume_text_changed(&app_weak));

        // 注册最小成交量改变事件
        let app_weak = app.as_weak();
        app_inputord.on_min_volume_text_changed(move || InputOrder::on_min_volume_text_changed(&app_weak));

        // 注册买卖方向改变的事件
        let app_weak = app.as_weak();
        app_inputord.on_dir_changed(move |is_buy| InputOrder::on_dir_changed(&app_weak, is_buy));

        // 注册报单价格上下调整时的事件
        let app_weak = app.as_weak();
        app_inputord.on_price_updown_changed(move |upflag| InputOrder::on_price_updown_changed(&app_weak, upflag));

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputord.on_volume_updown_changed(move |upflag| InputOrder::on_volume_updown_changed(&app_weak, upflag));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputord.on_req_tip_toggled(move |checked| InputOrder::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputord.on_ok_clicked(move |need_confirm| InputOrder::on_ok_clicked(&app_weak, need_confirm));

        log::trace!("Input order init completed");
    }

    /// 报单响应
    pub fn on_rsp_inputorder(&self, rsp: CtpRspInputOrderField) {
        let inputord_datas = INPUT_ORDER_DATAS.get().unwrap();
        *inputord_datas.rsp_inputorder.lock().unwrap() = rsp;
        inputord_datas.is_err_inputorder.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl InputOrder {
    /// 点击行情详情的价格
    fn on_price_clicked(app_weak: &Weak<App>, exchid: SharedString, insid: SharedString, price: SharedString, ptype: i32) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<FutTrd_InputOrder>();

        if exchid != app_inputord.get_exchid() || insid != app_inputord.get_insid() {
            app_inputord.set_exchid(exchid);
            app_inputord.set_insid(insid);
            InputOrder::on_insid_text_changed(app_weak);
        }

        if "--" != price.as_str() && price != app_inputord.get_price() {
            app_inputord.set_price(price);
            InputOrder::on_price_text_changed(app_weak);
        }
    }

    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputord_datas = INPUT_ORDER_DATAS.get().unwrap();
        inputord_datas.input_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputord = app.global::<FutTrd_InputOrder>();

        let insid = app_inputord.get_insid();
        let ins = { INS.get().unwrap().read().unwrap().get_ins_by_insid(&insid) };

        // 设置报价模式
        let mut price_type_model = 0;

        if let Some(ins) = ins {
            app_inputord.set_insid_has_error(false);

            // 设置报价模式
            if TIType::EXCH_CFFEX == ins.exchid {
                if TIType::PRODUCT_FUT == ins.producttype {
                    price_type_model = 1;
                } else if TIType::PRODUCT_OPT == ins.producttype {
                    price_type_model = 2;
                }
            }

            // 设置交易所编码
            let eid = app_inputord.get_exchid();
            if eid.as_str() != ins.exchname.as_str() {
                app_inputord.set_exchid(ins.exchname.clone().into());
                app_inputord.invoke_exchid_changed();
            }

            // 设置价格调整的参数
            app_inputord.set_price_dig(ins.ptw);
            app_inputord.set_price_step(ins.pricetick as f32);

            // 订阅行情
            let app_mddtl = app.global::<MarketDataDtl>();
            let cur_key = std::format!("{}{}", ins.exchname, ins.insid);
            let pre_key = app_mddtl.get_pre_key().to_string();
            if cur_key != pre_key {
                let insname = if !ins.symbol.is_empty() {
                    ins.symbol.clone()
                } else {
                    ins.name.clone()
                };

                app_mddtl.invoke_reset_by_sel_ins(
                    ins.exchname.clone().into(),
                    ins.insid.clone().into(),
                    insname.into(),
                    TIConvert::format_f(ins.uplmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.lowlmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.prestlprice, ins.ptw as usize).into(),
                );

                let sub_insid = ins.insid.clone();
                TOKIO_RT.get().unwrap().spawn(async move {
                    let _ = MD_API.get().unwrap().read().await.req_subscribe(&sub_insid).await;
                });
            }
        } else {
            app_inputord.set_exchid("".into());
            app_inputord.set_insid_has_error(true);
        }

        if price_type_model != app_inputord.get_price_type_model() {
            app_inputord.set_price_type_model(price_type_model);
            app_inputord.set_price_type(0);
            app_inputord.set_price_type_str("普通限价".into());
        }

        true
    }

    /// 报单价格有变动
    fn on_price_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<FutTrd_InputOrder>();

        let price = app_inputord.get_price().replace(",", "");
        if price.is_empty() {
            app_inputord.set_price_has_error(true);
            return false;
        }

        if price.trim().parse::<f64>().unwrap_or_default() <= Config::INVALID_PRICE {
            app_inputord.set_price_has_error(true);
            return false;
        }

        app_inputord.set_price_has_error(false);

        true
    }

    /// 报单数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<FutTrd_InputOrder>();

        let volume = app_inputord.get_volume().replace(",", "");
        if volume.is_empty() {
            app_inputord.set_volume_has_error(true);
            return false;
        }

        if volume.trim().parse::<i32>().unwrap_or_default() <= 0 {
            app_inputord.set_volume_has_error(true);
            return false;
        }

        app_inputord.set_volume_has_error(false);

        true
    }

    /// 最小成交量有变动
    fn on_min_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<FutTrd_InputOrder>();

        let min_volume = app_inputord.get_min_volume().replace(",", "");
        if min_volume.is_empty() {
            app_inputord.set_min_volume_has_error(false);
            return true;
        }

        let ret = min_volume.parse::<i32>();
        if ret.is_err() {
            app_inputord.set_min_volume_has_error(true);
            return false;
        }
        let min_volume = ret.unwrap();
        if min_volume <= 0 {
            app_inputord.set_min_volume_has_error(true);
            return false;
        }

        app_inputord.set_min_volume_has_error(false);

        true
    }

    /// 买卖方向改变的事件
    fn on_dir_changed(app_weak: &Weak<App>, is_buy: bool) {
        if is_buy {
            let app = app_weak.unwrap();
            let app_inputord = app.global::<FutTrd_InputOrder>();
            app_inputord.set_volume("1".into()); // 买入时数量默认为最小报单量
        }
    }

    // 报单价格上下调整时的事件
    fn on_price_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<FutTrd_InputOrder>();

        if app_inputord.get_price_has_error() {
            return;
        }

        let price_str = app_inputord.get_price().replace(",", "");
        let mut price = price_str.trim().parse::<f64>().unwrap_or_default();
        if price <= Config::INVALID_PRICE {
            return;
        }

        if upflag {
            price += app_inputord.get_price_step() as f64;
        } else {
            price -= app_inputord.get_price_step() as f64;
            if price <= Config::INVALID_PRICE {
                return;
            }
        }

        app_inputord.set_price(slint::format!("{:.*}", app_inputord.get_price_dig() as usize, price));
    }

    // 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<FutTrd_InputOrder>();

        if app_inputord.get_volume_has_error() {
            app_inputord.set_volume("1".into());
            app_inputord.set_volume_has_error(false);
            return;
        }

        let volume_str = app_inputord.get_volume().replace(",", "");
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            return;
        }

        if upflag {
            volume += 1;
        } else {
            volume -= 1;
            if volume <= 0 {
                return;
            }
        }

        app_inputord.set_volume(slint::format!("{}", volume));
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();
        app_set.set_trdreqtip_inputorder(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputOrder = checked;
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputord: FutTrd_InputOrder = app.global::<FutTrd_InputOrder>();

        // 资金账号
        let accid = {
            let accid = app_inputord.get_accountid();
            if accid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "资金账户不能为空".into());
                return;
            }
            accid.to_string()
        };

        // 买卖方向
        let side = app_inputord.get_is_buy();

        // 开平标志
        let offset = app_inputord.get_offset();

        // 投保标志
        let hedge = app_inputord.get_hedge();

        // 合约编码
        let insid = {
            let tmp = app_inputord.get_insid();
            let insid = Instrument::parse_id(&tmp.as_str()).0;
            if insid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约信息失败".into());
                return;
            }
            insid
        };

        // 交易所编码
        let exchid = {
            let exchid = app_inputord.get_exchid().to_string();
            if exchid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约所在交易所失败".into());
                return;
            }
            exchid
        };

        // 报单价格条件
        let price_type = app_inputord.get_price_type();

        // 报单价格条件描述
        let price_type_str = {
            let price_type_str = app_inputord.get_price_type_str();

            if price_type_str.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "报单价格类型不能为空".into());
                return;
            }

            price_type_str
        };

        // 报单价格条件类型
        let price_type_model = app_inputord.get_price_type_model();

        // 报单价格
        let price = {
            let price = app_inputord.get_price().replace(",", "");

            if price.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "价格不能为空".into());
                return;
            }

            let price = price.trim().parse::<f64>().unwrap_or_default();
            if price <= Config::INVALID_PRICE {
                show_input_msg_box(&app, 1, Default::default(), "请输入有效的价格".into());
                return;
            }

            price
        };

        // 报单数量
        let volume = {
            let volume = app_inputord.get_volume().replace(",", "");

            if volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "数量不能为空".into());
                return;
            }

            let volume = volume.trim().parse::<i32>().unwrap_or_default();
            if volume <= 0 {
                show_input_msg_box(&app, 1, Default::default(), "请输入有效的数量".into());
                return;
            }

            volume
        };

        // 最小成交量
        let min_volume = {
            let min_volume = app_inputord.get_min_volume().replace(",", "");

            let min_volume = {
                if min_volume.is_empty() {
                    1
                } else {
                    let ret = min_volume.parse::<u32>();
                    if ret.is_err() {
                        show_input_msg_box(&app, 1, Default::default(), "请输入有效的最小成交量".into());
                        return;
                    } else {
                        ret.ok().unwrap() as i32
                    }
                }
            };
            if min_volume <= 0 {
                show_input_msg_box(&app, 1, Default::default(), "请输入有效的最小成交量".into());
                return;
            }

            min_volume
        };

        let price_type = InputOrder::get_order_price_type(&exchid, price_type_model, price_type);
        if -1 == price_type.0 {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!(
                    "解析报单价格条件失败\n\n交易所编码:{}, 报单价格条件(类型:{}): {}]",
                    exchid,
                    price_type_model,
                    app_inputord.get_price_type()
                ),
            );
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputOrder } {
            show_input_msg_box(
                &app,
                100,
                "报单录入确认".into(),
                slint::format!(
                    "资金账户:{}\n\n交易所:{}    合约:{} \n\n{} {}{} {} 手, 价格:{}\n\n报单价格条件:[{}]\n最小成交量:{}",
                    accid,
                    exchid,
                    insid,
                    {
                        if 0 == hedge {
                            "投机"
                        } else if 1 == hedge {
                            "套保"
                        } else {
                            "套利"
                        }
                    },
                    {
                        if side {
                            "买入"
                        } else {
                            "卖出"
                        }
                    },
                    {
                        if 0 == offset {
                            "开仓"
                        } else if 1 == offset {
                            "平仓"
                        } else {
                            "平今"
                        }
                    },
                    volume,
                    price,
                    price_type_str,
                    min_volume
                ),
            );

            return;
        }

        let mut req = CtpReqInputOrderField::default();
        req.InvestorID = accid;
        req.InstrumentID = insid;
        req.ExchangeID = exchid;
        req.OrderPriceType = price_type.0;
        req.TimeCondition = price_type.1;
        req.VolumeCondition = price_type.2;
        req.LimitPrice = price;
        req.VolumeTotalOriginal = volume;
        req.MinVolume = min_volume;
        req.IsAutoSuspend = 0; // 不自动挂起
        req.UserForceClose = 0; // 非强平
        req.ContingentCondition = EContingentCondition::Immediately.into();
        req.ForceCloseReason = EForceCloseReason::NotForceClose.into();

        req.Direction = {
            if side {
                EDirection::Buy.into()
            } else {
                EDirection::Sell.into()
            }
        };

        req.CombOffsetFlag = {
            if 0 == offset {
                // 开仓
                "0".to_owned()
            } else if 1 == offset {
                // 平仓
                "1".to_owned()
            } else {
                // 平今
                "3".to_owned()
            }
        };

        req.CombHedgeFlag = {
            if 0 == hedge {
                // 投机
                "1".to_owned()
            } else if 1 == hedge {
                // 套保
                "3".to_owned()
            } else {
                // 套利
                "2".to_owned()
            }
        };

        let api = TRADE_FUT_API.get().unwrap().read().unwrap();
        if let Err(err) = api.req_orderinput(0, &req) {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("请求报单录入失败\n\n错误信息: {}", err),
            );
        }
    }
}

/// 私有
impl InputOrder {
    /// 获取CTP协议的报单价格条件
    ///
    /// exchid: 交易所编码<br>
    /// price_type_model: 报单价格条件下拉列表类型. 0:默认; 1:中金期货; 2:中金期权;<br>
    /// pricetype: 终端选择的报单价格条件<br>
    ///
    /// 返回值: (报单价格条件, 有效日期类型, 成交量类型)
    fn get_order_price_type(exchid: &str, price_type_model: i32, pricetype: i32) -> (i32, i32, i32) {
        if "CFFEX" == exchid {
            // 中金所期货报单价格条件
            if 1 == price_type_model {
                match pricetype {
                    // "普通限价"
                    0 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::GFD.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    // "限价 即时全部成交或撤销"
                    1 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::CV.into(),
                        );
                    }
                    // "限价 即时成交剩余撤销"
                    2 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    // "限价 即时成交(按最小成交量)剩余撤销"
                    3 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::MV.into(),
                        );
                    }
                    // "最优一挡 即时成交剩余撤销"
                    4 => {
                        return (
                            EOrderPriceType::BestPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    // "最优一挡 即时成交剩余转限价"
                    5 => {
                        return (
                            EOrderPriceType::BestPrice.into(),
                            ETimeCondition::GFD.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    // "最优五挡 即时成交剩余撤销"
                    6 => {
                        return (
                            EOrderPriceType::FiveLevelPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    // "最优五挡 即时成交剩余转限价"
                    7 => {
                        return (
                            EOrderPriceType::FiveLevelPrice.into(),
                            ETimeCondition::GFD.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    _ => {}
                }
            } else if 2 == price_type_model {
                // 中金所期权报单价格条件
                match pricetype {
                    // "普通限价"
                    0 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::GFD.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    // "限价 即时全部成交或撤销"
                    1 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::CV.into(),
                        );
                    }
                    // "限价 即时成交剩余撤销"
                    2 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::AV.into(),
                        );
                    }
                    // "限价 即时成交(按最小成交量)剩余撤销"
                    3 => {
                        return (
                            EOrderPriceType::LimitPrice.into(),
                            ETimeCondition::IOC.into(),
                            EVolumeCondition::MV.into(),
                        );
                    }
                    _ => {}
                }
            }
        }

        (-1, -1, -1)
    }
}

/// 更新UI
impl InputOrder {
    // 更新错误信息
    async fn update_errmsg(&self, app_weak: &Weak<App>) {
        let inputord_datas = INPUT_ORDER_DATAS.get().unwrap();

        let is_err_inputorder = inputord_datas
            .is_err_inputorder
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputorder {
            return;
        }

        let rsp = { inputord_datas.rsp_inputorder.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("报单录入响应失败\n\n错误码:{}\n错误信息:{}", rsp.ec, rsp.em),
            );
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新报单响应错误信息
        self.update_errmsg(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let inputord_datas = INPUT_ORDER_DATAS.get().unwrap();
        if inputord_datas
            .input_instid_changed
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok()
        {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_inputord = app.global::<FutTrd_InputOrder>();

                let insid = app_inputord.get_insid();

                if insid.len() > 25 {
                    // 目前期货期权单合约的编码长度还没有超过25位的
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    app_inputord.set_insid_model(items.into());
                    return;
                }

                let all_insid_data = app_inputord.get_all_insid_model();

                if insid.is_empty() {
                    app_inputord.set_insid_model(all_insid_data);
                    return;
                }

                let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());

                let mut find_flag = false;
                for i in 0..all_insid_data.row_count() {
                    let li = all_insid_data.row_data(i).unwrap();
                    if li.text.starts_with(insid.as_str()) {
                        items.push(li);
                        find_flag = true;
                    } else {
                        if find_flag {
                            break;
                        }
                    }
                }

                app_inputord.set_insid_model(items.into());
            });
        }
    }
}
