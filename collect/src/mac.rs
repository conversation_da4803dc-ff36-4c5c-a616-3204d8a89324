use regex::Regex;
use std::process::Command;

use crate::com::{self, IpMac, CollectInfo};

pub(crate) struct Mac {}

impl Mac {
    /// 获取采集信息
    pub fn get_collect_info() -> Result<CollectInfo, String> {
        // 终端类型(1)
        let terminal_type = com::get_terminal_type().unwrap();

        // 信息采集时间(19)
        let collect_time = com::get_collect_time();

        // IP(39)/MAC(12)
        let ret = Mac::get_ip_mac();
        if let Err(err) = ret {
            return Err(std::format!("get_ip_mac failed. {err}"));
        }
        let ipmac_vec = ret.unwrap();
        let mut ip1 = String::new();
        let mut mac1 = String::new();
        let mut ip2 = String::new();
        let mut mac2 = String::new();
        for ipmac in &ipmac_vec {
            if ip1.is_empty() {
                ip1 = {
                    if !ipmac.ipv4.is_empty() {
                        ipmac.ipv4.clone()
                    } else {
                        com::sub_str(&ipmac.ipv6, 39).to_owned()
                    }
                };
                mac1 = ipmac.mac.clone().replace(':', "");
                continue;
            }

            if ip2.is_empty() {
                ip2 = {
                    if !ipmac.ipv4.is_empty() {
                        ipmac.ipv4.clone()
                    } else {
                        com::sub_str(&ipmac.ipv6, 39).to_owned()
                    }
                };
                mac2 = ipmac.mac.clone().replace(':', "");
                break;
            }
        }

        // 设备名称(9)
        let ret = Mac::get_device_name();
        if let Err(err) = ret {
            return Err(std::format!("get_device_name failed. {err}"));
        }
        let device_name = com::replace_at_2_space(&ret.unwrap());
        let device_name = com::sub_str(&device_name, 9);

        // 操作系统版本(5)
        let ret = Mac::get_os_ver();
        if let Err(err) = ret {
            return Err(std::format!("get_os_ver failed. {err}"));
        }
        let os_ver = com::replace_at_2_space(&ret.unwrap());
        let os_ver = com::sub_str(&os_ver, 5);

        // 硬盘序列号(16)
        let ret = Mac::get_disk_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_disk_serial_number failed. {err}"));
        }
        let disk_serial_number = com::replace_at_2_space(&ret.unwrap());
        let disk_serial_number = com::sub_str(&disk_serial_number, 16);

        // 设备序列号(16)
        let ret = Mac::get_device_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_disk_serial_number failed. {err}"));
        }
        let device_serial_number = com::replace_at_2_space(&ret.unwrap());
        let device_serial_number = com::sub_str(&device_serial_number, 16);

        // 终端类型@信息采集时间@私网IP1@私网IP2@网卡Mac地址1@网卡Mac地址2@设备名称@操作系统版本@硬盘序列号@设备序列号
        let collect_info = std::format!("{terminal_type}@{collect_time}@{ip1}@{ip2}@{mac1}@{mac2}@{device_name}@{os_ver}@{disk_serial_number}@{device_serial_number}");

        #[cfg(debug_assertions)]
        {
            println!("collect_info(mac): {}", collect_info);
        }

        // 对采集信息进行加密
        let ret = com::encrypt(&collect_info);
        if let Err(err) = ret {
            return Err(std::format!("encrypt failed. {err}"));
        }

        Ok(ret.unwrap())
    }
}

impl Mac {
    /// 获取默认网卡名
    fn get_default_route_interface() -> Result<String, String> {
        let output = Command::new("route").args(&["-n", "get", "0.0.0.0"]).output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let re = Regex::new("interface").unwrap();
        let filtered_lines: Vec<&str> = stdout_str.lines().filter(|line| re.is_match(line)).collect();
        if !filtered_lines.is_empty() {
            let line = filtered_lines[0];
            if let Some(index) = line.find(": ") {
                if index + 2 < line.len() {
                    return Ok(line[index + 2..line.len()].to_owned());
                }
            }
        }

        Err(format!("get empty"))
    }

    /// 获取设备名称
    fn get_device_name() -> Result<String, String> {
        let output = Command::new("scutil").args(&["--get", "ComputerName"]).output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        if !stdout_str.is_empty() {
            return Ok(stdout_str.lines().next().unwrap().to_owned());
        }

        Err(format!("get empty"))
    }

    /// 获取操作系统版本
    fn get_os_ver() -> Result<String, String> {
        let output = Command::new("sw_vers").output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let re = Regex::new("ProductVersion").unwrap();
        let filtered_lines: Vec<&str> = stdout_str.lines().filter(|line| re.is_match(line)).collect();
        if !filtered_lines.is_empty() {
            let line = filtered_lines[0];
            if let Some(index) = line.find(":") {
                if index + 1 < line.len() {
                    let ret = line[index + 1..line.len()].trim();
                    return Ok(ret.to_owned());
                }
            }
        }

        Err(format!("get empty"))
    }

    /// 获取硬盘序列号
    fn get_disk_serial_number() -> Result<String, String> {
        // system_profiler SPSerialATADataType - 未测试
        // 如果你的 Mac 使用的是 NVMe SSD：使用
        // system_profiler SPNVMeDataType | grep "Serial Number" | awk -F': ' '{print $2}'
        // 本函数仅实现了 SPNVMeDataType

        let output = Command::new("system_profiler").arg("SPNVMeDataType").output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let re = Regex::new("Serial Number").unwrap();
        let filtered_lines: Vec<&str> = stdout_str.lines().filter(|line| re.is_match(line)).collect();
        if !filtered_lines.is_empty() {
            let line = filtered_lines[0];
            if let Some(index) = line.find(": ") {
                if index + 2 < line.len() {
                    return Ok(line[index + 2..line.len()].to_owned());
                }
            }
        }

        Err(format!("get empty"))
    }

    /// 获取设备序列号
    fn get_device_serial_number() -> Result<String, String> {
        // https://apple.stackexchange.com/questions/40243/how-can-i-find-the-serial-number-on-a-mac-programmatically-from-the-terminal
        // ioreg -c IOPlatformExpertDevice -d 2 | awk -F\" '/IOPlatformSerialNumber/{print $(NF-1)}'
        // system_profiler SPHardwareDataType

        let output = Command::new("system_profiler").arg("SPHardwareDataType").output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let re = Regex::new("Serial Number").unwrap();
        let filtered_lines: Vec<&str> = stdout_str.lines().filter(|line| re.is_match(line)).collect();
        if !filtered_lines.is_empty() {
            let line = filtered_lines[0];
            if let Some(index) = line.find(": ") {
                if index + 2 < line.len() {
                    return Ok(line[index + 2..line.len()].to_owned());
                }
            }
        }

        Err(format!("get empty"))
    }

    /// 获取IP/MAC地址
    /// 第一个地址为默认路由的地址
    pub(super) fn get_ip_mac() -> Result<Vec<IpMac>, String> {
        let ret = Mac::get_default_route_interface();
        if let Err(err) = ret {
            return Err(format!("get_default_route_interface failed. {}", err));
        }
        let default_interface = ret.unwrap();

        // 获取所有网络接口
        let interfaces = pnet::datalink::interfaces();

        let mut ipmac_vec = vec![];
        for interface in interfaces {
            if interface.is_up() && !interface.is_loopback() {
                let isdefault = default_interface == interface.name;

                let mac = interface.mac;
                if mac.is_none() {
                    continue;
                }
                let mac = mac.unwrap();
                if mac.is_zero() {
                    continue;
                }

                let mut ipmac = IpMac::default();
                ipmac.interface = interface.name;
                ipmac.mac = mac.to_string();

                for addr in interface.ips.clone() {
                    if ipmac.ipv4.is_empty() && addr.is_ipv4() {
                        ipmac.ipv4 = addr.ip().to_string();
                    }

                    if ipmac.ipv6.is_empty() && addr.is_ipv6() {
                        ipmac.ipv6 = addr.ip().to_string();
                    }

                    // 一块网卡最多只需要获取一个IPV4和一个IPV6地址即可
                    if !ipmac.ipv4.is_empty() && !ipmac.ipv6.is_empty() {
                        break;
                    }
                }

                if isdefault {
                    ipmac_vec.insert(0, ipmac);
                } else {
                    ipmac_vec.push(ipmac);
                }
            }
        }

        if !ipmac_vec.is_empty() {
            return Ok(ipmac_vec);
        }

        return Err("empty".to_owned());
    }
}

#[cfg(test)]
mod tests {
    use crate::mac::Mac;

    #[test]
    fn it_works() {
        let ret = Mac::get_collect_info();
        assert_eq!(true, ret.is_ok());

        if let Ok(col) = ret {
            println!("exinfo:{}\ntiinfo:{}", col.exinfo, col.tiinfo);
        }
    }
}
