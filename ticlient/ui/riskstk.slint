import { AppCom } from "struct.slint";
import { LockUI } from "lock.slint";
import { RiskStkPage } from "riskstk/riskstk.slint";
import { StatusStrip } from "./widgets/statusstrip.slint";
import { SettingPage } from "tradestk/setting.slint";
import { AboutPage } from "about.slint";

export component MainRiskStkUI {

    preferred_width: 100%;
    preferred_height: 100%;

    if AppCom.islocked: LockUI { }

    if !AppCom.islocked: VerticalLayout {
        RiskStkPage {}

        StatusStrip { }
    }

    if !AppCom.islocked && AppCom.show_set: SettingPage {
        z: 101;

        width: 800px;
        height: 300px;
    }

    if !AppCom.islocked && AppCom.show_about: AboutPage {
        z: 102;

        width: 380px;
        height: 250px;
    }
}
