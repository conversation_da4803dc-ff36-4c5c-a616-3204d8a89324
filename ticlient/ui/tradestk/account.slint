import { Theme, TableView, TVColumnHeader, TVCellType, TVVisible, Fonts, Label, Radius, Icons } from "@zstk/lib.slint";
import { ColWidth, NotifyColor, AppCom, AppInerArgs } from "../struct.slint";
import { StkTrd_Com, StkTrd_Account } from "struct.slint";

export component AccountPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;

    private property <TVColumnHeader> acc_header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 2,
        fix_tail_count: 1,
        columns: [
            /*   0 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
            /*   1 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
            /*   2 */{ title: @tr("总资产 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "上日可用 + 入金 - 出金 + 持仓市值 + 资金收支 - 手续费" },
            /*   3 */{ title: @tr("可用资金 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "总资产- 持仓市值 - 冻结手续费 - 冻结资金" },
            /*   4 */{ title: @tr("当日盈亏 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100, tips: "持仓市值 + 资金收支 - 手续费 - 开盘时的持仓市值" },
            /*   5 */{ title: @tr("资金收支"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /*   6 */{ title: @tr("资金冻结"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*   7 */{ title: @tr("手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*   8 */{ title: @tr("冻结手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*   9 */{ title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  10 */{ title: @tr("平仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  11 */{ title: @tr("入金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  12 */{ title: @tr("出金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  13 */{ title: @tr("证券市值"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  14 */{ title: @tr("上日可用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  15 */{ title: @tr(""), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80, cell_type: TVCellType.HyperlinkText },
        ]
    };

    private property <bool> credit_show_detail_amt: AppInerArgs.is_all_in_one ? AppInerArgs.trd_detail_amt : true;
    private property <length> credit_header_exchid_width: credit_show_detail_amt ? ColWidth.exchid : 0.5px;
    private property <TVVisible> credit_header_exchid_visible: credit_show_detail_amt ? TVVisible.Visible : TVVisible.Invisible;
    private property <TVColumnHeader> acc_credit_header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 7,
        fix_tail_count: 1,
        columns: [
            /*  0 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: credit_header_exchid_width, visible: credit_header_exchid_visible },
            /*  1 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },

            /*  2 */{ title: @tr("总资产"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  3 */{ title: @tr("可用资金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },

            /*  4 */{ title: @tr("总负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  5 */{ title: @tr("保证金可用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  6 */{ title: @tr("维持担保比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },

            /*  7 */{ title: @tr("当日盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  8 */{ title: @tr("资金收支"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /*  9 */{ title: @tr("资金冻结"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 10 */{ title: @tr("手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 11 */{ title: @tr("冻结手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 12 */{ title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 13 */{ title: @tr("平仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 14 */{ title: @tr("入金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 15 */{ title: @tr("出金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 16 */{ title: @tr("证券市值"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },

            /* 17 */{ title: @tr("融资负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 18 */{ title: @tr("融券负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 19 */{ title: @tr("两融冻结保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 20 */{ title: @tr("两融冻结费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 21 */{ title: @tr("未成交充抵保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_130 },
            /* 22 */{ title: @tr("充抵保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 23 */{ title: @tr("融资盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 24 */{ title: @tr("融券盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 25 */{ title: @tr("融券卖出金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 26 */{ title: @tr("融资保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 27 */{ title: @tr("融券保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 28 */{ title: @tr("两融利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 29 */{ title: @tr("两融费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },

            /* 30 */{ title: @tr("上日可用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /* 31 */{ title: @tr(""), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80, cell_type: TVCellType.HyperlinkText },

            // { title: @tr("信用总市值"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            // { title: @tr("信用占用金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
        ]
    };

    TableView {
        height: 100%;
        width: 100%;
        sortable: true;
        column_header: StkTrd_Com.iscredit ? acc_credit_header : acc_header;
        row_header: { visible: false };
        row_count: StkTrd_Account.row_data.length;

        sort_canceled => {
            StkTrd_Account.sort_asc_column_index = 0;
            StkTrd_Account.sort_dec_column_index = -1;
            StkTrd_Account.sort_ascending(StkTrd_Account.sort_asc_column_index);
        }

        sort_ascending(index) => {
            StkTrd_Account.sort_ascending(index);
            StkTrd_Account.sort_asc_column_index = index;
            StkTrd_Account.sort_dec_column_index = -1;
            true
        }

        sort_descending(index) => {
            StkTrd_Account.sort_descending(index);
            StkTrd_Account.sort_asc_column_index = -1;
            StkTrd_Account.sort_dec_column_index = index;
            true
        }

        get_cell_data(row_index, column_index) => {
            StkTrd_Account.row_data[row_index][column_index]
        }

        get_cell_data_color(row_index, column_index, data) => {
            if StkTrd_Com.iscredit {
                // 入金
                if 14 == column_index {
                    return NotifyColor.normal;
                }

                // 出金
                if 15 == column_index {
                    return NotifyColor.caution;
                }

                // 资金划转
                if 31 == column_index {
                    return Theme.accent_background;
                }

                // 这几列数据中小数含有逗号, ui处理不了, 交给回调处理
                if 6 == column_index {
                    return StkTrd_Account.get_row_data_color(row_index, 10000, data);
                }
                if 3 == column_index        // 可用资金
                    || 7 == column_index    // 当日盈亏
                    || 8 == column_index    // 资金收支
                    || 12 == column_index   // 持仓盈亏
                    || 13 == column_index   // 平仓盈亏
                    || 5 == column_index    // 保证金可用
                    || 23 == column_index   // 融资浮盈
                    || 24 == column_index   // 融券浮盈
                {
                    return StkTrd_Account.get_row_data_color(row_index, column_index, data);
                }

                if 4 == column_index        // 总负债
                    || 17 == column_index   // 融资负债
                    || 18 == column_index   // 融券负债
                {
                    return StkTrd_Account.get_row_data_color(row_index, 10001, data);
                }
            } else {
                // 入金
                if 11 == column_index {
                    return NotifyColor.normal;
                }

                // 出金
                if 12 == column_index {
                    return NotifyColor.caution;
                }

                // 资金划转
                if 15 == column_index {
                    return Theme.accent_background;
                }

                // 这几列数据中小数含有逗号, ui处理不了, 交给回调处理
                // 可用资金, 当日盈亏, 资金收支, 持仓盈亏, 平仓盈亏
                if 3 == column_index || 4 == column_index || 5 == column_index || 9 == column_index || 10 == column_index {
                    return StkTrd_Account.get_row_data_color(row_index, column_index, data);
                }
            }

            NotifyColor.default
        }

        cell_double_clicked(row_index, column_index) => {
            StkTrd_Com.accountid = StkTrd_Account.row_data[row_index][1];
        }

        cell_pointer_event(row_index, column_index, event, point) => {
            if PointerEventKind.up == event.kind {
                if PointerEventButton.left == event.button {
                    if StkTrd_Com.iscredit {
                        if 31 == column_index && "" != StkTrd_Account.row_data[row_index][31] {
                            StkTrd_Account.sel_accid = StkTrd_Account.row_data[row_index][1];
                            if "" != StkTrd_Account.sel_accid {
                                self.select_full_row = true;
                                self.set_current_row(row_index);
                                if !StkTrd_Account.show_fund_trans {
                                    StkTrd_Account.show_fund_trans = true;
                                }
                            }
                        }
                    } else {
                        if (15 == column_index && "" != StkTrd_Account.row_data[row_index][15]) {
                            StkTrd_Account.sel_accid = StkTrd_Account.row_data[row_index][1];
                            if "" != StkTrd_Account.sel_accid {
                                self.select_full_row = true;
                                self.set_current_row(row_index);
                                if !StkTrd_Account.show_fund_trans {
                                    StkTrd_Account.show_fund_trans = true;
                                }
                            }
                        }
                    }
                } else if PointerEventButton.right == event.button {
                    if StkTrd_Com.iscredit {
                        StkTrd_Account.sel_accid = StkTrd_Account.row_data[row_index][1];
                    } else {
                        StkTrd_Account.sel_accid = StkTrd_Account.row_data[row_index][1];
                    }

                    if "" != StkTrd_Account.sel_accid {
                        self.select_full_row = true;
                        self.set_current_row(row_index);

                        right_up_point = point;
                        if !StkTrd_Account.show_fund_trans {
                            i_pw_menu.show();
                        }
                    }
                }
            }
        }

        cell_key_copy_pressed(row_index, column_index) => {
            if column_index >= 0 {
                AppCom.copy_str_to_clipboard(StkTrd_Account.row_data[row_index][column_index]);
            } else {
                AppCom.copy_arr_to_clipboard(StkTrd_Account.row_data[row_index]);
            }
        }
    }

    i_pw_menu := PopupWindow {

        x: right_up_point.x - root.absolute_position.x;
        y: right_up_point.y - root.absolute_position.y;

        Rectangle {

            width: 100px;

            border-width: 1px;
            border-color: Theme.border;
            border_radius: Radius.extra_small;

            drop_shadow_blur: 15px;
            drop_shadow_color: Theme.control_background.darker(0.5);

            background: Theme.control_background;

            VerticalLayout {
                padding: 5px;
                spacing: 10px;

                i_menu_fundtrans := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 16px;
                            colorize: Theme.foreground;
                            source: Icons.list;
                        }

                        Label {
                            text: "资金划转";
                            horizontal-alignment: left;
                        }
                    }

                    i_menu_fundtrans_ta := TouchArea {
                        clicked => {
                            StkTrd_Account.show_fund_trans = true;
                        }
                    }
                }
            }
        }

        states [
            fundtrans_has_hover when i_menu_fundtrans_ta.has_hover: {
                i_menu_fundtrans.background: Theme.shadow;
            }
        ]
    }
}