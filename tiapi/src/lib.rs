#![allow(dead_code)]
#![allow(unused_variables)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]

pub mod api;
pub mod error;
pub mod protocol_pub;
pub mod serverinfo;
pub use self::error::{Error, Result};

mod api_impl;
mod protocol;
mod request;
mod version;
mod ws;
use api::tradefutapi::{ITradeFutSpi, TradeFutApi};
use api::tradeoptapi::{ITradeOptSpi, TradeOptApi};
use api::tradestkapi::{ITradeStkSpi, TradeStkApi};
use api::riskstkapi::{IRiskStkSpi, RiskStkApi};

/// 获取版本号
pub fn get_version() -> String {
    version::CRATE_VERSION.to_string()
}

/// 获取服务端信息
pub async fn get_svrinfo(encon: bool, url: &str) -> Result<String> {
    let mut req = request::HttpClient::new();
    req.set_encon(encon);
    req.get_svrinfo(url).await
}

/// 获取验证码
pub async fn get_vercode(encon: bool, url: &str) -> Result<(i32, bytes::Bytes)> {
    let mut req = request::HttpClient::new();
    req.set_encon(encon);
    req.get_vercode(url).await
}

/// 创建现货交易API
pub fn create_trade_stk_api<T: ITradeStkSpi + 'static>(spi: T) -> Result<Box<dyn TradeStkApi>> {
    Ok(Box::new(api_impl::tradestkapi::TradeStkApiImpl::new(spi)))
}

/// 创建期货交易API
pub fn create_trade_fut_api<T: ITradeFutSpi + 'static>(spi: T) -> Result<Box<dyn TradeFutApi>> {
    Ok(Box::new(api_impl::tradefutapi::TradeFutApiImpl::new(spi)))
}

/// 创建个股期权交易API
pub fn create_trade_opt_api<T: ITradeOptSpi + 'static>(spi: T) -> Result<Box<dyn TradeOptApi>> {
    Ok(Box::new(api_impl::tradeoptapi::TradeOptApiImpl::new(spi)))
}

/// 创建现货风控API
pub fn create_risk_stk_api<T: IRiskStkSpi + 'static>(spi: T) -> Result<Box<dyn RiskStkApi>> {
    Ok(Box::new(api_impl::riskstkapi::RiskStkApiImpl::new(spi)))
}
