[workspace]
resolver = "2"
members = ["lan", "collect", "mdapi", "ticlient", "comfun", "tiapi", "tiwrapper"]

[profile.release]
strip = true
opt-level = "z"  # Optimize for size
lto = true
codegen-units = 1
panic = "abort"

# cargo b --release --target x86_64-pc-windows-gnu
# cargo b --release --target x86_64-apple-darwin
# cargo doc --no-deps --open
# RUSTFLAGS="-C link-arg=-mmacosx-version-min=11.0" cargo build --release