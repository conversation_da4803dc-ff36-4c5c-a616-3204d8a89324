import { Theme, ThemeStyle, Radius, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Label } from "@zstk/lib.slint";

export component WndRec inherits Rectangle {

    in_out property <string> title;
    in_out property <bool> show_title: true;
    in_out property <bool> can_move: true;
    in_out property <bool> can_close: true;

    callback btn_close_clicked();

    private property <bool> hover_close_btn;

    border_width: 1px;
    border_color: Theme.border;
    border_radius: Radius.extra_small;
    background: ThemeStyle.Light == Theme.theme_style ? Theme.background.darker(0.02) : Theme.background.brighter(0.3);

    drop_shadow_blur: 50px;
    drop_shadow_color: ThemeStyle.Light == Theme.theme_style ? self.background.darker(0.3) : Theme.background.brighter(0.5);

    TouchArea {
        height: 100%;
        width: 100%;
    }

    VerticalLayout {
        padding-bottom: root.border_radius;
        padding-left: root.border_width;
        padding-right: root.border_width;

        HorizontalLayout {
            padding_right: 10px;
            height: show_title ? 30px : Radius.extra_small;
            visible: show_title;

            // Title
            Rectangle {
                if "" != title : Label {
                    vertical_alignment: center;
                    horizontal_alignment: center;
                    font_size: 1.2rem;
                    text: title;
                }

                TouchArea {
                    moved => {
                        if can_move && self.pressed {
                            root.x += self.mouse_x - self.pressed_x;
                            root.y += self.mouse_y - self.pressed_y;
                        }
                    }
                }
            }

            i_img_close := Image {
                image_fit: contain;
                width: 18px;
                source: Icons.close;
                colorize: can_close ? (hover_close_btn ? red : Theme.control_foreground) : Theme.foreground_disabled;

                i_touch_area_close := TouchArea {
                    mouse_cursor: can_close ? pointer : default;

                    clicked => {
                        if can_close {
                            btn_close_clicked();
                        }
                    }
                }
            }
        }

        Rectangle {
            height: 1px;
            background: Theme.border_spliter;
        }

        @children
    }

    Rectangle {
        clip: false;
        visible: can_close && hover_close_btn;
        z: 100;

        x: i_img_close.x - 10px;
        y: i_img_close.height + 2px;
        width: i_text.width + 10px;
        height: i_text.height + 10px;

        background: ThemeStyle.Light == Theme.theme_style ? Theme.background.darker(0.2) : Theme.background.brighter(0.2);
        opacity: 0.9;
        border_radius: Radius.extra_small;

        i_text := Label {
            font_size: Fonts.normal.size;
            text: "关闭";
        }
    }

    states [
        hover_close_btn when i_touch_area_close.has_hover: {
            root.hover_close_btn: true;
        }
    ]
}

export component WndDialog inherits Window {

    in_out property <bool> show_title: true;
    in_out property <bool> can_move: true;
    in_out property <bool> can_close: true;

    callback btn_close_clicked();

    private property <bool> hover_close_btn;

    height: 100%;
    width: 100%;

    init => {
        i_fs.focus();
    }

    TouchArea {
        height: 1000000%;
        width: 1000000%;
        z: 9999998;

        Rectangle {
            background: Theme.control_background_disabled;
            opacity: 0.35;
        }
    }

    popup := Rectangle {
        z: 9999999;

        private property <bool> hover_close_btn;

        VerticalLayout {
            alignment: center;

            HorizontalLayout {
                alignment: center;

                Rectangle {
                    clip: true;

                    border_width: 1px;
                    border_color: Theme.border;
                    border_radius: Radius.extra_small;
                    background: ThemeStyle.Light == Theme.theme_style ? Theme.background.darker(0.02) : Theme.background.brighter(0.3);

                    drop_shadow_blur: 50px;
                    drop_shadow_color: ThemeStyle.Light == Theme.theme_style ? self.background.darker(0.3) : Theme.background.brighter(0.5);

                    VerticalLayout {
                        padding-bottom: parent.border_radius;
                        padding-left: parent.border_width;
                        padding-right: parent.border_width;

                        // Title
                        Rectangle {
                            height: show_title ? 30px : Radius.extra_small;
                            visible: show_title;

                            HorizontalLayout {
                                padding_right: 10px;

                                Rectangle {
                                    if "" != root.title : Label {
                                        font_size: 1.2rem;
                                        text: root.title;
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        moved => {
                                            if can_move && self.pressed {
                                                popup.x += self.mouse_x - self.pressed_x;
                                                popup.y += self.mouse_y - self.pressed_y;
                                            }
                                        }
                                    }
                                }

                                i_img_close := Image {
                                    image_fit: contain;
                                    width: 18px;
                                    source: Icons.close;
                                    colorize: can_close ? (hover_close_btn ? red : Theme.control_foreground) : Theme.foreground_disabled;

                                    i_touch_area_close := TouchArea {
                                        mouse_cursor: can_close ? pointer : default;

                                        clicked => {
                                            if can_close {
                                                btn_close_clicked();
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        Rectangle {
                            height: 1px;
                            background: Theme.border_spliter;
                        }

                        @children
                    }
                }
            }
        }

        Rectangle {
            clip: false;
            visible: can_close && hover_close_btn;
            z: 100;

            x: i_img_close.x - 10px;
            y: i_img_close.height + 2px;
            width: i_text.width + 10px;
            height: i_text.height + 10px;

            background: ThemeStyle.Light == Theme.theme_style ? Theme.background.darker(0.2) : Theme.background.brighter(0.2);
            opacity: 0.9;
            border_radius: Radius.extra_small;

            i_text := Label {
                font_size: Fonts.normal.size;
                text: "关闭";
            }
        }

        states [
            hover_close_btn when i_touch_area_close.has_hover: {
                popup.hover_close_btn: true;
            }
        ]
    }

    i_fs := FocusScope {
        height: 0;
        width: 0;

        key_pressed(event) => {
            accept
        }
    }
}
