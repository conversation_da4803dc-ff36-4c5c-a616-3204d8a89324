use serde::{Deserialize, Serialize};

/// HTTP请求登录
#[derive(Default, Serialize, Debug)]
pub struct ReqLoginField {
    /// 请求编号
    pub reqid: i32,

    /// 用户名
    pub uid: String,

    /// 密码
    pub passwd: String,

    /// 动态密码
    pub onetimepassword: String,

    /// appid
    pub appid: String,

    /// appid认证码
    pub appididentify: String,

    /// 公有流续传
    pub pubsequenceno: i32,

    /// 私有流续传
    pub prvsequenceno: i32,

    /// api版本号
    pub apiversion: String,

    /// mac地址. 格式: **:**:**:**:**:**
    pub macaddress: String,

    /// 按照交易所规则加密上报的监管信息
    pub exrepeatinfo: String,

    /// 公司规则加密上报的监管信息
    pub tirepeatinfo: String,

    /// 客户端版本号
    pub cliver: String,

    /// api版本号(后面传终端版本号,更方便控制)
    pub apiver: String, //"20230523 ti_risk"

    /// 校验码序号
    pub vcseqnum: i32,

    /// 校验码
    pub vcdata: String,
}

/// HTTP请求登录响应
#[derive(Default, Deserialize, Debug)]
#[serde[default]]
pub struct RspLoginField {
    /// 错误码
    pub ec: i32,

    /// 错误信息
    pub em: String,

    /// 交易日期
    pub tradedate: String,

    /// 用户名
    pub userid: String,

    /// SessionID
    pub sid: String,

    // SessionID
    pub ssid: String,

    /// 权限
    pub permissions: i32,

    /// 是否已经登录交易
    pub IsLoginTrade: bool,
}

/// HTTP请求登出
pub struct ReqLogoutField {}

/// HTTP请求登出响应
#[derive(Default)]
pub struct RspLogoutField {
    pub userid: String,
}

/// HTTP请求登录(交易)

pub struct RiskReqLoginTrd {}

/// HTTP请求登录响应(交易)
#[derive(Default)]
pub struct RiskRspLoginTrdField {
    /// 登录成功的交易端ServerID
    pub SrvIDS: String,

    /// 登录失败的交易端ServerID
    pub FaildSrvIDS: String,
}
