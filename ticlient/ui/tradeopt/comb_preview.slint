import { Theme, Label, ComboBox } from "@zstk/lib.slint";
import { WndRec } from "../widgets/wndrec.slint";
import { TradeColor } from "../struct.slint";
import { LineEditEx } from "../widgets/lineeditex.slint";
import { OptTrd_InputOml } from "struct.slint";

export component CombPreviewPage {

    WndRec {
        title: "组合预览";
        btn_close_clicked => {
            OptTrd_InputOml.show_oml_preview = false;
        }

        GridLayout {
            spacing-vertical: 10px;
            spacing-horizontal: 10px;
            padding: 10px;

            // 组合策略
            Row {
                Label {
                    text: "   组合策略";
                    vertical-alignment: center;
                }

                ComboBox {
                    width: 200px;
                    placeholder_text: "请选择";
                    current_text <=> OptTrd_InputOml.op_strategyid;
                    current_index <=> OptTrd_InputOml.op_strategyid_idx;
                    model: OptTrd_InputOml.strategyid_model;
                    max_popup_height: 160px;

                    selected => {
                        if 0 == self.current_index || 1 == self.current_index || 2 == self.current_index || 3 == self.current_index {
                            OptTrd_InputOml.op_leg1_dir = "权利";
                            OptTrd_InputOml.op_leg2_dir = "义务";
                            OptTrd_InputOml.op_leg1_dir_i32 = 50;
                            OptTrd_InputOml.op_leg2_dir_i32 = 51;
                        } else {
                            OptTrd_InputOml.op_leg1_dir = "义务";
                            OptTrd_InputOml.op_leg2_dir = "义务";
                            OptTrd_InputOml.op_leg1_dir_i32 = 51;
                            OptTrd_InputOml.op_leg2_dir_i32 = 51;
                        }

                        OptTrd_InputOml.op_leg1_insid_changed();
                    }
                }
            }

            // 第1腿合约
            Row {

                Label {
                    text: " 第1腿合约";
                    vertical-alignment: center;
                }

                VerticalLayout {
                    spacing: 2px;

                    LineEditEx {
                        max_popup_height: 160px;
                        model <=> OptTrd_InputOml.op_leg1_insid_model;
                        has_error <=> OptTrd_InputOml.op_leg1_has_error;
                        current_text <=> OptTrd_InputOml.op_leg1_insid;
                        placeholder_text: "请输入";

                        edited => {
                            OptTrd_InputOml.op_leg1_insid_changed();
                        }
                    }

                    if !OptTrd_InputOml.op_leg1_has_error || "" != OptTrd_InputOml.op_leg1_insname : Label {
                        font_size: 0.90rem;
                        color: OptTrd_InputOml.op_leg1_has_error ? Theme.border_error : Theme.control_foreground;
                        text <=> OptTrd_InputOml.op_leg1_insname;
                    }
                }

                Label {
                    font_size: 0.95rem;
                    color: 50 == OptTrd_InputOml.op_leg1_dir_i32 ? TradeColor.dir_buy : TradeColor.dir_sell;
                    text <=> OptTrd_InputOml.op_leg1_dir;
                    vertical-alignment: center;
                }
            }

            // 第2腿合约
            Row {

                Label {
                    text: " 第2腿合约";
                    vertical-alignment: center;
                }

                VerticalLayout {
                    spacing: 2px;

                    LineEditEx {
                        read_only: true;
                        max_popup_height: 160px;
                        model <=> OptTrd_InputOml.op_leg2_insid_model;
                        has_error <=> OptTrd_InputOml.op_leg2_has_error;
                        current_text <=> OptTrd_InputOml.op_leg2_insid;
                        placeholder_text <=> OptTrd_InputOml.op_leg2_placeholder_text;

                        edited => {
                            OptTrd_InputOml.op_leg2_insid_changed();
                        }
                    }

                    if !OptTrd_InputOml.op_leg1_has_error || "" != OptTrd_InputOml.op_leg1_insname : Label {
                        font_size: 0.9rem;
                        color: OptTrd_InputOml.op_leg2_has_error ? Theme.border_error : Theme.control_foreground;
                        text <=> OptTrd_InputOml.op_leg2_insname;
                    }
                }

                Label {
                    font_size: 0.95rem;
                    color: TradeColor.dir_sell;
                    text <=> OptTrd_InputOml.op_leg2_dir;
                    vertical_alignment: center;
                }
            }
        }
    }
}
