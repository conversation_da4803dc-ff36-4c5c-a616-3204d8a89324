use serde::Deserialize;

/// 合约信息
#[derive(Default, Clone, Debug, Deserialize)]
#[serde[default]]
pub struct InstrumentField {
    /// 交易所编码
    pub exchid: i32,

    /// 合约编码
    pub insid: String,

    /// 合约名称
    pub name: String,

    /// 合约名称
    pub symbol: String,

    /// 产品代码
    pub productid: String,

    /// 标的商品代码
    pub underlyingid: String,

    /// 产品类型
    pub producttype: i32,

    /// 期权类型
    pub optionstype: i32,

    /// 合约数量乘数
    pub multiple: i32,

    /// 交割年份
    pub delyear: i32,

    /// 交割月
    pub delmonth: i32,

    /// 提前月份
    pub admonth: i32,

    /// 交易权限:1允许,0不允许
    pub tr: i32,

    /// 创建日
    pub createdate: String,

    /// 上市日
    pub opendate: String,

    /// 到期日
    pub expiredate: String,

    /// 开始交割日
    pub startdeldate: String,

    /// 最后交割日
    pub enddeldate: String,

    /// 挂牌基准价
    pub basisprice: f64,

    /// 行权价格
    pub strikeprice: f64,

    /// 市价单最大下单量
    pub maxmktvol: i32,

    /// 市价单最小下单量
    pub minmktvol: i32,

    /// 限价单最大下单量
    pub maxlmtvol: i32,

    /// 限价单最小下单量
    pub minlmtvol: i32,

    /// 最小变动价位
    pub pricetick: f64,

    /// 昨日结算价
    pub prestlprice: f64,

    /// 涨停板价格
    pub uplmtprice: f64,

    /// 跌停板价格
    pub lowlmtprice: f64,

    /// 现货 - 是否为T+1交易(0不是1是)
    pub tp1: i32,

    /// 现货 - 买单位数量
    pub buyordtick: i32,

    /// 现货 - 卖单位数量
    pub sellordtick: i32,

    /// 现货 - 公允价
    pub fairprice: f64,

    /// 现货 - 融资融券 - 是否为融资标的(0不是1是)
    pub ifn: i32,

    /// 现货 - 融资融券 - 是否为融券标的(0不是1是)
    pub isc: i32,

    /// 现货 - 融资融券 - 担保品折算率
    pub colldiskratio: f64,

    /// 现货 - 融资融券 - 融资保证金比例
    pub fimarginratio: f64,

    /// 现货 - 融资融券 - 融券保证金比例
    pub slmarginratio: f64,

    /// 现货 - 融资融券 - 是否允许融资
    pub enablefi: i32,

    /// 现货 - 融资融券 - 是否允许融券
    pub ebablesl: i32,

    /// 现货 - 融资融券 - 是否允许做抵押物
    pub enabledb: i32,

    /// 现货 - 融资融券 - 静态市盈率
    pub static_pe: f64,

    /// 现货 - 融资融券 - 静态市盈率. 0:未超过; 1:超过300或为负
    pub static_pe_flag: i32,
    
    /// 现货 - 融资融券 - 担保物比例
    pub db_ratio: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 交易所编码
    pub exchname: String,

    /// 最小变动价位对应的小数位数
    pub ptw: i32,
}

/// 非交易业务合约信息
#[derive(Default, Clone, Debug, Deserialize)]
#[serde[default]]
pub struct FJYInstrumentField {
    /// 交易所编码
    pub exchid: i32,

    /// 合约编码
    pub insid: String,

    /// 合约名称
    pub name: String,

    /// 非交易业务类型
    pub sh_fjytype: String,

    /// 产品证券代码
    pub sh_productid: String,

    /// 产品证券名称
    pub sh_productname: String,

    /// 非交易订单输入开始日期
    pub sh_startdate: String,

    /// 非交易订单输入结束日期
    pub sh_enddate: String,

    /// 非交易订单整手数
    pub sh_tickvol: i32,

    /// 非交易订单最小订单数量
    pub sh_minvol: i32,

    /// 非交易订单的最大订单数量
    pub sh_maxvol: i32,

    /// 非交易价格
    pub sh_price: f64,

    /// IPO涨停板价格
    pub sh_ipouplmtprice: f64,

    /// IPO跌停板价格
    pub sh_ipolowlmtprice: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 交易所编码
    pub exchname: String,
}
