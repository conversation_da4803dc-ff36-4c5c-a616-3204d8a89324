import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TableView } from "@zstk/lib.slint";
import { AppC<PERSON>, ColWidth, WidgetColor, AppCom } from "../struct.slint";
import { StkTrd_InFjyOrder } from "struct.slint";

export component InFjyOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <int> select_row_index: -1;
    function req_cancel_order(need_confirm: bool) {
        StkTrd_InFjyOrder.sel_cancel_order.exchid = "";
        if select_row_index >= 0 && StkTrd_InFjyOrder.row_data.length > select_row_index {
            StkTrd_InFjyOrder.sel_cancel_order.exchid = StkTrd_InFjyOrder.row_data[select_row_index][12];
            StkTrd_InFjyOrder.sel_cancel_order.accountid = StkTrd_InFjyOrder.row_data[select_row_index][13];
            StkTrd_InFjyOrder.sel_cancel_order.ordersysid = StkTrd_InFjyOrder.row_data[select_row_index][7];
        }
        StkTrd_InFjyOrder.cancel_order_clieked(StkTrd_InFjyOrder.sel_cancel_order, need_confirm);
    }

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 2,
                columns: [
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    { title: @tr("价格"), alignment: TextHorizontalAlignment.center, width: ColWidth.price },
                    { title: @tr("业务类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_fjytype },
                    { title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    { title: @tr("报单状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("剩余数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                    { title: @tr("目标代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                ]
            };
            row_count: StkTrd_InFjyOrder.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_InFjyOrder.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_clicked(row_index, column_index) => {
                self.select_full_row = true;
            }

            cell_double_clicked(row_index, column_index) => {
                if StkTrd_InFjyOrder.ctrls_enabled {
                    req_cancel_order(true);
                }
            }

            current_cell_changed(row_index, column_index) => {
                select_row_index = row_index;
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_InFjyOrder.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_InFjyOrder.row_data[row_index]);
                }
            }
        }

        HorizontalLayout {
            padding: 6px;
            spacing: 30px;
            height: 36px;

            Button {
                text: "撤单";
                background: WidgetColor.btn_background;
                enabled: StkTrd_InFjyOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkTrd_InFjyOrder.ctrls_enabled = false;
                    req_cancel_order(true);
                }
            }

            Button {
                text: "全撤";
                background: WidgetColor.btn_background;
                enabled: StkTrd_InFjyOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkTrd_InFjyOrder.ctrls_enabled = false;
                    StkTrd_InFjyOrder.cancel_order_all_clieked(StkTrd_InFjyOrder.row_data.length, true);
                }
            }
        }
    }
}
