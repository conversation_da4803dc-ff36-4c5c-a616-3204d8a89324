use crate::{
    common::{
        self,
        tistruct::{FilePathField, OrderInsertCancelMonitorField, TradePositionMonitorField, TradeSelfMonitorField},
    },
    slintui::*,
};
use slint::*;

/// 设置
pub(super) struct Setting {}

/// 构建与初始化
impl Setting {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let app_set = app.global::<OptTrd_Setting>();

        // 初始化设置的值
        {
            let cfg = common::global::CFG.get().unwrap().read().unwrap();

            app_set.set_trdreqtip_inputorder(cfg.com.ReqTradeTip.InputOrder);
            app_set.set_trdreqtip_ordercancel(cfg.com.ReqTradeTip.InputOrderAction);

            app_set.set_trdreqtip_combinsert(cfg.com.ReqTradeTip.CombInsert);
            app_set.set_trdreqtip_combcancecl(cfg.com.ReqTradeTip.CombAction);

            app_set.set_trdreqtip_inputexec(cfg.com.ReqTradeTip.ExecOrderInsert);
            app_set.set_trdreqtip_execcancel(cfg.com.ReqTradeTip.ExecOrderAction);

            app_set.set_trdreqtip_inputstklock(cfg.com.ReqTradeTip.InputStockLock);

            app_set.set_trdreqtip_inputquote(cfg.com.ReqTradeTip.InputQuote);
            app_set.set_trdreqtip_quotecancel(cfg.com.ReqTradeTip.InputQuoteAction);

            app_set.set_mon_tradeposition(MonTradePosition {
                ratio: cfg.opt_mon.TrdPosMonitor.Ratio.to_string().into(),
                trade_vol: cfg.opt_mon.TrdPosMonitor.TradeVolume.to_string().into(),
            });

            app_set.set_mon_orderinsertcancel(MonOrderinsertcancel {
                cancel_ratio: cfg.opt_mon.OrdInsertCancel.CancelRatio.to_string().into(),
                greater_than_num: cfg.opt_mon.OrdInsertCancel.GreaterThanNum.to_string().into(),
            });

            app_set.set_mon_tradeself(MonTradeself {
                trade_vol: cfg.opt_mon.TrdSelfMonitor.TradeVol.to_string().into(),
            });
        }

        // 交易请求确认 - 报单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_inputorder_changed(move || Setting::on_trdreqtip_inputorder_changed(&app_weak));

        // 交易请求确认 - 撤单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_ordercancel_changed(move || Setting::on_trdreqtip_ordercancel_changed(&app_weak));

        // 交易请求确认 - 组合
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_combinsert_changed(move || Setting::on_trdreqtip_combinsert_changed(&app_weak));

        // 交易请求确认 - 解组合
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_combcancecl_changed(move || Setting::on_trdreqtip_combcancecl_changed(&app_weak));

        // 交易请求确认 - 行权
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_inputexec_changed(move || Setting::on_trdreqtip_inputexec_changed(&app_weak));

        // 交易请求确认 - 撤行权
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_execcancel_changed(move || Setting::on_trdreqtip_execcancel_changed(&app_weak));

        // 交易请求确认 - 备兑解锁仓
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_inputstklock_changed(move || Setting::on_trdreqtip_inputstklock_changed(&app_weak));

        // 交易请求确认 - 报价
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_inputquote_changed(move || Setting::on_trdreqtip_inputquote_changed(&app_weak));

        // 交易请求确认 - 撤报价
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_quotecancel_changed(move || Setting::on_trdreqtip_quotecancel_changed(&app_weak));

        // 异常监控 - 成交持仓比 - 重置
        let app_weak = app.as_weak();
        app_set.on_reset_mon_tradeposition(move || Setting::on_reset_mon_tradeposition(&app_weak));

        // 异常监控 - 成交持仓比
        let app_weak = app.as_weak();
        app_set.on_mon_tradeposition_changed(move || Setting::on_mon_tradeposition_changed(&app_weak));

        // 异常监控 - 报撤单 - 重置
        let app_weak = app.as_weak();
        app_set.on_reset_mon_orderinsertcancel(move || Setting::on_reset_mon_orderinsertcancel(&app_weak));

        // 异常监控 - 报撤单
        let app_weak = app.as_weak();
        app_set.on_mon_orderinsertcancel_changed(move || Setting::on_mon_orderinsertcancel_changed(&app_weak));

        // 异常监控 - 自成交 - 重置
        let app_weak = app.as_weak();
        app_set.on_reset_mon_tradeself(move || Setting::on_reset_mon_tradeself(&app_weak));

        // 异常监控 - 自成交
        let app_weak = app.as_weak();
        app_set.on_mon_tradeself_changed(move || Setting::on_mon_tradeself_changed(&app_weak));

        log::trace!("Setting init completed");
    }
}

// 事件 - 交易请求确认
impl Setting {
    /// 交易请求确认 - 报单
    fn on_trdreqtip_inputorder_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputOrder = app_set.get_trdreqtip_inputorder();
    }

    /// 交易请求确认 - 撤单
    fn on_trdreqtip_ordercancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputOrderAction = app_set.get_trdreqtip_ordercancel();
    }

    /// 交易请求确认 - 组合
    fn on_trdreqtip_combinsert_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.CombInsert = app_set.get_trdreqtip_combinsert();
    }

    /// 交易请求确认 - 解组合
    fn on_trdreqtip_combcancecl_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.CombAction = app_set.get_trdreqtip_combcancecl();
    }

    /// 交易请求确认 - 行权
    fn on_trdreqtip_inputexec_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.ExecOrderInsert = app_set.get_trdreqtip_inputexec();
    }

    /// 交易请求确认 - 撤行权
    fn on_trdreqtip_execcancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.ExecOrderAction = app_set.get_trdreqtip_execcancel();
    }

    /// 交易请求确认 - 备兑解锁仓
    fn on_trdreqtip_inputstklock_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputStockLock = app_set.get_trdreqtip_inputstklock();
    }

    /// 交易请求确认 - 报价
    fn on_trdreqtip_inputquote_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputQuote = app_set.get_trdreqtip_inputquote();
    }

    /// 交易请求确认 - 撤报价
    fn on_trdreqtip_quotecancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputQuoteAction = app_set.get_trdreqtip_quotecancel();
    }
}

// 事件 - 异常监控 - 成交持仓比
impl Setting {
    /// 异常监控 - 成交持仓比 - 重置
    fn on_reset_mon_tradeposition(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        app_set.set_mon_tradeposition_err_status(0);
        app_set.set_mon_tradeposition_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.opt_mon.TrdPosMonitor = TradePositionMonitorField::default();
        app_set.set_mon_tradeposition(MonTradePosition {
            ratio: cfg.opt_mon.TrdPosMonitor.Ratio.to_string().into(),
            trade_vol: cfg.opt_mon.TrdPosMonitor.TradeVolume.to_string().into(),
        });

        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_POS_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.opt_mon.TrdPosMonitor, false);
    }

    /// 异常监控 - 成交持仓比
    fn on_mon_tradeposition_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let tpm = app_set.get_mon_tradeposition();

        let ret = tpm.trade_vol.parse::<i32>();
        if let Err(err) = ret {
            app_set.set_mon_tradeposition_err_status(1);
            app_set.set_mon_tradeposition_err_tips(slint::format!("请输入正确的数量. {}", err.to_string()));
            return;
        }
        let trade_vol = ret.unwrap();
        if trade_vol <= 0 {
            app_set.set_mon_tradeposition_err_status(1);
            app_set.set_mon_tradeposition_err_tips(slint::format!("数量必需大于0"));
            return;
        }

        let ret = tpm.ratio.parse::<f64>();
        if let Err(err) = ret {
            app_set.set_mon_tradeposition_err_status(2);
            app_set.set_mon_tradeposition_err_tips(slint::format!("请输入正确的成交持仓比. {}", err.to_string()));
            return;
        }
        let ratio = ret.unwrap();
        if ratio <= 0.000001 {
            app_set.set_mon_tradeposition_err_status(2);
            app_set.set_mon_tradeposition_err_tips(slint::format!("数量必需大于0.000001"));
            return;
        }

        app_set.set_mon_tradeposition_err_status(0);
        app_set.set_mon_tradeposition_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.opt_mon.TrdPosMonitor.TradeVolume = trade_vol;
        cfg.opt_mon.TrdPosMonitor.Ratio = ratio;
        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_POS_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.opt_mon.TrdPosMonitor, false);
    }
}

// 事件 - 异常监控 - 报撤单
impl Setting {
    /// 异常监控 - 报撤单 - 重置
    fn on_reset_mon_orderinsertcancel(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        app_set.set_mon_orderinsertcancel_err_status(0);
        app_set.set_mon_orderinsertcancel_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.opt_mon.OrdInsertCancel = OrderInsertCancelMonitorField::default();
        app_set.set_mon_orderinsertcancel(MonOrderinsertcancel {
            cancel_ratio: cfg.opt_mon.OrdInsertCancel.CancelRatio.to_string().into(),
            greater_than_num: cfg.opt_mon.OrdInsertCancel.GreaterThanNum.to_string().into(),
        });

        let path = std::format!(
            "{}/{}",
            cfg.com.FPath.LoginUserCfgDir,
            FilePathField::ORD_INSERT_CANCEL_MONITOR_PATH
        );
        common::global::serialize_write_user_data(&path, &cfg.opt_mon.OrdInsertCancel, false);
    }

    /// 异常监控 - 报撤单
    fn on_mon_orderinsertcancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let tsm = app_set.get_mon_orderinsertcancel();

        let ret = tsm.greater_than_num.parse::<i32>();
        if let Err(err) = ret {
            app_set.set_mon_orderinsertcancel_err_status(1);
            app_set.set_mon_orderinsertcancel_err_tips(slint::format!("请输入正确的总笔数. {}", err.to_string()));
            return;
        }
        let greater_than_num = ret.unwrap();
        if greater_than_num <= 0 {
            app_set.set_mon_orderinsertcancel_err_status(1);
            app_set.set_mon_orderinsertcancel_err_tips(slint::format!("总笔数必需大于0"));
            return;
        }

        app_set.set_mon_orderinsertcancel_err_status(0);
        app_set.set_mon_orderinsertcancel_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.opt_mon.OrdInsertCancel.GreaterThanNum = greater_than_num;
        let path = std::format!(
            "{}/{}",
            cfg.com.FPath.LoginUserCfgDir,
            FilePathField::ORD_INSERT_CANCEL_MONITOR_PATH
        );
        common::global::serialize_write_user_data(&path, &cfg.opt_mon.OrdInsertCancel, false);
    }
}

// 事件 - 异常监控 - 自成交
impl Setting {
    /// 异常监控 - 自成交 - 重置
    fn on_reset_mon_tradeself(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        app_set.set_mon_tradeself_err_status(0);
        app_set.set_mon_tradeself_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.opt_mon.TrdSelfMonitor = TradeSelfMonitorField::default();
        app_set.set_mon_tradeself(MonTradeself {
            trade_vol: cfg.opt_mon.TrdSelfMonitor.TradeVol.to_string().into(),
        });

        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.opt_mon.TrdSelfMonitor, false);
    }

    /// 异常监控 - 自成交
    fn on_mon_tradeself_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        let tsm = app_set.get_mon_tradeself();

        let ret = tsm.trade_vol.parse::<i32>();
        if let Err(err) = ret {
            app_set.set_mon_tradeself_err_status(1);
            app_set.set_mon_tradeself_err_tips(slint::format!("请输入正确的数量. {}", err.to_string()));
            return;
        }
        let trade_vol = ret.unwrap();
        if trade_vol <= 0 {
            app_set.set_mon_tradeself_err_status(1);
            app_set.set_mon_tradeself_err_tips(slint::format!("数量必需大于0"));
            return;
        }

        app_set.set_mon_tradeself_err_status(0);
        app_set.set_mon_tradeself_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.opt_mon.TrdSelfMonitor.TradeVol = trade_vol;
        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.opt_mon.TrdSelfMonitor, false);
    }
}
