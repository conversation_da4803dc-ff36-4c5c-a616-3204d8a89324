use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::apiproc::tradefutbuf::CtpLimitOpenMap;

use crate::{
    common::{global::TRADE_FUT_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type IDSet = dashmap::DashSet<String>;

/// 监控 - 限开仓 - 数据
struct MonLimitOpenDatas {
    /// 数据
    data_map: Arc<CtpLimitOpenMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 交易编码列表
    clientid_set: Arc<IDSet>,

    /// 品种编码列表
    productid_set: Arc<IDSet>,

    /// 统计编码列表
    stid_set: Arc<IDSet>,

    /// 是否需要过滤数据
    filter_changed: AtomicBool,
}
impl MonLimitOpenDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpLimitOpenMap::new()),
            data_changed: AtomicBool::new(false),

            clientid_set: Arc::new(IDSet::new()),
            productid_set: Arc::new(IDSet::new()),
            stid_set: Arc::new(IDSet::new()),

            filter_changed: AtomicBool::new(false),
        }
    }
}
static MON_LIMIT_OPEN_DATAS: OnceLock<MonLimitOpenDatas> = OnceLock::new();

/// 监控 - 限开仓
pub(super) struct MonLimitOpen {}

// 构建与初始化
impl MonLimitOpen {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = MON_LIMIT_OPEN_DATAS.set(MonLimitOpenDatas::new());

        let app_mon = app.global::<FutTrd_MonLimitOpen>();

        // 注册过滤条件改变事件
        let app_weak = app.as_weak();
        app_mon.on_filter_changed(move || MonLimitOpen::on_filter_changed(&app_weak));

        // 注册获取单元格颜色事件
        let app_weak = app.as_weak();
        app_mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonLimitOpen::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor limit open init completed");
    }
}

// 查询
impl MonLimitOpen {
    /// 查询限开仓信息
    pub fn qry_limitopen(&self) {
        let ret = { TRADE_FUT_BUF.get().unwrap().read().unwrap().pop_limitopen() };
        if ret.is_empty() {
            return;
        }

        let mlo_datas = MON_LIMIT_OPEN_DATAS.get().unwrap();

        for (key, lo) in ret {
            if !mlo_datas.clientid_set.contains(&lo.ClientID) {
                mlo_datas.clientid_set.insert(lo.ClientID.clone());
            }
            if !mlo_datas.productid_set.contains(&lo.ProductID) {
                mlo_datas.productid_set.insert(lo.ProductID.clone());
            }
            if !mlo_datas.stid_set.contains(&lo.STID) {
                mlo_datas.stid_set.insert(lo.STID.clone());
            }

            mlo_datas.data_map.insert(key, lo);
        }
        mlo_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl MonLimitOpen {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 开仓量
        if 5 == column_index {
            let data = data.to_string().replace(",", "");
            let data_vec: Vec<&str> = data.split('/').collect();
            if 2 == data_vec.len() {
                let var = data_vec[0].trim().parse::<i32>();
                let maxvar = data_vec[1].trim().parse::<i32>();
                if var.is_ok() && maxvar.is_ok() {
                    let var = var.unwrap();
                    let maxvar = maxvar.unwrap();
                    if var >= maxvar || maxvar <= 0 {
                        return nc.get_error();
                    } else {
                        let ratio = var as f64 / maxvar as f64;
                        if ratio >= 0.9 {
                            return nc.get_caution();
                        } else {
                            return nc.get_normal();
                        }
                    }
                }
            }
        }

        nc.get_default()
    }

    /// 过虑条件有变动
    fn on_filter_changed(app_weak: &Weak<App>) {
        MON_LIMIT_OPEN_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

// 更新UI
impl MonLimitOpen {
    // 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let mlo_datas = MON_LIMIT_OPEN_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mlo: FutTrd_MonLimitOpen = app.global::<FutTrd_MonLimitOpen>();

            if mlo_datas.clientid_set.len() != app_mlo.invoke_clientid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mlo_datas.clientid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlo.set_clientid_model(id_arr.into());
            }

            if mlo_datas.productid_set.len() != app_mlo.invoke_productid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mlo_datas.productid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlo.set_productid_model(id_arr.into());
            }

            if mlo_datas.stid_set.len() != app_mlo.invoke_stid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mlo_datas.stid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlo.set_stid_model(id_arr.into());
            }
        });
    }

    // 更新限开仓
    async fn update_limitopen(&self, app_weak: &Weak<App>) {
        let mlo_datas = MON_LIMIT_OPEN_DATAS.get().unwrap();

        let data_map_clone = {
            if mlo_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                mlo_datas.filter_changed.store(false, Ordering::Relaxed);
                mlo_datas.data_map.clone()
            } else {
                if mlo_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    mlo_datas.data_map.clone()
                } else {
                    Arc::new(CtpLimitOpenMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mlo: FutTrd_MonLimitOpen = app.global::<FutTrd_MonLimitOpen>();

            let filter_exchid = app_mlo.get_exchid().to_string();
            let filter_cliid = app_mlo.get_clientid().to_string();
            let filter_pid = app_mlo.get_productid().to_string();
            let filter_stid = app_mlo.get_stid().to_string();
            let filter_sttype = app_mlo.get_sttype().to_string();

            // 填充数据
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|lo| {
                let exchname = TIConvert::exch_name(lo.ExchID);
                if !filter_exchid.is_empty() {
                    if exchname != filter_exchid {
                        return;
                    }
                }

                if !filter_cliid.is_empty() {
                    if !lo.ClientID.contains(&filter_cliid) {
                        return;
                    }
                }

                if !filter_pid.is_empty() {
                    if !lo.ProductID.contains(&filter_pid) {
                        return;
                    }
                }

                if !filter_stid.is_empty() {
                    if !lo.STID.contains(&filter_stid) {
                        return;
                    }
                }

                let sttype = {
                    match lo.STType {
                        0 => "合约(期货)".to_owned(),
                        1 => "品种(期权)".to_owned(),
                        2 => "交割月份(期权)".to_owned(),
                        3 => "深度虚值合约(期权)".to_owned(),
                        _ => lo.STType.to_string(),
                    }
                };
                if !filter_sttype.is_empty() {
                    if sttype != filter_sttype {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(lo.AccountID.clone().into());

                // 交易所
                items.push(exchname.into());

                // 交易编码
                items.push(lo.ClientID.clone().into());

                // 品种编码
                items.push(lo.ProductID.clone().into());

                // 统计编码
                items.push(lo.STID.clone().into());

                // 开仓量
                items.push(slint::format!(
                    "{} / {}",
                    TIConvert::format_i(lo.OpenVolume),
                    TIConvert::format_i(lo.MaxOpenVolume)
                ));

                // 统计类型
                items.push(sttype.into());

                // 发生时间
                items.push(TIConvert::transtime(lo.TransTime).into());

                // Ratio(仅用于排序)
                let ratio = {
                    let mut ratio = 10000.; // 倍率初始值足够大. 防止排序时2.0大天12.1这样的情况
                    if 0 != lo.MaxOpenVolume {
                        ratio += lo.OpenVolume as f64 / lo.MaxOpenVolume as f64;
                    }
                    ratio
                };
                items.push(ratio.to_string().into());

                row_data.push(items.into());
            });

            // 根据 ratio 按降序排序
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(8).unwrap();
                let c_b = r_b.row_data(8).unwrap();
                c_b.cmp(&c_a)
            }));
            app_mlo.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新限开仓
        self.update_limitopen(&app_weak).await;
    }
}
