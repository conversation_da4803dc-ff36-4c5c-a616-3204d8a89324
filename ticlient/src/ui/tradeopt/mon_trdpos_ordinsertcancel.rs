use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::common::global::CFG;

use crate::{
    common::{global::TRADE_OPT_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type IDSet = dashmap::DashSet<String>;

/// 持仓成交比/报撤单
#[derive(Default, Debug, Clone)]
struct TrdPosOrdICField {
    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub ClientID: String,  // 交易编码
    pub ProductID: String, // 品种编码
    pub TransTime: i32,    // 发生时间

    // 持仓成交比
    pub TradeVolume: i32, // 成交量
    pub Position: i32,    // 持仓量
    pub Ratio: f64,       // 成交量/持仓量

    // 报撤单
    pub InsertNum: i32, // 报单笔数
    pub CancelNum: i32, // 撤单笔数
}

type TrdPosOrdICMap = dashmap::DashMap<String, TrdPosOrdICField>;

/// 监控 - 持仓成交比/报撤单 - 数据
struct MonTrdPosOrdICDatas {
    /// 数据
    data_map: Arc<TrdPosOrdICMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 交易编码列表
    clientid_set: Arc<IDSet>,

    /// 品种编码列表
    productid_set: Arc<IDSet>,

    /// 是否需要过滤数据
    filter_changed: AtomicBool,
}
impl MonTrdPosOrdICDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(TrdPosOrdICMap::new()),
            data_changed: AtomicBool::new(false),

            clientid_set: Arc::new(IDSet::new()),
            productid_set: Arc::new(IDSet::new()),

            filter_changed: AtomicBool::new(false),
        }
    }
}
static MON_TRDPOS_OIC_DATAS: OnceLock<MonTrdPosOrdICDatas> = OnceLock::new();

/// 监控 - 持仓成交比/报撤单
pub(super) struct MonTrdPosOrdIC {}

// 构建与初始化
impl MonTrdPosOrdIC {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = MON_TRDPOS_OIC_DATAS.set(MonTrdPosOrdICDatas::new());

        let app_mon = app.global::<OptTrd_MonTrdPosOrdInsertCancel>();

        // 注册过滤条件改变事件
        let app_weak = app.as_weak();
        app_mon.on_filter_changed(move || MonTrdPosOrdIC::on_filter_changed(&app_weak));

        // 注册获取单元格颜色事件
        let app_weak = app.as_weak();
        app_mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonTrdPosOrdIC::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor trdpos ord insert cancel init completed");
    }
}

// 查询
impl MonTrdPosOrdIC {
    /// 查询监控信息
    pub fn qry_trdpos_oic(&self) {
        let mon_datas = MON_TRDPOS_OIC_DATAS.get().unwrap();

        let ret1 = { TRADE_OPT_BUF.get().unwrap().read().unwrap().pop_tradeposition() };
        if !ret1.is_empty() {
            ret1.iter().for_each(|tp| {
                if !mon_datas.clientid_set.contains(&tp.ClientID) {
                    mon_datas.clientid_set.insert(tp.ClientID.clone());
                }
                if !mon_datas.productid_set.contains(&tp.ProductID) {
                    mon_datas.productid_set.insert(tp.ProductID.clone());
                }

                if mon_datas.data_map.contains_key(tp.key()) {
                    let mut data = mon_datas.data_map.get_mut(tp.key()).unwrap();

                    if data.TransTime < tp.TransTime {
                        data.TransTime = tp.TransTime;
                    }

                    data.TradeVolume = tp.TradeVolume;
                    data.Position = tp.Position;
                    data.Ratio = tp.Ratio;
                } else {
                    let mut data = TrdPosOrdICField::default();
                    data.ExchID = tp.ExchID;
                    data.AccountID = tp.AccountID.clone();
                    data.ClientID = tp.ClientID.clone();
                    data.ProductID = tp.ProductID.clone();
                    data.TransTime = tp.TransTime.clone();

                    data.TradeVolume = tp.TradeVolume;
                    data.Position = tp.Position;
                    data.Ratio = tp.Ratio;

                    mon_datas.data_map.insert(tp.key().to_owned(), data);
                }
            });
        }

        let ret2 = { TRADE_OPT_BUF.get().unwrap().read().unwrap().pop_orderinsertcancel() };
        if !ret2.is_empty() {
            ret2.iter().for_each(|oic| {
                if !mon_datas.clientid_set.contains(&oic.ClientID) {
                    mon_datas.clientid_set.insert(oic.ClientID.clone());
                }
                if !mon_datas.productid_set.contains(&oic.ProductID) {
                    mon_datas.productid_set.insert(oic.ProductID.clone());
                }

                if mon_datas.data_map.contains_key(oic.key()) {
                    let mut data = mon_datas.data_map.get_mut(oic.key()).unwrap();

                    if data.TransTime < oic.TransTime {
                        data.TransTime = oic.TransTime;
                    }

                    data.InsertNum = oic.InsertNum;
                    data.CancelNum = oic.CancelNum;
                } else {
                    let mut data = TrdPosOrdICField::default();
                    data.ExchID = oic.ExchID;
                    data.AccountID = oic.AccountID.clone();
                    data.ClientID = oic.ClientID.clone();
                    data.ProductID = oic.ProductID.clone();
                    data.TransTime = oic.TransTime.clone();

                    data.InsertNum = oic.InsertNum;
                    data.CancelNum = oic.CancelNum;

                    mon_datas.data_map.insert(oic.key().to_owned(), data);
                }
            });
        }

        if !ret1.is_empty() || !ret2.is_empty() {
            mon_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

/// 事件
impl MonTrdPosOrdIC {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 成交持仓比异常
        if 7 == column_index {
            if "否" == data.as_str() {
                return nc.get_normal();
            } else {
                return nc.get_error();
            }
        }

        // 成交持仓比
        if 6 == column_index {
            let app_mto = app.global::<OptTrd_MonTrdPosOrdInsertCancel>();
            let is_tp_abnormal = app_mto.invoke_get_cell_data(row_index, 7);

            if "否" == is_tp_abnormal.as_str() {
                if let Ok(ratio) = data.parse::<f64>() {
                    let mon_ratio = { CFG.get().unwrap().read().unwrap().opt_mon.TrdPosMonitor.Ratio };
                    if ratio >= mon_ratio {
                        return nc.get_error();
                    } else if ratio >= mon_ratio - 0.1 {
                        return nc.get_caution();
                    } else if ratio >= mon_ratio - 0.5 {
                        return nc.get_prompt();
                    } else {
                        return nc.get_normal();
                    }
                }
            } else {
                return nc.get_error();
            }
        }

        // 大于?万报单笔数, 大于?万撤单笔数
        if 8 == column_index || 9 == column_index {
            if let Ok(vol) = data.parse::<f64>() {
                if 0. == vol {
                    return nc.get_normal();
                } else {
                    return nc.get_caution();
                }
            }
        }

        // 撤单比率
        if 10 == column_index {
            if let Ok(vol) = data[..data.len() - 1].parse::<f64>() {
                if 0. == vol {
                    return nc.get_normal();
                } else {
                    return nc.get_caution();
                }
            }
        }

        nc.get_default()
    }

    /// 过虑条件有变动
    fn on_filter_changed(app_weak: &Weak<App>) {
        MON_TRDPOS_OIC_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

// 更新UI
impl MonTrdPosOrdIC {
    /// 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let mlp_datas = MON_TRDPOS_OIC_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mti = app.global::<OptTrd_MonTrdPosOrdInsertCancel>();

            if mlp_datas.clientid_set.len() != app_mti.invoke_clientid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mlp_datas.clientid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mti.set_clientid_model(id_arr.into());
            }

            if mlp_datas.productid_set.len() != app_mti.invoke_productid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mlp_datas.productid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mti.set_productid_model(id_arr.into());
            }
        });
    }

    /// 更新监控数据
    async fn update_limitpos(&self, app_weak: &Weak<App>) {
        let mla_datas = MON_TRDPOS_OIC_DATAS.get().unwrap();

        let data_map_clone = {
            if mla_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                mla_datas.filter_changed.store(false, Ordering::Relaxed);
                mla_datas.data_map.clone()
            } else {
                if mla_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    mla_datas.data_map.clone()
                } else {
                    Arc::new(TrdPosOrdICMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mto = app.global::<OptTrd_MonTrdPosOrdInsertCancel>();

            let filter_exchid = app_mto.get_exchid().to_string();
            let filter_cliid = app_mto.get_clientid().to_string();
            let filter_proid = app_mto.get_productid().to_string();

            let mon_trdpos = { CFG.get().unwrap().read().unwrap().opt_mon.TrdPosMonitor.clone() };
            let mon_oic = { CFG.get().unwrap().read().unwrap().opt_mon.OrdInsertCancel.clone() };
            app_mto.set_greaterthannum(TIConvert::format_f(mon_oic.GreaterThanNum as f64 / 10000.0, 1).into());

            // 填充数据
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|to| {
                let exchname = TIConvert::exch_name(to.ExchID);

                if !filter_exchid.is_empty() {
                    if exchname != filter_exchid {
                        return;
                    }
                }

                if !filter_cliid.is_empty() {
                    if !to.ClientID.contains(&filter_cliid) {
                        return;
                    }
                }

                if !filter_proid.is_empty() {
                    if !to.ProductID.contains(&filter_proid) {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 成交持仓比率
                let tp_ratio = if 0 == to.Position && to.TradeVolume < mon_trdpos.TradeVolume {
                    0.
                } else {
                    to.Ratio
                };

                // 成交持仓是否异常
                let is_tp_abnormal = if to.TradeVolume >= mon_trdpos.TradeVolume && tp_ratio >= mon_trdpos.Ratio {
                    true
                } else {
                    false
                };

                // 报单大于?万的数量
                let InsertNumEx = if to.InsertNum > mon_oic.GreaterThanNum {
                    to.InsertNum - mon_oic.GreaterThanNum
                } else {
                    0
                };

                // 撤单大于?万的数量
                let CancelNumEx = if to.CancelNum > mon_oic.GreaterThanNum {
                    to.CancelNum - mon_oic.GreaterThanNum
                } else {
                    0
                };

                // 报撤单比率
                let oic_ratio = if InsertNumEx > 0 {
                    mon_oic.CancelRatio * CancelNumEx as f64 / InsertNumEx as f64
                } else {
                    0.
                };

                // 用于排序的比率
                let sort_ratio = if is_tp_abnormal {
                    10000. + 1000. + tp_ratio + oic_ratio
                } else {
                    10000. + 1000. + tp_ratio + oic_ratio
                };

                // 资金账户
                items.push(to.AccountID.clone().into());

                // 交易所
                items.push(exchname.into());

                // 交易编码
                items.push(to.ClientID.clone().into());

                // 品种编码
                items.push(to.ProductID.clone().into());

                // 成交量
                items.push(TIConvert::format_i(to.TradeVolume).into());

                // 持仓量
                items.push(TIConvert::format_i(to.Position).into());

                // 成交持仓比
                items.push(slint::format!("{:.*}", 2, tp_ratio));

                // 成交持仓比异常
                items.push(slint::format!("{}", if is_tp_abnormal { "是" } else { "否" }));

                // 大于?万报单笔数
                items.push(TIConvert::format_i(InsertNumEx).into());

                // 大于?万撤单笔数
                items.push(TIConvert::format_i(CancelNumEx).into());

                // 撤单比率
                items.push(slint::format!("{:.*}%", 2, oic_ratio * 100.0));

                // 总报单笔数
                items.push(TIConvert::format_i(to.InsertNum).into());

                // 总撤单笔数
                items.push(TIConvert::format_i(to.CancelNum).into());

                // 发生时间
                items.push(TIConvert::transtime(to.TransTime).into());

                // sort_col
                items.push(sort_ratio.to_string().into());

                row_data.push(items.into());
            });

            // 根据 sort_col 按降序排序
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(14).unwrap();
                let c_b = r_b.row_data(14).unwrap();
                c_b.cmp(&c_a)
            }));
            app_mto.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {

        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新限额
        self.update_limitpos(&app_weak).await;
    }
}
