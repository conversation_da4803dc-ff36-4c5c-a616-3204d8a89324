use num_enum::{IntoPrimitive, TryFromPrimitive};
use serde::{Deserialize, Serialize};

/// 协议类型
#[derive(IntoPrimitive, TryFromPrimitive)]
#[repr(i32)]
pub(crate) enum EMessageType {
    // Begin Http请求协议
    TIMT_Req_Min = 9999,

    // 风控协议
    TIMT_Risk_Req_Min = 10000,

    TIMT_Risk_Req_Login,          // 请求登录
    TIMT_Risk_Req_Logout,         // 请求登出
    TIMT_Risk_Req_Qry_Instrument, // 请求查询合约
    TIMT_Risk_Req_Qry_Order,      // 请求查询委托
    TIMT_Risk_Req_Qry_Trade,      // 请求查询成交
    TIMT_Risk_Req_Qry_Exercise,   // 请求查询行权
    TIMT_Risk_Req_Qry_Oml,        // 请求查询组合通知
    TIMT_Risk_Req_Qry_Deposit,    // 请求查询出入金

    TIMT_Risk_Req_LoginTrade,             // 请求登录交易端（用于交易）
    TIMT_Risk_Req_InputOrder,             // 报单录入请求
    TIMT_Risk_Req_InputOrderAction,       // 报单操作请求
    TIMT_Risk_Req_CombActionInsert,       // 申请组合录入请求
    TIMT_Risk_Req_ExecOrderInsert,        // 执行宣告录入请求
    TIMT_Risk_Req_ExecOrderAction,        // 执行宣告操作请求
    TIMT_Risk_Req_ExecCombineOrderInsert, // 行权指令合并录入请求

    TIMT_Risk_Req_Max,

    // 交易协议:CTP
    TIMT_Ctp_Req_Min = 11000,

    TIMT_Ctp_Req_Auth,                            // 客户端认证请求
    TIMT_Ctp_Req_Login,                           // 用户登录请求
    TIMT_Ctp_Req_Logout,                          // 登出请求
    TIMT_Ctp_Req_InputOrder,                      // 报单录入请求
    TIMT_Ctp_Req_InputOrderAction,                // 报单操作请求
    TIMT_Ctp_Req_ExecOrderInsert,                 // 执行宣告录入请求
    TIMT_Ctp_Req_ExecOrderAction,                 // 执行宣告操作请求
    TIMT_Ctp_Req_ForQuoteInsert,                  // 询价录入请求
    TIMT_Ctp_Req_QuoteInsert,                     // 报价录入请求
    TIMT_Ctp_Req_QuoteAction,                     // 报价操作请求
    TIMT_Ctp_Req_LockInsert,                      // 锁定请求
    TIMT_Ctp_Req_CombActionInsert,                // 申请组合录入请求
    TIMT_Ctp_Req_BatchOrderAction,                // 批量报单操作请求
    TIMT_Ctp_Req_ParkedOrderInsert,               // 预埋单录入请求
    TIMT_Ctp_Req_ParkedOrderAction,               // 预埋撤单录入请求
    TIMT_Ctp_Req_RemoveParkedOrder,               // 请求删除预埋单
    TIMT_Ctp_Req_RemoveParkedOrderAction,         // 请求删除预埋撤单
    TIMT_Ctp_Req_RegisterUserSystemInfo,          // 注册用户终端信息，用于中继服务器多连接模式
    TIMT_Ctp_Req_SubmitUserSystemInfo,            // 上报用户终端信息，用于中继服务器操作员登录模式
    TIMT_Ctp_Req_UserPasswordUpdate,              // 用户口令更新请求
    TIMT_Ctp_Req_TradingAccountPasswordUpdate,    // 资金账户口令更新请求
    TIMT_Ctp_Req_SettlementInfoConfirm,           // 投资者结算结果确认
    TIMT_Ctp_Req_FromBankToFutureByFuture,        // 期货发起银行资金转期货请求
    TIMT_Ctp_Req_FromFutureToBankByFuture,        // 期货发起期货资金转银行请求
    TIMT_Ctp_Req_ReserveOpenAccountTpdByFuture,   // 期货发起第三方存管业务开户预指定请求
    TIMT_Ctp_Req_SecuritiDepositInterestByFuture, // 期货发起第三方存管证券资金结息请求
    TIMT_Ctp_Req_DayEndFileReadyByFuture,         // 期货发起日终文件就绪请求
    TIMT_Ctp_Req_ExecCombineOrderInsert,          // 行权指令合并录入请求
    TIMT_Ctp_Req_ExecCombineOrderAction,          // 行权指令合并操作请求
    TIMT_Ctp_Req_InternalTransfer,                // 请求资金内转

    TIMT_Ctp_Req_Max,

    TIMT_Ctp_Req_Qry_Min = 11300,

    TIMT_Ctp_Req_QryOrder,                         // 请求查询委托
    TIMT_Ctp_Req_QryTrade,                         // 请求查询成交
    TIMT_Ctp_Req_QryInvestorPosition,              // 请求查询投资者持仓
    TIMT_Ctp_Req_QryTradingAccount,                // 请求查询资金账户
    TIMT_Ctp_Req_QryInvestor,                      // 请求查询投资者
    TIMT_Ctp_Req_QryTradingCode,                   // 请求查询交易编码
    TIMT_Ctp_Req_QryInstrumentMarginRate,          // 请求查询合约保证金率
    TIMT_Ctp_Req_QryInstrumentCommissionRate,      // 请求查询合约手续费率
    TIMT_Ctp_Req_QryExchange,                      // 请求查询交易所
    TIMT_Ctp_Req_QryProduct,                       // 请求查询产品
    TIMT_Ctp_Req_QryInstrument,                    // 请求查询合约
    TIMT_Ctp_Req_QryDepthMarketData,               // 请求查询行情
    TIMT_Ctp_Req_QrySettlementInfo,                // 请求查询投资者结算结果
    TIMT_Ctp_Req_QryTransferBank,                  // 请求查询转帐银行
    TIMT_Ctp_Req_QryInvestorPositionDetail,        // 请求查询投资者持仓明细
    TIMT_Ctp_Req_QryNotice,                        // 查询客户通知
    TIMT_Ctp_Req_QrySettlementInfoConfirm,         // 请求查询结算信息确认
    TIMT_Ctp_Req_QryInvestorPositionCombineDetail, // 请求查询投资者组合持仓明细
    TIMT_Ctp_Req_QryCFMMCTradingAccountKey,        // 请求查询保证金监管系统经纪公司资金账户密钥
    TIMT_Ctp_Req_QryEWarrantOffset,                // 请求查询仓单折抵信息
    TIMT_Ctp_Req_QryInvestorProductGroupMargin,    // 请求查询投资者品种/跨品种保证金
    TIMT_Ctp_Req_QryExchangeMarginRate,            // 请求查询交易所保证金率
    TIMT_Ctp_Req_QryExchangeMarginRateAdjust,      // 请求查询交易所调整保证金率
    TIMT_Ctp_Req_QryExchangeRate,                  // 请求查询汇率
    TIMT_Ctp_Req_QrySecAgentACIDMap,               // 请求查询二级代理操作员银期权限
    TIMT_Ctp_Req_QryProductExchRate,               // 请求查询产品报价汇率
    TIMT_Ctp_Req_QryProductGroup,                  // 请求查询产品组
    TIMT_Ctp_Req_QryMMInstrumentCommissionRate,    // 请求查询做市商合约手续费率
    TIMT_Ctp_Req_QryMMOptionInstrCommRate,         // 请求查询做市商期权合约手续费
    TIMT_Ctp_Req_QryInstrumentOrderCommRate,       // 请求查询报单手续费
    TIMT_Ctp_Req_QryOptionInstrTradeCost,          // 请求查询期权交易成本
    TIMT_Ctp_Req_QryOptionInstrCommRate,           // 请求查询期权合约手续费
    TIMT_Ctp_Req_QryExecOrder,                     // 请求查询执行宣告
    TIMT_Ctp_Req_QryForQuote,                      // 请求查询询价
    TIMT_Ctp_Req_QryQuote,                         // 请求查询报价
    TIMT_Ctp_Req_QryLock,                          // 请求查询锁定
    TIMT_Ctp_Req_QryLockPosition,                  // 请求查询锁定证券仓位
    TIMT_Ctp_Req_QryETFOptionInstrCommRate,        // 请求查询ETF期权合约手续费
    TIMT_Ctp_Req_QryLimitPosi,                     // 请求查询持仓限制
    TIMT_Ctp_Req_QryInvestorLevel,                 // 请求查询投资者分级
    TIMT_Ctp_Req_QryExecFreeze,                    // 请求查询E+1日行权冻结
    TIMT_Ctp_Req_QryLimitAmount,                   // 请求查询金额限制
    TIMT_Ctp_Req_QryCombInstrumentGuard,           // 请求查询组合合约安全系数
    TIMT_Ctp_Req_QryCombAction,                    // 请求查询申请组合
    TIMT_Ctp_Req_QryTransferSerial,                // 请求查询转帐流水
    TIMT_Ctp_Req_QryAccountregister,               // 请求查询银期签约关系
    TIMT_Ctp_Req_QryContractBank,                  // 请求查询签约银行
    TIMT_Ctp_Req_QryParkedOrder,                   // 请求查询预埋单
    TIMT_Ctp_Req_QryParkedOrderAction,             // 请求查询预埋撤单
    TIMT_Ctp_Req_QryTradingNotice,                 // 请求查询交易通知
    TIMT_Ctp_Req_QryBrokerTradingParams,           // 请求查询经纪公司交易参数
    TIMT_Ctp_Req_QryBrokerTradingAlgos,            // 请求查询经纪公司交易算法
    TIMT_Ctp_Req_QryExecCombineOrder,              // 请求查询行权指令合并
    TIMT_Ctp_Req_QryInternalTransfer,              // 请求查询资金记录
    TIMT_Ctp_Req_QryFutureTradingAccount,          // 请求查询对应的期货资金账户
    TIMT_Ctp_Req_QueryMaxOrderVolume,              // 查询最大报单数量请求
    TIMT_Ctp_Req_QueryCFMMCTradingAccountToken,    // 请求查询监控中心用户令牌
    TIMT_Ctp_Req_QueryBankAccountMoneyByFuture,    // 期货发起查询银行余额请求

    TIMT_Ctp_Req_Qry_Max,

    // Ctp扩展协议
    TIMT_Ctp_EX_Req_Min = 11800,

    TIMT_Ctp_EX_Req_Risk_Login,        // 风控用户登录请求
    TIMT_Ctp_EX_Req_Risk_Subscribe,    // 风控用户订阅请求
    TIMT_Ctp_EX_Req_Risk_UnSubscribe,  // 风控用户退订阅请求
    TIMT_Ctp_Ex_Req_QryTradingAccount, // 请求查询资金账户(分交易所)
    TIMT_Ctp_Ex_Req_BusinessInsert,    // 股票非交易业务报单
    TIMT_Ctp_Ex_Req_BusinessAction,    // 股票非交易业务报单操作
    TIMT_Ctp_EX_Req_QryFJYInstrument,  // 请求查询非交易业务合约

    TIMT_Ctp_Ex_Req_FundTransfer,    // 资金划转
    TIMT_Ctp_Ex_Req_AFundTransfer,   // 对失败的资金划转再次请求划转
    TIMT_Ctp_Ex_Req_QryFundTransfer, // 资金划转查询

    TIMT_Ctp_Ex_Req_PositionTransfer,    // 持仓划拨
    TIMT_Ctp_Ex_Req_APositionTransfer,   // 对失败的持仓划拨再次请求划拨
    TIMT_Ctp_Ex_Req_QryPositionTransfer, // 持仓划拨查询

    TIMT_Ctp_Ex_Req_StockLock, // 证券锁定请求

    TIMT_Ctp_Ex_Req_ETFSS,      // ETF申赎
    TIMT_Ctp_Ex_Req_QryETFSS,   // 查询ETF申赎记录
    TIMT_Ctp_Ex_Req_QryETFInfo, // 查询ETF证券信息

    TIMT_Ctp_EX_Req_Max,

    // 风控协议(现货)
    TIMT_Risk_Stk_Req_Min = 12000,

    // 登录与登出使用 风控协议 中的登录登出
    TIMT_Risk_Stk_Req_Qry_Instrument, // 请求查询合约
    TIMT_Risk_Stk_Req_Qry_Order,      // 请求查询委托
    TIMT_Risk_Stk_Req_Qry_Trade,      // 请求查询成交
    TIMT_Risk_Stk_Req_Qry_Deposit,    // 请求查询出入金

    TIMT_Risk_Stk_Req_LoginTrade,       // 请求登录交易端（用于交易）
    TIMT_Risk_Stk_Req_InputOrder,       // 报单录入请求
    TIMT_Risk_Stk_Req_InputOrderAction, // 报单操作请求

    TIMT_Risk_Stk_Req_Qry_BusinessOrder, // 请求查询非交易业务委托
    TIMT_Risk_Stk_Req_Qry_BusinessTrade, // 请求查询非交易业务成交
    TIMT_Risk_Stk_Req_Qry_FJYInstrument, // 请求查询非交易业务合约

    TIMT_Risk_Stk_Req_Max,

    // 公共协议
    TIMT_Com_Req_Min = 13000,

    TIMT_Com_Req_Qry_ServerType, // 请求查询服务端类型
    TIMT_Com_Req_Qry_VerCode,    // 请求查询动态认证码
    TIMT_Com_Req_Max,

    TIMT_Req_Max,

    // End Http请求协议

    // Begin Websoket推送协议

    // 公共消息
    TIMT_WS_Com_Min = 60000,

    TIMT_WS_Com_Rtn_App,   // 系统级消息(比如心跳,交易端数据异常等)
    TIMT_WS_Com_Req_Login, // WS端请求登录
    TIMT_WS_Com_Rsp_Login, // WS端请求登录响应

    TIMT_WS_Com_Max,

    // 风控协议
    TIMT_WS_Risk_Min = 61000,

    TIMT_WS_Risk_Rtn_Account,           // 资金
    TIMT_WS_Risk_Rtn_Position,          // 持仓
    TIMT_WS_Risk_Rtn_PositionComb,      // 持仓组合
    TIMT_WS_Risk_Rtn_TradeSelf,         // 自成交
    TIMT_WS_Risk_Rtn_TradeSelfDtl,      // 自成交详情(即组成自成交的成交消息)
    TIMT_WS_Risk_Rtn_TradePosition,     // 成交持仓比例
    TIMT_WS_Risk_Rtn_PositionLimit,     // 限仓
    TIMT_WS_Risk_Rtn_OrderInsertCancel, // 报撤单
    TIMT_WS_Risk_Rtn_WithdrawDeposit,   // 出入金
    TIMT_WS_Risk_Rtn_Order,             // 委托
    TIMT_WS_Risk_Rtn_Trade,             // 成交
    TIMT_WS_Risk_Rtn_Oml,               // 期权组合详情
    TIMT_WS_Risk_Rtn_Exercise,          // 行权
    TIMT_WS_Risk_Rtn_Greeks,            // 希腊字母
    TIMT_WS_Risk_Rtn_MarketData,        // 行情

    TIMT_WS_Risk_Rsp_InputOrder,             // 报单录入响应
    TIMT_WS_Risk_Rsp_InputOrderAction,       // 报单操作响应
    TIMT_WS_Risk_Rsp_CombActionInsert,       // 申请组合录入响应
    TIMT_WS_Risk_Rsp_ExecOrderInsert,        // 执行宣告录入响应
    TIMT_WS_Risk_Rsp_ExecOrderAction,        // 执行宣告操作响应
    TIMT_WS_Risk_Rsp_ExecCombineOrderInsert, // 行权指令合并录入响应

    TIMT_WS_Risk_Rtn_OpenLimit, // 限开仓

    TIMT_WS_Risk_Max,

    // 交易协议:CTP
    TIMT_WS_Ctp_rsp_Min = 62000,

    // 请求响应
    TIMT_WS_Ctp_Rsp_InputOrder,             // 报单响应
    TIMT_WS_Ctp_Rsp_InputOrderAction,       // 报单操作响应
    TIMT_WS_Ctp_Rsp_ExecOrderInsert,        // 行权申请响应
    TIMT_WS_Ctp_Rsp_ExecOrderAction,        // 行权操作响应
    TIMT_WS_Ctp_Rsp_ForQuoteInsert,         // 询价
    TIMT_WS_Ctp_Rsp_QuoteInsert,            // 报价
    TIMT_WS_Ctp_Rsp_QuoteAction,            // 报价撤销
    TIMT_WS_Ctp_Rsp_LockInsert,             // 申请锁仓
    TIMT_WS_Ctp_Rsp_CombActionInsert,       // 申请组合
    TIMT_WS_Ctp_Rsp_ExecCombineOrderInsert, // 行权指令合并录入
    TIMT_WS_Ctp_Rsp_ExecCombineOrderAction, // 行权指令合并操作

    TIMT_WS_Ctp_rsp_Max,

    TIMT_WS_Ctp_Rsp_Qry_Min = 62300,

    // 查询响应
    TIMT_WS_Ctp_Rsp_QryOrder,                    // 查询委托
    TIMT_WS_Ctp_Rsp_QryTrade,                    // 查询成交
    TIMT_WS_Ctp_Rsp_QryInvestorPosition,         // 查询投资者持仓
    TIMT_WS_Ctp_Rsp_QryTradingAccount,           // 查询资金账户
    TIMT_WS_Ctp_Rsp_QryInvestor,                 // 查询投资者
    TIMT_WS_Ctp_Rsp_QryTradingCode,              // 查询交易编码
    TIMT_WS_Ctp_Rsp_QryInstrumentMarginRate,     // 查询合约保证金率
    TIMT_WS_Ctp_Rsp_QryInstrumentCommissionRate, // 查询合约手续费率
    TIMT_WS_Ctp_Rsp_QryExchange,                 // 查询交易所
    TIMT_WS_Ctp_Rsp_QryProduct,                  // 查询产品
    TIMT_WS_Ctp_Rsp_QryInstrument,               // 查询合约
    TIMT_WS_Ctp_Rsp_QryDepthMarketData,          // 查询行情
    TIMT_WS_Ctp_Rsp_QryPosiDetail,               // 查询持仓详情
    TIMT_WS_Ctp_Rsp_QryPosiCombineDetail,        // 查询组合持仓详情
    TIMT_WS_Ctp_Rsp_QryLimitPosi,                // 查询持仓限制
    TIMT_WS_Ctp_Rsp_QryLimitAmount,              // 查询金额限制
    TIMT_WS_CTP_RSP_QryBrokerTradingParams,      // 查询经纪公司交易参数
    TIMT_WS_CTP_RSP_QryETFOptionInstrCommRate,   // 查询ETF期权合约手续费
    TIMT_WS_CTP_RSP_QryCombAction,               // 查询申请组合
    TIMT_WS_Ctp_Rsp_QryQuote,                    // 查询报价

    TIMT_WS_Ctp_Rsp_Qry_Max,

    TIMT_WS_Ctp_Rtn_Min = 63000,

    // 推送 - 连接
    TIMT_WS_Ctp_Rtn_Connect,    // 连接(API端检测)
    TIMT_WS_Ctp_Rtn_Disconnect, // 断开连接(API端检测)
    TIMT_WS_Ctp_Rtn_Heartbeat,  // 心跳(API端检测)

    // 推送 - 报单
    TIMT_WS_Ctp_Rtn_Order,           // 报单推送
    TIMT_WS_Ctp_Rtn_Err_OrderInsert, // 报单录入错误推送
    TIMT_WS_Ctp_Rtn_Err_OrderAction, // 报单操作错误推送

    // 推送 - 成交
    TIMT_WS_Ctp_Rtn_Trade, // 成交推送

    // 推送 - 行权
    TIMT_WS_Ctp_Rtn_ExecOrder,                  // 行权推送
    TIMT_WS_Ctp_Rtn_Err_ExecOrderInsert,        // 行权申请错误推送
    TIMT_WS_Ctp_Rtn_Err_ExecOrderAction,        // 行权操作错误推送
    TIMT_WS_Ctp_Rtn_ExecCombineOrder,           // 行权指令合并推送
    TIMT_WS_Ctp_Rtn_Err_ExecCombineOrderInsert, // 行权指令合并录入错误推送
    TIMT_WS_Ctp_Rtn_Err_ExecCombineOrderAction, // 行权指令合并操作错误推送

    // 推送 - 组合
    TIMT_WS_Ctp_Rtn_CombAction,           // 组合推送
    TIMT_WS_Ctp_Rtn_Err_CombActionInsert, // 申请组合错误推送

    // 推送 - 报价
    TIMT_WS_Ctp_Rtn_Quote,           // 报价推送
    TIMT_WS_Ctp_Rtn_Err_QuoteInsert, // 报价录入错误推送
    TIMT_WS_Ctp_Rtn_Err_QuoteAction, // 报价操作错误推送

    TIMT_WS_Ctp_Rtn_Max,

    TIMT_WS_Ctp_Ex_Min = 63070,

    TIMT_WS_Ctp_Ex_Rsp_QryTradingAccount,   // 查询资金账户(分交易所)
    TIMT_WS_Ctp_Ex_Rtn_LimitAmount,         // 限额推送
    TIMT_WS_Ctp_Ex_Rtn_LimitPosi,           // 限仓推送
    TIMT_WS_Ctp_Ex_Rsp_BusinessInsert,      // 非交易业务报单响应
    TIMT_WS_Ctp_Ex_Rsp_BusinessAction,      // 非交易业务报单操作响应
    TIMT_WS_Ctp_Ex_Rtn_BusinessOrder,       // 非交易业务委托回报
    TIMT_WS_Ctp_Ex_Rtn_BusinessTrade,       // 非交易业务成交回报
    TIMT_WS_Ctp_Ex_Rtn_TradePosition,       // 成交持仓比例
    TIMT_WS_Ctp_EX_Rsp_QryFJYInstrument,    // 查询非交易业务合约响应
    TIMT_WS_Ctp_Ex_Rtn_TradeSelf,           // 自成交
    TIMT_WS_Ctp_Ex_Rtn_TradeSelfDtl,        // 自成交详情
    TIMT_WS_Ctp_Ex_Rtn_OrderInsertCancel,   // 报撤单
    TIMT_WS_Ctp_Ex_Rtn_ConvertBondsMon,     // 可转债监控信息
    TIMT_WS_Ctp_Ex_Rtn_LimitOpen,           // 限开仓推送
    TIMT_WS_Ctp_Ex_Rtn_TradeMon,            // 成交监控信息
    TIMT_WS_Ctp_Ex_Rtn_UpdateAccount,       // 资金更新(资金划转,出入金)信息
    TIMT_WS_Ctp_Ex_Rtn_UpdatePosition,      // 持仓更新(持仓划拨)信息
    TIMT_WS_Ctp_Ex_Rtn_CreditContract,      // 信用交易 - 合约
    TIMT_WS_Ctp_Ex_Rsp_StockLock,           // 证券锁定响应
    TIMT_WS_Ctp_Ex_Rtn_CreditLimit,         // 信用交易 - 授信额度
    TIMT_WS_Ctp_Ex_Rtn_CreditTcAmt,         // 信用交易 - 资金头寸
    TIMT_WS_Ctp_Ex_Rtn_CreditTcPos,         // 信用交易 - 股份头寸
    TIMT_WS_Ctp_Ex_Rtn_CreditConcentration, // 信用交易 - 集中度
    TIMT_WS_Ctp_Ex_Rtn_CreditReturnDtl,     // 信用交易 - 合约归还明细

    TIMT_WS_Ctp_Ex_Max,

    TIMT_WS_Ctp_Max,

    // 风控协议(现货)
    TIMT_WS_Risk_Stk_Min = 64000,

    TIMT_WS_Risk_Stk_Rtn_Account,              // 资金
    TIMT_WS_Risk_Stk_Rtn_Position,             // 持仓
    TIMT_WS_Risk_Stk_Rtn_TradeSelf,            // 自成交
    TIMT_WS_Risk_Stk_Rtn_TradeSelfDtl,         // 自成交详情(即组成自成交的成交消息)
    TIMT_WS_Risk_Stk_Rtn_TradePosition,        // 成交持仓比例
    TIMT_WS_Risk_Stk_Rtn_OrderInsertCancel,    // 报撤单
    TIMT_WS_Risk_Stk_Rtn_WithdrawDeposit,      // 出入金
    TIMT_WS_Risk_Stk_Rtn_Order,                // 委托
    TIMT_WS_Risk_Stk_Rtn_Trade,                // 成交
    TIMT_WS_Risk_Stk_Rtn_MarketData,           // 行情
    TIMT_WS_Risk_Stk_Rsp_InputOrder,           // 报单录入响应
    TIMT_WS_Risk_Stk_Rsp_InputOrderAction,     // 报单操作响应
    TIMT_WS_Risk_Stk_Rtn_BusinessOrder,        // 非交易业务委托
    TIMT_WS_Risk_Stk_Rtn_BusinessTrade,        // 非交易业务成交
    TIMT_WS_Risk_Stk_Rtn_ConvertBondsMon,      // 可转债监控信息
    TIMT_WS_Risk_Stk_Rtn_TradeMon,             // 成交监控信息
    TIMT_WS_Risk_Stk_Rtn_PositionTrans,        // 股份划拨
    TIMT_WS_Risk_Stk_Rtn_Credit_Contract,      // 信用交易 - 合约
    TIMT_WS_Risk_Stk_Rtn_Credit_Limit,         // 信用交易 - 授信额度
    TIMT_WS_Risk_Stk_Rtn_Credit_TcAmt,         // 信用交易 - 资金头寸
    TIMT_WS_Risk_Stk_Rtn_Credit_TcPos,         // 信用交易 - 股份头寸
    TIMT_WS_Risk_Stk_Rtn_Credit_Concentration, // 信用交易 - 集中度
    TIMT_WS_Risk_Stk_Rtn_Credit_ReturnDtl,     // 信用交易 - 合约归还明细

    TIMT_WS_Risk_Stk_Max,
    // End Websoket推送协议
}

/// 请求基类
#[derive(Default, Serialize)]
pub(crate) struct PTReqBase {
    /// RequestID
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub reqid: i32,

    /// UserID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub uid: String,

    /// SessionID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub sid: String,
}

/// 请求查询基类
#[derive(Serialize)]
pub(crate) struct PTReqQryBase {
    /// RequestID
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub reqid: i32,

    /// UserID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub uid: String,

    /// SessionID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub sid: String,

    /// 请求的页数索引(从1开始)
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub pageindex: i32,

    /// 每页大小
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub pagesize: i32,
}
impl Default for PTReqQryBase {
    fn default() -> Self {
        Self {
            pageindex: 1,
            pagesize: 50,
            reqid: 0,
            uid: String::new(),
            sid: String::new(),
        }
    }
}

// 将响应推送所有可能的字段头放入一个头中
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub(crate) struct PTMsgBase {
    /// 消息类型(message type)
    pub mt: i32,

    // PTRspBase
    /// Is Last
    pub lt: i32,

    /// Total count
    pub tc: i32,

    /// Request ID
    pub reqid: i32,

    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,

    // PTRspQryBase
    /// 满足条件的条数
    pub itemcount: i32,

    /// 当前页数的索引(从1开始)
    pub pageindex: i32,

    /// 满足条件的总页数
    pub pagetatal: i32,
}
