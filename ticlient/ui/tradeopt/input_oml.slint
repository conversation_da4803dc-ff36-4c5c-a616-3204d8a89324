import { Label, LineEdit, <PERSON><PERSON>, ComboBox, <PERSON>, Radius, <PERSON><PERSON><PERSON>, SpinBox } from "@zstk/lib.slint";
import { AppCom, TradeColor } from "../struct.slint";
import { LineEditEx } from "../widgets/lineeditex.slint";
import { OptTrd_Com, OptTrd_InputOml } from "struct.slint";

export component InputOmlPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <bool> show_tips;

    VerticalLayout {
        spacing: 2px;

        Rectangle {
            HorizontalLayout {
                spacing: 5px;

                Rectangle {
                    background: transparent;
                    width: 355px;

                    GridLayout {
                        spacing-vertical: 6px;
                        spacing-horizontal: 5px;
                        padding: 5px;

                        // 资金账户
                        Row {
                            Label {
                                width: 60px;
                                text: "资金账户";
                                vertical-alignment: center;
                            }

                            Label {
                                text: OptTrd_Com.accountid;
                                vertical-alignment: center;
                            }
                        }

                        // 组合类型
                        Row {
                            Label {
                                text: "组合类型";
                                vertical-alignment: center;
                            }

                            Rectangle {
                                clip: true;
                                height: 26px;
                                border_color: OptTrd_InputOml.get_comb_color();
                                border_width: 1px;
                                border_radius: Radius.extra_small;

                                HorizontalLayout {
                                    Rectangle {
                                        background: OptTrd_InputOml.is_comb ? TradeColor.dir_buy : transparent;

                                        Label {
                                            text: "组合";
                                            vertical_alignment: center;
                                            horizontal_alignment: center;
                                        }

                                        TouchArea {
                                            mouse_cursor: pointer;
                                            clicked => {
                                                OptTrd_InputOml.is_comb = true;
                                                OptTrd_InputOml.combtradeid = "";
                                            }
                                        }
                                    }

                                    Rectangle {
                                        background: OptTrd_InputOml.is_comb ? transparent : TradeColor.dir_sell;

                                        Label {
                                            text: "解除";
                                            vertical_alignment: center;
                                            horizontal_alignment: center;
                                        }

                                        TouchArea {
                                            mouse_cursor: pointer;
                                            clicked => {
                                                OptTrd_InputOml.is_comb = false;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        // 组合编码
                        Row {
                            Label {
                                text: "组合编码";
                                vertical-alignment: center;
                            }

                            LineEdit {
                                placeholder_text: "解除时有效, 双击组合持仓自动填充";
                                enabled: false;
                                text <=> OptTrd_InputOml.combtradeid;
                            }
                        }

                        // 组合策略
                        Row {
                            Label {
                                text: "组合策略";
                                vertical-alignment: center;
                            }

                            ComboBox {
                                placeholder_text: "请选择";
                                current_text <=> OptTrd_InputOml.strategyid;
                                current_index <=> OptTrd_InputOml.strategyid_idx;
                                model: OptTrd_InputOml.strategyid_model;
                                max_popup_height: 160px;

                                selected => {
                                    if 0 == self.current_index ||
                                       1 == self.current_index ||
                                       2 == self.current_index ||
                                       3 == self.current_index {
                                        OptTrd_InputOml.leg1_dir = "权利";
                                        OptTrd_InputOml.leg2_dir = "义务";
                                        OptTrd_InputOml.leg1_dir_i32 = 50;
                                        OptTrd_InputOml.leg2_dir_i32 = 51;
                                    } else  {
                                        OptTrd_InputOml.leg1_dir = "义务";
                                        OptTrd_InputOml.leg2_dir = "义务";
                                        OptTrd_InputOml.leg1_dir_i32 = 51;
                                        OptTrd_InputOml.leg2_dir_i32 = 51;
                                    }

                                    OptTrd_InputOml.clear_leg1_info();
                                    OptTrd_InputOml.clear_leg2_info();
                                    OptTrd_InputOml.strategyid_changed();
                                }
                            }

                            Label {
                                width: 15px;
                                text: "...";

                                vertical-alignment: center;

                                i_ta_scan_strategyid := TouchArea {
                                    mouse_cursor: pointer;
                                    clicked => {
                                        OptTrd_InputOml.show_oml_preview = true;
                                    }
                                }
                            }
                        }

                        // 第1腿合约
                        Row {

                            Label {
                                text: "第1腿合约";
                                vertical-alignment: center;
                            }

                            VerticalLayout {
                                spacing: 2px;

                                LineEditEx {
                                    max_popup_height: 160px;
                                    model <=> OptTrd_InputOml.leg1_insid_model;
                                    has_error <=> OptTrd_InputOml.leg1_has_error;
                                    current_text <=> OptTrd_InputOml.leg1_insid;
                                    placeholder_text: "请输入";

                                    edited => {
                                        OptTrd_InputOml.leg1_insid_changed();
                                    }
                                }

                                if !OptTrd_InputOml.leg1_has_error || "" != OptTrd_InputOml.leg1_insname : HorizontalLayout {
                                    Label {
                                        font_size: 0.9rem;
                                        color: OptTrd_InputOml.leg1_has_error ? Theme.border_error : Theme.control_foreground;
                                        text <=> OptTrd_InputOml.leg1_insname;
                                    }

                                    Rectangle {
                                    }

                                    Label {
                                        font_size: 0.9rem;
                                        text <=> OptTrd_InputOml.leg1_vol;
                                        color: OptTrd_InputOml.leg1_vol.to_float() > 0 ? Theme.accent_background : Theme.foreground;

                                        TouchArea {
                                            mouse_cursor: pointer;
                                            clicked => {
                                                if OptTrd_InputOml.leg1_vol.to_float() > 0 {
                                                    OptTrd_InputOml.volume = OptTrd_InputOml.leg1_vol;
                                                }
                                            }
                                        }
                                    }

                                    Rectangle {
                                        width: 5px;
                                    }
                                }
                            }

                            Label {
                                font_size: 0.95rem;
                                color: 50 == OptTrd_InputOml.leg1_dir_i32 ? TradeColor.dir_buy : TradeColor.dir_sell;
                                text <=> OptTrd_InputOml.leg1_dir;
                                vertical-alignment: center;
                            }
                        }

                        // 第2腿合约
                        Row {

                            Label {
                                text: "第2腿合约";
                                vertical-alignment: center;
                            }

                            VerticalLayout {
                                spacing: 2px;

                                LineEditEx {
                                    read_only: true;
                                    max_popup_height: 160px;
                                    model <=> OptTrd_InputOml.leg2_insid_model;
                                    has_error <=> OptTrd_InputOml.leg2_has_error;
                                    current_text <=> OptTrd_InputOml.leg2_insid;
                                    placeholder_text <=> OptTrd_InputOml.leg2_placeholder_text;

                                    edited => {
                                        OptTrd_InputOml.leg2_insid_changed();
                                    }
                                }

                                if !OptTrd_InputOml.leg2_has_error || "" != OptTrd_InputOml.leg2_insname : HorizontalLayout {
                                    Label {
                                        font_size: 0.9rem;
                                        color: OptTrd_InputOml.leg2_has_error ? Theme.border_error : Theme.control_foreground;
                                        text <=> OptTrd_InputOml.leg2_insname;
                                    }

                                    Rectangle {
                                    }

                                    Label {
                                        font_size: 0.9rem;
                                        text <=> OptTrd_InputOml.leg2_vol;
                                        color: OptTrd_InputOml.leg2_vol.to_float() > 0 ? Theme.accent_background : Theme.foreground;

                                        TouchArea {
                                            mouse_cursor: pointer;
                                            clicked => {
                                                if OptTrd_InputOml.leg2_vol.to_float() > 0 {
                                                    OptTrd_InputOml.volume = OptTrd_InputOml.leg2_vol;
                                                }
                                            }
                                        }
                                    }

                                    Rectangle {
                                        width: 5px;
                                    }
                                }
                            }

                            Label {
                                font_size: 0.95rem;
                                color: TradeColor.dir_sell;
                                text <=> OptTrd_InputOml.leg2_dir;

                                vertical_alignment: center;
                            }
                        }

                        // 组合数量
                        Row {

                            Label {
                                text: "组合数量";
                                vertical_alignment: center;
                            }

                            SpinBox {
                                height: 26px;
                                placeholder_text: "请输入";
                                text <=> OptTrd_InputOml.volume;
                                has_error <=> OptTrd_InputOml.volume_has_error;

                                edited => {
                                    OptTrd_InputOml.volume_text_changed();
                                }

                                up_down_clicked(upflag) => {
                                    OptTrd_InputOml.volume_updown_changed(upflag);
                                }
                            }
                        }

                        // 重填与确定
                        Row {

                            Label {
                                text: "    重填";
                                vertical_alignment: center;
                                color: Theme.border_focus;

                                TouchArea {
                                    mouse_cursor: pointer;
                                    clicked => {
                                        OptTrd_InputOml.is_comb = true;
                                        OptTrd_InputOml.combtradeid = "";
                                        OptTrd_InputOml.strategyid = "CNSJC - 认购牛市价差策略";
                                        OptTrd_InputOml.strategyid_idx = 0;

                                        OptTrd_InputOml.leg1_insid_model = [];
                                        OptTrd_InputOml.leg2_insid_model = [];

                                        OptTrd_InputOml.leg1_insid = "";
                                        OptTrd_InputOml.leg1_insname = "";
                                        OptTrd_InputOml.leg1_vol = "0";
                                        OptTrd_InputOml.leg1_dir = "权利";
                                        OptTrd_InputOml.leg1_dir_i32 = 50;
                                        OptTrd_InputOml.leg1_has_error = true;

                                        OptTrd_InputOml.leg2_insid = "";
                                        OptTrd_InputOml.leg2_insname = "";
                                        OptTrd_InputOml.leg2_vol = "0";
                                        OptTrd_InputOml.leg2_dir = "义务";
                                        OptTrd_InputOml.leg2_dir_i32 = 51;
                                        OptTrd_InputOml.leg2_has_error = true;
                                        OptTrd_InputOml.leg2_placeholder_text = "请先输入第1腿合约";

                                        OptTrd_InputOml.volume = "1";
                                        OptTrd_InputOml.volume_has_error = false;
                                    }
                                }
                            }

                            Button {
                                text: @tr("确定{}", OptTrd_InputOml.is_comb ? "组合" : "解除");
                                background: OptTrd_InputOml.get_comb_color();

                                clicked => {
                                    if AppCom.check_td_session_timeout() {
                                        return ;
                                    }

                                    OptTrd_InputOml.accountid = OptTrd_Com.accountid;
                                    OptTrd_InputOml.ok_clicked(true);
                                }
                            }
                        }
                    }
                }

                Rectangle {
                    horizontal-stretch: 1;
                }
            }
        }

        Rectangle {
            vertical_stretch: 1;
        }
    }

    Rectangle {
        clip: false;
        visible: show_tips;
        z: 100;

        x: 290px;
        y: 93px;
        width: 70px;
        height: 30px;

        background: Theme.control_background.darker(0.1);
        opacity: 0.9;
        border_radius: Radius.extra_small;

        i_text := Label {
            color: Theme.foreground;
            font_size: Fonts.normal.size;
            text: @tr("组合预览");
        }
    }

    states [
        i_ta_scan_strategyid_has_hover when i_ta_scan_strategyid.has_hover: {
            root.show_tips: true;
        }
    ]
}
