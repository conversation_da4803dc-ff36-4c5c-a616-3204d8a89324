use std::sync::{Arc, RwLock};

use tiapi::protocol_pub::{
    account_risk_stk::{AccountsStkField, CreditLimitField, CreditTcAmtField, CreditTcPosField, WithdrawDepositStkField},
    app::{AppHeartbeatField, AppTDServerErrorField, AppTDSessionTimeoutField},
    instrument::{FJYInstrumentField, InstrumentField},
    monitor_risk_stk::{
        ConvertBondsMonStkField, OrderInsertCancelStkField, TradeMonStkField, TradePositionStkField, TradeSelfStkField,
    },
    order_risk_stk::{BusinessOrderStkField, OrderStkField},
    position_risk_stk::{
        CreditConcentrationField, CreditContractField, CreditReturnDtlField, PositionStkField, PositionTransStkField,
    },
    trade_risk_stk::{BusinessTradeStkField, TradeStkField},
};

use super::riskstkbuf::RiskStkBuf;

pub struct RiskStkSpi {
    buf: Option<Arc<RwLock<RiskStkBuf>>>,
}

impl RiskStkSpi {
    pub fn new() -> Self {
        Self { buf: None }
    }

    pub fn register_buf(&mut self, buf: Arc<RwLock<RiskStkBuf>>) {
        self.buf = Some(buf);
    }
}

impl tiapi::api::riskstkapi::IRiskStkSpi for RiskStkSpi {
    /// 连接成功
    fn on_rtn_connect(&self) {
        log::warn!("on_rtn_connect");
    }

    /// 断开连接
    fn on_rtn_disconnect(&self) {
        log::warn!("on_rtn_disconnect");

        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_disconnect();
        }
    }

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {
        log::warn!(
            "on_rtn_tdservererror. {}, {}, {}, {}, {}",
            tderr.AID,
            tderr.SID,
            tderr.Msg,
            tderr.Type,
            tderr.Time,
        );
    }

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {
        log::warn!(
            "on_rtn_tdsessiontimeout. {}, {}, {}, {}",
            tdst.AID,
            tdst.SID,
            tdst.Msg,
            tdst.Time
        );

        // if let Some(buf) = self.buf.as_ref() {
        //     buf.read().unwrap().on_rtn_tdsessiontimeout(tdst);
        // }
    }

    /// 资金消息
    fn on_rtn_account(&self, acc: AccountsStkField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_account(acc);
        }
    }

    /// 信用合约消息
    fn on_rtn_credit_contract(&self, cc: CreditContractField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credit_contract(cc);
        }
    }

    /// 信用授信额度消息
    fn on_rtn_credit_limit(&self, limit: CreditLimitField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credit_limit(limit);
        }
    }

    /// 信用资金头寸消息
    fn on_rtn_credit_tcamt(&self, tcamt: CreditTcAmtField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credit_tcamt(tcamt);
        }
    }

    /// 信用股份头寸消息
    fn on_rtn_credit_tcpos(&self, tcpos: CreditTcPosField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credit_tcpos(tcpos);
        }
    }

    /// 信用集中度消息
    fn on_rtn_credit_concentration(&self, cc: CreditConcentrationField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credit_concentration(cc);
        }
    }

    /// 信用归还明细消息
    fn on_rtn_credit_returndtl(&self, revdtl: CreditReturnDtlField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credit_returndtl(revdtl);
        }
    }

    /// 持仓消息
    fn on_rtn_position(&self, position: PositionStkField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_position(position);
        }
    }

    /// 出入金消息
    fn on_rtn_withdrawdeposit(&self, wd: WithdrawDepositStkField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_withdrawdeposit(wd);
        }
    }

    /// 股份划转
    fn on_rtn_position_trans(&self, position_trans: PositionTransStkField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_position_trans(position_trans);
        }
    }

    /// 委托消息
    fn on_rtn_order(&self, ord: OrderStkField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_order(ord);
        }
    }

    /// 成交消息
    fn on_rtn_trade(&self, trd: TradeStkField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_trade(trd);
        }
    }

    /// 非交易业务委托消息
    fn on_rtn_businessorder(&self, ord: BusinessOrderStkField) {}

    /// 非交易业务成交消息
    fn on_rtn_businesstrade(&self, trd: BusinessTradeStkField) {}

    /// 自成交详情消息
    fn on_rtn_tradeselfdtl(&self, trd: TradeStkField) {}

    /// 自成交消息
    fn on_rtn_tradeself(&self, trdself: TradeSelfStkField) {}

    /// 成交持仓比消息
    fn on_rtn_tradeposition(&self, tradepos: TradePositionStkField) {}

    /// 报撤单消息
    fn on_rtn_orderinsertcancel(&self, ordinsertcancel: OrderInsertCancelStkField) {}

    /// 可转债监控信息
    fn on_rtn_convertbondsmon(&self, convertbondsmon: ConvertBondsMonStkField) {}

    /// 成交监控信息
    fn on_rtn_trademon(&self, trademon: TradeMonStkField) {}

    /// 查询合约响应
    fn on_rsp_qry_instrument(&self, reqid: i32, ins: InstrumentField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_qry_instrument(reqid, ins);
        }
    }

    /// 查询非交易业务合约响应
    fn on_rsp_qry_fly_instrument(&self, reqid: i32, ins: FJYInstrumentField) {}
}
