use std::rc::Rc;

use crate::{
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::account_risk_stk::ReqQryCreditLimitField;

/// 查询 - 信用交易 - 授信额度明细
pub(in crate::ui::riskstk::credit) struct QueryCreditLimit {}

impl QueryCreditLimit {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let qry = app.global::<StkRskCredit_QryCreditLimit>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryCreditLimit::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryCreditLimit::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryCreditLimit::on_page_size_changed(&app_weak, size));

        log::trace!("Query credit return amount detail init completed");
    }
}

impl QueryCreditLimit {
    /// 查询事件
    fn on_query(app_weak: &Weak<RiskStkCreditDetailPage>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();

        let app_qry = app.global::<StkRskCredit_QryCreditLimit>();

        let req = ReqQryCreditLimitField {
            LType: app_qry.get_ltype_int(),
            ExchID: TIConvert::exch_id(&app_qry.get_exchid()),
            AccountID: app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned(),
            TransTime0: app_qry.get_starttime_int(),
            TransTime1: app_qry.get_endtime_int(),
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qry.get_page_index()
            }
        };
        let page_size = app_qry.get_page_size();

        let ret = {
            RISK_STK_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_credit_limit(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            //show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
            println!("查询结果为空");
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qry.set_page_index_model(page_index_data.into());
            app_qry.set_item_total(ret.0);
            app_qry.set_page_index(0);
            app_qry.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|wd| {
            let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

            // 交易所
            items.push(slint::format!("{}", TIConvert::exch_name(wd.ExchID)));

            // 资金账户
            items.push(wd.AccountID.clone().into());

            // 授信类型
            items.push(
                match wd.FrontNo {
                    -100 => "融资额度",
                    -101 => "融券额度",
                    _ => "未知",
                }
                .into(),
            );

            // 转入额度
            items.push(slint::format!("{:.*}", Config::AMT_DIGITS, wd.Deposit));

            // 转出额度
            items.push(slint::format!("{:.*}", Config::AMT_DIGITS, wd.Withdraw));

            // 处理时间
            items.push(TIConvert::transtime(wd.DealTime).into());

            row_data.push(items.into());
        });

        app_qry.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<RiskStkCreditDetailPage>) {
        QueryCreditLimit::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<RiskStkCreditDetailPage>, page_index: i32) {
        QueryCreditLimit::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<RiskStkCreditDetailPage>, page_size: i32) {
        QueryCreditLimit::on_query(app_weak, true);
    }
}
