import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";

export component LeftBottomPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "委托" },
        { text: "成交" },
        { text: "出入金" },
        { text: "股份划转明细" },
        { text: "资金划转记录" },
        { text: "股份划转记录" },
        { text: "证券信息" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.letf_bottom_selidx;
        }

        if 0 == StkRsk_TabSelIdx.letf_bottom_selidx:  Rectangle { Text {text: "委托";} }
        if 1 == StkRsk_TabSelIdx.letf_bottom_selidx:  Rectangle { Text {text: "成交";} }
        if 2 == StkRsk_TabSelIdx.letf_bottom_selidx:  Rectangle { Text {text: "出入金";} }
        if 3 == StkRsk_TabSelIdx.letf_bottom_selidx:  Rectangle { Text {text: "股份划转明细";} }
        if 4 == StkRsk_TabSelIdx.letf_bottom_selidx:  Rectangle { Text {text: "资金划转记录";} }
        if 5 == StkRsk_TabSelIdx.letf_bottom_selidx:  Rectangle { Text {text: "股份划转记录";} }
        if 6 == StkRsk_TabSelIdx.letf_bottom_selidx:  Rectangle { Text {text: "证券信息";} }
    }
}
