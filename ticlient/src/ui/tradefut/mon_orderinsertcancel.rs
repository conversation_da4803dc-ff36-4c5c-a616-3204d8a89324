use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::apiproc::tradefutbuf::CtpOrderInsertCancelMap;

use crate::{
    common::{global::TRADE_FUT_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type IDSet = dashmap::DashSet<String>;

/// 监控 - 报撤单 - 数据
struct MonLimitOrderInsertCancelDatas {
    /// 数据
    data_map: Arc<CtpOrderInsertCancelMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 交易编码列表
    clientid_set: Arc<IDSet>,

    /// 品种编码列表
    productid_set: Arc<IDSet>,

    /// 合约编码列表
    insid_set: Arc<IDSet>,

    /// 是否需要过滤数据
    filter_changed: AtomicBool,
}
impl MonLimitOrderInsertCancelDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpOrderInsertCancelMap::new()),
            data_changed: AtomicBool::new(false),

            clientid_set: Arc::new(IDSet::new()),
            productid_set: Arc::new(IDSet::new()),
            insid_set: Arc::new(IDSet::new()),

            filter_changed: AtomicBool::new(false),
        }
    }
}
static MON_LIMIT_ORDER_INSERT_CANCEL_DATAS: OnceLock<MonLimitOrderInsertCancelDatas> = OnceLock::new();

/// 监控 - 报撤单
pub(super) struct MonLimitOrderInsertCancel {}

// 构建与初始化
impl MonLimitOrderInsertCancel {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = MON_LIMIT_ORDER_INSERT_CANCEL_DATAS.set(MonLimitOrderInsertCancelDatas::new());

        let app_mon = app.global::<FutTrd_MonOrderInsertCancel>();

        // 注册过滤条件改变事件
        let app_weak = app.as_weak();
        app_mon.on_filter_changed(move || MonLimitOrderInsertCancel::on_filter_changed(&app_weak));

        // 注册获取单元格颜色事件
        let app_weak = app.as_weak();
        app_mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonLimitOrderInsertCancel::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor order insert cancel init completed");
    }
}

// 查询
impl MonLimitOrderInsertCancel {
    /// 查询报撤单信息
    pub fn qry_orderinsertcancel(&self) {
        let ret = { TRADE_FUT_BUF.get().unwrap().read().unwrap().pop_orderinsertcancel() };
        if ret.is_empty() {
            return;
        }

        let mloic_datas = MON_LIMIT_ORDER_INSERT_CANCEL_DATAS.get().unwrap();

        for (key, lp) in ret {
            if !mloic_datas.clientid_set.contains(&lp.ClientID) {
                mloic_datas.clientid_set.insert(lp.ClientID.clone());
            }
            if !mloic_datas.productid_set.contains(&lp.ProductID) {
                mloic_datas.productid_set.insert(lp.ProductID.clone());
            }
            if !mloic_datas.insid_set.contains(&lp.InstrumentID) {
                mloic_datas.insid_set.insert(lp.InstrumentID.clone());
            }

            mloic_datas.data_map.insert(key, lp);
        }
        mloic_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl MonLimitOrderInsertCancel {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 频繁报撤单, 大额报撤单
        if 6 == column_index || 7 == column_index {
            let data = data.to_string().replace(",", "");
            let data_vec: Vec<&str> = data.split('/').collect();
            if 2 == data_vec.len() {
                let var = data_vec[0].trim().parse::<i32>();
                let maxvar = data_vec[1].trim().parse::<i32>();
                if var.is_ok() && maxvar.is_ok() {
                    let var = var.unwrap();
                    let maxvar = maxvar.unwrap();
                    if var >= maxvar || maxvar <= 0 {
                        return nc.get_error();
                    } else {
                        let ratio = var as f64 / maxvar as f64;
                        if ratio >= 0.9 {
                            return nc.get_caution();
                        } else {
                            return nc.get_normal();
                        }
                    }
                }
            }
        }

        nc.get_default()
    }

    /// 过虑条件有变动
    fn on_filter_changed(app_weak: &Weak<App>) {
        MON_LIMIT_ORDER_INSERT_CANCEL_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

// 更新UI
impl MonLimitOrderInsertCancel {
    // 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let mloic_datas = MON_LIMIT_ORDER_INSERT_CANCEL_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mlp: FutTrd_MonOrderInsertCancel = app.global::<FutTrd_MonOrderInsertCancel>();

            if mloic_datas.clientid_set.len() != app_mlp.invoke_clientid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mloic_datas.clientid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlp.set_clientid_model(id_arr.into());
            }

            if mloic_datas.productid_set.len() != app_mlp.invoke_productid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mloic_datas.productid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlp.set_productid_model(id_arr.into());
            }

            if mloic_datas.insid_set.len() != app_mlp.invoke_insid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mloic_datas.insid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlp.set_insid_model(id_arr.into());
            }
        });
    }

    // 更新报撤单
    async fn update_limitordinsertcancel(&self, app_weak: &Weak<App>) {
        let mloic_datas = MON_LIMIT_ORDER_INSERT_CANCEL_DATAS.get().unwrap();

        let data_map_clone = {
            if mloic_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                mloic_datas.filter_changed.store(false, Ordering::Relaxed);
                mloic_datas.data_map.clone()
            } else {
                if mloic_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    mloic_datas.data_map.clone()
                } else {
                    Arc::new(CtpOrderInsertCancelMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mlp: FutTrd_MonOrderInsertCancel = app.global::<FutTrd_MonOrderInsertCancel>();

            let filter_exchid = app_mlp.get_exchid().to_string();
            let filter_cliid = app_mlp.get_clientid().to_string();
            let filter_pid = app_mlp.get_productid().to_string();
            let filter_insid = app_mlp.get_insid().to_string();

            // 填充数据
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|loic| {
                let exchname = TIConvert::exch_name(loic.ExchID);

                if !filter_exchid.is_empty() {
                    if exchname != filter_exchid {
                        return;
                    }
                }

                if !filter_cliid.is_empty() {
                    if !loic.ClientID.contains(&filter_cliid) {
                        return;
                    }
                }

                if !filter_pid.is_empty() {
                    if !loic.ProductID.contains(&filter_pid) {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !loic.InstrumentID.contains(&filter_insid) {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(loic.AccountID.clone().into());

                // 交易所
                items.push(exchname.into());

                // 交易编码
                items.push(loic.ClientID.clone().into());

                // 品种编码
                items.push(loic.ProductID.clone().into());

                // 合约编码
                items.push(loic.InstrumentID.clone().into());

                // 总报单笔数
                items.push(slint::format!("{}", TIConvert::format_i(loic.InsertNum)));

                // 频繁报撤单
                items.push(slint::format!(
                    "{} / {}",
                    TIConvert::format_i(loic.CancelNum),
                    TIConvert::format_i(loic.MCancelNum)
                ));

                // 大额报撤单
                items.push(slint::format!(
                    "{} / {}",
                    TIConvert::format_i(loic.LCancelNum),
                    TIConvert::format_i(loic.MLCancelNum)
                ));

                // 大额报撤单标准
                items.push(slint::format!("{}", TIConvert::format_i(loic.SLCancelNum)));

                // 发生时间
                items.push(TIConvert::transtime(loic.TransTime).into());

                // Ratio(仅用于排序)
                let ratio = {
                    let mut ratio = 10000.; // 倍率初始值足够大. 防止排序时2.0大天12.1这样的情况
                    if 0 != loic.MCancelNum {
                        ratio += loic.CancelNum as f64 / loic.MCancelNum as f64;
                    }
                    if 0 != loic.MLCancelNum {
                        ratio += loic.LCancelNum as f64 / loic.MLCancelNum as f64;
                    }
                    ratio
                };
                items.push(ratio.to_string().into());

                row_data.push(items.into());
            });

            // 根据 ratio 按降序排序
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(10).unwrap();
                let c_b = r_b.row_data(10).unwrap();
                c_b.cmp(&c_a)
            }));
            app_mlp.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新报撤单
        self.update_limitordinsertcancel(&app_weak).await;
    }
}
