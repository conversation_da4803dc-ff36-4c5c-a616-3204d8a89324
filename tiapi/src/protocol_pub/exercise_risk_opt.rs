/// 行权消息
#[derive(Default)]
pub struct ExerciseField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 原始报单交易用户代码
    pub UserID: String,

    /// 报单状态
    pub OrdStatus: i32,

    /// 组合行权标志
    pub CombFlag: i32,

    /// 本地报单编号
    pub LocalOrderNo: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 数量
    pub Volume: i32,

    /// 订单所有类型
    pub OwnerType: i32,

    /// 发生时间
    pub TransTime: i32,
}

/// 行权请求查询消息
#[derive(Default)]
pub struct ReqQryExerciseField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 行权响应查询消息
#[derive(Default)]
pub struct RspQryExerciseField {
    pub array: Vec<ExerciseField>,
}
