use crate::protocol_pub::{
    account_risk_fut::*, app::*, exercise_risk_fut::ExerciseFutField, greeks::GreeksField, monitor_risk_fut::*,
    order_risk_fut::OrderFutField, position_risk_fut::PositionFutField, trade_risk_fut::TradeFutField,
};

/// 期货风控回调类
pub trait IRiskFutSpi: Send + Sync {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {}

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {}

    /// 资金消息
    fn on_rtn_account(&self, acc: AccountsFutField) {}

    /// 出入金消息
    fn on_rtn_withdrawdeposit(&self, wd: WithdrawDepositFutField) {}

    /// 持仓消息
    fn on_rtn_position(&self, position: PositionFutField) {}

    /// 行权消息
    fn on_rtn_exercise(&self, ex: ExerciseFutField) {}

    /// 委托消息
    fn on_rtn_order(&self, ord: OrderFutField) {}

    /// 成交消息
    fn on_rtn_trade(&self, trd: TradeFutField) {}

    /// 希腊字母
    fn on_rtn_greeks(&self, greeks: GreeksField) {}

    /// 自成交详情消息
    fn on_rtn_tradeselfdtl(&self, trd: TradeFutField) {}

    /// 自成交消息
    fn on_rtn_tradeself(&self, trdself: TradeSelfFutField) {}

    /// 限仓消息
    fn on_rtn_limitposition(&self, limitpos: LimitPositionFutField) {}

    /// 限开仓消息
    fn on_rtn_limitopen(&self, limitopen: LimitOpenFutField) {}

    /// 报撤单消息
    fn on_rtn_orderinsertcancel(&self, ordinsertcancel: OrderInsertCancelFutField) {}
}
