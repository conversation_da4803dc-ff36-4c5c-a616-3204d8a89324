import { Theme, Fonts, TableView, TVColumnHeader, Label, ComboBox, Button, Radius, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";
import { StkTrd_TabSelIdx, StkTrd_InputOrder, StkTrd_Com, StkTrd_Position } from "struct.slint";

export component PositionPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;

    private property <TVColumnHeader> pos_header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 1,
        columns: [
            /*  0 */{ title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
            /*  1 */{ title: @tr("持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日持仓量 + 当日买入 - 当日卖出" },
            /*  2 */{ title: @tr("当前可用 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "T + 0 : 持仓量 - 冻结持仓 + 划转量 \nT + 1 : 上日持仓量 - 当日卖出 - 冻结持仓 + 划转量" },
            /*  3 */{ title: @tr("上日持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日结算持仓量, 盘中该值不会变动" },
            /*  4 */{ title: @tr("当日买入"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  5 */{ title: @tr("当日卖出"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  6 */{ title: @tr("划转量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "值大于 0 : 从主席划入 TITD\n值小于 0 : 从 TITD 划入主席" },
            /*  7 */{ title: @tr("冻结持仓"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  8 */{ title: @tr("持仓均价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            /*  9 */{ title: @tr("持仓成本"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 10 */{ title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 11 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
            /* 12 */{ title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },
            /* 13 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
        ]
    };

    private property <TVColumnHeader> pos_credit_header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 1,
        columns: [
            /*  0 */{ title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
            /*  1 */{ title: @tr("持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日持仓量 + 当日买入 - 当日卖出" },
            /*  2 */{ title: @tr("当前可用 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "T + 0 : 持仓量 - 冻结持仓 + 划转量 \nT + 1 : 上日持仓量 - 当日卖出 - 冻结持仓 + 划转量" },
            /*  3 */{ title: @tr("上日持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日结算持仓量, 盘中该值不会变动" },
            /*  4 */{ title: @tr("当日买入"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  5 */{ title: @tr("当日卖出"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  6 */{ title: @tr("划转量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "值大于 0 : 从主席划入 TITD\n值小于 0 : 从 TITD 划入主席" },
            /*  7 */{ title: @tr("冻结持仓"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  8 */{ title: @tr("持仓均价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            /*  9 */{ title: @tr("持仓成本"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 10 */{ title: @tr("最新价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            /* 11 */{ title: @tr("持仓市值"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 12 */{ title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 13 */{ title: @tr("现券还券量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /* 14 */{ title: @tr("融资已还折算量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 15 */{ title: @tr("融资未还折算量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 16 */{ title: @tr("折算率"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            /* 17 */{ title: @tr("充抵保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 18 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
            /* 19 */{ title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },
            /* 20 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
        ]
    };

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> StkTrd_Position.exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkTrd_Position.filter_changed();
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }

            i_cbx_insid := ComboBox {
                width: 100px;
                max_popup_height: root.height - 50px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> StkTrd_Position.insid;
                model <=> StkTrd_Position.insid_model;

                edited(by_selected) => {
                    StkTrd_Position.insid_text_changed();
                    if !by_selected {
                        StkTrd_Position.filter_changed();
                    }
                }

                selected => {
                    StkTrd_Position.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            Button {
                width: 80px;
                height: 26px;
                background: WidgetColor.btn_background;
                text: "股份划转";
                clicked => {
                    if "" == StkTrd_Position.sel_accid {
                        StkTrd_Position.sel_accid = StkTrd_Com.accountid;
                    }
                    StkTrd_Position.show_pos_trans = true;
                }
            }

            ExportButton {
                clicked(type) => {
                    StkTrd_Position.export_clicked(type);
                }
            }
        }

        TableView {
            sortable: true;
            column_header: StkTrd_Com.iscredit ? pos_credit_header : pos_header;
            row_count: StkTrd_Position.row_data.length;

            sort_canceled => {
                StkTrd_Position.sort_asc_column_index = -1;
                StkTrd_Position.sort_dec_column_index = -1;
            }

            sort_ascending(index) => {
                StkTrd_Position.sort_ascending(index);
                StkTrd_Position.sort_asc_column_index = index;
                StkTrd_Position.sort_dec_column_index = -1;
                true
            }

            sort_descending(index) => {
                StkTrd_Position.sort_descending(index);
                StkTrd_Position.sort_asc_column_index = -1;
                StkTrd_Position.sort_dec_column_index = index;
                true
            }

            get_cell_data(row_index, column_index) => {
                StkTrd_Position.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_double_clicked(row_index, column_index) => {
                StkTrd_TabSelIdx.input_selidx = 0;

                if StkTrd_Position.row_data.length > row_index {
                    if StkTrd_Com.iscredit {

                    } else {
                        StkTrd_InputOrder.is_buy = false;
                        StkTrd_InputOrder.price = "";
                        StkTrd_InputOrder.volume = StkTrd_Position.row_data[row_index][2];

                        if StkTrd_Position.row_data[row_index][11] != StkTrd_InputOrder.exchid {
                            StkTrd_InputOrder.exchid = StkTrd_Position.row_data[row_index][11];
                            StkTrd_InputOrder.exchid_changed();
                        }
                        if StkTrd_Position.row_data[row_index][0] != StkTrd_InputOrder.insid {
                            StkTrd_InputOrder.insid = StkTrd_Position.row_data[row_index][0];
                            StkTrd_InputOrder.insid_text_changed();
                        }
                    }
                }
            }

            current_cell_changed(row_index, column_index) => {
                if StkTrd_Com.iscredit {
                    if StkTrd_Position.sel_accid != StkTrd_Position.row_data[row_index][20] {
                        StkTrd_Position.sel_accid = StkTrd_Position.row_data[row_index][20];
                    }

                    if StkTrd_Position.sel_exchid != StkTrd_Position.row_data[row_index][18] {
                        StkTrd_Position.sel_exchid = StkTrd_Position.row_data[row_index][18];
                    }

                    if StkTrd_Position.sel_insid != StkTrd_Position.row_data[row_index][0] {
                        StkTrd_Position.sel_insid = StkTrd_Position.row_data[row_index][0];
                    }

                    if StkTrd_Position.sel_volume != StkTrd_Position.row_data[row_index][2] {
                        StkTrd_Position.sel_volume = StkTrd_Position.row_data[row_index][2];
                    }
                } else {
                    if StkTrd_Position.sel_accid != StkTrd_Position.row_data[row_index][13] {
                        StkTrd_Position.sel_accid = StkTrd_Position.row_data[row_index][13];
                    }

                    if StkTrd_Position.sel_exchid != StkTrd_Position.row_data[row_index][11] {
                        StkTrd_Position.sel_exchid = StkTrd_Position.row_data[row_index][11];
                    }

                    if StkTrd_Position.sel_insid != StkTrd_Position.row_data[row_index][0] {
                        StkTrd_Position.sel_insid = StkTrd_Position.row_data[row_index][0];
                    }

                    if StkTrd_Position.sel_volume != StkTrd_Position.row_data[row_index][2] {
                        StkTrd_Position.sel_volume = StkTrd_Position.row_data[row_index][2];
                    }
                }
            }

            cell_pointer_event(row_index, column_index, event, point) => {
                if PointerEventButton.right == event.button && PointerEventKind.up == event.kind {
                    self.select_full_row = true;
                    self.set_current_row(row_index);

                    right_up_point = point;
                    if !StkTrd_Position.show_pos_trans {
                        i_pw_menu.show();
                    }
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_Position.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_Position.row_data[row_index]);
                }
            }
        }
    }

    i_pw_menu := PopupWindow {

        x: right_up_point.x - root.absolute_position.x;
        y: right_up_point.y - root.absolute_position.y;

        Rectangle {

            width: 100px;

            border-width: 1px;
            border-color: Theme.border;
            border_radius: Radius.extra_small;

            drop_shadow_blur: 15px;
            drop_shadow_color: Theme.control_background.darker(0.5);

            background: Theme.control_background;

            VerticalLayout {
                padding: 5px;
                spacing: 10px;

                i_menu_fundtrans := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 16px;
                            colorize: Theme.foreground;
                            source: Icons.list;
                        }

                        Label {
                            text: "股份划转";
                            horizontal-alignment: left;
                        }
                    }

                    i_menu_fundtrans_ta := TouchArea {
                        clicked => {
                            StkTrd_Position.show_pos_trans = true;
                        }
                    }
                }
            }
        }

        states [
            postrans_has_hover when i_menu_fundtrans_ta.has_hover: {
                i_menu_fundtrans.background: Theme.shadow;
            }
        ]
    }
}
