import { Theme, TabView } from "@zstk/lib.slint";

import { MonTrdPosOrdInsertCancelPage } from "mon_trdpos_ordinsertcancel.slint";
import { MonLimitAmountPage } from "mon_limitamount.slint";
import { MonLimitPositionPage } from "mon_limitposition.slint";
import { MonSelfTradePage } from "mon_selftrade.slint";
import { MonSelfTradeDtlPage } from "mon_selftradedtl.slint";

import { OptTrd_TabSelIdx } from "struct.slint";

export component MonitorPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: [
                { text: "成交持仓比 / 报撤单" },
                { text: "限额" },
                { text: "限仓" },
                { text: "自成交" },
                { text: "自成交详情" },
            ];

            current_index <=> OptTrd_TabSelIdx.mon_subselidx;
        }

        if 0 == OptTrd_TabSelIdx.mon_subselidx: MonTrdPosOrdInsertCancelPage { }

        if 1 == OptTrd_TabSelIdx.mon_subselidx: MonLimitAmountPage { }

        if 2 == OptTrd_TabSelIdx.mon_subselidx: MonLimitPositionPage { }

        if 3 == OptTrd_TabSelIdx.mon_subselidx: MonSelfTradePage { }

        if 4 == OptTrd_TabSelIdx.mon_subselidx: MonSelfTradeDtlPage { }
    }
}
