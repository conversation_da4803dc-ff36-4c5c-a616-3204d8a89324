use serde::Deserialize;

/// 委托消息
#[derive(Default, Debug, Clone, Deserialize, PartialEq)]
#[serde[default]]
pub struct CtpOrderField {
    // CThostFtdcOrderField
    pub BrokerID: String,           // 经纪公司代码
    pub InvestorID: String,         // 投资者代码
    pub InstrumentID: String,       // 合约代码
    pub OrderRef: String,           // 报单引用
    pub UserID: String,             // 用户代码
    pub OrderPriceType: i32,        // 报单价格条件
    pub Direction: i32,             // 买卖方向
    pub CombOffsetFlag: String,     // 组合开平标志
    pub CombHedgeFlag: String,      // 组合投机套保标志
    pub LimitPrice: f64,            // 价格
    pub VolumeTotalOriginal: i32,   // 数量
    pub TimeCondition: i32,         // 有效期类型
    pub GTDDate: String,            // GTD日期
    pub VolumeCondition: i32,       // 成交量类型
    pub MinVolume: i32,             // 最小成交量
    pub ContingentCondition: i32,   // 触发条件
    pub StopPrice: f64,             // 止损价
    pub ForceCloseReason: i32,      // 强平原因
    pub IsAutoSuspend: i32,         // 自动挂起标志
    pub BusinessUnit: String,       // 业务单元
    pub RequestID: i32,             // 请求编号
    pub OrderLocalID: String,       // 本地报单编号
    pub ExchangeID: String,         // 交易所代码
    pub ParticipantID: String,      // 会员代码
    pub ClientID: String,           // 客户代码
    pub ExchangeInstID: String,     // 合约在交易所的代码
    pub TraderID: String,           // 交易所交易员代码
    pub InstallID: i32,             // 安装编号
    pub OrderSubmitStatus: i32,     // 报单提交状态
    pub NotifySequence: i32,        // 报单提示序号
    pub TradingDay: String,         // 交易日
    pub SettlementID: i32,          // 结算编号
    pub OrderSysID: String,         // 报单编号
    pub OrderSource: i32,           // 报单来源
    pub OrderStatus: i32,           // 报单状态
    pub OrderType: i32,             // 报单类型
    pub VolumeTraded: i32,          // 今成交数量
    pub VolumeTotal: i32,           // 剩余数量
    pub InsertDate: String,         // 报单日期
    pub InsertTime: String,         // 委托时间
    pub ActiveTime: String,         // 激活时间
    pub SuspendTime: String,        // 挂起时间
    pub UpdateTime: String,         // 最后修改时间
    pub CancelTime: String,         // 撤销时间
    pub ActiveTraderID: String,     // 最后修改交易所交易员代码
    pub ClearingPartID: String,     // 结算会员编号
    pub SequenceNo: i32,            // 序号
    pub FrontID: i32,               // 前置编号
    pub SessionID: i32,             // 会话编号
    pub UserProductInfo: String,    // 用户端产品信息
    pub StatusMsg: String,          // 状态信息
    pub UserForceClose: i32,        // 用户强评标志
    pub ActiveUserID: String,       // 操作用户代码
    pub BrokerOrderSeq: i32,        // 经纪公司报单编号
    pub RelativeOrderSysID: String, // 相关报单
    pub ZCETotalTradedVolume: i32,  // 郑商所成交数量
    pub IsSwapOrder: i32,           // 互换单标志
    pub BranchID: String,           // 营业部编号
    pub InvestUnitID: String,       // 投资单元代码
    pub AccountID: String,          // 资金账号
    pub CurrencyID: String,         // 币种代码
    pub MacAddress: String,         // Mac地址
    pub OwnerType: String,          // 所有者类型
    pub ComTradeID: String,         // 组合编号
    pub LoginPBU: String,           // 登陆PBU
    pub IPAddress: String,          // IP地址

    /// 委托的最后状态
    /// <br>TI_OrdStatus_Success 1          已报入
    /// <br>TI_OrdStatus_Trade 2            部分成交
    /// <br>TI_OrdStatus_All 3              全部成交
    /// <br>TI_OrdStatus_Cancel 4           已撤单
    /// <br>TI_OrdStatus_Reject 8           已拒绝
    /// <br>TI_OrdStatus_TradeCancel 'x'    部成部撤
    pub LastStatus: i32,    // 委托的最后状态
    pub Key: String,        // 标识本条委托的关键字
    pub TIOwnerType: i32,   // 所有者类型
}

/// 委托请求查询消息
#[derive(Default)]
pub struct CtpReqQryOrderField {
    /// 交易所代码
    pub ExchID: String,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: String,
    pub TransTime0_Int: i32,

    /// 结束时间
    pub TransTime1: String,
    pub TransTime1_Int: i32,
}

/// 委托响应查询消息
#[derive(Default)]
pub struct CtpRspQryOrderField {
    /// 委托消息
    pub array: Vec<CtpOrderField>,
}

/// 组合消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde(default)]
pub struct CtpOmlField {
    // CThostFtdcCombActionField
    pub BrokerID: String,        // 经纪公司代码
    pub InvestorID: String,      // 投资者代码
    pub InstrumentID: String,    // 合约代码
    pub CombActionRef: String,   // 组合引用
    pub UserID: String,          // 用户代码
    pub Direction: i32,          // 买卖方向
    pub Volume: i32,             // 数量
    pub CombDirection: i32,      // 组合指令方向
    pub HedgeFlag: i32,          // 投机套保标志
    pub ActionLocalID: String,   // 本地申请组合编号
    pub ExchangeID: String,      // 交易所代码
    pub ParticipantID: String,   // 会员代码
    pub ClientID: String,        // 客户代码
    pub ExchangeInstID: String,  // 合约在交易所的代码
    pub TraderID: String,        // 交易所交易员代码
    pub InstallID: i32,          // 安装编号
    pub ActionStatus: i32,       // 组合状态
    pub NotifySequence: i32,     // 报单提示序号
    pub TradingDay: String,      // 交易日
    pub SettlementID: i32,       // 结算编号
    pub SequenceNo: i32,         // 序号
    pub FrontID: i32,            // 前置编号
    pub SessionID: i32,          // 会话编号
    pub UserProductInfo: String, // 用户端产品信息
    pub StatusMsg: String,       // 状态信息
    pub reserve1: String,        // 保留的无效字段
    pub MacAddress: String,      // Mac地址
    pub ComTradeID: String,      // 组合编号
    pub BranchID: String,        // 营业部编号
    pub InvestUnitID: String,    // 投资单元代码
    pub OwnerType: String,       // 所有者类型
    pub ActionTime: String,      // 操作时间
    pub LoginPBU: String,        // 登陆PBU
    pub IPAddress: String,       // IP地址
}

/// 请求查询组合消息
#[derive(Default)]
pub struct CtpReqQryOmlField {
    /// 交易所代码
    pub ExchID: String,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 指令方向
    pub CombDir: i32,

    /// 组合编号
    pub CombTradeID: String,

    /// 开始时间
    pub TransTime0: String,
    pub TransTime0_Int: i32,

    /// 结束时间
    pub TransTime1: String,
    pub TransTime1_Int: i32,
}

/// 报价消息
#[derive(Default, Debug, Clone, Deserialize, PartialEq)]
#[serde[default]]
pub struct CtpQuoteField {
    // CThostFtdcQuoteField
    pub BrokerID: String,        // 经纪公司代码
    pub InvestorID: String,      // 投资者代码
    pub InstrumentID: String,    // 合约代码
    pub QuoteRef: String,        // 报价引用
    pub UserID: String,          // 用户代码
    pub AskPrice: f64,           // 卖价格
    pub BidPrice: f64,           // 买价格
    pub AskVolume: i32,          // 卖数量
    pub BidVolume: i32,          // 买数量
    pub RequestID: i32,          // 请求编号
    pub BusinessUnit: String,    // 业务单元
    pub AskOffsetFlag: i32,      // 卖开平标志
    pub BidOffsetFlag: i32,      // 买开平标志
    pub AskHedgeFlag: i32,       // 卖投机套保标志
    pub BidHedgeFlag: i32,       // 买投机套保标志
    pub QuoteLocalID: String,    // 本地报价编号
    pub ExchangeID: String,      // 交易所代码
    pub ParticipantID: String,   // 会员代码
    pub ClientID: String,        // 客户代码
    pub ExchangeInstID: String,  // 合约在交易所的代码
    pub TraderID: String,        // 交易所交易员代码
    pub InstallID: i32,          // 安装编号
    pub NotifySequence: i32,     // 报价提示序号
    pub OrderSubmitStatus: i32,  // 报价提交状态
    pub TradingDay: String,      // 交易日
    pub SettlementID: i32,       // 结算编号
    pub QuoteSysID: String,      // 报价编号
    pub InsertDate: String,      // 报单日期
    pub InsertTime: String,      // 插入时间
    pub CancelTime: String,      // 撤销时间
    pub QuoteStatus: i32,        // 报价状态
    pub ClearingPartID: String,  // 结算会员编号
    pub SequenceNo: i32,         // 序号
    pub AskOrderSysID: String,   // 卖方报单编号
    pub BidOrderSysID: String,   // 买方报单编号
    pub FrontID: i32,            // 前置编号
    pub SessionID: i32,          // 会话编号
    pub UserProductInfo: String, // 用户端产品信息
    pub StatusMsg: String,       // 状态信息
    pub ActiveUserID: String,    // 操作用户代码
    pub BrokerQuoteSeq: i32,     // 经纪公司报价编号
    pub AskOrderRef: String,     // 衍生卖报单引用
    pub BidOrderRef: String,     // 衍生买报单引用
    pub ForQuoteSysID: String,   // 应价编号
    pub BranchID: String,        // 营业部编号
    pub InvestUnitID: String,    // 投资单元代码
    pub AccountID: String,       // 资金账号
    pub CurrencyID: String,      // 币种代码
    pub MacAddress: String,      // Mac地址
    pub LoginPBU: String,        // 登陆PBU
    pub IPAddress: String,       // IP地址
    pub ReplaceSysID: String,    // 被顶单编号

    // 以下非协议内容
    //#define TI_OrdStatus_Success 1    // 已报入
    //#define TI_OrdStatus_Trade 2      // 部分成交
    //#define TI_OrdStatus_All 3        // 全部成交
    //#define TI_OrdStatus_Cancel 4     // 已撤单
    //#define TI_OrdStatus_Reject 8     // 已拒绝
    //#define TI_OrdStatus_TradeCancel 'x' // 部成部撤
    pub LastStatus: i32,    // 委托的最后状态
    pub BidLastStatus: i32, // 委托的最后状态
    pub AskLastStatus: i32, // 委托的最后状态
    pub Key: String,        // 标识本条委托的关键字
}

/// 报价请求查询消息
#[derive(Default)]
pub struct CtpReqQryQuoteField {
    /// 交易所代码
    pub ExchID: String,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: String,
    pub TransTime0_Int: i32,

    /// 结束时间
    pub TransTime1: String,
    pub TransTime1_Int: i32,
}

/// 报价响应查询消息
#[derive(Default)]
pub struct CtpRspQryQuoteField {
    pub array: Vec<CtpQuoteField>,
}

/// 非交易业务委托
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpBusinessOrderField {
    pub FrontNo: i32,   // 交易端前置
    pub SessionNo: i32, // 交易端SessionNO
    pub RequestNo: i32, // 请求编号

    pub BizSeqNum: i32, // 业务序号(按资金账户从1递增)
    pub MsgSeqNum: i32, // 消息序号(按资金账户-消息类型从1递增)

    pub ExchID: i32,         // 交易所编码
    pub AccountID: String,   // 资金帐号
    pub AccountType: i32, // 账号类型
    pub ClientID: String,    // 交易编码

    pub InstrumentID: String,    // 合约编码
    pub DstInstrumentID: String, // 合约编码
    pub UserID: String,          // 原始报单交易用户代码

    /// 订单状态
    /// <br>TI_OrdStatus_Success 1          已报入
    /// <br>TI_OrdStatus_Trade 2            部分成交
    /// <br>TI_OrdStatus_All 3              全部成交
    /// <br>TI_OrdStatus_Cancel 4           已撤单
    /// <br>TI_OrdStatus_Reject 8           已拒绝
    /// <br>TI_OrdStatus_TradeCancel 'x'    部成部撤
    pub OrdStatus: i32,
    pub LocalOrderNo: i32,  // 本地报单编号
    pub OrderSysID: String, // 交易所报单编号

    pub Volume: i32, // 数量
    pub Price: f64,  // 价格

    pub BusinessType: i32, // 业务类型
    pub Side: i32,         // 买卖方向

    pub LeavesVolume: i32, // 订单剩余数量(交易所返回字段,每个交易所的含义不同)
    pub CancelVolume: i32, // 订单撤销数量

    pub TransTime: i32, // 发生时间
}

/// 非交易业务委托请求查询消息
#[derive(Default)]
pub struct CtpReqQryBusinessOrderField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}