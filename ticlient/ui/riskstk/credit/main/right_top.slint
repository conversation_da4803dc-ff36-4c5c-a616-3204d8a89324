import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";

export component RightTopPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "融资授信额度" },
        { text: "融券授信额度" },
        { text: "资金头寸" },
        { text: "股份头寸" },
        { text: "集中度" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.right_top_selidx;
        }

        if 0 == StkRsk_TabSelIdx.right_top_selidx:  Rectangle { Text {text: "融资授信额度";} }
        if 1 == StkRsk_TabSelIdx.right_top_selidx:  Rectangle { Text {text: "融券授信额度";} }
        if 2 == StkRsk_TabSelIdx.right_top_selidx:  Rectangle { Text {text: "资金头寸";} }
        if 3 == StkRsk_TabSelIdx.right_top_selidx:  Rectangle { Text {text: "股份头寸";} }
        if 4 == StkRsk_TabSelIdx.right_top_selidx:  Rectangle { Text {text: "集中度";} }
    }
}
