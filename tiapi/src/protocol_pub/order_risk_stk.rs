use serde::Deserialize;

/// 委托消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct OrderStkField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 原始报单交易用户代码
    pub UserID: String,

    /// 报单状态
    pub OrdStatus: i32,

    /// 本地报单编号
    pub LocalOrderNo: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 数量
    pub Volume: i32,

    /// 价格
    pub Price: f64,

    /// 买卖方向
    pub Side: i32,

    /// 报单价格条件
    pub PriceType: i32,

    /// 有效期类型
    pub TimeInForce: i32,

    /// 订单所有类型
    pub OwnerType: i32,

    /// 剩余数量(交易所返回字段,每个交易所的含义不同)
    pub LeavesVolume: i32,

    /// 撤销数量
    pub CancelVolume: i32,

    /// 发生时间
    pub TransTime: i32,
}

/// 非交易业务委托消息
#[derive(Default)]
pub struct BusinessOrderStkField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号(按资金账户-消息类型从1递增)
    pub MsgSeqNum: i32,

    /// 业务序号(按资金账户从1递增)
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 合约编码
    pub DstInstrumentID: String,

    /// 原始报单交易用户代码
    pub UserID: String,

    /// 报单状态
    pub OrdStatus: i32,

    /// 本地报单编号
    pub LocalOrderNo: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 数量
    pub Volume: i32,

    /// 价格
    pub Price: f64,

    /// 业务类型
    pub BusinessType: i32,

    /// 买卖方向
    pub Side: i32,

    /// 剩余数量(交易所返回字段,每个交易所的含义不同)
    pub LeavesVolume: i32,

    /// 撤销数量
    pub CancelVolume: i32,

    /// 发生时间
    pub TransTime: i32,
}

/// 委托请求查询消息
#[derive(Default)]
pub struct ReqQryOrderStkField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 委托响应查询消息
#[derive(Default)]
pub struct RspQryOrderStkField {
    /// 委托消息
    pub array: Vec<OrderStkField>,
}

/// 非交易业务委托请求查询消息
#[derive(Default)]
pub struct PTReqQryBusinessOrderStk {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 非交易业务委托响应查询消息
#[derive(Default)]
pub struct PTRspQryBusinessOrderStk {
    /// 非交易业务委托消息
    pub array: Vec<BusinessOrderStkField>,
}
