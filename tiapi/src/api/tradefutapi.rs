use crate::{
    protocol_pub::{
        account_trade_ctp::{CtpAccountField, CtpReqQryTradingAccountExField, CtpReqQryTradingAccountField},
        app::*,
        exercise_trade_ctp::CtpExecOrderField,
        instrument_trade_ctp::{CtpReqQryInstrumentField, CtpRspQryInstrumentField},
        login_trade_ctp::{CtpLoginRetField, CtpReqLoginField},
        monitor_trade_ctp::*,
        order_trade_ctp::{CtpOrderField, CtpQuoteField},
        position_trade_ctp::{CtpPositionField, CtpReqQryPositionField},
        trade_req_ctp::*,
        trade_trade_ctp::CtpTradeField,
    },
    serverinfo::ServerInfo,
};

/// 期货交易回调类
pub trait ITradeFutSpi: Send + Sync {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {}

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {}

    /// 委托消息
    fn on_rtn_order(&self, ord: CtpOrderField) {}

    /// 成交消息
    fn on_rtn_trade(&self, trd: CtpTradeField) {}

    /// 执行宣告消息
    fn on_rtn_execorder(&self, eo: CtpExecOrderField) {}

    /// 报价消息
    fn on_rtn_quote(&self, quote: CtpQuoteField) {}

    /// 自成交
    fn on_rtn_tradeself(&self, trdslf: CtpTradeSelfField) {}

    /// 自成交详情
    fn on_rtn_tradeselfdtl(&self, trdslf: CtpTradeSelfDtlField) {}

    /// 报撤单
    fn on_rtn_orderinsertcancel(&self, ordinsertcancel: CtpFutOrderInsertCancelField) {}

    /// 限持仓
    fn on_rtn_limitposition(&self, lp: CtpFutLimitPositionField) {}

    /// 限开仓
    fn on_rtn_limitopen(&self, lo: CtpFutLimitOpenField) {}

    /// 报单录入响应消息
    fn on_rsp_inputorder(&self, rsp: CtpRspInputOrderField, reqid: i32, islast: bool) {}

    /// 报单操作响应
    fn on_rsp_inputorderaction(&self, rsp: CtpRspInputOrderActionField, reqid: i32, islast: bool) {}

    /// 行权响应
    fn on_rsp_inputexecorder(&self, rsp: CtpRspInputExecOrderField, reqid: i32, islast: bool) {}

    /// 行权操作响应
    fn on_rsp_inputexecorderaction(&self, rsp: CtpRspInputExecOrderActionField, reqid: i32, islast: bool) {}

    /// 报价录入响应消息
    fn on_rsp_inputquote(&self, rsp: CtpRspInputQuoteField, reqid: i32, islast: bool) {}

    /// 报价操作响应
    fn on_rsp_inputquoteaction(&self, rsp: CtpRspInputQuoteActionField, reqid: i32, islast: bool) {}

    /// 查询合约响应
    fn on_rsp_qry_instrument(&self, ins: CtpRspQryInstrumentField, reqid: i32, islast: bool) {}

    /// 查询资金响应消息
    fn on_rsp_qry_tradingaccount(&self, acc: CtpAccountField, reqid: i32, islast: bool) {}

    /// 查询资金(分交易所)响应消息
    fn on_rsp_qry_tradingaccountex(&self, acc: CtpAccountField, reqid: i32, islast: bool) {}

    /// 查询持仓响应消息
    fn on_rsp_qry_investorposition(&self, pos: CtpPositionField, reqid: i32, islast: bool) {}
}

/// 期货交易API
pub trait TradeFutApi: Send + Sync {
    /// 初始化
    fn init(&mut self);

    /// 释放资源
    fn release(&mut self);

    /// 设置服务端信息(请求登录前必须设置)
    fn set_svrinfo(&mut self, si: &ServerInfo);

    /// 请求登录<br>
    /// 同步接口. 连接->登录<br>
    /// 返回值说明. 成功发送登录请求前的错误均放在Err中, 成功发送请求后无论登录成功与否都放在OK中
    fn req_login(&self, req: &CtpReqLoginField) -> crate::Result<CtpLoginRetField>;

    /// 请求查询合约
    fn req_qry_instrument(&self, reqid: i32, req: &CtpReqQryInstrumentField) -> crate::Result<()>;

    /// 请求查询资金账户
    fn req_qry_tradingaccount(&self, reqid: i32, req: &CtpReqQryTradingAccountField) -> crate::Result<()>;

    /// 请求查询资金账户(分交易所)
    fn req_qry_tradingaccountex(&self, reqid: i32, req: &CtpReqQryTradingAccountExField) -> crate::Result<()>;

    /// 请求查询持仓
    fn req_qry_investorposition(&self, reqid: i32, req: &CtpReqQryPositionField) -> crate::Result<()>;

    /// 请求报单
    fn req_orderinput(&self, reqid: i32, req: &CtpReqInputOrderField) -> crate::Result<()>;

    /// 请求撤单
    fn req_orderaction(&self, reqid: i32, req: &CtpReqInputOrderActionField) -> crate::Result<()>;

    /// 请求行权
    fn req_exec_orderinput(&self, reqid: i32, req: &CtpReqInputExecOrderField) -> crate::Result<()>;

    /// 请求撤行权
    fn req_exec_orderaction(&self, reqid: i32, req: &CtpReqInputExecOrderActionField) -> crate::Result<()>;

    /// 请求合并行权
    fn req_exec_comb_orderinput(&self, reqid: i32, req: &CtpReqInputExecCombineOrderField) -> crate::Result<()>;
}
