import { <PERSON>, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { TimeSelect, TimeItem } from "../widgets/time.slint";
import { ExportButton } from "../widgets/export.slint";

export global StkTrd_QryOrder {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> insid;
    in_out property <string> ordstatus;
    in_out property <string> ordersysid;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);
    callback export_clicked(/* type */ int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

export component QryOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkTrd_QryOrder.exchid;
        i_le_insid.text = StkTrd_QryOrder.insid;
        i_cbx_ordstatus.current_text = StkTrd_QryOrder.ordstatus;
        i_le_ordsysid.text = StkTrd_QryOrder.ordersysid;
        i_ts_start.time = StkTrd_QryOrder.starttime;
        i_ts_end.time = StkTrd_QryOrder.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text: StkTrd_QryOrder.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }

            i_le_insid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: StkTrd_QryOrder.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "报单状态";
                vertical_alignment: center;
            }

            i_cbx_ordstatus := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text: StkTrd_QryOrder.ordstatus;
                model: CommonModel.orderstatus_model;
            }

            Label {
                text: "报单编码";
                vertical_alignment: center;
            }

            i_le_ordsysid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: StkTrd_QryOrder.ordersysid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "发生时间";
                vertical_alignment: center;
            }

            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: StkTrd_QryOrder.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: StkTrd_QryOrder.endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                    StkTrd_QryOrder.exchid = i_cbx_exch.current_text;
                    StkTrd_QryOrder.insid = i_le_insid.text;
                    StkTrd_QryOrder.ordstatus = i_cbx_ordstatus.current_text;
                    StkTrd_QryOrder.ordersysid = i_le_ordsysid.text;
                    StkTrd_QryOrder.starttime = i_ts_start.time;
                    StkTrd_QryOrder.endtime = i_ts_end.time;

                    StkTrd_QryOrder.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    StkTrd_QryOrder.export_clicked(type);
                }
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 2,
                columns: [
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    { title: @tr("金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    { title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    { title: @tr("报单状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("报单价格条件"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_pricetype },
                    { title: @tr("有效期类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_timecondition },
                    { title: @tr("成交量类型"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_volumecondition },
                    { title: @tr("剩余数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                ]
            };
            row_count: StkTrd_QryOrder.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_QryOrder.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_QryOrder.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_QryOrder.row_data[row_index]);
                }
            }
        }

        if StkTrd_QryOrder.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkTrd_QryOrder.page_index_model;
            item_total <=> StkTrd_QryOrder.item_total;
            page_total <=> StkTrd_QryOrder.page_total;
            page_index <=> StkTrd_QryOrder.page_index;
            page_size <=> StkTrd_QryOrder.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryOrder.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryOrder.page_size_changed(size);
            }
        }
    }
}
