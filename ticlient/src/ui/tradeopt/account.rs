use std::{
    ops::Sub,
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
};

use crate::{
    common::{config::Config, global::TRADE_OPT_API, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::account_trade_ctp::{self, CtpAccountField};

type CtpAccountMap = dashmap::DashMap<String, CtpAccountField>;

/// 资金
pub(super) struct Account {
    /// 是否有汇总资金数据
    has_summary: AtomicBool,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpAccountMap>,
}

impl Account {
    pub fn new() -> Self {
        Self {
            has_summary: AtomicBool::new(false),
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpAccountMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_acc = app.global::<OptTrd_Account>();

        let app_weak = app.as_weak();
        app_acc.on_sort_ascending(move |column_index| Account::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_sort_descending(move |column_index| Account::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_get_row_data_color(move |row_index, column_index, data| {
            Account::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_acc.on_req_fund_trans(move |ft| Account::on_req_fund_trans(&app_weak, ft));

        log::trace!("Account init completed");
    }
}

impl Account {
    /// 请求查询资金信息
    pub fn qry_account(&self) {
        let req = account_trade_ctp::CtpReqQryTradingAccountField::default();

        let api = TRADE_OPT_API.get().unwrap().read().unwrap();

        if let Err(err) = api.req_qry_tradingaccount(0, &req) {
            log::warn!("req_qry_tradingaccount failed. {}", err);
        }

        let req = account_trade_ctp::CtpReqQryTradingAccountExField::default();
        if let Err(err) = api.req_qry_tradingaccountex(0, &req) {
            log::warn!("req_qry_tradingaccountex failed. {}", err);
        }
    }

    /// 响应查询资金信息
    pub fn on_rsp_qry_acc(&self, acc: CtpAccountField) {
        if acc.BrokerID.is_empty() && 1 == acc.SettlementID {
            return; // 汇总资金如果只由一条详情汇总而来则不用显示(显示详情即可, 为兼容老版本(无本字段即为0)判断条件为=1)
        }

        if !self.has_summary.load(Ordering::Relaxed) {
            if acc.BrokerID.is_empty() {
                self.has_summary.store(true, Ordering::Relaxed);
            }
        }

        let mut insert_flag = true;
        let key = std::format!("{}{}", acc.BrokerID, acc.AccountID);

        if self.data_map.contains_key(&key) {
            let preacc = self.data_map.get(&key).unwrap();
            if acc == *preacc {
                insert_flag = false;
            }
        }

        if insert_flag {
            self.data_map.insert(key, acc);
            self.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

impl Account {
    /// 资金划转
    fn on_req_fund_trans(app_weak: &Weak<App>, ft: FundTransField) {
        let app = app_weak.unwrap();

        let mut errmsg = String::new();

        let mut req = account_trade_ctp::CtpReqFundTransferField::default();
        req.AccountID = ft.accountid.as_str().into();
        req.Passwd = ft.password.as_str().into();
        req.OutSys = ft.outsys;
        req.InSys = ft.insys;
        req.Amount = -1f64;
        let ret = ft.amount.trim().parse::<f64>();
        if let Ok(amt) = ret {
            req.Amount = amt;
        } else {
            errmsg = ret.err().unwrap().to_string();
        }

        // 参数检查
        {
            if 1 != req.OutSys && 2 != req.OutSys && 9 != req.OutSys {
                show_msg_box(
                    &app,
                    3,
                    Default::default(),
                    slint::format!("不支持的出金系统. {}", req.OutSys),
                );
                return;
            }

            if 1 != req.InSys && 2 != req.InSys && 9 != req.InSys {
                show_msg_box(&app, 3, Default::default(), slint::format!("不支持的入金系统. {}", req.InSys));
                return;
            }

            if req.OutSys == req.InSys {
                show_msg_box(&app, 3, Default::default(), "出金系统不能与入金系统相同".into());
                return;
            }

            if req.Amount <= 0f64 || 0.005.sub(req.Amount) > 0.000001 {
                show_msg_box(
                    &app,
                    3,
                    Default::default(),
                    slint::format!(
                        "请输入正确的划转金额.\n输入值:{}, 解析值:{}\n错误信息: {}",
                        ft.amount,
                        req.Amount,
                        errmsg
                    ),
                );
                return;
            }
        }

        let mut rsp = account_trade_ctp::CtpRspFundTransferField::default();
        {
            let api = TRADE_OPT_API.get().unwrap().read().unwrap();
            if let Err(err) = api.req_fundtrans(&req, &mut rsp) {
                show_msg_box(&app, 3, Default::default(), slint::format!("请求资金划转失败.\n\n{}", err));
                return;
            }
        }

        let mut src_success_cnt = 0;
        let mut dst_success_cnt = 0;
        let all_cnt = rsp.Arr.len() as i32;
        rsp.Arr.iter().for_each(|ret| {
            if 1 == ret.status {
                if !ret.sourceclusterid.is_empty() {
                    src_success_cnt += 1;
                }
                if !ret.destclusterid.is_empty() {
                    dst_success_cnt += 1;
                }
            } else {
                if ret.sourceclusterid.is_empty() {
                    errmsg += std::format!("\n节点:{}, 错误信息:{}", ret.destclusterid, ret.responsestr).as_str();
                } else {
                    errmsg += std::format!("\n节点:{}, 错误信息:{}", ret.sourceclusterid, ret.responsestr).as_str();
                }
            }
        });

        if src_success_cnt + dst_success_cnt == all_cnt && src_success_cnt >= 1 && dst_success_cnt >= 1 {
            show_msg_box(&app, 1, Default::default(), "资金划转成功".into());
        } else {
            if 0 == src_success_cnt && 0 == dst_success_cnt {
                show_msg_box(&app, 3, Default::default(), slint::format!("请求资金划转失败\n\n{}", errmsg));
            } else {
                show_msg_box(
                    &app,
                    2,
                    Default::default(),
                    slint::format!(
                        "请求资金划转部分成功({}/{}).\n\n{}",
                        src_success_cnt + dst_success_cnt,
                        all_cnt,
                        errmsg
                    ),
                );
            }
        }
    }

    /// 升序
    fn on_sort_ascending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<OptTrd_Account>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<OptTrd_Account>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        if let Ok(var) = data.replace(",", "").parse::<f64>() {
            if var >= 0. {
                return nc.get_normal();
            }
            return nc.get_error();
        }

        nc.get_default()
    }
}

impl Account {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                self.data_map.clone()
            } else {
                Arc::new(CtpAccountMap::new())
            }
        };
        if data_map_clone.is_empty() {
            return;
        }
        let has_summary = self.has_summary.load(Ordering::Relaxed);

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            let app_com = app.global::<OptTrd_Com>();
            data_map_clone.iter().for_each(|acc| {
                let defaccid = app_com.get_accountid();
                if defaccid.is_empty() {
                    app_com.set_accountid(acc.AccountID.clone().into());
                }

                let exchid = TIConvert::exch_name_by_str(&acc.BrokerID);

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 交易所
                items.push(exchid.into());

                // 资金账号
                items.push(acc.AccountID.clone().into());

                // 动态权益
                items.push(TIConvert::format_f(acc.Balance, Config::AMT_DIGITS).into());

                // 市值权益
                items.push(TIConvert::format_f(acc.ValueBalance, Config::AMT_DIGITS).into());

                // 可用资金
                items.push(TIConvert::format_f(acc.Available, Config::AMT_DIGITS).into());

                // 实收保证金
                items.push(TIConvert::format_f(acc.CurrMargin, Config::AMT_DIGITS).into());

                // 交易所保证金
                items.push(TIConvert::format_f(acc.ExchangeMargin, Config::AMT_DIGITS).into());

                // 冻结保证金
                items.push(TIConvert::format_f(acc.FrozenMargin, Config::AMT_DIGITS).into());

                // 冻结资金
                items.push(TIConvert::format_f(acc.FrozenCash, Config::AMT_DIGITS).into());

                // 冻结手续费
                items.push(TIConvert::format_f(acc.FrozenCommission, Config::AMT_DIGITS).into());

                // 权利金收支
                items.push(TIConvert::format_f(acc.CashIn, Config::AMT_DIGITS).into());

                // 手续费
                items.push(TIConvert::format_f(acc.Commission, Config::AMT_DIGITS).into());

                // 入金
                items.push(TIConvert::format_f(acc.Deposit, Config::AMT_DIGITS).into());

                // 出金
                items.push(TIConvert::format_f(acc.Withdraw, Config::AMT_DIGITS).into());

                // 平仓盈亏
                items.push(TIConvert::format_f(acc.CloseProfit, Config::AMT_DIGITS).into());

                // 上次结算准备金
                items.push(TIConvert::format_f(acc.PreBalance, Config::AMT_DIGITS).into());

                // 资金划转
                items.push(if has_summary {
                    if acc.BrokerID.is_empty() {
                        "资金划转".into()
                    } else {
                        "".into()
                    }
                } else {
                    "资金划转".into()
                });

                row_data.push(items.into());
            });

            let app_acc = app.global::<OptTrd_Account>();

            let asc_column_index = app_acc.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_acc.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            app_acc.set_row_data(row_data.into());
        });
    }
}
