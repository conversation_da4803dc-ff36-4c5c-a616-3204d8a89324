import { Theme, MessageBox, MBButtons, MBIcon, MBResult, Label, ScrollView, Fonts } from "@zstk/lib.slint";

import { AppResult, AppCom, AppInerArgs, AppStatusStrip, AppAbout, EAppType, ENotifyType, NotifyColor, TradeColor, EOS, ERenderer } from "struct.slint";
export { AppResult, AppCom, AppInerArgs, AppStatusStrip, AppAbout, EAppType, ENotifyType, NotifyColor, TradeColor }

import { LoginForm } from "login.slint";
export { LoginForm }

import { MarketDataDtl } from "md/mdstruct.slint";
export { MarketDataDtl }

import {
    StkTrd_Com, StkTrd_TabSelIdx, StkTrd_Setting,
    StkTrd_MarketData,
    StkTrd_Account, StkTrd_Position, StkTrd_CreditContract, StkTrd_CreditLimitAmt, StkTrd_CreditLimitPos, StkTrd_CreditTcAmt, StkTrd_CreditTcPos,
    StkTrd_CancelTip, StkTrd_InOrder, StkTrd_InFjyOrder,
    StkTrd_QryOrder, StkTrd_QryTrade, StkTrd_QryFjyOrder, StkTrd_QryFjyTrade, StkTrd_QryWithdrawDeposit, StkTrd_QryPosTrans, StkTrd_QryFundTransDtl, StkTrd_PositionTransDtl, StkTrd_QryInstrument, StkTrd_QryCreditReAmtDtl,
    StkTrd_MonOrderInsertCancel, StkTrd_MonConvertBonds, StkTrd_MonSelfTrade, StkTrd_MonSelfTradeDtl, StkTrd_CreditConcentration,
    StkTrd_InputTip, StkTrd_InputOrder, StkTrd_InputOrderCredit, StkTrd_InputFjyOrder
} from "tradestk/tradestk.slint";
export {
    StkTrd_Com, StkTrd_TabSelIdx, StkTrd_Setting,
    StkTrd_MarketData,
    StkTrd_Account, StkTrd_Position, StkTrd_CreditContract, StkTrd_CreditLimitAmt, StkTrd_CreditLimitPos, StkTrd_CreditTcAmt, StkTrd_CreditTcPos,
    StkTrd_CancelTip, StkTrd_InOrder, StkTrd_InFjyOrder,
    StkTrd_QryOrder, StkTrd_QryTrade, StkTrd_QryFjyOrder, StkTrd_QryFjyTrade, StkTrd_QryWithdrawDeposit, StkTrd_QryPosTrans, StkTrd_QryFundTransDtl, StkTrd_PositionTransDtl, StkTrd_QryInstrument, StkTrd_QryCreditReAmtDtl,
    StkTrd_MonOrderInsertCancel, StkTrd_MonConvertBonds, StkTrd_MonSelfTrade, StkTrd_MonSelfTradeDtl, StkTrd_CreditConcentration,
    StkTrd_InputTip, StkTrd_InputOrder, StkTrd_InputOrderCredit, StkTrd_InputFjyOrder
}

import {
    FutTrd_Com, FutTrd_TabSelIdx, FutTrd_Setting,
    FutTrd_MarketData,
    FutTrd_Account, FutTrd_Position,
    FutTrd_CancelTip, FutTrd_InOrder, FutTrd_InExercise,
    FutTrd_QryOrder, FutTrd_QryTrade, FutTrd_QryExercise,
    FutTrd_MonOrderInsertCancel, FutTrd_MonLimitPosition, FutTrd_MonLimitOpen, FutTrd_MonSelfTrade, FutTrd_MonSelfTradeDtl,
    FutTrd_InputTip, FutTrd_InputOrder, FutTrd_InputExercise
} from "tradefut/tradefut.slint";
export {
    FutTrd_Com, FutTrd_TabSelIdx, FutTrd_Setting,
    FutTrd_MarketData,
    FutTrd_Account, FutTrd_Position,
    FutTrd_CancelTip, FutTrd_InOrder, FutTrd_InExercise,
    FutTrd_QryOrder, FutTrd_QryTrade, FutTrd_QryExercise,
    FutTrd_MonOrderInsertCancel, FutTrd_MonLimitPosition, FutTrd_MonLimitOpen, FutTrd_MonSelfTrade, FutTrd_MonSelfTradeDtl,
    FutTrd_InputTip, FutTrd_InputOrder, FutTrd_InputExercise
}

import {
    OptTrd_Com, OptTrd_TabSelIdx, OptTrd_Setting,
    OptTrd_MarketData,
    OptTrd_Account, OptTrd_Position, OptTrd_PositionComb,
    OptTrd_CancelTip, OptTrd_InOrder, OptTrd_InExercise, OptTrd_InExerciseComb, OptTrd_InQuote,
    OptTrd_QryOrder, OptTrd_QryTrade, OptTrd_QryOml, OptTrd_QryExercise, OptTrd_QryExerciseComb, OptTrd_QryQuote, OptTrd_QryWithdrawDeposit, OptTrd_QryInstrument, OptTrd_QryFundTransDtl,
    OptTrd_MonTrdPosOrdInsertCancel, OptTrd_MonLimitAmount, OptTrd_MonLimitPosition, OptTrd_MonSelfTrade, OptTrd_MonSelfTradeDtl,
    OptTrd_InputTip, OptTrd_InputOrder, OptTrd_InputOml, OptTrd_InputExercise, OptTrd_InputStockLock, OptTrd_InputQuote
} from "tradeopt/tradeopt.slint";
export {
    OptTrd_Com, OptTrd_TabSelIdx, OptTrd_Setting,
    OptTrd_MarketData,
    OptTrd_Account, OptTrd_Position, OptTrd_PositionComb,
    OptTrd_CancelTip, OptTrd_InOrder, OptTrd_InExercise, OptTrd_InExerciseComb, OptTrd_InQuote,
    OptTrd_QryOrder, OptTrd_QryTrade, OptTrd_QryOml, OptTrd_QryExercise, OptTrd_QryExerciseComb, OptTrd_QryQuote, OptTrd_QryWithdrawDeposit, OptTrd_QryInstrument, OptTrd_QryFundTransDtl,
    OptTrd_MonTrdPosOrdInsertCancel, OptTrd_MonLimitAmount, OptTrd_MonLimitPosition, OptTrd_MonSelfTrade, OptTrd_MonSelfTradeDtl,
    OptTrd_InputTip, OptTrd_InputOrder, OptTrd_InputOml, OptTrd_InputExercise, OptTrd_InputStockLock, OptTrd_InputQuote
}

import {
    StkRsk_Com,
    RiskStkCreditDetailPage,
    StkRskCredit_Com,
    StkRskCredit_Account, StkRskCredit_Position, StkRskCredit_GaCha,
    StkRskCredit_CreditContract, StkRskCredit_CreditLimitAmt, StkRskCredit_CreditLimitPos, StkRskCredit_CreditTcAmt, StkRskCredit_CreditTcPos, StkRskCredit_CreditConcentration,
    StkRskCredit_QryCreditReAmtDtl, StkRskCredit_QryWithdrawDeposit, StkRskCredit_QryCreditLimit, StkRskCredit_QryCreditTcAmt
} from "riskstk/riskstk.slint";
export {
    StkRsk_Com,
    RiskStkCreditDetailPage,
    StkRskCredit_Com,
    StkRskCredit_Account, StkRskCredit_Position, StkRskCredit_GaCha,
    StkRskCredit_CreditContract, StkRskCredit_CreditLimitAmt, StkRskCredit_CreditLimitPos, StkRskCredit_CreditTcAmt, StkRskCredit_CreditTcPos, StkRskCredit_CreditConcentration,
    StkRskCredit_QryCreditReAmtDtl, StkRskCredit_QryWithdrawDeposit, StkRskCredit_QryCreditLimit, StkRskCredit_QryCreditTcAmt
}

import { MainTradeStkUI } from "tradestk.slint";
import { MainTradeFutUI } from "tradefut.slint";
import { MainTradeOptUI } from "tradeopt.slint";
import { MainRiskStkUI } from "riskstk.slint";

 export component App inherits Window {

    in_out property <EAppType> app_type: EAppType.Unknown;
    private property<{family: string, size: length, weight: int}> default_font;

    preferred_width: 1480px;
    preferred_height: 900px;
    title: get_title();
    icon: @image_url("resource/logo.png");
    background: Theme.background;

    public function set_font(os: EOS, renderer: ERenderer) {
        default_font = AppCom.get_default_font(os, renderer);
        root.default-font-family = default_font.family;
        root.default-font-size = default_font.size;
        root.default-font-weight = default_font.weight;
    }

    function get_title() -> string {
        if EAppType.StkOpt == app_type || EAppType.Stk == app_type || EAppType.StkCredit == app_type || EAppType.Fut == app_type {
            return "EaseRisk";
        }
        if EAppType.StkOptTrd == app_type || EAppType.StkTrd == app_type || EAppType.StkCreditTrd == app_type || EAppType.FutTrd == app_type {
            return "EaseTrader";
        }
        "ticlient"
    }

    ScrollView {
        interactive: false;
        HorizontalLayout {  // bug: 目前版本中如果不加这一层就不能自动推导 viewport_width,viewport_height
            Rectangle {
                min_height: 900px;
                min_width: 1480px;

                if EAppType.StkOpt == app_type: Rectangle {
                    Label {
                        font_size: 48 * 0.0769rem;
                        text: @tr("StkOpt not implemented");
                    }
                }

                if EAppType.StkOptTrd == app_type: MainTradeOptUI { }

                if EAppType.Stk == app_type || EAppType.StkCredit == app_type: MainRiskStkUI { }

                if EAppType.StkTrd == app_type || EAppType.StkCreditTrd == app_type: MainTradeStkUI { }

                if EAppType.Fut == app_type: Rectangle {
                    Label {
                        font_size: 48 * 0.0769rem;
                        text: @tr("Fut not implemented");
                    }
                }

                if EAppType.FutTrd == app_type: MainTradeFutUI { }

                if EAppType.Unknown == app_type: Rectangle {
                    Label {
                        font_size: 48 * 0.0769rem;
                        text: @tr("Unknown app type");
                    }
                }

                if AppCom.app_ret.state > 0: Rectangle {
                    init => {
                        if 1 == AppCom.app_ret.state {
                            i_msgbox.open_title(AppCom.app_ret.title, AppCom.app_ret.msg, MBButtons.Ok, MBIcon.Info);
                        } else if 2 == AppCom.app_ret.state {
                            i_msgbox.open_title(AppCom.app_ret.title, AppCom.app_ret.msg, MBButtons.Ok, MBIcon.Warning);
                        } else if 3 == AppCom.app_ret.state {
                            i_msgbox.open_title(AppCom.app_ret.title, AppCom.app_ret.msg, MBButtons.Ok, MBIcon.Error);
                        } else if 4 == AppCom.app_ret.state {
                            i_msgbox.open_title(AppCom.app_ret.title, AppCom.app_ret.msg, MBButtons.Ok, MBIcon.None);
                        }
                        AppCom.app_ret = { state: 0, title: "", msg: "" };
                    }
                }
                i_msgbox := MessageBox {
                    width: root.width;
                    height: root.height;

                    clicked(id, ret) => {
                        self.close();
                    }
                }

                if AppCom.app_close.state > 0: Rectangle {
                    init => {
                        if 100 == AppCom.app_close.state {
                            i_msgbox_close.id = AppCom.app_close.state;
                            i_msgbox_close.open_title(AppCom.app_close.title, AppCom.app_close.msg, MBButtons.Ok, MBIcon.Error);
                        } else if 101 == AppCom.app_close.state {
                            i_msgbox_close.id = AppCom.app_close.state;
                            i_msgbox_close.open_title(AppCom.app_close.title, AppCom.app_close.msg, MBButtons.YesNo, MBIcon.Question);
                            AppCom.app_close = { state: 0, title:"", msg: "" };
                        }
                    }
                }
                i_msgbox_close := MessageBox {
                    width: root.width;
                    height: root.height;

                    clicked(id, ret) => {
                        if (101 == id && MBResult.Yes == ret) || 100 == id {
                            AppCom.close(id);
                        }
                        self.close();
                    }
                }
            }
        }
    }
}
