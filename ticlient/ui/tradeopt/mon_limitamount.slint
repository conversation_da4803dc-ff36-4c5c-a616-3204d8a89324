import { Fonts, Label, ComboBox, TableView } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";

export global OptTrd_MonLimitAmount {
    in property <[[string]]> row_data;

    in_out property <string> exchid;

    callback filter_changed();
    pure callback get_row_data_color(int, int, string) -> brush;

    public function get_cell_data(row_index: int, column_index: int) -> string {
        if row_index < row_data.length && column_index < 6 {
            row_data[row_index][column_index]
        }
        else{
            ""
        }
    }
}

export component MonLimitAmountPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_MonLimitAmount.exchid;
                model: CommonModel.exch_opt_model;

                selected => {
                    OptTrd_MonLimitAmount.filter_changed();
                }
            }

            Rectangle { }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("权利仓最大权利金"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_130 },
                    { title: @tr("权利仓当前权利金"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_130 },
                    { title: @tr("剩余额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    { title: @tr("占用比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                ]
            };
            row_count: OptTrd_MonLimitAmount.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_MonLimitAmount.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                OptTrd_MonLimitAmount.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_MonLimitAmount.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_MonLimitAmount.row_data[row_index]);
                }
            }
        }
    }
}
