use serde::{Deserialize, Serialize};

/// 将结构体序列化成字符串
pub(crate) fn to_string<T: ?Sized + Serialize>(t: &T) -> crate::Result<String> {
    let ret = serde_json::to_string(&t);
    if ret.is_err() {
        return Err(format!("{}", ret.err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
}

/// 将字符串反序列化成结构体
pub(crate) fn from_str<'a, T: Deserialize<'a>>(s: &'a str) -> crate::Result<T> {
    let ret = serde_json::from_str(s);
    if ret.is_err() {
        return Err(format!("{}", ret.err().unwrap().to_string()));
    }

    Ok(ret.unwrap())
}
