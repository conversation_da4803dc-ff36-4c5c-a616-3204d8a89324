use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        config::Config,
        ctptype::CTPType,
        global::{CFG, INS, MD_API, TOKIO_RT, TRADE_OPT_API},
        instrument::Instrument,
        ticonvert::TIConvert,
    },
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{CtpReqInputQuoteField, CtpRspInputQuoteField};

use super::tradeopt::show_input_msg_box;

/// 报价录入数据
struct InputQuoteDatas {
    /******************************************************************************************************************/
    /// 在报价过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputquote: AtomicBool,

    /// 报价响应数据
    rsp_inputquote: Arc<Mutex<CtpRspInputQuoteField>>,

    /// 输入的合约是否有改变
    input_instid_changed: AtomicBool,
}
impl InputQuoteDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputquote: AtomicBool::new(false),
            rsp_inputquote: Arc::new(Mutex::new(CtpRspInputQuoteField::default())),
            input_instid_changed: AtomicBool::new(false),
        }
    }
}
static INPUTQUOTE_DATAS: OnceLock<InputQuoteDatas> = OnceLock::new();

/// 报价录入
pub(super) struct InputQuote {}

/// 构建与初始化
impl InputQuote {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化报价录入数据
        let _ = INPUTQUOTE_DATAS.set(InputQuoteDatas::new());

        let app_inputquote = app.global::<OptTrd_InputQuote>();

        // 添加合约
        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
        let all_insid = { INS.get().unwrap().read().unwrap().get_opt_id_all_with_name() };
        all_insid.iter().for_each(|it| {
            items.push(crate::slintui::LineItem {
                text: slint::format!("{}", it.key()),
                remark: slint::format!("{}", it.value().1),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(items.sort_by(move |r_a, r_b| r_a.text.cmp(&r_b.text)));
        app_inputquote.set_insid_model(sort_model.clone().into());
        app_inputquote.set_all_insid_model(sort_model.into());

        // 注册合约改变事件
        let app_weak = app.as_weak();
        app_inputquote.on_insid_text_changed(move || InputQuote::on_insid_text_changed(&app_weak));

        // 注册价格改变事件
        let app_weak = app.as_weak();
        app_inputquote.on_price_text_changed(move |is_bid| InputQuote::on_price_text_changed(&app_weak, is_bid));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputquote.on_volume_text_changed(move |is_bid| InputQuote::on_volume_text_changed(&app_weak, is_bid));

        // 注册价格上下调整时的事件
        let app_weak = app.as_weak();
        app_inputquote.on_bid_price_updown_changed(move |upflag| InputQuote::on_bid_price_updown_changed(&app_weak, upflag));

        // 注册价格上下调整时的事件
        let app_weak = app.as_weak();
        app_inputquote.on_ask_price_updown_changed(move |upflag| InputQuote::on_ask_price_updown_changed(&app_weak, upflag));

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputquote
            .on_volume_updown_changed(move |upflag, is_bid| InputQuote::on_volume_updown_changed(&app_weak, upflag, is_bid));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputquote.on_req_tip_toggled(move |checked| InputQuote::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputquote.on_ok_clicked(move |need_confirm| InputQuote::on_ok_clicked(&app_weak, need_confirm));

        log::trace!("Input quote init completed");
    }

    /// 报单响应
    pub fn on_rsp_inputquote(&self, rsp: CtpRspInputQuoteField) {
        let inputquote_datas = INPUTQUOTE_DATAS.get().unwrap();
        *inputquote_datas.rsp_inputquote.lock().unwrap() = rsp;
        inputquote_datas.is_err_inputquote.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl InputQuote {
    /// 点击行情详情的价格
    pub fn on_price_clicked(app_weak: &Weak<App>, exchid: SharedString, insid: SharedString, price: SharedString, ptype: i32) {
        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        let tmp_insid = slint::format!("{}|{}", insid, exchid);

        if exchid != app_inputquote.get_exchid()
            || (insid != app_inputquote.get_insid() && tmp_insid != app_inputquote.get_insid())
        {
            app_inputquote.set_exchid(exchid);
            app_inputquote.set_insid(insid);
            InputQuote::on_insid_text_changed(app_weak);
        }

        if 1 == ptype {
            app_inputquote.set_bid_price(price.to_string().into());
            app_inputquote.set_bid_price_has_error(false);
        } else if 2 == ptype {
            app_inputquote.set_ask_price(price.to_string().into());
            app_inputquote.set_ask_price_has_error(false);
        }
    }

    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputquote_datas = INPUTQUOTE_DATAS.get().unwrap();
        inputquote_datas.input_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        let insid = app_inputquote.get_insid();
        let ins = { INS.get().unwrap().read().unwrap().get_ins_by_insid(&insid) };

        // 合约名称
        let mut insname: String = match insid.is_empty() {
            true => "".to_owned(),
            false => "无效合约".to_owned(),
        };

        if let Some(ins) = ins {
            app_inputquote.set_insid_has_error(false);

            insname = if !ins.symbol.is_empty() {
                ins.symbol.clone()
            } else {
                ins.name.clone()
            };

            // 设置交易所编码
            let eid = app_inputquote.get_exchid();
            if eid.as_str() != ins.exchname.as_str() {
                app_inputquote.set_exchid(ins.exchname.clone().into());
            }

            // 设置价格
            app_inputquote.set_bid_price(ins.lowlmtprice.to_string().into());
            app_inputquote.set_ask_price(ins.uplmtprice.to_string().into());
            app_inputquote.set_bid_price_has_error(false);
            app_inputquote.set_ask_price_has_error(false);

            // 设置价格调整的参数
            app_inputquote.set_price_dig(ins.ptw);
            app_inputquote.set_price_step(ins.pricetick as f32);

            // 订阅行情
            let app_mddtl = app.global::<MarketDataDtl>();
            let cur_key = std::format!("{}{}", ins.exchname, ins.insid);
            let pre_key = app_mddtl.get_pre_key().to_string();
            if cur_key != pre_key {
                let insname = if !ins.symbol.is_empty() {
                    ins.symbol.clone()
                } else {
                    ins.name.clone()
                };

                app_mddtl.invoke_reset_by_sel_ins(
                    ins.exchname.clone().into(),
                    ins.insid.clone().into(),
                    insname.into(),
                    TIConvert::format_f(ins.uplmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.lowlmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.prestlprice, ins.ptw as usize).into(),
                );

                let sub_insid = ins.insid.clone();
                TOKIO_RT.get().unwrap().spawn(async move {
                    let _ = MD_API.get().unwrap().read().await.req_subscribe(&sub_insid).await;
                });
            }
        } else {
            app_inputquote.set_exchid("".into());
            app_inputquote.set_insid_has_error(true);
        }

        if *insname != *app_inputquote.get_insname() {
            app_inputquote.set_insname(insname.into());
        }

        true
    }

    /// 价格有变动
    fn on_price_text_changed(app_weak: &Weak<App>, is_bid: bool) -> bool {
        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        let price = if is_bid {
            app_inputquote.get_bid_price()
        } else {
            app_inputquote.get_ask_price()
        };

        let price = price.parse::<f64>().unwrap_or_default();

        if is_bid {
            app_inputquote.set_bid_price_has_error(price <= Config::INVALID_PRICE)
        } else {
            app_inputquote.set_ask_price_has_error(price <= Config::INVALID_PRICE)
        };

        true
    }

    /// 数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>, is_bid: bool) -> bool {
        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        let volume = if is_bid {
            app_inputquote.get_bid_volume()
        } else {
            app_inputquote.get_ask_volume()
        };

        let has_error = match volume.trim().parse::<i32>() {
            Ok(vol) if vol >= 0 => false,
            _ => true,
        };

        if is_bid {
            app_inputquote.set_bid_volume_has_error(has_error)
        } else {
            app_inputquote.set_ask_volume_has_error(has_error)
        };

        true
    }

    // 报单价格上下调整时的事件
    fn on_bid_price_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        if app_inputquote.get_bid_price_has_error() {
            return;
        }

        let price_str = app_inputquote.get_bid_price().replace(",", "");
        let mut price = price_str.parse::<f64>().unwrap_or_default();
        if price <= Config::INVALID_PRICE {
            return;
        }

        if upflag {
            price += app_inputquote.get_price_step() as f64;
        } else {
            price -= app_inputquote.get_price_step() as f64;
            if price <= Config::INVALID_PRICE {
                return;
            }
        }

        app_inputquote.set_bid_price(slint::format!("{:.*}", app_inputquote.get_price_dig() as usize, price));
    }

    // 报单价格上下调整时的事件
    fn on_ask_price_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        if app_inputquote.get_ask_price_has_error() {
            return;
        }

        let price_str = app_inputquote.get_ask_price().replace(",", "");
        let mut price = price_str.parse::<f64>().unwrap_or_default();
        if price <= 0.0 {
            return;
        }

        if upflag {
            price += app_inputquote.get_price_step() as f64;
        } else {
            price -= app_inputquote.get_price_step() as f64;
            if price <= 0.0 {
                return;
            }
        }

        app_inputquote.set_ask_price(slint::format!("{:.*}", app_inputquote.get_price_dig() as usize, price));
    }

    // 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool, is_bid: bool) {
        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        let volume_str = if is_bid {
            if app_inputquote.get_bid_volume_has_error() {
                app_inputquote.set_bid_volume("1".into());
                app_inputquote.set_bid_volume_has_error(false);
                return;
            }
            app_inputquote.get_bid_volume().replace(",", "")
        } else {
            if app_inputquote.get_ask_volume_has_error() {
                app_inputquote.set_ask_volume("1".into());
                app_inputquote.set_ask_volume_has_error(false);
                return;
            }
            app_inputquote.get_ask_volume().replace(",", "")
        };
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume < 0 {
            return;
        }

        if upflag {
            volume += 1;
        } else {
            volume -= 1;
            if volume < 0 {
                return;
            }
        }

        if is_bid {
            app_inputquote.set_bid_volume(slint::format!("{}", volume));
        } else {
            app_inputquote.set_ask_volume(slint::format!("{}", volume));
        }
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();
        app_set.set_trdreqtip_inputquote(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputQuote = checked;
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputquote = app.global::<OptTrd_InputQuote>();

        // 获取参数
        let accid = app_inputquote.get_accountid();
        let exchid = app_inputquote.get_exchid();
        let insid = {
            let id = app_inputquote.get_insid();
            Instrument::parse_id(&id).0
        };
        let bid_offset = app_inputquote.get_bid_offset();
        let bid_price = app_inputquote.get_bid_price();
        let bid_volume = app_inputquote.get_bid_volume().trim().replace(",", "");
        let ask_offset = app_inputquote.get_ask_offset();
        let ask_price = app_inputquote.get_ask_price();
        let ask_volume = app_inputquote.get_ask_volume().trim().replace(",", "");

        // 输入参数检查
        {
            if accid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "账户不能为空".into());
                return;
            }

            if insid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约信息失败".into());
                return;
            }

            if exchid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约所在交易所失败".into());
                return;
            }

            if bid_price.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "买方向的价格不能为空".into());
                return;
            }

            if bid_volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "买方向的数量不能为空".into());
                return;
            }

            if ask_price.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "卖方向的价格不能为空".into());
                return;
            }

            if ask_volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "卖方向的数量不能为空".into());
                return;
            }
        }

        // 数量
        let (bid_volume, ask_volume) = {
            let bid_volume = match bid_volume.trim().parse::<i32>() {
                Ok(vol) if vol >= 0 => vol,
                _ => {
                    show_input_msg_box(&app, 1, Default::default(), "请输入买方向的有效数量".into());
                    return;
                }
            };

            let ask_volume = match ask_volume.trim().parse::<i32>() {
                Ok(vol) if vol >= 0 => vol,
                _ => {
                    show_input_msg_box(&app, 1, Default::default(), "请输入卖方向的有效数量".into());
                    return;
                }
            };

            (bid_volume, ask_volume)
        };

        // 价格
        let (bid_price, ask_price) = {
            let bid_price = bid_price.trim().parse::<f64>().unwrap_or_default();
            if bid_price <= Config::INVALID_PRICE {
                show_input_msg_box(&app, 1, Default::default(), "请输入买方向的有效价格".into());
                return;
            }

            let ask_price = ask_price.trim().parse::<f64>().unwrap_or_default();
            if ask_price <= Config::INVALID_PRICE {
                show_input_msg_box(&app, 1, Default::default(), "请输入卖方向的有效价格".into());
                return;
            }

            if bid_volume > 0 && ask_volume > 0 {
                if bid_price > ask_price {
                    show_input_msg_box(&app, 1, Default::default(), "卖出价格必须大于买入价格".into());
                    return;
                }
            }

            (bid_price, ask_price)
        };

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputQuote } {
            show_input_msg_box(
                &app,
                100,
                slint::format!("报价确认"),
                slint::format!(
                    "资金账户:{}\n\n交易所:{}    合约:{}\n\n买入方向:{}, 价格:{}, 数量:{}\n卖出方向:{}, 价格:{}, 数量:{}",
                    accid,
                    exchid,
                    insid,
                    if 0 == bid_offset { "开仓" } else { "平仓" },
                    bid_price,
                    bid_volume,
                    if 0 == ask_offset { "开仓" } else { "平仓" },
                    ask_price,
                    ask_volume
                ),
            );

            return;
        }

        let req = CtpReqInputQuoteField {
            AskHedgeFlag: CTPType::HEDGE_SPECULATION,
            BidHedgeFlag: CTPType::HEDGE_SPECULATION,
            InvestorID: accid.to_string(),
            InstrumentID: insid.to_string(),
            BidVolume: bid_volume,
            AskVolume: ask_volume,
            BidPrice: bid_price,
            AskPrice: ask_price,
            BidOffsetFlag: if 0 == bid_offset {
                CTPType::OC_OPEN
            } else {
                CTPType::OC_CLOSE
            },
            AskOffsetFlag: if 0 == ask_offset {
                CTPType::OC_OPEN
            } else {
                CTPType::OC_CLOSE
            },
            ..Default::default()
        };
        if let Err(err) = TRADE_OPT_API.get().unwrap().read().unwrap().req_quoteinput(0, &req) {
            show_input_msg_box(&app, 1, Default::default(), slint::format!("报价失败\n\n错误信息: {}", err));
        }
    }
}

/// 更新UI
impl InputQuote {
    // 更新响应信息
    async fn update_rspmsg(&self, app_weak: &Weak<App>) {
        let inputquote_datas = INPUTQUOTE_DATAS.get().unwrap();

        let is_err_inputquote = inputquote_datas
            .is_err_inputquote
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputquote {
            return;
        }

        let rsp = { inputquote_datas.rsp_inputquote.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_msg_box(
                &app,
                3,
                slint::format!("报价失败"),
                slint::format!("错误码:{}; 错误信息:{}", rsp.ec, rsp.em),
            );
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新响应信息
        self.update_rspmsg(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let inputquote_datas = INPUTQUOTE_DATAS.get().unwrap();
        if inputquote_datas
            .input_instid_changed
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok()
        {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_inputquote = app.global::<OptTrd_InputQuote>();

                let insid = app_inputquote.get_insid();

                if insid.len() > 8 {
                    // 目前个股期权单合约的编码长度还没有超过8位的
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    app_inputquote.set_insid_model(items.into());
                    return;
                }

                let all_insid_data = app_inputquote.get_all_insid_model();

                if insid.is_empty() {
                    app_inputquote.set_insid_model(all_insid_data);
                    return;
                }

                let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());

                let mut find_flag = false;
                for i in 0..all_insid_data.row_count() {
                    let li = all_insid_data.row_data(i).unwrap();
                    if li.text.starts_with(insid.as_str()) {
                        items.push(li);
                        find_flag = true;
                    } else {
                        if find_flag {
                            break;
                        }
                    }
                }

                app_inputquote.set_insid_model(items.into());
            });
        }
    }
}
