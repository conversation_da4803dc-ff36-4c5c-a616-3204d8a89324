import { Theme, TableView, TVColumnHeader, TVVisible, Fonts, Label, Radius, Icons, ComboBox } from "@zstk/lib.slint";
import { ColWidth, NotifyColor, AppCom, AppInerArgs } from "../../../struct.slint";
import { StkRskCredit_GaCha, StkRskCredit_Com } from "../struct.slint";

export component GaChaPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <length> header_exchid_width: AppInerArgs.show_detail ? ColWidth.exchid : 0.5px;
    private property <TVVisible> header_exchid_visible: AppInerArgs.show_detail ? TVVisible.Visible : TVVisible.Invisible;

    private property <TVColumnHeader> header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 2,
        fix_tail_count: 1,
        columns: [
            /*  0 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: header_exchid_width, visible: header_exchid_visible },
            /*  1 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
            /*  2 */{ title: @tr("总负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  3 */{ title: @tr("初始负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  4 */{ title: @tr("当日已还"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  5 */{ title: @tr("当日借出"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  6 */{ title: @tr("差额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "当日已还 - 当日借出" },
        ]
    };

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "资金账号";
                vertical_alignment: center;
            }

            ComboBox {
                width: 130px;
                height: AppCom.widgets_height;
                drop_down_list: false;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_GaCha.filter_accid;
                model <=> StkRskCredit_Com.accid_model;

                edited(by_selected) => {
                    StkRskCredit_GaCha.filter_changed();
                }

                selected => {
                    StkRskCredit_GaCha.filter_changed();
                }
            }

            Rectangle {}
        }

        TableView {
            sortable: true;
            column_header: header;
            row_count: StkRskCredit_GaCha.row_data.length;

            // 默认以差额进行升序排序
            sort_canceled => {
                StkRskCredit_GaCha.sort_asc_column_index = 6;
                StkRskCredit_GaCha.sort_dec_column_index = -1;
                StkRskCredit_GaCha.sort_ascending(StkRskCredit_GaCha.sort_asc_column_index);
            }
            sort_ascending(index) => {
                StkRskCredit_GaCha.sort_asc_column_index = index;
                StkRskCredit_GaCha.sort_dec_column_index = -1;
                StkRskCredit_GaCha.sort_ascending(index);
                true
            }
            sort_descending(index) => {
                StkRskCredit_GaCha.sort_asc_column_index = -1;
                StkRskCredit_GaCha.sort_dec_column_index = index;
                StkRskCredit_GaCha.sort_descending(index);
                true
            }

            get_cell_data(row_index, column_index) => {
                StkRskCredit_GaCha.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                if 2 == column_index || 6 == column_index {
                    return StkRskCredit_GaCha.get_row_data_color(row_index, column_index, data);
                }

                NotifyColor.default
            }

            cell_double_clicked(row_index, column_index) => {
                StkRskCredit_Com.accountid = StkRskCredit_GaCha.row_data[row_index][1];
                StkRskCredit_Com.trd_accountid_changed(StkRskCredit_Com.accountid);
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_GaCha.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_GaCha.row_data[row_index]);
                }
            }
        }
    }
}