use std::sync::{Arc, RwLock};

use tiapi::{
    api::SpiDowncast,
    protocol_pub::{
        account_trade_ctp::{self, CtpTradingCodeField},
        app, instrument_trade_ctp, monitor_trade_ctp, order_trade_ctp,
        position_trade_ctp::{self, CtpCreditReturnDtlField},
        trade_req_ctp, trade_trade_ctp,
    },
};

use super::tradestkbuf::TradeStkBuf;

pub struct TradeStkSpi {
    buf: Option<Arc<RwLock<TradeStkBuf>>>,
}

impl TradeStkSpi {
    pub fn new() -> Self {
        Self { buf: None }
    }

    pub fn register_buf(&mut self, buf: Arc<RwLock<TradeStkBuf>>) {
        self.buf = Some(buf);
    }
}

impl SpiDowncast for TradeStkSpi {
    fn a_ref(&self) -> &dyn std::any::Any {
        self
    }
}

impl tiapi::api::tradestkapi::ITradeStkSpi for TradeStkSpi {
    /// 断开连接
    fn on_rtn_disconnect(&self) {
        log::warn!("on_rtn_disconnect");

        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_disconnect();
        }
    }

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: app::AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: app::AppTDServerErrorField) {
        log::warn!(
            "on_rtn_tdservererror. {}, {}, {}, {}, {}",
            tderr.AID,
            tderr.SID,
            tderr.Msg,
            tderr.Type,
            tderr.Time,
        );

        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tdservererror(tderr);
        }
    }

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: app::AppTDSessionTimeoutField) {
        log::warn!(
            "on_rtn_tdsessiontimeout. {}, {}, {}, {}",
            tdst.AID,
            tdst.SID,
            tdst.Msg,
            tdst.Time
        );

        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tdsessiontimeout(tdst);
        }
    }

    /// 委托消息
    fn on_rtn_order(&self, ord: order_trade_ctp::CtpOrderField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_order(ord);
        }
    }

    /// 成交消息
    fn on_rtn_trade(&self, trd: trade_trade_ctp::CtpTradeField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_trade(trd);
        }
    }

    /// 非交易业务委托消息
    fn on_rtn_businessorder(&self, ord: order_trade_ctp::CtpBusinessOrderField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_businessorder(ord);
        }
    }

    /// 非交易业务成交消息
    fn on_rtn_businesstrade(&self, trd: trade_trade_ctp::CtpBusinessTradeField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_businesstrade(trd);
        }
    }

    /// 出入金消息
    fn on_rtn_wd(&self, wd: account_trade_ctp::CtpWithdrawDepositField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_wd(wd);
        }
    }

    /// 股份划转消息
    fn on_rtn_positiontrans(&self, pt: position_trade_ctp::CtpPositionTransField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_positiontrans(pt);
        }
    }

    /// 查询交易编码响应
    fn on_rsp_qry_tradingcode(&self, tc: CtpTradingCodeField, reqid: i32, islast: bool) {
        if !tc.ClientID.is_empty() {
            if let Some(buf) = self.buf.as_ref() {
                buf.read().unwrap().on_rsp_qry_tradingcode(tc, islast);
            }
        }
    }

    /// 查询合约响应
    fn on_rsp_qry_instrument(&self, ins: instrument_trade_ctp::CtpRspQryInstrumentField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_qry_instrument(ins, islast);
        }
    }

    /// 查询非交易合约响应
    fn on_rsp_qry_fjyinstrument(&self, ins: instrument_trade_ctp::CtpRspQryFJYInstrumentField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_qry_fjyinstrument(ins, islast);
        }
    }

    /// 查询资金响应消息
    fn on_rsp_qry_tradingaccount(&self, acc: account_trade_ctp::CtpAccountField, reqid: i32, islast: bool) {
        if !acc.AccountID.is_empty() {
            if let Some(buf) = self.buf.as_ref() {
                buf.read().unwrap().on_rsp_qry_tradingaccount(acc);
            }
        }
    }

    /// 查询资金(分交易所)响应消息
    fn on_rsp_qry_tradingaccountex(&self, acc: account_trade_ctp::CtpAccountField, reqid: i32, islast: bool) {
        if !acc.AccountID.is_empty() {
            if let Some(buf) = self.buf.as_ref() {
                buf.read().unwrap().on_rsp_qry_tradingaccount(acc);
            }
        }
    }

    /// 查询持仓响应消息
    fn on_rsp_qry_investorposition(&self, pos: position_trade_ctp::CtpPositionField, reqid: i32, islast: bool) {
        if !pos.InvestorID.is_empty() {
            if let Some(buf) = self.buf.as_ref() {
                buf.read().unwrap().on_rsp_qry_investorposition(pos);
            }
        }
    }

    /// 报单录入响应消息
    fn on_rsp_inputorder(&self, rsp: trade_req_ctp::CtpRspInputOrderField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputorder(rsp, reqid, islast);
        }
    }

    /// 报单操作响应
    fn on_rsp_inputorderaction(&self, rsp: trade_req_ctp::CtpRspInputOrderActionField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputorderaction(rsp, reqid, islast);
        }
    }

    /// 非交易业务报单录入响应消息
    fn on_rsp_businessorder(&self, rsp: trade_req_ctp::CtpRspBusinessOrderField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_businessorder(rsp, reqid, islast);
        }
    }

    /// 非交易业务报单操作响应
    fn on_rsp_businessorderaction(&self, rsp: trade_req_ctp::CtpRspBusinessOrderActionField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_businessorderaction(rsp, reqid, islast);
        }
    }

    /// 自成交
    fn on_rtn_tradeself(&self, trdslf: monitor_trade_ctp::CtpTradeSelfField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tradeself(trdslf);
        }
    }

    /// 自成交详情
    fn on_rtn_tradeselfdtl(&self, trd: monitor_trade_ctp::CtpTradeSelfDtlField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tradeselfdtl(trd);
        }
    }

    /// 成交持仓比例
    fn on_rtn_tradeposition(&self, tp: monitor_trade_ctp::CtpTradePositionField) {}

    /// 报撤单
    fn on_rtn_orderinsertcancel(&self, oic: monitor_trade_ctp::CtpOrderInsertCancelField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_orderinsertcancel(oic);
        }
    }

    /// 可转债监控
    fn on_rtn_convertbondsmon(&self, cbm: monitor_trade_ctp::CtpConvertBondsMonField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_convertbondsmon(cbm);
        }
    }

    /// 成交监控
    fn on_rtn_trademon(&self, tm: monitor_trade_ctp::CtpTradeMonField) {}

    /// 信用授信额度
    fn on_rtn_creditlimit(&self, cl: position_trade_ctp::CtpCreditLimitField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_creditlimit(cl);
        }
    }

    /// 信用资金头寸
    fn on_rtn_credittcamt(&self, amt: position_trade_ctp::CtpCreditTcAmtField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credittcamt(amt);
        }
    }

    /// 信用股份头寸
    fn on_rtn_credittcpos(&self, pos: position_trade_ctp::CtpCreditTcPosField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_credittcpos(pos);
        }
    }

    /// 信用合约
    fn on_rtn_creditcontract(&self, cc: position_trade_ctp::CtpCreditContractField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_creditcontract(cc);
        }
    }

    /// 信用集中度
    fn on_rtn_creditconcentration(&self, cc: position_trade_ctp::CtpCreditConcentrationField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_creditconcentration(cc);
        }
    }

    /// 信用合约归还明细
    fn on_rtn_creditretdtl(&self, rtd: CtpCreditReturnDtlField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_creditretdtl(rtd);
        }
    }
}
