import { ListViewItem } from "@zstk/lib.slint";
import { LineItem } from "../widgets/lineeditex.slint";

import { AppResult, TradeColor, MonConvertbonds, MonOrderinsertcancel, MonTradeself, AppInerArgs } from "../struct.slint";

// 公共
export global StkTrd_Com {
    in_out property <string> accountid; // 资金账户
    in_out property <bool> iscredit;    // 是否融资融券
    in_out property <string> transpwd;
}

// 选择的Tab的索引
export global StkTrd_TabSelIdx {
    in_out property <int> selidx;
    in_out property <int> in_subselidx;
    in_out property <int> rtn_subselidx;
    in_out property <int> mon_subselidx;
    in_out property <int> credit_tc_subselidx;

    in_out property <int> input_selidx;
}

// 资金划转
export struct FundTransField {
    accountid: string,
    password: string,
    outsys: int,
    insys: int,
    amount: string
}

// 股份划转
export struct PositionTransField
{
    accountid: string,
    password: string,
    exchid: string,
    insid: string,
    transflag: int,
    volume: string
}

// 资金信息
export global StkTrd_Account {
    in_out property <[[string]]> row_data;

    in_out property <int> sort_asc_column_index: 0;
    in_out property <int> sort_dec_column_index: -1;

    in_out property <string> sel_accid;
    in_out property <bool> show_fund_trans;

    callback sort_ascending(int);
    callback sort_descending(int);
    pure callback get_row_data_color(int, int, string) -> brush;

    in property <bool> ctrls_enabled: true;
    callback req_fund_trans(/* ft */ FundTransField);
}

// 持仓信息
export global StkTrd_Position {

    in property <[[string]]> row_data;
    in property <[ListViewItem]> insid_model;

    in_out property <string> exchid;
    in_out property <string> insid;

    callback insid_text_changed();
    callback filter_changed();
    callback export_clicked(/* type */ int);

    in_out property <string> sel_accid;
    in_out property <string> sel_exchid;
    in_out property <string> sel_insid;
    in_out property <string> sel_volume;
    in_out property <bool> show_pos_trans;
    in property <bool> ctrls_enabled: true;
    callback req_pos_trans(/* pt */ PositionTransField);

    in_out property <int> sort_asc_column_index: 0;
    in_out property <int> sort_dec_column_index: -1;
    callback sort_ascending(int);
    callback sort_descending(int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

// 信用交易 - 信用合约
export global StkTrd_CreditContract {

    in property <[[string]]> row_data;
    in property <[ListViewItem]> insid_model;

    in_out property <int> side;
    in_out property <string> side_str;
    in_out property <string> exchid;
    in_out property <string> insid;

    callback filter_changed();
    callback export_clicked(/* type */ int);

    pure callback get_row_data_color(int, int, string) -> brush;

    public function get_row_data() -> [[string]] {
        row_data
    }

    public function insid_model_len() -> int {
        insid_model.length
    }

    /////////////////////////////////////////////////////////////////
    /// 直接还款

    in_out property <bool> show_return_amt;         // 是否显示直接还款
    in_out property <string> reamt_accid;           // 资金账号
    in_out property <string> reamt_exchid: "SSE";   // 交易市场
    in_out property <string> reamt_insid;           // 证券代码
    in_out property <string> reamt_amt;             // 还款金额
    in_out property <string> reamt_req_ret;         // 请求结果
    in property <bool> reamt_ctrls_enabled: true;   // 还款控件是否可用
    callback reamt_clicked();

    /////////////////////////////////////////////////////////////////
    /// 现券还券

    in_out property <bool> show_return_pos;         // 是否显示直接还券
    in_out property <string> repos_accid;           // 资金账号
    in_out property <string> repos_exchid;          // 交易市场
    in_out property <string> repos_insid;           // 证券代码
    in_out property <string> repos_pos;             // 还券数量
    in_out property <string> repos_req_ret;         // 请求结果
    in property <bool> repos_ctrls_enabled: true;   // 还券控件是否可用
    callback repos_clicked();
}

// 信用交易 - 融资授信额度
export global StkTrd_CreditLimitAmt {

    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <string> exchid;

    out property <bool> only_show_summary: AppInerArgs.is_all_in_one ? !AppInerArgs.trd_detail_amt : false;

    callback filter_changed();
    callback export_clicked(/* type */ int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

// 信用交易 - 融券授信额度
export global StkTrd_CreditLimitPos {

    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <string> exchid;

    out property <bool> only_show_summary: AppInerArgs.is_all_in_one ? !AppInerArgs.trd_detail_amt : false;

    callback filter_changed();
    callback export_clicked(/* type */ int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

// 信用交易 - 资金头寸
export global StkTrd_CreditTcAmt {

    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <int> tctype;
    in_out property <string> tctype_str;
    in_out property <string> exchid;

    out property <bool> only_show_summary: AppInerArgs.is_all_in_one ? !AppInerArgs.trd_detail_amt : false;

    callback filter_changed();
    callback export_clicked(/* type */ int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

// 信用交易 - 股份头寸
export global StkTrd_CreditTcPos {

    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in property <[ListViewItem]> insid_model;

    in_out property <int> tctype;
    in_out property <string> tctype_str;
    in_out property <string> exchid;
    in_out property <string> insid;

    callback filter_changed();
    callback export_clicked(/* type */ int);

    public function get_row_data() -> [[string]] {
        row_data
    }

    public function insid_model_len() -> int {
        insid_model.length
    }
}

// 录入提示
export global StkTrd_InputTip {
    in property <AppResult> app_ret;
}

// 撤单提示
export global StkTrd_CancelTip {
    in property <AppResult> app_ret;
}

// 报单价格条件
export global StkTrd_OrdPriceType {
    // SSE的报单价格条件
    out property <[ListViewItem]> sse_price_type_model: [
        { text: "普通限价"},
        { text: "最优5档即时成交剩余撤销"},
        { text: "最优5档即时成交剩余转限价"},
        { text: "本方最优价"},
        { text: "对手方最优价"},
    ];

    // SZSE的报单价格条件
    out property <[ListViewItem]> szse_price_type_model: [
        { text: "普通限价"},
        { text: "对手方最优价"},
        { text: "本方最优剩余转限价"},
        { text: "最优5档即时成交剩余撤销"},
        { text: "市价即时成交剩余撤销"},
        { text: "市价全部成交或撤销"},
    ];

    // BSE的报单价格条件
    out property <[ListViewItem]> bse_price_type_model: [
        { text: "普通限价"},
        { text: "最优5档即时成交剩余撤销"},
        { text: "最优5档即时成交剩余转限价"},
        { text: "本方最优价"},
        { text: "对手方最优价"},
    ];
}

// 报单录入
export global StkTrd_InputOrder {
    // 资金账号
    in_out property <string> accountid: "";

    // 是否买入
    in_out property <bool> is_buy: true;

    // 是否预埋单
    in_out property <bool> is_yumai: false;

    // 交易所编码
    in_out property <string> exchid: "SSE";

    // 证券代码
    in_out property <string> insid;

    // 证券代码是否错误
    in_out property <bool> insid_has_error: true;

    // 报单价格条件
    in_out property <string> price_type: "普通限价";

    // 报单价格
    in_out property <string> price;

    // 报单价格是否错误
    in_out property <bool> price_has_error: true;

    // 报单数量
    in_out property <string> volume: "100";

    // 报单数量是否错误
    in_out property <bool> volume_has_error: false;

    // 合约列表
    in_out property <[LineItem]> insid_model;
    in_out property <[LineItem]> all_insid_model;

    // 市场有变化时的回调
    callback exchid_changed();

    // 合约有变动时的回调
    callback insid_text_changed() -> bool;

    // 报单价格有变动时的回调
    callback price_text_changed() -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 买卖方向有变动时的回调
    callback dir_changed(/* is_buy */ bool);

    // 报单数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 价格上下调整时的回调
    callback price_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // 最小买入量
    in property <int> buy_min_vol: 100;

    // 数量加的步长
    in property <int> buy_vol_step: 100;

    // 数量减的步长
    in property <int> sell_vol_step: 100;

    // 价格的小数位数
    in property <int> price_dig: 3;

    // 价格加减的步长
    in property <float> price_step: 0.001;

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 设置市场
    public function set_market(id: string) {
        exchid = id;
        insid = "";
        price = "";
        price_has_error = true;
        volume = "100";
        volume_has_error = false;
        exchid_changed();
    }

    // 当前市场是否是SSE
    pure public function is_sel_sse() -> bool {
        "SSE" == exchid
    }

    // 当前市场是否是SZSE
    pure public function is_sel_szse() -> bool {
        "SZSE" == exchid
    }

    // 当前市场是否是BSE
    pure public function is_sel_bse() -> bool {
        "BSE" == exchid
    }

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        is_buy ? TradeColor.dir_buy : TradeColor.dir_sell
    }
}

// 报单录入(信用)
export global StkTrd_InputOrderCredit {
    // 资金账号
    in_out property <string> accountid: "";

    // 交易类型. 0:普通; 1:融资; 2:融券
    in_out property <int> trade_type: 0;

    // 交易类型.
    // 普通: 0:买入; 1:卖出
    // 融资: 0:融资买入; 1:卖券还款; 2:直接还款
    // 融券: 0:融券卖出; 1:买券还券; 2:现券还券; 3:归还券息
    in_out property <int> side: 0;

    // 是否预埋单
    in_out property <bool> is_yumai: false;

    // 交易所编码
    in_out property <string> exchid: "SSE";

    // 证券代码
    in_out property <string> insid;

    // 证券代码是否错误
    in_out property <bool> insid_has_error: true;

    // 报单价格条件
    in_out property <string> price_type: "普通限价";
    in_out property <int> price_type_int: 0;

    // 报单价格
    in_out property <string> price;

    // 报单价格是否错误
    in_out property <bool> price_has_error: true;

    // 报单数量
    in_out property <string> volume: "100";

    // 报单数量是否错误
    in_out property <bool> volume_has_error: false;

    // 合约列表
    in_out property <[LineItem]> insid_model;
    in_out property <[LineItem]> all_insid_model;

    // 是否是买入
    public function is_buy_dir() -> bool {
        if 0 == trade_type && 0 == side {
            // 担保品买入
            true
        } else if 1 == trade_type && 0 == side {
            // 融资买入
            true
        } else if 2 == trade_type && 1 == side {
            // 买券还券
            true
        } else {
            false
        }
    }

    // 市场有变化时的回调
    callback exchid_changed();

    // 合约有变动时的回调
    callback insid_text_changed() -> bool;

    // 报单价格有变动时的回调
    callback price_text_changed() -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 交易类型方向有变动时的回调
    callback trade_type_side_changed();

    // 报单数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 价格上下调整时的回调
    callback price_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // 最小买入量
    in property <int> buy_min_vol: 100;

    // 数量加的步长
    in property <int> buy_vol_step: 100;

    // 数量减的步长
    in property <int> sell_vol_step: 100;

    // 价格的小数位数
    in property <int> price_dig: 3;

    // 价格加减的步长
    in property <float> price_step: 0.001;

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 设置市场
    public function set_market(id: string) {
        exchid = id;
        insid = "";
        price = "";
        price_has_error = true;
        volume = "100";
        volume_has_error = false;
        exchid_changed();
    }

    // 当前市场是否是SSE
    pure public function is_sel_sse() -> bool {
        "SSE" == exchid
    }

    // 当前市场是否是SZSE
    pure public function is_sel_szse() -> bool {
        "SZSE" == exchid
    }

    // 当前市场是否是BSE
    pure public function is_sel_bse() -> bool {
        "BSE" == exchid
    }

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        if 0 == trade_type {
            0 == side ? TradeColor.dir_buy : TradeColor.dir_sell
        } else if 1 == trade_type {
            0 == side ? TradeColor.dir_buy : (1 == side ? TradeColor.dir_sell : TradeColor.dir_other)
        } else if 2 == trade_type {
            0 == side ? TradeColor.dir_sell : (1 == side ? TradeColor.dir_buy : TradeColor.dir_other)
        } else {
            transparent
        }
    }

    // 获取买卖方向颜色
    pure public function get_dir_txt() -> string {
        if 0 == trade_type {
            0 == side ? "买入" : "卖出"
        } else if 1 == trade_type {
            0 == side ? "融资买入" : (1 == side ? "卖券还款" : "直接还款")
        } else if 2 == trade_type {
            0 == side ? "融券卖出" : (1 == side ? "买券还券" : (2 == side ? "现券还券" : "归还券息"))
        } else {
            "未知操作"
        }
    }
}

// 非交易报单录入
export global StkTrd_InputFjyOrder {
    // 是否买入
    in_out property <bool> is_buy: false;

    // 报单相关
    in_out property <string> accountid: "";
    out property <string> exchid_str: "SSE";
    out property <int> exchid: 1;
    in_out property <string> insid: "";
    in_out property <string> insname: "";
    in_out property <bool> insid_has_error: true;
    in_out property <int> fjy_type;
    in_out property <string> fjy_type_str: "质押式回购(逆回购)";
    in_out property <string> price;
    in_out property <bool> price_has_error: true;
    in_out property <string> volume: "1000";
    in_out property <bool> volume_has_error;
    in_out property <int> tickvolume: 1000;
    in_out property <int> minvolume: 1000;
    in_out property <int> maxvolume: ********;
    in_out property <float> tickprice: 0.005;

    // 合约列表
    in_out property <[LineItem]> insid_model;
    in_out property <[LineItem]> all_insid_model;

    // SSE的报单价格条件
    out property <[ListViewItem]> sse_price_type_model: [
        { text: "发行"},
        { text: "配股、科创板配售"},
        { text: "配转债"},
        { text: "要约预受"},
        { text: "要约撤销"},
        { text: "开放式基金申购"},
        { text: "开放式基金赎回"},
        { text: "开放式基金认购"},
        { text: "开放式基金转托管"},
        { text: "开放式基金分红设置"},
        { text: "开放式基金转换"},
        { text: "余券划转"},
        { text: "还券划转"},
        { text: "担保品划入"},
        { text: "担保品划出"},
        { text: "券源划入"},
        { text: "券源划出"},
        { text: "质押式回购(逆回购)"},
    ];

    // SZSE的报单价格条件
    out property <[ListViewItem]> szse_price_type_model: [
        { text: "发行"},
        { text: "质押式回购(逆回购)"},
    ];

    // 市场有变化时的回调(1: SSE; 2:SZSE)
    callback exchid_sel_changed(/** exchid */ int);

    // 合约有变动时的回调
    callback insid_text_changed() -> bool;

    // 报单价格有变动时的回调
    callback price_text_changed() -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 报单数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 价格上下调整时的回调
    callback price_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 重置行情信息
    public function reset_sel_ins_info() {
        insid = "";
        insname = "";
        insid_has_error = true;

        price = "";
        price_has_error = true;

        volume = "1000";
        volume_has_error = false;
    }

    // 设置交易所
    public function set_exch(id: int) {
        exchid = id;
        if 1 == id {
            exchid_str = "SSE";
        } else {
            exchid_str = "SZSE";
        }

        exchid_sel_changed(id);
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 设置市场
    public function set_market(id: int) {
        reset_sel_ins_info();
        set_exch(id);
    }

    // 当前市场是否是SSE
    pure public function is_sel_sse() -> bool {
        1 == exchid
    }

    // 当前市场是否是SZSE
    pure public function is_sel_szse() -> bool {
        2 == exchid
    }

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        is_buy ? TradeColor.dir_buy : TradeColor.dir_sell
    }
}

// 普通撤单结构
struct CancelOrderField {
    exchid: string,
    accountid: string,
    ordersysid: string,
    bs: string,
}

// 非交易业务撤单结构
struct CancelFjyOrderField {
    exchid: string,
    accountid: string,
    ordersysid: string,
}

// 撤单
export global StkTrd_InOrder {

    // 在途订单
    in property <[[string]]> row_data;

    // 控件是否可用
    in property <bool> ctrls_enabled: true;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_order_clieked(/* ord */ CancelOrderField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_order_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤买/卖单
    // buy_sell: 0:buy; other:sell
    callback cancel_order_bs_clieked(/* buy_sell */ int,  /* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelOrderField> cancel_order;
    public function get_cancel_order(row_index: int) -> CancelOrderField {
        if row_index >= row_data.length {
            cancel_order.exchid = "";
            cancel_order.accountid = "";
            cancel_order.ordersysid = "";
            return cancel_order;
        }

        cancel_order.exchid = row_data[row_index][13];
        cancel_order.accountid = row_data[row_index][14];
        cancel_order.ordersysid = row_data[row_index][6];
        cancel_order.bs = row_data[row_index][4];
        cancel_order
    }

    // 撤选中单的撤单信息
    in_out property <CancelOrderField> sel_cancel_order;
}

// 非交易业务撤单
export global StkTrd_InFjyOrder {

    // 在途非交易业务订单
    in property <[[string]]> row_data;

    // 控件是否可用
    in property <bool> ctrls_enabled: true;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_order_clieked(/* ord */ CancelFjyOrderField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_order_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelFjyOrderField> cancel_order;
    public function get_cancel_order(row_index: int) -> CancelFjyOrderField {
        if row_index >= row_data.length {
            cancel_order.exchid = "";
            cancel_order.accountid = "";
            cancel_order.ordersysid = "";
            return cancel_order;
        }

        cancel_order.exchid = row_data[row_index][12];
        cancel_order.accountid = row_data[row_index][13];
        cancel_order.ordersysid = row_data[row_index][7];
        cancel_order
    }

    // 撤选中单的撤单信息
    in_out property <CancelFjyOrderField> sel_cancel_order;
}

// 设置
export global StkTrd_Setting {
    // 当前选中的项
    in_out property <{ parent: int, item: int }> sel_item: { parent: 0, item: 0 };

    // 标题
    in_out property <string> set_txt;

    // 交易请求确认 - 报单
    in_out property <bool> trdreqtip_inputorder: true;
    callback trdreqtip_inputorder_changed();

    // 交易请求确认 - 撤单
    in_out property <bool> trdreqtip_ordercancel: true;
    callback trdreqtip_ordercancel_changed();

    // 交易请求确认 - 非交易报单
    in_out property <bool> trdreqtip_fjy_inputorder: true;
    callback trdreqtip_fjy_inputorder_changed();

    // 交易请求确认 - 非交易撤单
    in_out property <bool> trdreqtip_fjy_ordercancel: true;
    callback trdreqtip_fjy_ordercancel_changed();

    // 异常监控 - 可转债
    in_out property <MonConvertbonds> mon_convertbonds;
    in_out property <string> mon_convertbonds_err_tips;
    in_out property <int> mon_convertbonds_err_status;
    callback mon_convertbonds_changed();
    callback reset_mon_convertbonds();

    // 异常监控 - 报撤单
    in_out property <MonOrderinsertcancel> mon_orderinsertcancel;
    in_out property <string> mon_orderinsertcancel_err_tips;
    in_out property <int> mon_orderinsertcancel_err_status;
    callback mon_orderinsertcancel_changed();
    callback reset_mon_orderinsertcancel();

    // 异常监控 - 自成交
    in_out property <MonTradeself> mon_tradeself;
    in_out property <string> mon_tradeself_err_tips;
    in_out property <int> mon_tradeself_err_status;
    callback mon_tradeself_changed();
    callback reset_mon_tradeself();
}
