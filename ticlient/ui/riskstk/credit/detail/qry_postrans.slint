import { Theme, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom, NotifyColor } from "../../../struct.slint";
import { PageItem } from "../../../widgets/page.slint";
import { TimeSelect, TimeItem } from "../../../widgets/time.slint";
import { StkRskCredit_QryPosTrans } from "../struct.slint";

export component QryPositionTransPage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkRskCredit_QryPosTrans.exchid;
        i_le_insid.text = StkRskCredit_QryPosTrans.insid;
        i_ts_start.time = StkRskCredit_QryPosTrans.starttime;
        i_ts_end.time = StkRskCredit_QryPosTrans.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            i_cbx_exch := ComboBox {
                width: 70px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text: StkRskCredit_QryPosTrans.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }
            i_le_insid := LineEdit {
                width: 85px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text: StkRskCredit_QryPosTrans.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "处理时间";
                vertical_alignment: center;
            }
            HorizontalLayout {
                i_ts_start := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryPosTrans.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryPosTrans.endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                height: AppCom.widgets_height;
                background: WidgetColor.btn_background;

                clicked => {
                    StkRskCredit_QryPosTrans.exchid = i_cbx_exch.current_text;
                    StkRskCredit_QryPosTrans.insid = i_le_insid.text;
                    StkRskCredit_QryPosTrans.starttime = i_ts_start.time;
                    StkRskCredit_QryPosTrans.endtime = i_ts_end.time;
                    StkRskCredit_QryPosTrans.starttime_int = i_ts_start.time_int;
                    StkRskCredit_QryPosTrans.endtime_int = i_ts_end.time_int;

                    StkRskCredit_QryPosTrans.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("处理时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("请求序号"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    { title: @tr("响应序号"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    { title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("划转方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("划转数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                ]
            };
            row_count: StkRskCredit_QryPosTrans.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_QryPosTrans.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_QryPosTrans.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_QryPosTrans.row_data[row_index]);
                }
            }
        }

        if StkRskCredit_QryPosTrans.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkRskCredit_QryPosTrans.page_index_model;
            item_total <=> StkRskCredit_QryPosTrans.item_total;
            page_total <=> StkRskCredit_QryPosTrans.page_total;
            page_index <=> StkRskCredit_QryPosTrans.page_index;
            page_size <=> StkRskCredit_QryPosTrans.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryPosTrans.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryPosTrans.page_size_changed(size);
            }
        }
    }
}
