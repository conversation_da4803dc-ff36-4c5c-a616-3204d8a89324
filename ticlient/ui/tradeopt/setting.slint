import { Theme, Icons, GroupListView, GroupListViewItem, VerticalSideBar, Switch, Label, LineEdit, ComboBox } from "@zstk/lib.slint";
import { OptTrd_Setting } from "struct.slint";
import { AppCom } from "../struct.slint";
import { WndRec } from "../widgets/wndrec.slint";
import { OptTrd_Com } from "tradeopt.slint";

// 设置 - 公共 - 主题
component SetComTheme {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        OptTrd_Setting.set_txt = "主题";
    }

    VerticalLayout {
        padding: 20px;

        HorizontalLayout {
            spacing: 20px;

            Label {
                text: "外观";
                vertical_alignment: center;
            }

            ComboBox {
                width: 90px;
                height: 24px;
                model: [
                    { text: "跟随系统" }, { text: "浅色" }, { text: "深色" }
                ];

                init => {
                    self.select(AppCom.theme_style);
                }

                selected(index) => {
                    AppCom.set_theme_style(index);
                }
            }

            Rectangle {}
        }

        Rectangle {}
    }
}

// 设置 - 交易 - 交易请求确认
component SetTrdReqConfirm {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        OptTrd_Setting.set_txt = "交易请求确认";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        GridLayout {
            spacing: 10px;

            Row {
                Label {
                    width: 120px;
                    text: "报单请求确认";
                }

                Switch {
                    checked <=> OptTrd_Setting.trdreqtip_inputorder;
                    toggled => {
                        OptTrd_Setting.trdreqtip_inputorder_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "撤单请求确认";
                }

                Switch {
                    checked <=> OptTrd_Setting.trdreqtip_ordercancel;
                    toggled => {
                        OptTrd_Setting.trdreqtip_ordercancel_changed();
                    }
                }
            }

            Row {
                Rectangle {
                    height: 1px;
                 }
            }

            Row {
                Label {
                    width: 120px;
                    text: "组合请求确认";
                }

                Switch {
                    checked <=> OptTrd_Setting.trdreqtip_combinsert;
                    toggled => {
                        OptTrd_Setting.trdreqtip_combinsert_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "解组合请求确认";
                }

                Switch {
                    checked <=> OptTrd_Setting.trdreqtip_combcancecl;
                    toggled => {
                        OptTrd_Setting.trdreqtip_combcancecl_changed();
                    }
                }
            }

            Row {
                Rectangle {
                    height: 1px;
                 }
            }

            Row {
                Label {
                    width: 120px;
                    text: "行权请求确认";
                }

                Switch {
                    checked <=> OptTrd_Setting.trdreqtip_inputexec;
                    toggled => {
                        OptTrd_Setting.trdreqtip_inputexec_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "撤销行权请求确认";
                }

                Switch {
                    checked <=> OptTrd_Setting.trdreqtip_execcancel;
                    toggled => {
                        OptTrd_Setting.trdreqtip_execcancel_changed();
                    }
                }
            }

            Row {
                Rectangle {
                    height: 1px;
                 }
            }

            Row {
                Label {
                    text: "备兑解锁仓确认";
                }

                Switch {
                    checked <=> OptTrd_Setting.trdreqtip_inputstklock;
                    toggled => {
                        OptTrd_Setting.trdreqtip_inputstklock_changed();
                    }
                }
            }

            Row {
                Rectangle {
                    height: 1px;
                 }
            }

            Row {
                Label {
                    visible: OptTrd_Com.ismarket;
                    height: OptTrd_Com.ismarket ? 21px : 0px;
                    text: "报价请求确认";
                }

                Switch {
                    visible: OptTrd_Com.ismarket;
                    height: OptTrd_Com.ismarket ? 18px : 0px;
                    checked <=> OptTrd_Setting.trdreqtip_inputquote;
                    toggled => {
                        OptTrd_Setting.trdreqtip_inputquote_changed();
                    }
                }
            }

            Row {
                Label {
                    visible: OptTrd_Com.ismarket;
                    height: OptTrd_Com.ismarket ? 21px : 0px;
                    text: "撤销报价请求确认";
                }

                Switch {
                    visible: OptTrd_Com.ismarket;
                    height: OptTrd_Com.ismarket ? 18px : 0px;
                    checked <=> OptTrd_Setting.trdreqtip_quotecancel;
                    toggled => {
                        OptTrd_Setting.trdreqtip_quotecancel_changed();
                    }
                }
            }
        }

        Label {
            text: "提示: 本页选项每次登录均默认开启, 设置仅用于本次登录";
        }
    }
}

// 设置 - 异常监控 - 成交持仓比
component SetMonitorTradePosition {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        OptTrd_Setting.set_txt = "异常监控 - 成交持仓比";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        Label {
            height: 26px;
            text: "同时满足以下条件, 则认为成交持仓比异常";
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "1. 成交量大于等于  ";
                vertical_alignment: center;
            }

            i_le_trdvol := LineEdit {
                width: 120px;
                text: OptTrd_Setting.mon_tradeposition.trade-vol;
                placeholder_text: "值为大于0的整数";
                has_error: 1 == OptTrd_Setting.mon_tradeposition_err_status;

                edited => {
                    OptTrd_Setting.mon_tradeposition.trade-vol = self.text;
                    OptTrd_Setting.mon_tradeposition_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "2. 成交持仓比大于等于  ";
                vertical_alignment: center;
            }

            i_le_ratio := LineEdit {
                width: 120px;
                text: OptTrd_Setting.mon_tradeposition.ratio;
                placeholder_text: "值为大于0的小数";
                has_error: 2 == OptTrd_Setting.mon_tradeposition_err_status;

                edited => {
                    OptTrd_Setting.mon_tradeposition.ratio = self.text;
                    OptTrd_Setting.mon_tradeposition_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                width: 70px;
                text: "默认值";
                color: Theme.border_focus;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        OptTrd_Setting.reset_mon_tradeposition();
                        i_le_trdvol.text = OptTrd_Setting.mon_tradeposition.trade-vol;
                        i_le_ratio.text = OptTrd_Setting.mon_tradeposition.ratio;
                    }
                }
            }

            Label {
                text: "提示: 本页数据设置后, 重启后全部生效...";
            }
        }

        Label {
            text: OptTrd_Setting.mon_tradeposition_err_tips;
            color: red;
        }
    }
}

// 设置 - 异常监控 - 报撤单
component SetMonitorOrdInsertCancel {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        OptTrd_Setting.set_txt = "异常监控 - 报撤单";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        Label {
            height: 26px;
            text: "满足以下条件, 则认为报撤单异常";
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "总笔数大于等于  ";
                vertical_alignment: center;
            }

            i_le_number := LineEdit {
                width: 120px;
                text: OptTrd_Setting.mon_orderinsertcancel.greater_than_num;
                placeholder_text: "值为大于0的整数";
                has_error: 1 == OptTrd_Setting.mon_orderinsertcancel_err_status;

                edited => {
                    OptTrd_Setting.mon_orderinsertcancel.greater_than_num = self.text;
                    OptTrd_Setting.mon_orderinsertcancel_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                width: 70px;
                text: "默认值";
                color: Theme.border_focus;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        OptTrd_Setting.reset_mon_orderinsertcancel();
                        i_le_number.text = OptTrd_Setting.mon_orderinsertcancel.greater_than_num;
                    }
                }
            }

            Label {
                text: "提示: 本页数据设置后, 重启后全部生效...";
            }
        }

        Label {
            text: OptTrd_Setting.mon_orderinsertcancel_err_tips;
            color: red;
        }
    }
}

// 设置 - 异常监控 - 自成交
component SetMonitorTradeSelf {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        OptTrd_Setting.set_txt = "异常监控 - 自成交";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        Label {
            height: 26px;
            text: "满足以下条件, 则认为自成交异常";
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "自成交数量大于等于  ";
                vertical_alignment: center;
            }

            i_le_trdvol := LineEdit {
                width: 120px;
                text: OptTrd_Setting.mon_tradeself.trade_vol;
                placeholder_text: "值为大于0的整数";
                has_error: 1 == OptTrd_Setting.mon_tradeself_err_status;

                edited => {
                    OptTrd_Setting.mon_tradeself.trade_vol = self.text;
                    OptTrd_Setting.mon_tradeself_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                width: 70px;
                text: "默认值";
                color: Theme.border_focus;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        OptTrd_Setting.reset_mon_tradeself();
                        i_le_trdvol.text = OptTrd_Setting.mon_tradeself.trade_vol;
                    }
                }
            }

            Label {
                text: "提示: 本页数据设置后, 重启后全部生效...";
            }
        }

        Label {
            text: OptTrd_Setting.mon_tradeself_err_tips;
            color: red;
        }
    }
}

component SideBarView inherits VerticalSideBar {
    in_out property <[GroupListViewItem]> items <=> navigation.model;
    in_out property <{ parent: int, item: int}> current_item <=> navigation.current_item;

    callback current_item_changed <=> navigation.current_item_changed;

    forward_focus: navigation;

    navigation := GroupListView {
        vertical_stretch: 1;
        current_item: { item: 0, parent: 0 };
    }
}

component SetPages {
    in property <{ parent: int, item: int}> sel_item;

    preferred_width: 100%;
    preferred_height: 100%;

    if (0 == root.sel_item.parent && 0 == root.sel_item.item): SetComTheme { }

    if (1 == root.sel_item.parent && 0 == root.sel_item.item): SetTrdReqConfirm { }

    if (2 == root.sel_item.parent && 0 == root.sel_item.item): SetMonitorTradePosition {
    }
    if (2 == root.sel_item.parent && 1 == root.sel_item.item): SetMonitorOrdInsertCancel {
    }
    if (2 == root.sel_item.parent && 2 == root.sel_item.item): SetMonitorTradeSelf {
    }
}

export component SettingPage {

    private property <bool> hover_close_btn;

    private property <GroupListViewItem> widgets1: { text: "公共", items: [
        { leading_icon: Icons.list, text: "主题" },
    ] };

    private property <GroupListViewItem> widgets2: { text: "交易", items: [
        { leading_icon: Icons.list, text: "交易请求确认" },
    ] };

    private property <GroupListViewItem> resources: {
        text: "异常监控",
        items: [
            { leading_icon: Icons.list, text: "成交持仓比" },
            { leading_icon: Icons.list, text: "报撤单" },
            { leading_icon: Icons.list, text: "自成交" },
        ]
    };

    preferred-width: 100%;
    preferred-height: 100%;

    WndRec {
        title: @tr("{}", OptTrd_Setting.set_txt);
        btn_close_clicked => {
            AppCom.show_set = false;
        }

        Rectangle {
            HorizontalLayout {
                SideBarView {
                    resizable: true;
                    title: @tr("设置");
                    items: [root.widgets1, root.widgets2, root.resources];
                    current_item <=> OptTrd_Setting.sel_item;
                }

                Rectangle {
                    SetPages {
                        sel_item: OptTrd_Setting.sel_item;
                    }
                }
            }
        }
    }
}
