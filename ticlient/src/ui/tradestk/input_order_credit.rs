use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        config::Config,
        ctptype::CTPType,
        global::{CFG, INS, MD_API, TOKIO_RT, TRADE_STK_API},
        instrument::Instrument,
        ticonvert::TIConvert,
        titype::TIType,
    },
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{
    CtpReqInputOrderField, CtpRspInputOrderField,
    TradeType::{EContingentCondition, EDirection, EForceCloseReason},
};

use super::tradestk::show_input_msg_box;

/// 报单录入数据
struct InputOrderCreditDatas {
    /******************************************************************************************************************/
    /// 在报单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputorder: AtomicBool,

    /// 报单响应数据
    rsp_inputorder: Arc<Mutex<CtpRspInputOrderField>>,

    /// 输入的合约是否有改变
    input_instid_changed: AtomicBool,
}
impl InputOrderCreditDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputorder: AtomicBool::new(false),
            rsp_inputorder: Arc::new(Mutex::new(CtpRspInputOrderField::default())),
            input_instid_changed: AtomicBool::new(false),
        }
    }
}
static INPUTORDERCREDIT_DATAS: OnceLock<InputOrderCreditDatas> = OnceLock::new();

/// 报单
pub(super) struct InputOrderCredit {}

/// 构建与初始化
impl InputOrderCredit {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化报单录入数据
        let _ = INPUTORDERCREDIT_DATAS.set(InputOrderCreditDatas::new());

        // 注册点击行情详情的价格时的事件
        let app_weak = app.as_weak();
        let app_mddtl = app.global::<MarketDataDtl>();
        app_mddtl.on_price_clicked(move |exchid, insid, price, ptype| {
            InputOrderCredit::on_price_clicked(&app_weak, exchid, insid, price, ptype)
        });

        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        // 注册市场改变事件
        let app_weak = app.as_weak();
        app_inputord.on_exchid_changed(move || InputOrderCredit::on_exchid_changed(&app_weak));

        // 注册合约改变事件
        let app_weak = app.as_weak();
        app_inputord.on_insid_text_changed(move || InputOrderCredit::on_insid_text_changed(&app_weak));

        // 注册价格改变事件
        let app_weak = app.as_weak();
        app_inputord.on_price_text_changed(move || InputOrderCredit::on_price_text_changed(&app_weak));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputord.on_volume_text_changed(move || InputOrderCredit::on_volume_text_changed(&app_weak));

        // 注册交易类型方向改变的事件
        let app_weak = app.as_weak();
        app_inputord.on_trade_type_side_changed(move || InputOrderCredit::on_trade_type_side_changed(&app_weak));

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputord.on_volume_updown_changed(move |upflag| InputOrderCredit::on_volume_updown_changed(&app_weak, upflag));

        // 注册报单价格上下调整时的事件
        let app_weak = app.as_weak();
        app_inputord.on_price_updown_changed(move |upflag| InputOrderCredit::on_price_updown_changed(&app_weak, upflag));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputord.on_req_tip_toggled(move |checked| InputOrderCredit::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputord.on_ok_clicked(move |need_confirm| InputOrderCredit::on_ok_clicked(&app_weak, need_confirm));

        log::trace!("Input order credit init completed");
    }

    pub fn init_async(&self, app: &crate::slintui::App) {
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        // 添加合约
        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
        let all_insid = {
            INS.get()
                .unwrap()
                .read()
                .unwrap()
                .get_id_with_name_by_exchid(TIType::EXCH_SSE)
        };
        all_insid.iter().for_each(|it| {
            items.push(crate::slintui::LineItem {
                text: slint::format!("{}", it.key()),
                remark: slint::format!("{}", it.value()),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(items.sort_by(move |r_a, r_b| r_a.text.cmp(&r_b.text)));
        app_inputord.set_insid_model(sort_model.clone().into());
        app_inputord.set_all_insid_model(sort_model.into());
    }

    /// 报单响应
    pub fn on_rsp_inputorder(&self, rsp: CtpRspInputOrderField) {
        let inputord_datas = INPUTORDERCREDIT_DATAS.get().unwrap();
        *inputord_datas.rsp_inputorder.lock().unwrap() = rsp;
        inputord_datas.is_err_inputorder.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl InputOrderCredit {
    /// 点击行情详情的价格
    fn on_price_clicked(app_weak: &Weak<App>, exchid: SharedString, insid: SharedString, price: SharedString, ptype: i32) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        if exchid != app_inputord.get_exchid() {
            app_inputord.set_exchid(exchid);
            InputOrderCredit::on_exchid_changed(app_weak);
        }

        if insid != app_inputord.get_insid() {
            app_inputord.set_insid(insid);
            InputOrderCredit::on_insid_text_changed(app_weak);
        }

        if "--" != price.as_str() && price != app_inputord.get_price() {
            app_inputord.set_price(price);
            InputOrderCredit::on_price_text_changed(app_weak);
        }
    }

    /// 市场有变动
    fn on_exchid_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();
        app_inputord.set_price_type("普通限价".into());

        let exchid = match app_inputord.get_exchid().as_str() {
            "SSE" => TIType::EXCH_SSE,
            "SZSE" => TIType::EXCH_SZSE,
            "BSE" => TIType::EXCH_BSE,
            _ => -1,
        };

        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
        let inids_map = { INS.get().unwrap().read().unwrap().get_id_with_name_by_exchid(exchid) };
        inids_map.iter().for_each(|it| {
            items.push(crate::slintui::LineItem {
                text: slint::format!("{}", it.key()),
                remark: slint::format!("{}", it.value()),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(items.sort_by(move |r_a, r_b| r_a.text.cmp(&r_b.text)));
        app_inputord.set_insid_model(sort_model.clone().into());
        app_inputord.set_all_insid_model(sort_model.into());
    }

    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputord_datas = INPUTORDERCREDIT_DATAS.get().unwrap();
        inputord_datas.input_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        let insid = app_inputord.get_insid();
        let ins = {
            let id = app_inputord.get_insid();
            INS.get().unwrap().read().unwrap().get_ins_by_insid(&id)
        };
        if let Some(ins) = ins {
            app_inputord.set_insid_has_error(false);

            // 设置交易所编码
            let eid = app_inputord.get_exchid();
            if eid.as_str() != ins.exchname.as_str() {
                app_inputord.set_exchid(ins.exchname.clone().into());
                app_inputord.invoke_exchid_changed();
            }

            // 设置数量调整的参数
            let vol_chg = if ins.buyordtick > 0 {
                (
                    ins.minlmtvol.max(ins.minmktvol).max(ins.buyordtick),
                    ins.buyordtick,
                    ins.sellordtick,
                )
            } else {
                // 老版本的服务端没有这个字段即值为0
                (100, 100, 100)
            };
            app_inputord.set_buy_min_vol(vol_chg.0);
            app_inputord.set_buy_vol_step(vol_chg.1);
            app_inputord.set_sell_vol_step(vol_chg.2);
            if app_inputord.invoke_is_buy_dir() {
                app_inputord.set_volume(slint::format!("{}", vol_chg.0));
                app_inputord.set_volume_has_error(false);
            }

            // 设置价格调整的参数
            app_inputord.set_price_dig(ins.ptw);
            app_inputord.set_price_step(ins.pricetick as f32);

            // 订阅行情
            let app_mddtl = app.global::<MarketDataDtl>();
            let cur_key = std::format!("{}{}", ins.exchname, ins.insid);
            let pre_key = app_mddtl.get_pre_key().to_string();
            if cur_key != pre_key {
                let insname = if !ins.symbol.is_empty() {
                    ins.symbol.clone()
                } else {
                    ins.name.clone()
                };

                app_mddtl.invoke_reset_by_sel_ins(
                    ins.exchname.clone().into(),
                    ins.insid.clone().into(),
                    insname.into(),
                    TIConvert::format_f(ins.uplmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.lowlmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.prestlprice, ins.ptw as usize).into(),
                );

                let sub_insid = ins.insid.clone();
                TOKIO_RT.get().unwrap().spawn(async move {
                    let _ = MD_API.get().unwrap().read().await.req_subscribe(&sub_insid).await;
                });
            }
        } else {
            app_inputord.set_insid_has_error(true);
        }

        true
    }

    /// 报单价格有变动
    fn on_price_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        let price = app_inputord.get_price().replace(",", "");
        if price.is_empty() {
            app_inputord.set_price_has_error(true);
            return false;
        }

        if price.trim().parse::<f64>().unwrap_or_default() <= Config::INVALID_PRICE {
            app_inputord.set_price_has_error(true);
            return false;
        }

        app_inputord.set_price_has_error(false);

        true
    }

    /// 报单数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        let volume = app_inputord.get_volume().replace(",", "");
        if volume.is_empty() {
            app_inputord.set_volume_has_error(true);
            return false;
        }

        if volume.trim().parse::<i32>().unwrap_or_default() <= 0 {
            app_inputord.set_volume_has_error(true);
            return false;
        }

        app_inputord.set_volume_has_error(false);

        true
    }

    /// 交易类型方向改变的事件
    fn on_trade_type_side_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();
        if app_inputord.invoke_is_buy_dir() {
            let buy_min_vol = app_inputord.get_buy_min_vol();
            app_inputord.set_volume(slint::format!("{}", buy_min_vol));
            app_inputord.set_volume_has_error(false);
        }
    }

    /// 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        if app_inputord.get_volume_has_error() {
            return;
        }

        let volume_str = app_inputord.get_volume().replace(",", "");
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            return;
        }

        let is_buy = app_inputord.invoke_is_buy_dir();
        let step = if is_buy {
            app_inputord.get_buy_vol_step()
        } else {
            app_inputord.get_sell_vol_step()
        };

        if upflag {
            volume += if !is_buy && volume < 100 { 1 } else { step.max(100) };
        } else {
            volume -= if !is_buy && volume <= 100 { 1 } else { step.max(100) };
            if volume <= 0 {
                return;
            }
        }
        if is_buy {
            if volume < app_inputord.get_buy_min_vol() {
                volume = app_inputord.get_buy_min_vol();
            }
        }

        app_inputord.set_volume(slint::format!("{}", volume));
    }

    /// 报单价格上下调整时的事件
    fn on_price_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        if app_inputord.get_price_has_error() {
            return;
        }

        let price_str = app_inputord.get_price().replace(",", "");
        let mut price = price_str.trim().parse::<f64>().unwrap_or_default();
        if price <= Config::INVALID_PRICE {
            return;
        }

        if upflag {
            price += app_inputord.get_price_step() as f64;
        } else {
            price -= app_inputord.get_price_step() as f64;
            if price <= Config::INVALID_PRICE {
                return;
            }
        }

        app_inputord.set_price(slint::format!("{:.*}", app_inputord.get_price_dig() as usize, price));
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();
        app_set.set_trdreqtip_inputorder(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputOrder = checked;
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<StkTrd_InputOrderCredit>();

        // 获取参数
        let accid = app_inputord.get_accountid();
        let trade_type = app_inputord.get_trade_type();
        let side = app_inputord.get_side();
        let exchid = app_inputord.get_exchid();
        let price_type = app_inputord.get_price_type();
        let insid = Instrument::parse_id(&app_inputord.get_insid()).0;
        let price = app_inputord.get_price().replace(",", "");
        let volume = app_inputord.get_volume().replace(",", "");
        let yumai: bool = app_inputord.get_is_yumai();

        // 输入参数检查
        {
            if 0 == trade_type || 1 == trade_type || 2 == trade_type {
                if 0 != side && 1 != side {
                    show_input_msg_box(&app, 1, Default::default(), slint::format!("方向错误"));
                    return;
                }
            } else {
                show_input_msg_box(&app, 1, Default::default(), slint::format!("类型错误"));
                return;
            }

            if accid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), slint::format!("账户不能为空"));
                return;
            }

            if insid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), slint::format!("获取证券信息失败"));
                return;
            }

            if exchid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), slint::format!("获取证券所在交易所失败"));
                return;
            }

            if price_type.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), slint::format!("报单价格类型不能为空"));
                return;
            }

            if price.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), slint::format!("价格不能为空"));
                return;
            }

            if volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), slint::format!("数量不能为空"));
                return;
            }
        }

        let price = price.trim().parse::<f64>().unwrap_or_default();
        if price <= Config::INVALID_PRICE {
            show_input_msg_box(&app, 1, Default::default(), slint::format!("请输入有效的价格"));
            return;
        }

        let volume = volume.trim().parse::<u32>().unwrap_or_default();
        if volume <= 0 {
            show_input_msg_box(&app, 1, Default::default(), slint::format!("请输入有效的数量"));
            return;
        }

        let price_type = super::input_order::InputOrder::get_order_price_type(&exchid, &price_type);
        if -1 == price_type.0 {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!(
                    "解析报单价格条件失败\n\n交易所编码:{}, 报单价格条件:{}]",
                    exchid,
                    app_inputord.get_price_type()
                ),
            );
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputOrder } {
            let direction = match (trade_type, side) {
                (0, 0) => "买入",
                (0, 1) => "卖出",
                (1, 0) => "融资买入",
                (1, 1) => "卖券还款",
                (2, 0) => "融券卖出",
                (2, 1) => "买券还券",
                _ => "error",
            };

            show_input_msg_box(
                &app,
                100,
                "报单录入确认".into(),
                slint::format!(
                    "账户:{}\n\n交易所:{}    代码:{}\n\n[{}] {} 股, 价格:{} ",
                    accid,
                    exchid,
                    insid,
                    direction,
                    volume,
                    price
                ),
            );

            return;
        }

        let mut req = CtpReqInputOrderField::default();
        if yumai {
            req.BusinessUnit = "9".to_owned();
        }
        req.CombOffsetFlag = "0".to_owned();
        req.CombHedgeFlag = "1".to_owned();
        req.ContingentCondition = EContingentCondition::Immediately.into();
        req.ForceCloseReason = EForceCloseReason::NotForceClose.into();
        req.InvestorID = accid.to_string();
        req.InstrumentID = insid.to_string();
        req.ExchangeID = exchid.to_string();
        req.LimitPrice = price;
        req.VolumeTotalOriginal = volume as i32;
        req.OrderPriceType = price_type.0;
        req.TimeCondition = price_type.1;
        req.VolumeCondition = price_type.2;
        req.Direction = {
            if 0 == trade_type {
                if 0 == side {
                    EDirection::Buy.into()
                } else {
                    EDirection::Sell.into()
                }
            } else if 1 == trade_type {
                if 0 == side {
                    EDirection::BuyCredit.into()
                } else {
                    EDirection::SellReturn.into()
                }
            } else {
                if 0 == side {
                    EDirection::SellCredit.into()
                } else {
                    EDirection::BuyReturn.into()
                }
            }
        };

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let api = TRADE_STK_API.get().unwrap().read().await;
            if let Err(err) = api.req_orderinput(0, &req).await {
                let _ = wapp.upgrade_in_event_loop(move |app| {
                    show_input_msg_box(
                        &app,
                        1,
                        Default::default(),
                        slint::format!("请求报单录入失败\n\n错误信息: {}", err),
                    );
                });
            }
        });
    }
}

/// 更新UI
impl InputOrderCredit {
    // 更新错误信息
    async fn update_errmsg(&self, app_weak: &Weak<App>) {
        let inputord_datas = INPUTORDERCREDIT_DATAS.get().unwrap();

        let is_err_inputorder = inputord_datas
            .is_err_inputorder
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputorder {
            return;
        }

        let rsp = { inputord_datas.rsp_inputorder.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!(
                    "{}失败\n\n错误码:{}\n错误信息:{}",
                    match rsp.Direction {
                        CTPType::SIDE_RETURN_FUND => "直接还款响应",
                        CTPType::SIDE_RETURN_STOCK => "现券还券响应",
                        _ => "报单录入响应",
                    },
                    rsp.ec,
                    rsp.em
                ),
            );
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新报单响应错误信息
        self.update_errmsg(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let inputord_datas = INPUTORDERCREDIT_DATAS.get().unwrap();
        if inputord_datas
            .input_instid_changed
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok()
        {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_inputord = app.global::<StkTrd_InputOrderCredit>();

                let insid = app_inputord.get_insid();
                if insid.len() > 6 + 5 {
                    // 目前证券合约的编码长度只有6位
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    app_inputord.set_insid_model(items.into());
                    return;
                }

                let all_insid_data = app_inputord.get_all_insid_model();

                if insid.is_empty() {
                    app_inputord.set_insid_model(all_insid_data);
                    return;
                }

                let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());

                let mut find_flag = false;
                for i in 0..all_insid_data.row_count() {
                    let li = all_insid_data.row_data(i).unwrap();
                    if li.text.starts_with(insid.as_str()) {
                        items.push(li);
                        find_flag = true;
                    } else {
                        if find_flag {
                            break;
                        }
                    }
                }

                app_inputord.set_insid_model(items.into());
            });
        }
    }
}
