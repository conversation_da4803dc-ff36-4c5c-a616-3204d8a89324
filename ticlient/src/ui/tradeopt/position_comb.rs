use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{self, config::Config, global::TRADE_OPT_API, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::position_trade_ctp::{CtpPositionCombField, CtpReqQryPositionCombDtlField};

// 自守义的组合结构
#[derive(Default, Clone, PartialEq)]
struct CtpPosCombField {
    leg1: CtpPositionCombField,

    leg2_insid: String,
    leg2_side: i32,
    leg2_volume: i32,
}
type CtpPosCombMap = dashmap::DashMap<String, CtpPosCombField>;
type PosInsIDSet = dashmap::DashSet<String>;

/// 持仓数据
struct PositionCombDatas {
    /// 数据
    data_map: Arc<CtpPosCombMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,

    /// 所有的持仓合约列表
    insid_set: Arc<PosInsIDSet>,

    /// 所有的持仓组合合约列表
    combtradeid_set: Arc<PosInsIDSet>,

    /// 合约编码是否有编辑过 - 如果编辑过,需要重新更新下拉列表
    insid_edited: AtomicBool,

    /// 是否有新的持仓合约 - 如果有,需要重新更新下拉列表
    new_insid: AtomicBool,

    /// 组合编码是否有编辑过 - 如果编辑过,需要重新更新下拉列表
    combtradeid_edited: AtomicBool,

    /// 是否有新的组合持仓合约 - 如果有,需要重新更新下拉列表
    new_combtradeid: AtomicBool,
}
impl PositionCombDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpPosCombMap::new()),
            data_changed: AtomicBool::new(false),

            filter_clicked: AtomicBool::new(false),

            insid_set: Arc::new(PosInsIDSet::new()),
            combtradeid_set: Arc::new(PosInsIDSet::new()),

            insid_edited: AtomicBool::new(false),
            new_insid: AtomicBool::new(false),

            combtradeid_edited: AtomicBool::new(false),
            new_combtradeid: AtomicBool::new(false),
        }
    }
}
static POSITION_COMB_DATAS: OnceLock<PositionCombDatas> = OnceLock::new();

// 组合持仓
pub(super) struct PositionComb {}

/// 构建与初始化
impl PositionComb {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = POSITION_COMB_DATAS.set(PositionCombDatas::new());

        let app_pos = app.global::<OptTrd_PositionComb>();

        let app_weak = app.as_weak();
        app_pos.on_sort_ascending(move |column_index| PositionComb::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_sort_descending(move |column_index| PositionComb::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_insid_text_changed(move || PositionComb::on_insid_text_changed(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_combid_text_changed(move || PositionComb::on_combid_text_changed(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_filter_changed(move || PositionComb::on_filter_changed(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_export_clicked(move |etype| PositionComb::on_export_clicked(&app_weak, etype));

        let app_weak = app.as_weak();
        app_pos.on_get_row_data_color(move |row_index, column_index, data| {
            PositionComb::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Position comb init completed");
    }
}

/// 与API的交互
impl PositionComb {
    /// 请求查询组合持仓信息
    pub fn qry_poscomb(&self) {
        let req = CtpReqQryPositionCombDtlField::default();

        let api = TRADE_OPT_API.get().unwrap().read().unwrap();
        if let Err(err) = api.req_qry_investorposition_combdtl(0, &req) {
            log::warn!("req_qry_investorposition_combdtl failed. {}", err);
        }
    }

    /// 响应查询组合持仓信息
    pub fn on_rsp_qry_poscomb(&self, pos: CtpPositionCombField) {
        let mut pos = pos;

        // 这两种组合CTP中的顺序与交易所的顺序正好相反
        if "PNSJC" == pos.CombID || "CXSJC" == pos.CombID {
            if 0 == pos.LegID {
                pos.LegID = 1;
            } else if 1 == pos.LegID {
                pos.LegID = 0;
            }
        }

        let pos_datas = POSITION_COMB_DATAS.get().unwrap();

        if !pos_datas.insid_set.contains(&pos.InstrumentID) {
            pos_datas.insid_set.insert(pos.InstrumentID.clone());
            POSITION_COMB_DATAS.get().unwrap().new_insid.store(true, Ordering::Relaxed);
        }

        if !pos_datas.combtradeid_set.contains(&pos.ComTradeID) {
            pos_datas.combtradeid_set.insert(pos.ComTradeID.clone());
            POSITION_COMB_DATAS
                .get()
                .unwrap()
                .new_combtradeid
                .store(true, Ordering::Relaxed);
        }

        let key = std::format!("{}{}{}", pos.InvestorID, pos.ExchangeID, pos.ComTradeID);

        if pos_datas.data_map.contains_key(&key) {
            let mut prepos = pos_datas.data_map.get_mut(&key).unwrap();

            if 0 == pos.LegID {
                if prepos.leg1 != pos {
                    prepos.leg1 = pos;
                    pos_datas.data_changed.store(true, Ordering::Relaxed);
                }
            } else if 1 == pos.LegID {
                if prepos.leg2_insid != pos.InstrumentID
                    || prepos.leg2_side != pos.Direction
                    || prepos.leg2_volume != pos.TotalAmt
                {
                    prepos.leg2_side = pos.Direction;
                    prepos.leg2_volume = pos.TotalAmt;
                    prepos.leg2_insid = pos.InstrumentID;
                    pos_datas.data_changed.store(true, Ordering::Relaxed);
                }
            }
        } else {
            let mut prepos = CtpPosCombField::default();
            if 0 == pos.LegID {
                prepos.leg1 = pos;
            } else if 1 == pos.LegID {
                prepos.leg2_side = pos.Direction;
                prepos.leg2_volume = pos.TotalAmt;
                prepos.leg2_insid = pos.InstrumentID;
            }
            pos_datas.data_map.insert(key, prepos);
            pos_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

/// 事件
impl PositionComb {
    /// 升序
    fn on_sort_ascending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_pos = app.global::<OptTrd_PositionComb>();
        let row_data = app_pos.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_pos.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_pos = app.global::<OptTrd_PositionComb>();
        let row_data = app_pos.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_pos.set_row_data(sort_model.into());
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 持仓方向
        if 4 == column_index {
            if data.as_str().contains("多") || data.as_str().contains("Long") {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) {
        POSITION_COMB_DATAS.get().unwrap().insid_edited.store(true, Ordering::Relaxed);
    }

    /// 组合编码有变动
    fn on_combid_text_changed(app_weak: &Weak<App>) {
        POSITION_COMB_DATAS
            .get()
            .unwrap()
            .combtradeid_edited
            .store(true, Ordering::Relaxed);
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        POSITION_COMB_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("position_comb_dtl_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出组合持仓失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "组合策略",
            "组合编码",
            "组合状态",
            "持仓量",
            "第1腿合约编码",
            "第1腿合约名称",
            "第1腿方向",
            "第1腿数量",
            "第2腿合约编码",
            "第2腿合约名称",
            "第2腿方向",
            "第2腿数量",
            "实收保证金",
            "交易所保证金",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出组合持仓失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<OptTrd_PositionComb>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let pos_datas = POSITION_COMB_DATAS.get().unwrap();
            let data_map_clone = pos_datas.data_map.clone();
            data_map_clone.iter().for_each(|pos| {
                let items = PositionComb::get_row_items(&pos);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出组合持仓失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出组合持仓成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}

/// 更新UI
impl PositionComb {
    /// 获取一行数据
    fn get_row_items(pos: &CtpPosCombField) -> Rc<VecModel<SharedString>> {
        let mut leg1_insname = "".to_owned();
        let mut leg2_insname = "".to_owned();
        if !pos.leg1.ExchangeID.is_empty() {
            let ins = common::global::INS.get().unwrap().read().unwrap();

            if let Some(ins) = ins.get_ins(TIConvert::exch_id(&pos.leg1.ExchangeID), &pos.leg1.InstrumentID) {
                leg1_insname = ins.symbol.clone();
            }

            if !pos.leg2_insid.is_empty() {
                if let Some(ins) = ins.get_ins(TIConvert::exch_id(&pos.leg1.ExchangeID), &pos.leg2_insid) {
                    leg2_insname = ins.symbol.clone();
                }
            }
        }

        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 资金账号
        items.push(pos.leg1.InvestorID.clone().into());

        // 交易所
        items.push(pos.leg1.ExchangeID.clone().into());

        // 组合策略
        items.push(pos.leg1.CombID.clone().into());

        // 组合编码
        items.push(pos.leg1.ComTradeID.clone().into());

        // 组合状态
        items.push(
            if !pos.leg1.InstrumentID.is_empty() {
                if pos.leg1.TotalAmt > 0 {
                    "组合"
                } else {
                    "已解除"
                }
            } else {
                ""
            }
            .into(),
        );

        // 持仓量
        items.push(TIConvert::format_i(pos.leg1.TotalAmt).into());

        // 第1腿合约编码
        items.push(pos.leg1.InstrumentID.clone().into());

        // 第1腿合约名称
        items.push(leg1_insname.into());

        // 第1腿方向
        items.push(TIConvert::ctp_leg_side(pos.leg1.Direction).into());

        // 第1腿数量
        items.push(TIConvert::format_i(pos.leg1.TotalAmt).into());

        // 第2腿合约编码
        items.push(pos.leg2_insid.clone().into());

        // 第2腿合约名称
        items.push(leg2_insname.into());

        // 第2腿方向
        items.push(TIConvert::ctp_leg_side(pos.leg2_side).into());

        // 第2腿数量
        items.push(TIConvert::format_i(pos.leg2_volume).into());

        // 实收保证金
        items.push(TIConvert::format_f(pos.leg1.Margin, Config::AMT_DIGITS).into());

        // 交易所保证金
        items.push(TIConvert::format_f(pos.leg1.ExchMargin, Config::AMT_DIGITS).into());

        items
    }

    // 更新合约下拉列表
    async fn update_insid(&self, app_weak: &Weak<App>) {
        let pos_datas = POSITION_COMB_DATAS.get().unwrap();

        let insid_edited = pos_datas
            .insid_edited
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        let new_pos_insid = pos_datas
            .new_insid
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        if !insid_edited && !new_pos_insid {
            return;
        }

        let insid_set_clone = pos_datas.insid_set.clone();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_pos = app.global::<OptTrd_PositionComb>();
            let filter = app_pos.get_insid().trim().to_owned();
            if insid_edited || new_pos_insid {
                let insid_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                insid_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                insid_set_clone.iter().for_each(|insid| {
                    if insid.contains(&filter) {
                        insid_arr.push(ListViewItem {
                            text: insid.clone().into(),
                            ..Default::default()
                        });
                    }
                });

                app_pos.set_insid_model(insid_arr.into());
            }
        });
    }

    // 更新组合编码下拉列表
    async fn update_combtradeid(&self, app_weak: &Weak<App>) {
        let pos_datas = POSITION_COMB_DATAS.get().unwrap();

        let combtradeid_edited = pos_datas
            .combtradeid_edited
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        let new_combtradeid = pos_datas
            .new_combtradeid
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        if !combtradeid_edited && !new_combtradeid {
            return;
        }

        let combtradeid_set_clone = pos_datas.combtradeid_set.clone();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_pos = app.global::<OptTrd_PositionComb>();
            let filter = app_pos.get_combtradeid().trim().to_owned();
            if combtradeid_edited || new_combtradeid {
                let combtradeid_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                combtradeid_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                combtradeid_set_clone.iter().for_each(|combtradeid| {
                    if combtradeid.contains(&filter) {
                        combtradeid_arr.push(ListViewItem {
                            text: combtradeid.clone().into(),
                            ..Default::default()
                        });
                    }
                });

                app_pos.set_combtradeid_model(combtradeid_arr.into());
            }
        });
    }

    // 更新持仓
    async fn update_position(&self, app_weak: &Weak<App>) {
        let pos_datas = POSITION_COMB_DATAS.get().unwrap();

        let data_map_clone = {
            if pos_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                pos_datas.filter_clicked.store(false, Ordering::Relaxed);
                pos_datas.data_map.clone()
            } else {
                if pos_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    pos_datas.data_map.clone()
                } else {
                    Arc::new(CtpPosCombMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_pos = app.global::<OptTrd_PositionComb>();

            let filter_exchid = app_pos.get_exchid().as_str().to_owned();
            let filter_strategyid = app_pos.get_strategyid().as_str().to_owned();
            let filter_state = app_pos.get_state();
            let filter_combtradeid = app_pos.get_combtradeid().trim().to_owned();
            let filter_insid = app_pos.get_insid().trim().to_owned();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|pos| {
                if !filter_exchid.is_empty() {
                    if pos.leg1.ExchangeID != filter_exchid {
                        return;
                    }
                }

                if !filter_strategyid.is_empty() {
                    if pos.leg1.CombID != filter_strategyid {
                        return;
                    }
                }

                if filter_state >= 1 {
                    // 组合
                    if 1 == filter_state {
                        if pos.leg1.TotalAmt <= 0 {
                            return;
                        }
                    }

                    // 已解除
                    if 2 == filter_state {
                        if pos.leg1.TotalAmt > 0 {
                            return;
                        }
                    }
                }

                if !filter_combtradeid.is_empty() {
                    if !pos.leg1.ComTradeID.contains(&filter_combtradeid) {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !pos.leg1.InstrumentID.contains(&filter_insid) && !pos.leg2_insid.contains(&filter_insid) {
                        return;
                    }
                }

                let items = PositionComb::get_row_items(&pos);
                row_data.push(items.into());
            });

            let asc_column_index = app_pos.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_pos.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            app_pos.set_row_data(row_data.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新合约下拉列表
        self.update_insid(&app_weak).await;

        // 更新组合编码下拉列表
        self.update_combtradeid(&app_weak).await;

        // 更新持仓
        self.update_position(&app_weak).await;
    }
}
