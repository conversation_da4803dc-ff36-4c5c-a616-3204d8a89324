import { Button, Label, LineEdit } from "@zstk/lib.slint";
import { WndDialog } from "../widgets/wndrec.slint";
import { AppCom, WidgetColor } from "../struct.slint";
import { OptTrd_Position } from "struct.slint";

export component CoveredConvertPage {
    forward_focus: i_le_vol;

    function enable_ctrls(enabled: bool) {
        i_le_vol.enabled = enabled;
        i_btn_trans.enabled = enabled;
        i_wnd.can_close = enabled;
    }

    i_wnd := WndDialog {
        title: 1 == OptTrd_Position.show_covered_type ? "转备兑仓" : "转普通仓";
        btn_close_clicked => {
            OptTrd_Position.show_covered_convert = false;
        }

        VerticalLayout {
            spacing: 10px;
            padding: 10px;

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "资金账号";
                }

                LineEdit {
                    width: 150px;
                    enabled: false;
                    text: OptTrd_Position.sel_cc_accid;
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "交易所";
                }

                LineEdit {
                    width: 150px;
                    enabled: false;
                    text: OptTrd_Position.sel_cc_exchid;
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "合约编码";
                }

                LineEdit {
                    width: 150px;
                    enabled: false;
                    text: OptTrd_Position.sel_cc_insid;
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "转换数量";
                }

                i_le_vol := LineEdit {
                    width: 150px;
                    text <=> OptTrd_Position.sel_cc_volume;
                }
            }

            HorizontalLayout {
                Rectangle {
                    horizontal_stretch: 1;
                }

                i_btn_trans := Button {
                    width: 80px;
                    height: 25px;
                    background: WidgetColor.btn_background;
                    text: "确定转换";
                    clicked => {
                        if AppCom.check_td_session_timeout() {
                            return ;
                        }

                        enable_ctrls(false);

                        OptTrd_Position.corvered_convert_clicked();

                        enable_ctrls(true);
                    }
                }
            }
        }
    }
}
