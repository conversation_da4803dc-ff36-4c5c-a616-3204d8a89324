import { Theme, TabView } from "@zstk/lib.slint";

import { InOrderPage } from "in_order.slint";
import { InExercisePage } from "in_exercise.slint";

import { FutTrd_TabSelIdx } from "struct.slint";

export component InPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: [
                { text: "委托" },
                { text: "行权" },
            ];

            current_index <=> FutTrd_TabSelIdx.in_subselidx;
        }

        if 0 == FutTrd_TabSelIdx.in_subselidx: InOrderPage { }

        if 1 == FutTrd_TabSelIdx.in_subselidx: InExercisePage { }
    }
}
