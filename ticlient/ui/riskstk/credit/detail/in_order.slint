import { Theme, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom, NotifyColor } from "../../../struct.slint";
import { PageItem } from "../../../widgets/page.slint";
import { TimeSelect, TimeItem } from "../../../widgets/time.slint";
import { StkRskCredit_InOrder } from "../struct.slint";

export component InOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <int> select_row_index: -1;
    function req_cancel_order(need_confirm: bool) {
        StkRskCredit_InOrder.sel_cancel_order.exchid = "";
        if select_row_index >= 0 && StkRskCredit_InOrder.row_data.length > select_row_index {
            StkRskCredit_InOrder.sel_cancel_order.exchid = StkRskCredit_InOrder.row_data[select_row_index][13];
            StkRskCredit_InOrder.sel_cancel_order.accountid = StkRskCredit_InOrder.row_data[select_row_index][1];
            StkRskCredit_InOrder.sel_cancel_order.ordersysid = StkRskCredit_InOrder.row_data[select_row_index][7];
        }
        StkRskCredit_InOrder.cancel_order_clieked(StkRskCredit_InOrder.sel_cancel_order, need_confirm);
    }

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 2,
                columns: [
                    /*  0 */{ title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    /*  1 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    /*  2 */{ title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    /*  3 */{ title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /*  4 */{ title: @tr("价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    /*  5 */{ title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    /*  6 */{ title: @tr("报单状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    /*  7 */{ title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    /*  8 */{ title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    /*  9 */{ title: @tr("报单价格条件"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_pricetype },
                    /* 10 */{ title: @tr("有效期类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_timecondition },
                    /* 11 */{ title: @tr("剩余数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 12 */{ title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                    /* 13 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },

                ]
            };
            row_count: StkRskCredit_InOrder.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_InOrder.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_clicked(row_index, column_index) => {
                self.select_full_row = true;
            }

            cell_double_clicked(row_index, column_index) => {
                // 风控端暂不实现交易功能
                // if StkRskCredit_InOrder.ctrls_enabled {
                //     req_cancel_order(true);
                // }
            }

            current_cell_changed(row_index, column_index) => {
                select_row_index = row_index;
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_InOrder.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_InOrder.row_data[row_index]);
                }
            }
        }

        // 风控端暂不实现交易功能
        if false: HorizontalLayout {
            padding: 6px;
            spacing: 30px;
            height: 36px;

            Button {
                text: "撤单";
                background: WidgetColor.btn_background;
                enabled: StkRskCredit_InOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkRskCredit_InOrder.ctrls_enabled = false;
                    req_cancel_order(true);
                }
            }

            Button {
                text: "全撤";
                background: WidgetColor.btn_background;
                enabled: StkRskCredit_InOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkRskCredit_InOrder.ctrls_enabled = false;
                    StkRskCredit_InOrder.cancel_order_all_clieked(StkRskCredit_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤买";
                background: WidgetColor.btn_background;
                enabled: StkRskCredit_InOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkRskCredit_InOrder.ctrls_enabled = false;
                    StkRskCredit_InOrder.cancel_order_bs_clieked(0, StkRskCredit_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤卖";
                background: WidgetColor.btn_background;
                enabled: StkRskCredit_InOrder.ctrls_enabled;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkRskCredit_InOrder.ctrls_enabled = false;
                    StkRskCredit_InOrder.cancel_order_bs_clieked(1, StkRskCredit_InOrder.row_data.length, true);
                }
            }
        }
    }
}
