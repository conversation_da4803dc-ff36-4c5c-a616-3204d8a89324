#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

#[cfg(target_os = "windows")]
mod windows_impl {

    use ::std::os::windows::process::CommandExt;
    use std::path::Path;
    use std::process::Command;
    use wait_timeout::ChildExt;

    /// 渲染器
    #[derive(serde::Deserialize, Debug, <PERSON><PERSON>, Default)]
    #[serde[default]]
    struct RendererInfo {
        pub renderer: String,
        pub gapi: String,
    }

    /// 文件路径
    #[derive(Debug)]
    struct FilePathField {
        /// 程序所在目录
        pub exe_dir: String,

        /// 程序工作目录
        pub work_dir: String,
    }
    impl FilePathField {
        pub fn new() -> Self {
            let exe_dir = match std::env::current_exe()
                .ok()
                .and_then(|p| p.parent().map(|parent| parent.display().to_string()))
            {
                Some(dir) => dir,
                None => ".\\".to_owned(),
            };

            let work_dir = std::env::var("USERPROFILE")
                .ok()
                .and_then(|dir| Some(std::format!("{}\\.ti\\ticlient", dir)))
                .unwrap_or_else(|| exe_dir.clone());

            Self {
                exe_dir: exe_dir,
                work_dir: work_dir,
            }
        }
    }

    /// 创建目录
    fn create_dir(dirpath: String) -> Result<(), String> {
        let metadata = std::fs::metadata(dirpath.clone());
        if metadata.is_ok() && metadata.unwrap().is_dir() {
            return Ok(());
        }

        let ret = std::fs::create_dir_all(dirpath.clone());
        if let Some(err) = ret.err() {
            return Err(err.to_string());
        }

        Ok(())
    }

    /// 创建日志
    fn setup_logger(dirpath: &str) -> Result<(), String> {
        let logdir = Path::new(dirpath).join("logs");
        create_dir(logdir.to_string_lossy().to_string())?;

        let logfile = logdir.join(std::format!("app_{}.log", chrono::Local::now().format("%Y-%m-%d_%H_%M_%S")));

        let f = fern::log_file(&logfile).map_err(|err| format!("create log file failed. {}", err))?;
        let dispatch = fern::Dispatch::new()
            .format(|out, message, record| {
                out.finish(format_args!(
                    "[{} {} {}:{}] {}",
                    chrono::Local::now().format("%Y%m%d %H:%M:%S.%3f"),
                    record.level(),
                    record.target(),
                    record.line().or_else(|| { Some(0) }).unwrap(),
                    message
                ))
            })
            .level(log::LevelFilter::Debug)
            .chain(f);

        #[cfg(debug_assertions)]
        let dispatch = dispatch.chain(std::io::stdout());

        dispatch
            .apply()
            .map_err(|err| format!("apply log dispatch failed. {}", err))?;

        Ok(())
    }

    /// 获取默认的(上次成功的)渲染器
    fn get_default_renderer(dirpath: &str) -> RendererInfo {
        let path = Path::new(dirpath).join("drg.json");
        match std::fs::read_to_string(&path) {
            Ok(datas) => serde_json::from_str::<RendererInfo>(&datas).unwrap_or_else(|err| {
                log::warn!("get_default_renderer parse failed. {}", err.to_string());
                RendererInfo::default()
            }),
            Err(err) => {
                log::warn!("get_default_renderer failed. {}", err.to_string());
                RendererInfo::default()
            }
        }
    }

    /// 运行程序
    fn run_app(program: &str, renderer: &str, gapi: &str) -> bool {
        let (r, g) = match (renderer, gapi) {
            ("skia", "d3d") => ("skia", "d3d"),
            ("skia", _) => ("skia", ""),
            ("femtovg", _) => ("femtovg", ""),
            ("software", _) => ("software", ""),
            _ => {
                log::error!("run_app: Unsupported renderer:{}, gapi:{}", renderer, gapi);
                return false;
            }
        };

        let output = Command::new(program)
            .creation_flags(0x08000000)
            .args(["-r", r, "-g", &g])
            .spawn();
        if let Err(err) = output {
            log::error!("exec program failed. {:?}", err.to_string());
            return false;
        }

        let mut child = output.unwrap();
        match child.wait_timeout(std::time::Duration::from_secs(30)).unwrap() {
            Some(status) => status.success(), // 正常退出返回true，异常退出返回false
            None => true,                     // 进程仍然存在，认为成功
        }
    }

    /// 程序入口
    pub fn run() {
        // 获取默认路径并初始化日志
        let fpath = FilePathField::new();
        if let Err(err) = setup_logger(&fpath.exe_dir) {
            println!("setup_logger failed. {}", err);
            return;
        }
        log::info!("App start successfully");

        // 要执行的程序
        let program = {
            let mut program = std::format!("{}\\EaseTrader.exe", fpath.exe_dir);
            if !Path::new(&program).exists() {
                log::warn!("The file EaseTrader.exe is not exists");

                program = std::format!("{}\\EaseRisk.exe", fpath.exe_dir);
                if !Path::new(&program).exists() {
                    log::warn!("The file EaseRisk.exe is not exists");
                    log::error!("App stop due to both EaseTrader.exe and EaseRisk.exe are not exists");
                    return;
                }
            }

            program
        };

        // 创建程序工作目录
        if let Err(err) = create_dir(fpath.work_dir.clone()) {
            log::warn!("Create work dir failed. {}", err);
            return;
        }

        // 解析工作目录下的默认渲染器
        let default_renderer = {
            let default_renderer = get_default_renderer(&fpath.work_dir);
            log::info!("get_default_renderer. {:?}", default_renderer);

            // 再次验证默认的渲染器, 防止文件被手工修
            let (r, g) = match (default_renderer.renderer.as_str(), default_renderer.gapi.as_str()) {
                ("skia", "") => ("skia", ""),
                ("skia", "d3d") => ("skia", "d3d"),
                ("femtovg", _) => ("femtovg", ""),
                ("software", _) => ("software", ""),
                _ => ("", ""),
            };

            RendererInfo {
                renderer: r.into(),
                gapi: g.into(),
            }
        };
        if !default_renderer.renderer.is_empty() {
            if run_app(&program, &default_renderer.renderer, &default_renderer.gapi) {
                log::info!("Run with default renderer successfully");
                log::info!("App stop successfully");
                return;
            } else {
                log::warn!("Run with default renderer failed");
            }
        }

        // 尝试依次使用指定的渲染器
        let try_renderers = [
            ("skia", "d3d", "skia-d3d"),
            ("skia", "", "skia-software"),
            ("femtovg", "", "femtovg"),
            ("software", "", "software"),
        ];
        for (renderer, gapi, name) in try_renderers.iter() {
            if run_app(&program, renderer, gapi) {
                log::info!("Run with {} successfully", name);
                log::info!("App stop successfully");
                return;
            } else {
                log::warn!("Run with {} failed", name);
            }
        }

        log::info!("App stop due to failed operation");
    }
}

fn main() {
    #[cfg(target_os = "windows")]
    windows_impl::run();

    #[cfg(not(target_os = "windows"))]
    println!("tiwrapper is only supported on Windows");
}
