import { <PERSON>, <PERSON><PERSON>s, <PERSON><PERSON>iew, <PERSON>, <PERSON>mbo<PERSON>ox, <PERSON><PERSON>, <PERSON>dius, <PERSON><PERSON><PERSON> } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";
import { StkTrd_Com, StkTrd_CreditContract } from "struct.slint";

export component CreditContractPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;
    private property <int> sel_return_type;     // 1. 融资; 2. 融券

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "合约类型";
                vertical_alignment: center;
            }

            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> StkTrd_CreditContract.side_str;
                model: [
                    { text: "", tag: 0, },
                    { text: "融资买入", tag: 3, },
                    { text: "融券卖出", tag: 4, },
                ];

                selected => {
                    StkTrd_CreditContract.side = self.current_value.tag;
                    StkTrd_CreditContract.filter_changed();
                }
            }

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> StkTrd_CreditContract.exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkTrd_CreditContract.filter_changed();
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }

            i_cbx_insid := ComboBox {
                width: 100px;
                max_popup_height: root.height - 50px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> StkTrd_CreditContract.insid;
                model <=> StkTrd_CreditContract.insid_model;

                edited(by_selected) => {
                    StkTrd_CreditContract.filter_changed();
                }

                selected => {
                    StkTrd_CreditContract.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            Button {
                width: 80px;
                height: 26px;
                background: WidgetColor.btn_background;
                text: "直接还款";
                clicked => {
                    if "" == StkTrd_CreditContract.reamt_accid {
                        StkTrd_CreditContract.reamt_accid = StkTrd_Com.accountid;
                    }
                    StkTrd_CreditContract.show_return_amt = true;
                }
            }

            Button {
                width: 80px;
                height: 26px;
                background: WidgetColor.btn_background;
                enabled: false; // 融券相关功能未实现
                text: "现券还券";
                clicked => {
                    if "" == StkTrd_CreditContract.repos_accid {
                        StkTrd_CreditContract.repos_accid = StkTrd_Com.accountid;
                    }
                    StkTrd_CreditContract.show_return_pos = true;
                }
            }

            ExportButton {
                clicked(type) => {
                    StkTrd_CreditContract.export_clicked(type);
                }
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 6,
                columns: [
                    /*  0 */{ title: @tr("开仓日期"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    /*  1 */{ title: @tr("到期日期"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    /*  2 */{ title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    /*  3 */{ title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    /*  4 */{ title: @tr("合约类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },

                    /*  5 */{ title: @tr("委托编号 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid, tips: "历史合约 : 导入的合约编码 \n当日合约 : 交易所报单编码" },

                    /*  6 */{ title: @tr("合约数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /*  7 */{ title: @tr("合约金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /*  8 */{ title: @tr("合约费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    /*  9 */{ title: @tr("合约利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },

                    /* 10 */{ title: @tr("已还数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 11 */{ title: @tr("已还金额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110, tips: "已还本金 + 已还费用" },
                    /* 12 */{ title: @tr("已还利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },

                    /* 13 */{ title: @tr("当日已还数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 14 */{ title: @tr("当日已还本金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 15 */{ title: @tr("当日已还费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 16 */{ title: @tr("当日已还利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_105 },

                    /* 17 */{ title: @tr("未还数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 18 */{ title: @tr("未还金额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110, tips: "未还本金 + 未还费用" },
                    /* 19 */{ title: @tr("未还利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    /* 20 */{ title: @tr("合约负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },

                    /* 21 */{ title: @tr("最新价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    /* 22 */{ title: @tr("折算率"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    /* 23 */{ title: @tr("保证金率 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80, tips: "融资买入 : 融资保证金率\n融券卖出 : 融券保证金率" },

                    /* 24 */{ title: @tr("融资折算未还量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 25 */{ title: @tr("融资盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 26 */{ title: @tr("融资保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                    /* 27 */{ title: @tr("融券卖出金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 28 */{ title: @tr("融券盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 29 */{ title: @tr("融券保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                    /* 30 */{ title: @tr("委托价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    /* 31 */{ title: @tr("委托数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 32 */{ title: @tr("委托金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                    /* 33 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    /* 34 */{ title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },
                    /* 35 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                ]
            };
            row_count: StkTrd_CreditContract.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_CreditContract.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkTrd_CreditContract.get_row_data_color(row_index, column_index, data)
            }

            current_cell_changed(row_index, column_index) => {
                if "融资买入" == StkTrd_CreditContract.row_data[row_index][4] {
                    sel_return_type = 1;

                    if StkTrd_CreditContract.reamt_accid != StkTrd_CreditContract.row_data[row_index][35] {
                        StkTrd_CreditContract.reamt_accid = StkTrd_CreditContract.row_data[row_index][35];
                    }

                    if StkTrd_CreditContract.reamt_exchid != StkTrd_CreditContract.row_data[row_index][33] {
                        StkTrd_CreditContract.reamt_exchid = StkTrd_CreditContract.row_data[row_index][33];
                    }

                    // 暂不支持还指定合约
                    // if StkTrd_CreditContract.reamt_insid != StkTrd_CreditContract.row_data[row_index][3] {
                    //     StkTrd_CreditContract.reamt_insid = StkTrd_CreditContract.row_data[row_index][3];
                    // }
                } else {
                    sel_return_type = 2;

                    if StkTrd_CreditContract.repos_accid != StkTrd_CreditContract.row_data[row_index][35] {
                        StkTrd_CreditContract.repos_accid = StkTrd_CreditContract.row_data[row_index][35];
                    }

                    if StkTrd_CreditContract.repos_exchid != StkTrd_CreditContract.row_data[row_index][33] {
                        StkTrd_CreditContract.repos_exchid = StkTrd_CreditContract.row_data[row_index][33];
                    }

                    if StkTrd_CreditContract.repos_insid != StkTrd_CreditContract.row_data[row_index][3] {
                        StkTrd_CreditContract.repos_insid = StkTrd_CreditContract.row_data[row_index][3];
                    }
                }
            }

            cell_pointer_event(row_index, column_index, event, point) => {
                if PointerEventButton.right == event.button && PointerEventKind.up == event.kind {
                    self.select_full_row = true;
                    self.set_current_row(row_index);

                    right_up_point = point;
                    if 1 == sel_return_type {
                        if !StkTrd_CreditContract.show_return_amt {
                            // i_pw_menu.show();    暂时不支持右键归还菜单,还指定为指定合约,不是证券代码
                        }
                    }
                    if 2 == sel_return_type {
                        if !StkTrd_CreditContract.show_return_pos {
                            // i_pw_menu.show();    暂不支持右键归还菜单,还券未实现
                        }
                    }
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_CreditContract.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_CreditContract.row_data[row_index]);
                }
            }
        }
    }

    i_pw_menu := PopupWindow {

        x: right_up_point.x - root.absolute_position.x;
        y: right_up_point.y - root.absolute_position.y;

        Rectangle {

            width: 100px;

            border-width: 1px;
            border-color: Theme.border;
            border_radius: Radius.extra_small;

            drop_shadow_blur: 15px;
            drop_shadow_color: Theme.control_background.darker(0.5);

            background: Theme.control_background;

            VerticalLayout {
                padding: 5px;
                spacing: 10px;

                i_menu_fundtrans := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 16px;
                            source: Icons.list;
                        }

                        Label {
                            text: 1 == sel_return_type ? "直接还款" : "直接还券";
                            horizontal-alignment: left;
                        }
                    }

                    i_menu_ta := TouchArea {
                        clicked => {
                            if 1 == sel_return_type {
                                StkTrd_CreditContract.show_return_amt = true;
                            } else {
                                StkTrd_CreditContract.show_return_pos = true;
                            }
                        }
                    }
                }
            }
        }

        states [
            postrans_has_hover when i_menu_ta.has_hover: {
                i_menu_fundtrans.background: Theme.shadow;
            }
        ]
    }
}
