use crate::{
    protocol::{
        ptapp::AppHeartbeat,
        ptmd::{MdSubscribeField, PTReqMdLogin, PTRtnMarketDataL1},
    },
    serverinfo::MDServerInfo,
};

/// 行情回调类
pub trait IMdSpi: Send + Sync {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    /// <br> `heartbeat` 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeat) {}

    /// 请求行情登录响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_login(&self, ec: i32, em: &String, reqid: i32, islast: i32) {}

    /// 请求行情登出响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_logout(&self, ec: i32, em: &String, reqid: i32, islast: i32) {}

    /// 请求行情订阅响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`sub`: 行情订阅字段
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_md_subscribe(&self, ec: i32, em: &String, sub: &MdSubscribeField, reqid: i32, islast: i32) {}

    /// 请求行情退订阅响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`sub`: 行情退订阅字段
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_md_un_subscribe(&self, ec: i32, em: &String, sub: &MdSubscribeField, reqid: i32, islast: i32) {}

    /// L1行情推送
    /// <br>`md`: L1行情
    fn on_rtn_market_data_l1(&self, md: PTRtnMarketDataL1) {}
}

/// 行情API接口
#[async_trait::async_trait]
pub trait MdApi: Send + Sync {
    /// 设置服务端信息
    async fn set_svrinfo(&mut self, si: &MDServerInfo);

    /// 初始化
    async fn init(&mut self) -> crate::Result<()>;

    /// 释放
    async fn release(&mut self);

    /// 请求登录
    async fn req_login(&self, req: &PTReqMdLogin) -> crate::Result<()>;

    /// 请求登出
    async fn req_logout(&self) -> crate::Result<()>;

    /// 请求订阅
    async fn req_subscribe(&self, iid: &str) -> crate::Result<()>;

    /// 请求退订阅
    async fn req_unsubscribe(&self, iid: &str) -> crate::Result<()>;
}
