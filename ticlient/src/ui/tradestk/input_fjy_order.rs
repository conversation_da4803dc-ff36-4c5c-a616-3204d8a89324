use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        config::Config,
        global::{CFG, INS, TOKIO_RT, TRADE_STK_API},
        titype::TIType,
    },
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{CtpReqBusinessOrderField, CtpRspBusinessOrderField};

use super::tradestk::show_input_msg_box;

/// 非交易报单录入数据
struct InputFjyOrderDatas {
    /// 在报单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputfjyorder: AtomicBool,

    /// 非交易业务报单响应数据
    rsp_inputfjyorder: Arc<Mutex<CtpRspBusinessOrderField>>,

    /// 输入的合约是否有改变
    input_instid_changed: AtomicBool,
}
impl InputFjyOrderDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputfjyorder: AtomicBool::new(false),
            rsp_inputfjyorder: Arc::new(Mutex::new(CtpRspBusinessOrderField::default())),
            input_instid_changed: AtomicBool::new(false),
        }
    }
}
static INPUT_FJY_ORDER_DATAS: OnceLock<InputFjyOrderDatas> = OnceLock::new();

/// 非交易业务报单
pub(super) struct InputFjyOrder {}

/// 构建与初始化
impl InputFjyOrder {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化报单录入数据
        let _ = INPUT_FJY_ORDER_DATAS.set(InputFjyOrderDatas::new());

        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        // 交易所改变事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_exchid_sel_changed(move |exchid| InputFjyOrder::on_exchid_sel_changed(&app_weak, exchid));

        // 合约改变事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_insid_text_changed(move || InputFjyOrder::on_insid_text_changed(&app_weak));

        // 注册价格改变事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_price_text_changed(move || InputFjyOrder::on_price_text_changed(&app_weak));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_volume_text_changed(move || InputFjyOrder::on_volume_text_changed(&app_weak));

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_volume_updown_changed(move |upflag| InputFjyOrder::on_volume_updown_changed(&app_weak, upflag));

        // 注册报单价格上下调整时的事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_price_updown_changed(move |upflag| InputFjyOrder::on_price_updown_changed(&app_weak, upflag));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_req_tip_toggled(move |checked| InputFjyOrder::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputfjyord.on_ok_clicked(move |need_confirm| InputFjyOrder::on_ok_clicked(&app_weak, need_confirm));

        log::trace!("Input fjy order init completed");
    }

    /// 初始化
    pub fn init_async(&self, app: &crate::slintui::App) {
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();
        app_inputfjyord.invoke_exchid_sel_changed(TIType::EXCH_SSE);
    }

    /// 撤单响应
    pub fn on_rsp_businessorder(&self, rsp: CtpRspBusinessOrderField) {
        let inputfjyord_datas = INPUT_FJY_ORDER_DATAS.get().unwrap();
        *inputfjyord_datas.rsp_inputfjyorder.lock().unwrap() = rsp;
        inputfjyord_datas.is_err_inputfjyorder.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl InputFjyOrder {
    /// 交易所改变
    fn on_exchid_sel_changed(app_weak: &Weak<App>, exchid: i32) {
        let app = app_weak.unwrap();
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
        let inids_map = { INS.get().unwrap().read().unwrap().get_fjy_id_with_name_by_exchid(exchid) };
        inids_map.iter().for_each(|it| {
            items.push(crate::slintui::LineItem {
                text: slint::format!("{}", it.key()),
                remark: slint::format!("{}", it.value()),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(items.sort_by(move |r_a, r_b| r_a.text.cmp(&r_b.text)));
        app_inputfjyord.set_insid_model(sort_model.clone().into());
        app_inputfjyord.set_all_insid_model(sort_model.into());
    }

    /// 显示的合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputfjyord_datas = INPUT_FJY_ORDER_DATAS.get().unwrap();
        inputfjyord_datas.input_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        let insid = app_inputfjyord.get_insid();
        let ins = { INS.get().unwrap().read().unwrap().get_fjy_ins_by_insid(insid.as_str()) };
        if ins.is_none() {
            app_inputfjyord.set_insname("".into());
            app_inputfjyord.set_insid_has_error(true);
            return true;
        }
        let ins = ins.unwrap();
        if ins.exchid != app_inputfjyord.get_exchid() {
            app_inputfjyord.invoke_set_exch(ins.exchid);
            inputfjyord_datas.input_instid_changed.store(true, Ordering::Relaxed);
        }

        app_inputfjyord.set_insname(ins.name.clone().into());
        app_inputfjyord.set_tickvolume(ins.sh_tickvol);
        app_inputfjyord.set_maxvolume(ins.sh_maxvol);
        app_inputfjyord.set_minvolume(ins.sh_minvol);

        app_inputfjyord.set_insid_has_error(false);

        true
    }

    /// 报单价格有变动
    fn on_price_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        let price = app_inputfjyord.get_price().replace(",", "");
        if price.is_empty() {
            app_inputfjyord.set_price_has_error(true);
            return false;
        }

        if price.trim().parse::<f64>().unwrap_or_default() <= Config::INVALID_PRICE {
            app_inputfjyord.set_price_has_error(true);
            return false;
        }

        app_inputfjyord.set_price_has_error(false);

        true
    }

    /// 报单数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        let volume = app_inputfjyord.get_volume().replace(",", "");
        if volume.is_empty() {
            app_inputfjyord.set_volume_has_error(true);
            return false;
        }

        let volume = volume.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            app_inputfjyord.set_volume_has_error(true);
            return false;
        }

        app_inputfjyord.set_volume_has_error(false);

        true
    }

    /// 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        if app_inputfjyord.get_volume_has_error() {
            return;
        }

        let volume_str = app_inputfjyord.get_volume().replace(",", "");
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            return;
        }

        let tickvolume = app_inputfjyord.get_tickvolume();
        if upflag {
            volume += tickvolume;
        } else {
            volume -= tickvolume;
            if volume < tickvolume {
                volume = tickvolume;
            }
        }

        app_inputfjyord.set_volume(slint::format!("{}", volume));
    }

    /// 报单价格上下调整时的事件
    fn on_price_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        if app_inputfjyord.get_price_has_error() {
            return;
        }

        let price_str = app_inputfjyord.get_price().replace(",", "");
        let mut price = price_str.trim().parse::<f64>().unwrap_or_default();
        if price <= Config::INVALID_PRICE {
            return;
        }

        let tickprice = app_inputfjyord.get_tickprice() as f64;

        if upflag {
            price += tickprice;
        } else {
            price -= tickprice;
            if price <= Config::INVALID_PRICE {
                return;
            }
        }

        app_inputfjyord.set_price(slint::format!("{:.*}", Config::STK_PRICE_DIGITS, price));
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();
        app_set.set_trdreqtip_fjy_inputorder(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.FjyInputOrder = checked;
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

        // 获取参数
        let accid = app_inputfjyord.get_accountid();
        let insid = app_inputfjyord.get_insid();
        let exchid = app_inputfjyord.get_exchid_str();
        let side = app_inputfjyord.get_is_buy();
        let price = app_inputfjyord.get_price().replace(",", "");
        let volume = app_inputfjyord.get_volume().replace(",", "");

        // 输入参数检查
        {
            if accid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "账户不能为空".into());
                return;
            }

            if exchid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "请先选择市场".into());
                return;
            }

            if insid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取非交易证券信息失败".into());
                return;
            }

            if price.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "价格不能为空".into());
                return;
            }

            if volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "金额不能为空".into());
                return;
            }
        }

        let ret = price.trim().parse::<f64>();
        if ret.is_err() {
            show_input_msg_box(&app, 1, Default::default(), "请输入有效的价格".into());
            return;
        }
        let price = ret.ok().unwrap();

        let ret = volume.trim().parse::<u32>();
        if ret.is_err() {
            show_input_msg_box(&app, 1, Default::default(), "请输入有效的金额(只支持整数)".into());
            return;
        }
        let volume = ret.ok().unwrap();

        let fjy_type = InputFjyOrder::get_business_type(&exchid, app_inputfjyord.get_fjy_type());
        if fjy_type.is_none() {
            show_input_msg_box(&app, 1, Default::default(), "获取非交易业务类型错误".into());
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.FjyInputOrder } {
            show_input_msg_box(
                &app,
                100,
                "非交易业务报单录入确认".into(),
                slint::format!(
                    "账户:{}\n\n交易所:{}    代码:{}\n\n[{}] {}, 价格:{} ",
                    accid,
                    exchid,
                    insid,
                    {
                        if side {
                            "买入"
                        } else {
                            "卖出"
                        }
                    },
                    volume,
                    price
                ),
            );
            return;
        }

        let mut req = CtpReqBusinessOrderField::default();
        req.BusinessType = fjy_type.unwrap();
        req.AccountID = accid.into();
        req.ExchID = exchid.into();
        req.InsID = insid.into();
        req.DstInsID = "".to_owned();
        req.Volume = volume as i32;
        req.Price = price;
        req.Side = match side {
            true => 1,  // 买
            false => 2, // 卖
        };

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let api = TRADE_STK_API.get().unwrap().read().await;
            if let Err(err) = api.req_fjy_orderinput(0, &req).await {
                let _ = wapp.upgrade_in_event_loop(move |app| {
                    show_input_msg_box(
                        &app,
                        1,
                        Default::default(),
                        slint::format!("请求非交易业务报单录入失败\n\n错误信息: {}", err),
                    );
                });
            }
        });
    }
}

impl InputFjyOrder {
    /// 获取非交易业务类型
    fn get_business_type(exchid: &str, fjy_type: i32) -> Option<i32> {
        if "SSE" == exchid {
            return Some(fjy_type + 1);
        } else {
            if 0 == fjy_type {
                return Some(1);
            }
            if 1 == fjy_type {
                return Some(18);
            }
        }

        None
    }
}

/// 更新UI
impl InputFjyOrder {
    // 更新合约下拉列表
    async fn update_insid(&self, app_weak: &Weak<App>) {}

    // 更新错误信息
    async fn update_errmsg(&self, app_weak: &Weak<App>) {
        let inputfjyord_datas = INPUT_FJY_ORDER_DATAS.get().unwrap();

        let is_err_inputfjyorder = inputfjyord_datas
            .is_err_inputfjyorder
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputfjyorder {
            return;
        }

        let rsp = { inputfjyord_datas.rsp_inputfjyorder.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("非交易业务报单录入响应失败\n\n错误码:{}\n错误信息:{}", rsp.ec, rsp.em),
            );
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新报单响应错误信息
        self.update_errmsg(&app_weak).await;

        // 更新合约下拉列表
        self.update_insid(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let inputfjyord_datas = INPUT_FJY_ORDER_DATAS.get().unwrap();
        if inputfjyord_datas
            .input_instid_changed
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok()
        {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_inputfjyord = app.global::<StkTrd_InputFjyOrder>();

                let insid = app_inputfjyord.get_insid();

                if insid.len() > 6 + 5 {
                    // 目前证券合约的编码长度只有6位
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    app_inputfjyord.set_insid_model(items.into());
                    return;
                }

                let all_insid_data = app_inputfjyord.get_all_insid_model();

                if insid.is_empty() {
                    app_inputfjyord.set_insid_model(all_insid_data);
                    return;
                }

                let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());

                let mut find_flag = false;
                for i in 0..all_insid_data.row_count() {
                    let li = all_insid_data.row_data(i).unwrap();
                    if li.text.starts_with(insid.as_str()) {
                        items.push(li);
                        find_flag = true;
                    } else {
                        if find_flag {
                            break;
                        }
                    }
                }

                app_inputfjyord.set_insid_model(items.into());
            });
        }
    }
}
