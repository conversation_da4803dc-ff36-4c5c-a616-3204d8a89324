import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { PositionPage } from "../detail/position.slint";
import { CreditContractPage } from "credit_contract.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";

export component LeftMiddlePage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "持仓" },
        { text: "合约" },
        { text: "在途订单" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.letf_middle_selidx;
        }

        if 0 == StkRsk_TabSelIdx.letf_middle_selidx: PositionPage { }
        if 1 == StkRsk_TabSelIdx.letf_middle_selidx: CreditContractPage { }
        if 2 == StkRsk_TabSelIdx.letf_middle_selidx: Rectangle { Text {text: "在途订单";}}
    }
}
