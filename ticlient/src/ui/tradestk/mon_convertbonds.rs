use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
};

use crate::{
    apiproc::tradestkbuf::CtpConvertBondsMonMap,
    common::{
        config::Config,
        global::{CFG, TRADE_STK_BUF},
        ticonvert::TIConvert,
    },
    slintui::*,
};
use slint::*;

/// 监控 - 可转债
pub(super) struct MonConvertBonds {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpConvertBondsMonMap>,
}

impl MonConvertBonds {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpConvertBondsMonMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let mon = app.global::<StkTrd_MonConvertBonds>();

        let app_weak = app.as_weak();
        mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonConvertBonds::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor convert bonds init completed");
    }
}

impl MonConvertBonds {
    /// 查询可转债信息
    pub fn qry_convertbonds(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_convertbonds() };
        if ret.is_empty() {
            return;
        }

        for (key, oic) in ret {
            self.data_map.insert(key, oic);
        }
        self.data_changed.store(true, Ordering::Relaxed);
    }
}

impl MonConvertBonds {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 是否异常
        if 4 == column_index {
            if "是" == data.as_str() {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }
}

impl MonConvertBonds {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                self.data_map.clone()
            } else {
                Arc::new(CtpConvertBondsMonMap::new())
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            let cbmon = { CFG.get().unwrap().read().unwrap().stk_mon.CovrtBondMonitor.clone() };
            data_map_clone.iter().for_each(|cb| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(cb.AccountID.clone().into());

                // 交易所
                items.push(TIConvert::exch_name(cb.ExchID).into());

                // 交易编码
                items.push(cb.ClientID.clone().into());

                // 合约编码
                items.push(cb.InstrumentID.clone().into());

                let mut ratio = 0.;
                let mut isabnormal = "否";
                if cb.MaxPosition > 0 {
                    ratio = (cb.BuyVol + cb.SellVol) as f64 / 2.0 / cb.MaxPosition as f64;
                    if (cb.BuyAmount >= cbmon.TradeAmount || cb.SellAmount >= cbmon.TradeAmount) && ratio >= cbmon.Ratio {
                        isabnormal = "是";
                    }
                }

                // 是否异常
                items.push(isabnormal.into());

                // 买入金额
                items.push(TIConvert::format_f(cb.BuyAmount, Config::AMT_DIGITS).into());

                // 卖出金额
                items.push(TIConvert::format_f(cb.SellAmount, Config::AMT_DIGITS).into());

                // 比率
                items.push(slint::format!("{:.2}", ratio));

                // 买入数量
                items.push(slint::format!("{}", cb.BuyVol));

                // 卖出数量
                items.push(slint::format!("{}", cb.SellVol));

                // 最大持仓量
                items.push(slint::format!("{}", cb.MaxPosition));

                // 当前持仓量
                items.push(slint::format!("{}", cb.Position));

                // 发生时间
                items.push(TIConvert::transtime(cb.TransTime).into());

                row_data.push(items.into());
            });

            app.global::<StkTrd_MonConvertBonds>().set_row_data(row_data.into());
        });
    }
}
