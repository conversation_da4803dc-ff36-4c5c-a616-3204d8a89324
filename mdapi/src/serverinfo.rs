/// 行情配置
#[derive(<PERSON><PERSON>, Debug)]
pub struct MDServerInfo {
    /// 是否加密. 值为1时加密,其他值不加密
    pub encon: i32,
    /// 服务端地址
    pub host: String,
    /// 端口
    pub port: i32,
    /// uri
    pub uri: String,
}

impl Default for MDServerInfo {
    fn default() -> Self {
        Self {
            encon: 1,
            host: Default::default(),
            port: Default::default(),
            uri: Default::default(),
        }
    }
}

impl MDServerInfo {
    /// 获取连接URL
    pub(crate) fn get_url(&self) -> String {
        if 1 == self.encon {
            format!("wss://{}:{}{}", self.host, self.port, self.uri)
        } else {
            format!("ws://{}:{}{}", self.host, self.port, self.uri)
        }
    }
}
