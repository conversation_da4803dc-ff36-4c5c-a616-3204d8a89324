import { Theme, Icons, GroupListView, GroupListViewItem, VerticalSideBar, Switch, Label, LineEdit, ComboBox } from "@zstk/lib.slint";
import { StkTrd_Setting, StkTrd_Com } from "struct.slint";
import { AppCom } from "../struct.slint";
import { WndRec } from "../widgets/wndrec.slint";

// 设置 - 公共 - 主题
component SetComTheme {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        StkTrd_Setting.set_txt = "主题";
    }

    VerticalLayout {
        padding: 20px;

        HorizontalLayout {
            spacing: 20px;

            Label {
                text: "外观";
                vertical-alignment: center;
            }

            ComboBox {
                width: 90px;
                height: 24px;
                model: [
                    { text: "跟随系统" }, { text: "浅色" }, { text: "深色" }
                ];

                init => {
                    self.select(AppCom.theme_style);
                }

                selected(index) => {
                    AppCom.set_theme_style(index);
                }
            }

            Rectangle {}
        }

        Rectangle {}
    }
}

// 设置 - 交易 - 交易请求确认
component SetTrdReqConfirm {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        StkTrd_Setting.set_txt = "交易请求确认";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        if !StkTrd_Com.iscredit : GridLayout {
            spacing: 10px;

            Row {
                Label {
                    width: 150px;
                    text: "报单请求确认";
                }

                Switch {
                    checked <=> StkTrd_Setting.trdreqtip_inputorder;
                    toggled => {
                        StkTrd_Setting.trdreqtip_inputorder_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "撤单请求确认";
                }

                Switch {
                    checked <=> StkTrd_Setting.trdreqtip_ordercancel;
                    toggled => {
                        StkTrd_Setting.trdreqtip_ordercancel_changed();
                    }
                }
            }

            Row {
                Rectangle {
                    height: 1px;
                 }
            }

            Row {
                Label {
                    text: "非交易业务报单请求确认";
                }

                Switch {
                    checked <=> StkTrd_Setting.trdreqtip_fjy_inputorder;
                    toggled => {
                        StkTrd_Setting.trdreqtip_fjy_inputorder_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "非交易业务撤单请求确认";
                }

                Switch {
                    checked <=> StkTrd_Setting.trdreqtip_fjy_ordercancel;
                    toggled => {
                        StkTrd_Setting.trdreqtip_fjy_ordercancel_changed();
                    }
                }
            }
        }

        if StkTrd_Com.iscredit : GridLayout {
            spacing: 10px;

            Row {
                Label {
                    width: 150px;
                    text: "报单请求确认";
                }

                Switch {
                    checked <=> StkTrd_Setting.trdreqtip_inputorder;
                    toggled => {
                        StkTrd_Setting.trdreqtip_inputorder_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "撤单请求确认";
                }

                Switch {
                    checked <=> StkTrd_Setting.trdreqtip_ordercancel;
                    toggled => {
                        StkTrd_Setting.trdreqtip_ordercancel_changed();
                    }
                }
            }
        }

        Label {
            text: "提示: 本页选项每次登录均默认开启, 设置仅用于本次登录";
        }
    }
}

// 设置 - 异常监控 - 可转债
component SetMonitorBondCovrt {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        StkTrd_Setting.set_txt = "异常监控 - 可转债";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        Label {
            height: 26px;
            text: "同时满足以下条件, 则认为可转债交易异常";
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "1. 成交金额阀值大于等于  ";
                vertical_alignment: center;
            }

            i_le_amount := LineEdit {
                width: 120px;
                text: StkTrd_Setting.mon_convertbonds.trade_amount;
                placeholder_text: "值为大于0的小数";
                has_error: 1 == StkTrd_Setting.mon_convertbonds_err_status;

                edited => {
                    StkTrd_Setting.mon_convertbonds.trade_amount = self.text;
                    StkTrd_Setting.mon_convertbonds_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "2. ((买入成交数量 + 卖出成交数量) / 2) / 当日最大持仓数量 大于等于  ";
                vertical_alignment: center;
            }

            i_le_ratio := LineEdit {
                width: 120px;
                text: StkTrd_Setting.mon_convertbonds.ratio;
                placeholder_text: "值为大于0的小数";
                has_error: 2 == StkTrd_Setting.mon_convertbonds_err_status;

                edited => {
                    StkTrd_Setting.mon_convertbonds.ratio = self.text;
                    StkTrd_Setting.mon_convertbonds_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                width: 70px;
                text: "默认值";
                color: Theme.border_focus;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        StkTrd_Setting.reset_mon_convertbonds();
                        i_le_amount.text = StkTrd_Setting.mon_convertbonds.trade_amount;
                        i_le_ratio.text = StkTrd_Setting.mon_convertbonds.ratio;
                    }
                }
            }

            Label {
                text: "提示: 本页数据设置后, 重启后全部生效...";
            }
        }

        Label {
            text: StkTrd_Setting.mon_convertbonds_err_tips;
            color: red;
        }
    }
}

// 设置 - 异常监控 - 报撤单
component SetMonitorOrdInsertCancel {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        StkTrd_Setting.set_txt = "异常监控 - 报撤单";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        Label {
            height: 26px;
            text: "满足以下条件, 则认为报撤单异常";
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "总笔数大于等于  ";
                vertical_alignment: center;
            }

            i_le_number := LineEdit {
                width: 120px;
                text: StkTrd_Setting.mon_orderinsertcancel.greater_than_num;
                placeholder_text: "值为大于0的整数";
                has_error: 1 == StkTrd_Setting.mon_orderinsertcancel_err_status;

                edited => {
                    StkTrd_Setting.mon_orderinsertcancel.greater_than_num = self.text;
                    StkTrd_Setting.mon_orderinsertcancel_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                width: 70px;
                text: "默认值";
                color: Theme.border_focus;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        StkTrd_Setting.reset_mon_orderinsertcancel();
                        i_le_number.text = StkTrd_Setting.mon_orderinsertcancel.greater_than_num;
                    }
                }
            }

            Label {
                text: "提示: 本页数据设置后, 重启后全部生效...";
            }
        }

        Label {
            text: StkTrd_Setting.mon_orderinsertcancel_err_tips;
            color: red;
        }
    }
}

// 设置 - 异常监控 - 自成交
component SetMonitorTradeSelf {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        StkTrd_Setting.set_txt = "异常监控 - 自成交";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        Label {
            height: 26px;
            text: "满足以下条件, 则认为自成交异常";
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "自成交数量大于等于  ";
                vertical_alignment: center;
            }

            i_le_trdvol := LineEdit {
                width: 120px;
                text: StkTrd_Setting.mon_tradeself.trade_vol;
                placeholder_text: "值为大于0的整数";
                has_error: 1 == StkTrd_Setting.mon_tradeself_err_status;

                edited => {
                    StkTrd_Setting.mon_tradeself.trade_vol = self.text;
                    StkTrd_Setting.mon_tradeself_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                width: 70px;
                text: "默认值";
                color: Theme.border_focus;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        StkTrd_Setting.reset_mon_tradeself();
                        i_le_trdvol.text = StkTrd_Setting.mon_tradeself.trade_vol;
                    }
                }
            }

            Label {
                text: "提示: 本页数据设置后, 重启后全部生效...";
            }
        }

        Label {
            text: StkTrd_Setting.mon_tradeself_err_tips;
            color: red;
        }
    }
}

component SideBarView inherits VerticalSideBar {
    in_out property <[GroupListViewItem]> items <=> navigation.model;
    in_out property <{ parent: int, item: int}> current_item <=> navigation.current_item;

    callback current_item_changed <=> navigation.current_item_changed;

    forward_focus: navigation;

    navigation := GroupListView {
        vertical_stretch: 1;
        current_item: { item: 0, parent: 0 };
    }
}

component SetPages {
    in property <{ parent: int, item: int}> sel_item;

    preferred_width: 100%;
    preferred_height: 100%;

    if (0 == root.sel_item.parent && 0 == root.sel_item.item): SetComTheme { }

    if (1 == root.sel_item.parent && 0 == root.sel_item.item): SetTrdReqConfirm { }

    if (2 == root.sel_item.parent && 0 == root.sel_item.item): SetMonitorBondCovrt { }
    if (2 == root.sel_item.parent && 1 == root.sel_item.item): SetMonitorOrdInsertCancel {
    }
    if (2 == root.sel_item.parent && 2 == root.sel_item.item): SetMonitorTradeSelf {
    }
}

export component SettingPage {

    private property <bool> hover_close_btn;

    private property <GroupListViewItem> widgets1: { text: "公共", items: [
        { leading_icon: Icons.list, text: "主题" },
    ] };

    private property <GroupListViewItem> widgets2: { text: "交易", items: [
        { leading_icon: Icons.list, text: "交易请求确认" },
    ] };

    private property <GroupListViewItem> resources: {
        text: "异常监控",
        items: [
            { leading_icon: Icons.list, text: "可转债" },
            { leading_icon: Icons.list, text: "报撤单" },
            { leading_icon: Icons.list, text: "自成交" },
        ]
    };

    preferred-width: 100%;
    preferred-height: 100%;

    WndRec {
        title: @tr("{}", StkTrd_Setting.set_txt);
        btn_close_clicked => {
            AppCom.show_set = false;
        }

        Rectangle {
            HorizontalLayout {
                SideBarView {
                    resizable: true;
                    title: @tr("设置");
                    items: [root.widgets1, root.widgets2, root.resources];
                    current_item <=> StkTrd_Setting.sel_item;
                }

                Rectangle {
                    SetPages {
                        sel_item: StkTrd_Setting.sel_item;
                    }
                }
            }
        }
    }
}
