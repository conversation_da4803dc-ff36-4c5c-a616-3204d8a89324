import { Theme, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom, NotifyColor } from "../../../struct.slint";
import { PageItem } from "../../../widgets/page.slint";
import { TimeSelect, TimeItem } from "../../../widgets/time.slint";
import { StkRskCredit_QryTrade } from "../struct.slint";

export component QryTradePage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkRskCredit_QryTrade.exchid;
        i_le_insid.text = StkRskCredit_QryTrade.insid;
        i_le_tradeid.text = StkRskCredit_QryTrade.tradeid;
        i_le_ordsysid.text = StkRskCredit_QryTrade.ordersysid;
        i_ts_start.time = StkRskCredit_QryTrade.starttime;
        i_ts_end.time = StkRskCredit_QryTrade.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            i_cbx_exch := ComboBox {
                width: 70px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text: StkRskCredit_QryTrade.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }
            i_le_insid := LineEdit {
                width: 85px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text: StkRskCredit_QryTrade.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "成交编号";
                vertical_alignment: center;
            }
            i_le_tradeid := LineEdit {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text: StkRskCredit_QryTrade.tradeid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "报单编码";
                vertical_alignment: center;
            }
            i_le_ordsysid := LineEdit {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text: StkRskCredit_QryTrade.ordersysid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "发生时间";
                vertical_alignment: center;
            }
            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: StkRskCredit_QryTrade.starttime;
                    height: AppCom.widgets_height;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: StkRskCredit_QryTrade.endtime;
                    height: AppCom.widgets_height;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                    StkRskCredit_QryTrade.exchid = i_cbx_exch.current_text;
                    StkRskCredit_QryTrade.insid = i_le_insid.text;
                    StkRskCredit_QryTrade.tradeid = i_le_tradeid.text;
                    StkRskCredit_QryTrade.ordersysid = i_le_ordsysid.text;
                    StkRskCredit_QryTrade.starttime = i_ts_start.time;
                    StkRskCredit_QryTrade.endtime = i_ts_end.time;
                    StkRskCredit_QryTrade.starttime_int = i_ts_start.time_int;
                    StkRskCredit_QryTrade.endtime_int = i_ts_end.time_int;

                    StkRskCredit_QryTrade.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 3,
                columns: [
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("成交数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("成交价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    { title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    { title: @tr("成交编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.tradeid },
                    { title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },

                ]
            };
            row_count: StkRskCredit_QryTrade.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_QryTrade.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_QryTrade.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_QryTrade.row_data[row_index]);
                }
            }
        }

        if StkRskCredit_QryTrade.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkRskCredit_QryTrade.page_index_model;
            item_total <=> StkRskCredit_QryTrade.item_total;
            page_total <=> StkRskCredit_QryTrade.page_total;
            page_index <=> StkRskCredit_QryTrade.page_index;
            page_size <=> StkRskCredit_QryTrade.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryTrade.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryTrade.page_size_changed(size);
            }
        }
    }
}
