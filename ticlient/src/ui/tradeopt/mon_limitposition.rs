use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::apiproc::tradeoptbuf::CtpLimitPositionMap;

use crate::{
    common::{global::TRADE_OPT_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type IDSet = dashmap::DashSet<String>;

/// 监控 - 限仓 - 数据
struct MonLimitPosDatas {
    /// 数据
    data_map: Arc<CtpLimitPositionMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 交易编码列表
    clientid_set: Arc<IDSet>,

    /// 品种编码列表
    productid_set: Arc<IDSet>,

    /// 是否需要过滤数据
    filter_changed: AtomicBool,
}
impl MonLimitPosDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpLimitPositionMap::new()),
            data_changed: AtomicBool::new(false),

            clientid_set: Arc::new(IDSet::new()),
            productid_set: Arc::new(IDSet::new()),

            filter_changed: AtomicBool::new(false),
        }
    }
}
static MON_LIMIT_POSITION_DATAS: OnceLock<MonLimitPosDatas> = OnceLock::new();

/// 监控 - 限仓
pub(super) struct MonLimitPosition {}

// 构建与初始化
impl MonLimitPosition {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = MON_LIMIT_POSITION_DATAS.set(MonLimitPosDatas::new());

        let app_mon = app.global::<OptTrd_MonLimitPosition>();

        // 注册过滤条件改变事件
        let app_weak = app.as_weak();
        app_mon.on_filter_changed(move || MonLimitPosition::on_filter_changed(&app_weak));

        // 注册获取单元格颜色事件
        let app_weak = app.as_weak();
        app_mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonLimitPosition::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor limit position init completed");
    }
}

// 查询
impl MonLimitPosition {
    /// 查询限仓信息
    pub fn qry_limitposition(&self) {
        let ret = { TRADE_OPT_BUF.get().unwrap().read().unwrap().pop_limitposition() };
        if ret.is_empty() {
            return;
        }

        let mlp_datas = MON_LIMIT_POSITION_DATAS.get().unwrap();

        for (key, lp) in ret {
            if !mlp_datas.clientid_set.contains(&lp.ClientID) {
                mlp_datas.clientid_set.insert(lp.ClientID.clone());
            }
            if !mlp_datas.productid_set.contains(&lp.ProductID) {
                mlp_datas.productid_set.insert(lp.ProductID.clone());
            }

            mlp_datas.data_map.insert(key, lp);
        }
        mlp_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl MonLimitPosition {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 当前权利仓数量, 当前持仓数量, 当日买开数量
        if 5 == column_index || 7 == column_index || 9 == column_index {
            let app_mlp = app.global::<OptTrd_MonLimitPosition>();

            let ratio = app_mlp.invoke_get_cell_data(row_index, 11);
            if let Ok(ratio) = ratio.parse::<f64>() {
                let ratio = ratio - 10000.;
                if ratio < 0.8 {
                    return nc.get_normal();
                } else {
                    if ratio >= 1.0 {
                        return nc.get_error();
                    } else if ratio >= 0.9 {
                        return nc.get_caution();
                    } else {
                        return nc.get_prompt();
                    }
                }
            }
        }

        nc.get_default()
    }

    /// 过虑条件有变动
    fn on_filter_changed(app_weak: &Weak<App>) {
        MON_LIMIT_POSITION_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

// 更新UI
impl MonLimitPosition {
    // 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let mlp_datas = MON_LIMIT_POSITION_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mlp = app.global::<OptTrd_MonLimitPosition>();

            if mlp_datas.clientid_set.len() != app_mlp.invoke_clientid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mlp_datas.clientid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlp.set_clientid_model(id_arr.into());
            }

            if mlp_datas.productid_set.len() != app_mlp.invoke_productid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mlp_datas.productid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mlp.set_productid_model(id_arr.into());
            }
        });
    }

    // 更新限仓
    async fn update_limitpos(&self, app_weak: &Weak<App>) {
        let mlp_datas = MON_LIMIT_POSITION_DATAS.get().unwrap();

        let data_map_clone = {
            if mlp_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                mlp_datas.filter_changed.store(false, Ordering::Relaxed);
                mlp_datas.data_map.clone()
            } else {
                if mlp_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    mlp_datas.data_map.clone()
                } else {
                    Arc::new(CtpLimitPositionMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mlp = app.global::<OptTrd_MonLimitPosition>();

            let filter_exchid = app_mlp.get_exchid().to_string();
            let filter_cliid = app_mlp.get_clientid().to_string();
            let filter_pid = app_mlp.get_productid().to_string();

            // 填充数据
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|lp| {
                let exchname = TIConvert::exch_name(lp.ExchID);

                if !filter_exchid.is_empty() {
                    if exchname != filter_exchid {
                        return;
                    }
                }

                if !filter_cliid.is_empty() {
                    if !lp.ClientID.contains(&filter_cliid) {
                        return;
                    }
                }

                if !filter_pid.is_empty() {
                    if !lp.ProductID.contains(&filter_pid) {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(lp.AccountID.clone().into());

                // 交易所
                items.push(exchname.into());

                // 交易编码
                items.push(lp.ClientID.clone().into());

                // 品种编码
                items.push(lp.ProductID.clone().into());

                // 权利仓最大数量
                items.push(slint::format!("{}", lp.MaxLongVolume));

                // 当前权利仓数量
                items.push(slint::format!("{}", lp.CurLongVolume));

                // 总持仓最大数量
                items.push(slint::format!("{}", lp.MaxTotalVolume));

                // 当前持仓数量
                items.push(slint::format!("{}", lp.CurTotalVolume));

                // 当日买开最大数量
                items.push(slint::format!("{}", lp.MaxTodayBuyVolume));

                // 当日买开数量
                items.push(slint::format!("{}", lp.CurTodayBuyVolume));

                // 发生时间
                items.push(TIConvert::transtime(lp.TransTime).into());

                // sort_col(仅用于排序)
                let ratio = {
                    let r1 = if lp.MaxLongVolume > 0 {
                        lp.CurLongVolume as f64 / lp.MaxLongVolume as f64
                    } else {
                        0.0
                    };

                    let r2 = if lp.MaxTotalVolume > 0 {
                        lp.CurTotalVolume as f64 / lp.MaxTotalVolume as f64
                    } else {
                        0.0
                    };

                    let r3 = if lp.MaxTodayBuyVolume > 0 {
                        lp.CurTodayBuyVolume as f64 / lp.MaxTodayBuyVolume as f64
                    } else {
                        0.0
                    };

                    // 倍率初始值足够大. 防止排序时2.0大天12.1这样的情况
                    r1 + r2 + r3 + 10000.
                };
                items.push(ratio.to_string().into());

                row_data.push(items.into());
            });

            // 根据 sort_col 按降序排序
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(11).unwrap();
                let c_b = r_b.row_data(11).unwrap();
                c_b.cmp(&c_a)
            }));
            app_mlp.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新限仓
        self.update_limitpos(&app_weak).await;
    }
}
