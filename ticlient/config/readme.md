# si.json

用于APP的配置, 文件必须存在

### 标准格式:

```
[
    {
        "Name": "证券交易测试",
        "Tag": 201,
        "Host": "*************",
        "Port": 10988
    },

    ...
]
```

- Name: 服务端名称. 值必须唯一, 用于在终端上展示以供客户选择
- Tag: 客户端类型. 100:个股期权风控; 101:个股期权交易; 200:证券风控; 201:表示证券交易; 300:期货风控; 301:期货交易;
- Host: 服务端地址
- Port: 服务端端口

### 全字段格式 - 仅用于类似于开发环境里的配置

```
[
    {
        "Name": "111_期货风控测试",
        "Tag": 300,
        "encon": 0,
        "proxy": 0,
        "Host": "*************",
        "Port": 7600,
        "TrdHost": "*************",
        "TrdPort": 7500
    },
    ...
]
```

与标准格式相同的字段外的说明
- encon: 数据是否加密. 0:不加密; 1:加密
- proxy: 数据是否代理. 0:否; 1:是
- TrdHost: 仅风控使用. 风控端连接交易服务端的地址(如果不设置取Host的值)
- TrdPort: 仅风控使用. 风控端连接交易服务端的端口(如果不设置取Port的值)

# log.json

用于对日志的配置, 如果文件不存在使用默认配置(即记录info及更高级别的日志, 不过滤其他crate的日志)

### 字段说明

```
{
    "level": 2,
    "module": [
        {
            "name": "aaa",
            "level": 5
        }
    ]
}
```

1. level: 默认日志级别. 0:Off; 1:Error; 2:Warn; 3:Info; 4:Debug; 5:Trace
2. module: 对每个模块的具体设置. 其中: name表示模块的名称; level表示日志级别
