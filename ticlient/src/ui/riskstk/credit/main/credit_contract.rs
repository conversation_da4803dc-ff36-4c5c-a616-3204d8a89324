use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::CreditContractMap,
    common::{self, config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

///合约数据
struct CreditContractDatas {
    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl CreditContractDatas {
    pub fn new() -> Self {
        Self {
            filter_clicked: AtomicBool::new(false),
        }
    }
}
static CREDIT_CONTRACT_DATAS: OnceLock<CreditContractDatas> = OnceLock::new();

/// 合约
pub(in crate::ui::riskstk::credit) struct CreditContract {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CreditContractMap>,
}

impl CreditContract {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CreditContractMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = CREDIT_CONTRACT_DATAS.set(CreditContractDatas::new());

        let app_cc = app.global::<StkRskCredit_CreditContract>();

        let app_weak = app.as_weak();
        app_cc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditContract::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_cc.on_filter_changed(move || CreditContract::on_filter_changed(&app_weak));

        log::trace!("CreditContract init completed");
    }
}

impl CreditContract {
    /// 请求查询到期合约信息
    pub fn qry_credit_contract(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_credit_expire_contract() };
        if ret.is_empty() {
            return;
        }

        for (key, cc) in ret {
            self.data_map.insert(key, cc);
        }
        self.data_changed.store(true, Ordering::Relaxed);
    }
}

impl CreditContract {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();

        let nc = app.global::<NotifyColor>();

        if 5 == column_index {
            if "融资买入" == data.as_str() {
                return nc.get_caution();
            } else {
                return nc.get_normal();
            }
        }

        // 已还
        if 11 <= column_index && column_index <= 17 {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data > 0.0 {
                return nc.get_normal();
            }
        }

        // 未还
        if 18 <= column_index && column_index <= 21 {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data > 0.0 {
                return nc.get_error();
            }
        }

        nc.get_default()
    }

    fn on_filter_changed(app_weak: &Weak<App>) {
        CREDIT_CONTRACT_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }
}

impl CreditContract {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let cc_datas = CREDIT_CONTRACT_DATAS.get().unwrap();

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                cc_datas.filter_clicked.store(false, Ordering::Relaxed);
                self.data_map.clone()
            } else {
                if cc_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    self.data_map.clone()
                } else {
                    Arc::new(CreditContractMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_cc = app.global::<StkRskCredit_CreditContract>();

            let filter_accid = app_cc.get_filter_accid().to_string();
            let filter_side = app_cc.get_filter_side();
            let filter_exchid = app_cc.get_filter_exchid().to_string();
            let filter_insid = app_cc.get_filter_insid().to_string();
            let filter_ordsysid = app_cc.get_filter_ordsysid().to_string();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|cc| {
                let exchid = TIConvert::exch_name(cc.ExchID);

                if !filter_accid.is_empty() {
                    if !cc.AccID.contains(&filter_accid) {
                        return;
                    }
                }

                if 0 != filter_side {
                    if cc.Side != filter_side {
                        return;
                    }
                }

                if !filter_exchid.is_empty() {
                    if !exchid.contains(&filter_exchid) {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !cc.InsID.contains(&filter_insid) {
                        return;
                    }
                }

                if !filter_ordsysid.is_empty() {
                    if !cc.OrderSysID.contains(&filter_ordsysid) {
                        return;
                    }
                }

                let mut insname = "".to_owned();
                let mut digits = Config::STK_PRICE_DIGITS;
                {
                    if let Some(ins) = common::global::INS
                        .get()
                        .unwrap()
                        .read()
                        .unwrap()
                        .get_ins(cc.ExchID, &cc.InsID)
                    {
                        insname = ins.name.clone();
                        digits = ins.ptw as usize;
                    }
                };

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 开仓日期
                items.push(slint::format!("{}", cc.OpenDate));

                // 到期日期
                items.push(slint::format!("{}", cc.ExpireDate));

                // 资金账号
                items.push(cc.AccID.clone().into());

                // 股东代码
                items.push(cc.CliID.clone().into());

                // 证券代码
                items.push(cc.InsID.clone().into());

                // 合约类型
                items.push(TIConvert::side(cc.Side).into());

                // 委托编码
                items.push(cc.OrderSysID.clone().into());

                // 合约数量
                items.push(TIConvert::format_i(cc.Volume).into());

                // 合约金额
                items.push(TIConvert::format_f(cc.Amount, Config::AMT_DIGITS).into());

                // 合约手续费
                items.push(TIConvert::format_f(cc.Commi, Config::AMT_DIGITS).into());

                // 合约利息
                items.push(TIConvert::format_f(cc.Interest, Config::AMT_DIGITS).into());

                // 已还数量
                items.push(TIConvert::format_i(cc.ReVol).into());

                // 已还金额
                items.push(TIConvert::format_f(cc.ReAmt, Config::AMT_DIGITS).into());

                // 已还利息
                items.push(TIConvert::format_f(cc.ReInterest, Config::AMT_DIGITS).into());

                // 当日已还数量
                items.push(TIConvert::format_i(cc.TdReVol).into());

                // 当日已还金额
                items.push(TIConvert::format_f(cc.TdReAmt, Config::AMT_DIGITS).into());

                // 当日已还费用
                items.push(TIConvert::format_f(cc.TdReCommi, Config::AMT_DIGITS).into());

                // 当日已还利息
                items.push(TIConvert::format_f(cc.TdReInterest, Config::AMT_DIGITS).into());

                // 未还数量
                items.push(TIConvert::format_i(cc.NdVol).into());

                // 未还金额
                items.push(TIConvert::format_f(cc.NdAmt, Config::AMT_DIGITS).into());

                // 未还利息
                items.push(TIConvert::format_f(cc.NdInterest, Config::AMT_DIGITS).into());

                // 合约负债
                items.push(TIConvert::format_f(cc.NdAmt + cc.NdInterest, Config::AMT_DIGITS).into());

                // 最新价
                items.push(TIConvert::format_f(cc.LPrice, digits).into());

                // 折算率
                items.push(TIConvert::format_f(cc.DiscRatio, Config::AMT_DIGITS).into());

                // 保证金率
                items.push(TIConvert::format_f(cc.MarginRatio, Config::AMT_DIGITS).into());

                // 融资折算未还量
                items.push(TIConvert::format_i(cc.RzNdVol as i32).into());

                // 融资浮盈
                items.push(TIConvert::format_f(cc.XYBuy, Config::AMT_DIGITS).into());

                // 融资保证金
                items.push(TIConvert::format_f(cc.XYBuyMargin, Config::AMT_DIGITS).into());

                // 融券卖出金额
                items.push(TIConvert::format_f(cc.XYSellAmt, Config::AMT_DIGITS).into());

                // 融券浮盈
                items.push(TIConvert::format_f(cc.XYSell, Config::AMT_DIGITS).into());

                // 融券保证金
                items.push(TIConvert::format_f(cc.XYSellMargin, Config::AMT_DIGITS).into());

                // 委托价格
                items.push(TIConvert::format_f(cc.OrderPrice, digits).into());

                // 委托数量
                items.push(TIConvert::format_i(cc.OrderVolume).into());

                // 委托金额
                items.push(TIConvert::format_f(cc.OrderAmount, Config::AMT_DIGITS).into());

                // 交易所
                items.push(exchid.into());

                // 合约名称
                items.push(insname.into());

                row_data.push(items.into());
            });

            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = slint::format!(
                    "{:>8}{:>8}{:>20}",
                    r_a.row_data(1).unwrap(),
                    r_a.row_data(0).unwrap(),
                    r_a.row_data(6).unwrap()
                );
                let c_b = slint::format!(
                    "{:>8}{:>8}{:>20}",
                    r_b.row_data(1).unwrap(),
                    r_b.row_data(0).unwrap(),
                    r_b.row_data(6).unwrap()
                );
                c_a.cmp(&c_b)
            }));

            app_cc.set_row_data(sort_model.into());
        });
    }
}
