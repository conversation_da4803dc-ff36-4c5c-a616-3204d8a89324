use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    apiproc::tradeoptbuf::CtpQuoteMap,
    common::{
        config::Config,
        global::{CFG, TRADE_OPT_API, TRADE_OPT_BUF},
        ticonvert::TIConvert,
    },
    slintui::*,
};
use dashmap::DashMap;
use slint::*;
use tiapi::protocol_pub::{
    order_trade_ctp::CtpQuoteField,
    trade_req_ctp::{CtpReqInputQuoteActionField, CtpRspInputQuoteActionField},
};

use super::tradeopt::show_cancel_msg_box;

/// 在途 - 报价 - 数据
struct InQuoteDatas {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpQuoteMap>,

    /// 撤单响应数据
    rsp_cancel: Arc<Mutex<CtpRspInputQuoteActionField>>,

    /// 在撤单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_in_cancel: AtomicBool,

    /// 用于全撤时, 当在撤单过程中发生错误停止继续撤单
    need_stop_cancel: AtomicBool,
}
impl InQuoteDatas {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpQuoteMap::new()),

            rsp_cancel: Arc::new(Mutex::new(CtpRspInputQuoteActionField::default())),
            is_err_in_cancel: AtomicBool::new(false),

            need_stop_cancel: AtomicBool::new(false),
        }
    }
}
static IN_QUOTE_DATAS: OnceLock<InQuoteDatas> = OnceLock::new();

/// 在途 - 报价
pub(super) struct InQuote {}

// 构建与初始化
impl InQuote {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = IN_QUOTE_DATAS.set(InQuoteDatas::new());

        let app_inquote = app.global::<OptTrd_InQuote>();

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inquote.on_req_tip_toggled(move |checked| InQuote::on_req_tip_toggled(&app_weak, checked));

        // 注册撤单笔事件
        let app_weak: Weak<App> = app.as_weak();
        app_inquote
            .on_cancel_order_clieked(move |ord, need_confirm| InQuote::on_cancel_order_clieked(&app_weak, ord, need_confirm));

        // 注册撤全部事件
        let app_weak = app.as_weak();
        app_inquote.on_cancel_order_all_clieked(move |row_cnt, need_confirm| {
            InQuote::on_cancel_order_all_clieked(&app_weak, row_cnt, need_confirm)
        });

        log::trace!("InQuote init completed");
    }
}

// 查询与响应
impl InQuote {
    /// 请求查询报价信息
    pub fn qry_in_quote(&self) {
        let ret = { TRADE_OPT_BUF.get().unwrap().read().unwrap().pop_in_quote() };
        if ret.is_empty() {
            return;
        }

        let mut data_changed = false;
        let inquote_datas = IN_QUOTE_DATAS.get().unwrap();

        ret.iter().for_each(|it| {
            let key = it.key();
            let ord = it.value();

            if inquote_datas.data_map.contains_key(key) {
                if 1 != ord.LastStatus && 2 != ord.LastStatus {
                    inquote_datas.data_map.remove(key);
                    data_changed = true;
                } else {
                    let mut pre_quote = inquote_datas.data_map.get_mut(key).unwrap();
                    pre_quote.LastStatus = ord.LastStatus;
                    pre_quote.BidLastStatus = ord.BidLastStatus;
                    pre_quote.AskLastStatus = ord.AskLastStatus;
                    pre_quote.InsertTime = ord.InsertTime.clone();
                    data_changed = true;
                }
            } else {
                if 1 == ord.LastStatus || 2 == ord.LastStatus {
                    inquote_datas.data_map.insert(key.clone(), ord.clone());
                    data_changed = true;
                }
            }
        });

        if data_changed {
            inquote_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }

    /// 撤报价操作响应
    pub fn on_rsp_inputquoteaction(&self, rsp: CtpRspInputQuoteActionField) {
        let inquote_datas = IN_QUOTE_DATAS.get().unwrap();

        inquote_datas.need_stop_cancel.store(true, Ordering::Relaxed);

        *inquote_datas.rsp_cancel.lock().unwrap() = rsp;
        inquote_datas.is_err_in_cancel.store(true, Ordering::Relaxed);
    }
}

// 事件
impl InQuote {
    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();
        app_set.set_trdreqtip_quotecancel(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputQuoteAction = checked;
    }

    /// 撤单笔
    fn on_cancel_order_clieked(app_weak: &Weak<App>, ord: CancelOrderField, need_confirm: bool) {
        let app = app_weak.unwrap();
        let in_quote = app.global::<OptTrd_InQuote>();

        if ord.exchid.is_empty() {
            show_cancel_msg_box(&app, 1, Default::default(), "请先选中要撤的报价".into());
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputQuoteAction } {
            show_cancel_msg_box(
                &app,
                100,
                "撤报价请求确认".into(),
                slint::format!(
                    "资金账户: {}\n\n交易所: {}\n交易所编码: [{}]",
                    ord.accountid,
                    ord.exchid,
                    ord.ordersysid
                ),
            );

            return;
        }

        let req = CtpReqInputQuoteActionField {
            ActionFlag: 48,
            ExchangeID: ord.exchid.as_str().into(),
            InvestorID: ord.accountid.as_str().into(),
            QuoteSysID: ord.ordersysid.as_str().into(),
            ..Default::default()
        };

        let api = TRADE_OPT_API.get().unwrap().read().unwrap();
        if let Err(err) = api.req_quoteaction(0, &req) {
            show_cancel_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("请求报价失败.\n\nn错误信息: {}", err),
            );
        }
    }

    /// 撤全部
    fn on_cancel_order_all_clieked(app_weak: &Weak<App>, row_cnt: i32, need_confirm: bool) {
        let app = app_weak.unwrap();
        let in_quote = app.global::<OptTrd_InQuote>();

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputQuoteAction } {
            show_cancel_msg_box(&app, 101, Default::default(), "是否撤销当前所有的有效报价单?".into());
            return;
        }

        if 0 == row_cnt {
            show_cancel_msg_box(&app, 1, Default::default(), "没有报价单可撤".into());
            return;
        }

        // 获取当前所有的在途报价单
        let mut quote_vec = Vec::new();
        for row_index in 0..row_cnt {
            quote_vec.push(in_quote.invoke_get_cancel_order(row_index))
        }

        // 开始撤报价单
        let inquote_datas = IN_QUOTE_DATAS.get().unwrap();
        inquote_datas.need_stop_cancel.store(false, Ordering::Relaxed);
        let api = TRADE_OPT_API.get().unwrap().read().unwrap();
        for ord in quote_vec {
            if inquote_datas.need_stop_cancel.load(Ordering::Relaxed) {
                // 撤报价单过程中遇到错误, 不再继续撤报价单
                return;
            }

            let req = CtpReqInputQuoteActionField {
                ActionFlag: 48,
                ExchangeID: ord.exchid.as_str().into(),
                InvestorID: ord.accountid.as_str().into(),
                QuoteSysID: ord.ordersysid.as_str().into(),
                ..Default::default()
            };
            if let Err(err) = api.req_quoteaction(0, &req) {
                show_cancel_msg_box(
                    &app,
                    1,
                    Default::default(),
                    slint::format!(
                        "请求撤报价单失败\n\n资金账户: {}\n\n交易所: {}\n交易所报价编码: [{}]\n\n错误信息: {}",
                        ord.accountid,
                        ord.exchid,
                        ord.ordersysid,
                        err
                    ),
                );
                return;
            }
        }
    }
}

// 更新UI
impl InQuote {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let inquote_datas = IN_QUOTE_DATAS.get().unwrap();

        let is_err_in_cancel = inquote_datas
            .is_err_in_cancel
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        let rsp_cancel: CtpRspInputQuoteActionField;
        let data_map_clone: Arc<DashMap<String, CtpQuoteField>>;

        if !is_err_in_cancel {
            let data_changed = inquote_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok();
            if !data_changed {
                return;
            }

            rsp_cancel = CtpRspInputQuoteActionField::default();
            data_map_clone = inquote_datas.data_map.clone();
        } else {
            rsp_cancel = inquote_datas.rsp_cancel.lock().unwrap().clone();
            data_map_clone = Arc::new(DashMap::new());
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            if is_err_in_cancel {
                show_cancel_msg_box(
                    &app,
                    1,
                    "撤报价失败".into(),
                    slint::format!(
                        "交易所报价编号:{}\n错误信息:[{}, {}]",
                        rsp_cancel.QuoteSysID,
                        rsp_cancel.ec,
                        rsp_cancel.em
                    ),
                );
                return;
            }

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            data_map_clone.iter().for_each(|quote| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账号
                items.push(quote.InvestorID.clone().into());

                // 交易所
                items.push(quote.ExchangeID.clone().into());

                // 交易编码
                items.push(quote.ClientID.clone().into());

                // 发生时间
                items.push(quote.InsertTime.clone().into());

                // 合约编码
                items.push(quote.InstrumentID.clone().into());

                // 报价状态
                items.push(TIConvert::order_status(quote.LastStatus).into());

                // 交易所报价编号
                items.push(quote.QuoteSysID.clone().into());

                // 本地报价编号
                items.push(quote.QuoteLocalID.clone().into());

                // 买报价状态
                items.push(TIConvert::order_status(quote.BidLastStatus).into());

                // 买报单编号
                items.push(quote.BidOrderSysID.clone().into());

                // 买开平标志
                items.push(if 0 != quote.BidVolume {
                    TIConvert::ctp_offsetflag(quote.BidOffsetFlag).into()
                } else {
                    "".into()
                });

                // 买价格
                items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, quote.BidPrice));

                // 买数量
                items.push(slint::format!("{}", quote.BidVolume));

                // 卖报价状态
                items.push(TIConvert::order_status(quote.AskLastStatus).into());

                // 卖报单编号
                items.push(quote.AskOrderSysID.clone().into());

                // 卖开平标志
                items.push(if 0 != quote.AskVolume {
                    TIConvert::ctp_offsetflag(quote.AskOffsetFlag).into()
                } else {
                    "".into()
                });

                // 卖价格
                items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, quote.AskPrice));

                // 卖数量
                items.push(slint::format!("{}", quote.AskVolume));

                // 用户代码
                items.push(quote.UserID.clone().into());

                // 序号(用于排序)
                items.push(slint::format!("{:>13}", quote.SequenceNo));

                row_data.push(items.into());
            });

            // 按时间列降序显示
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(19 as usize).unwrap();
                let c_b = r_b.row_data(19 as usize).unwrap();
                c_b.cmp(&c_a)
            }));

            app.global::<OptTrd_InQuote>().set_row_data(sort_model.into());
        });
    }
}
