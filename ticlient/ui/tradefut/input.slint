import { TabView } from "@zstk/lib.slint";

import { InputOrderPage } from "input_order.slint";
import { InputExercisePage } from "input_exercise.slint";

import { FutTrd_TabSelIdx } from "struct.slint";

export component InputPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        padding_left: 1px;
        spacing: 2px;

        TabView {
            height: 30px;

            items: [
                { text: "报单录入" },
                { text: "行权录入" },
            ];

            current_index <=> FutTrd_TabSelIdx.input_selidx;
        }

        if 0 == FutTrd_TabSelIdx.input_selidx: InputOrderPage { }

        if 1 == FutTrd_TabSelIdx.input_selidx: InputExercisePage { }
    }
}
