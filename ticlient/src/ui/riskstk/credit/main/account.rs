use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::AccountsStkMap,
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type AccIDMap = dashmap::DashSet<String>;

/// 资金数据
struct AccountDatas {
    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl AccountDatas {
    pub fn new() -> Self {
        Self {
            filter_clicked: AtomicBool::new(false),
        }
    }
}
static ACCOUNT_DATAS: OnceLock<AccountDatas> = OnceLock::new();

/// 资金
pub(in crate::ui::riskstk::credit) struct Account {
    /// 显示交易所资金明细(目前仅针对信用交易)
    show_detail_amt: AtomicBool,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<AccountsStkMap>,

    /// 账号列表
    accid_set: Arc<AccIDMap>,

    /// 账号列表否有变动
    accid_changed: AtomicBool,
}

impl Account {
    pub fn new() -> Self {
        Self {
            show_detail_amt: AtomicBool::new(false),

            data_changed: AtomicBool::new(false),
            data_map: Arc::new(AccountsStkMap::new()),

            accid_set: Arc::new(AccIDMap::new()),
            accid_changed: AtomicBool::new(false),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = ACCOUNT_DATAS.set(AccountDatas::new());

        let app_acc = app.global::<StkRskCredit_Account>();

        self.show_detail_amt
            .store(app.global::<AppInerArgs>().get_show_detail(), Ordering::Relaxed);

        let app_weak = app.as_weak();
        app_acc.on_get_row_data_color(move |row_index, column_index, data| {
            Account::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_acc.on_sort_ascending(move |column_index| Account::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_sort_descending(move |column_index| Account::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_filter_changed(move || Account::on_filter_changed(&app_weak));

        log::trace!("Account init completed");
    }
}

impl Account {
    /// 请求查询资金信息
    pub fn qry_account(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_account() };
        if ret.is_empty() {
            return;
        }

        let show_detail_amt = self.show_detail_amt.load(Ordering::Relaxed);

        let mut update_false = false;
        for (key, acc) in ret {
            if !self.accid_set.contains(&acc.AccountID) {
                self.accid_set.insert(acc.AccountID.clone());
                self.accid_changed.store(true, Ordering::Relaxed);
            }

            if show_detail_amt {
                if 0 == acc.ExchID {
                    continue;
                }
            } else {
                if 0 != acc.ExchID {
                    continue;
                }
            }

            update_false = true;
            self.data_map.insert(key, acc);
        }

        if update_false {
            self.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

impl Account {
    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        ACCOUNT_DATAS.get().unwrap().filter_clicked.store(true, Ordering::Relaxed);
    }

    /// 升序
    fn on_sort_ascending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<StkRskCredit_Account>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<StkRskCredit_Account>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 维持担保比例
        if 10000 == column_index {
            if "无负债" == data.as_str() {
                return nc.get_normal();
            }

            let var = data.replace(",", "").replace("%", "").parse::<f64>().unwrap_or(1000000.0);
            if var <= 300.0 {
                return nc.get_error();
            } else if var <= 1000.0 {
                return nc.get_caution();
            } else {
                return nc.get_normal();
            }
        }

        // 负债
        if 10001 == column_index {
            if let Ok(var) = data.replace(",", "").parse::<f64>() {
                if var > 0. {
                    return nc.get_error();
                }
                return nc.get_normal();
            }
        }

        // 其他列处理方式一样
        if let Ok(var) = data.replace(",", "").parse::<f64>() {
            if var >= 0. {
                return nc.get_normal();
            }
            return nc.get_error();
        }

        nc.get_default()
    }
}

impl Account {
    /// 更新资金账号列表
    pub async fn update_accid(&self, app_weak: Weak<App>) {
        if self
            .accid_changed
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok()
        {
            let accid_set = self.accid_set.clone();

            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let mut accid_vec: Vec<String> = accid_set.iter().map(|accid| accid.clone()).collect();
                accid_vec.sort_by(|a, b| a.cmp(b));

                let accid_model: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());
                accid_vec.iter().for_each(|accid| {
                    accid_model.push(ListViewItem {
                        text: accid.clone().into(),
                        ..Default::default()
                    });
                });
                let app_com = app.global::<StkRskCredit_Com>();
                app_com.set_accid_model(accid_model.clone().into());
                app_com.invoke_accid_model_changed(accid_model.into());
            });
        }
    }

    /// 更新资金信息
    pub async fn update_acc(&self, app_weak: Weak<App>) {
        let acc_datas = ACCOUNT_DATAS.get().unwrap();

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                acc_datas.filter_clicked.store(false, Ordering::Relaxed);
                self.data_map.clone()
            } else {
                if acc_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    self.data_map.clone()
                } else {
                    Arc::new(AccountsStkMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            let app_acc = app.global::<StkRskCredit_Account>();

            let filter_accid = app_acc.get_filter_accid().as_str().to_owned();

            data_map_clone.iter().for_each(|acc| {
                if !filter_accid.is_empty() {
                    if !acc.AccountID.contains(&filter_accid) {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 交易所
                items.push(TIConvert::exch_name(acc.ExchID).into());

                // 资金账号
                items.push(acc.AccountID.clone().into());

                // 总资产
                items.push(TIConvert::format_f(acc.Balance, Config::AMT_DIGITS).into());

                // 可用资金
                items.push(TIConvert::format_f(acc.Available, Config::AMT_DIGITS).into());

                // 总负债
                items.push(TIConvert::format_f(acc.XYAllDebt, Config::AMT_DIGITS).into());

                // 保证金可用
                items.push(TIConvert::format_f(acc.XYAvlMargin, Config::AMT_DIGITS).into());

                // 维持担保比例
                items.push(if -1.0 != acc.XYMRatio {
                    if acc.XYMRatio <= 1000000.0 {
                        slint::format!("{}%", TIConvert::format_f(acc.XYMRatio * 100.0, Config::AMT_DIGITS))
                    } else {
                        "大于100,000,000%".into()
                    }
                } else {
                    "无负债".into()
                });

                // 当日盈亏
                items.push(TIConvert::format_f(acc.Profits, Config::AMT_DIGITS).into());

                // 资金收支
                items.push(TIConvert::format_f(acc.Premium, Config::AMT_DIGITS).into());

                // 资金冻结
                items.push(TIConvert::format_f(acc.FrozenPremium, Config::AMT_DIGITS).into());

                // 手续费
                items.push(TIConvert::format_f(acc.Commision, Config::AMT_DIGITS).into());

                // 冻结手续费
                items.push(TIConvert::format_f(acc.FrozenCommission, Config::AMT_DIGITS).into());

                // 持仓盈亏
                items.push(TIConvert::format_f(acc.PositionProfit, Config::AMT_DIGITS).into());

                // 平仓盈亏
                items.push(TIConvert::format_f(acc.CloseProfit, Config::AMT_DIGITS).into());

                // 入金
                items.push(TIConvert::format_f(acc.Deposit, Config::AMT_DIGITS).into());

                // 出金
                items.push(TIConvert::format_f(acc.Withdraw, Config::AMT_DIGITS).into());

                // 股票市值
                items.push(TIConvert::format_f(acc.PosValue, Config::AMT_DIGITS).into());

                // 融资负债
                items.push(TIConvert::format_f(acc.XYRzDebt, Config::AMT_DIGITS).into());

                // 融券负债
                items.push(TIConvert::format_f(acc.XYRqDebt, Config::AMT_DIGITS).into());

                // 信用冻结资金
                items.push(TIConvert::format_f(acc.XYFroPrem, Config::AMT_DIGITS).into());

                // 信用冻结手续费
                items.push(TIConvert::format_f(acc.XYFroCommi, Config::AMT_DIGITS).into());

                // 未成交充抵保证金
                items.push(TIConvert::format_f(acc.XYBuyCdMargin, Config::AMT_DIGITS).into());

                // 充抵保证金
                items.push(TIConvert::format_f(acc.XYBuyValue, Config::AMT_DIGITS).into());

                // 融资浮盈
                items.push(TIConvert::format_f(acc.XYBuy, Config::AMT_DIGITS).into());

                // 融券浮盈
                items.push(TIConvert::format_f(acc.XYSell, Config::AMT_DIGITS).into());

                // 融券卖出金额
                items.push(TIConvert::format_f(acc.XYSellAmt, Config::AMT_DIGITS).into());

                // 融资保证金
                items.push(TIConvert::format_f(acc.XYBuyMargin, Config::AMT_DIGITS).into());

                // 融券保证金
                items.push(TIConvert::format_f(acc.XYSellMargin, Config::AMT_DIGITS).into());

                // 信用利息
                items.push(TIConvert::format_f(acc.XYInterest, Config::AMT_DIGITS).into());

                // 信用手续费
                items.push(TIConvert::format_f(acc.XYCommi, Config::AMT_DIGITS).into());

                // 上日可用
                items.push(TIConvert::format_f(acc.DistribFund, Config::AMT_DIGITS).into());

                // 用于排序
                items.push(slint::format!("{}", if acc.XYMRatio >= 0. { acc.XYMRatio } else { f64::MAX }));

                row_data.push(items.into());
            });

            let asc_column_index = app_acc.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_acc.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            app_acc.set_row_data(row_data.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        self.update_accid(app_weak.clone()).await;
        self.update_acc(app_weak).await;
    }
}
