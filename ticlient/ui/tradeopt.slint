import { AppCom } from "struct.slint";
import { LockUI } from "lock.slint";
import { StatusStrip } from "widgets/statusstrip.slint";
import { AboutPage } from "about.slint";
import { TradeOptPage } from "tradeopt/tradeopt.slint";
import { SettingPage } from "tradeopt/setting.slint";

export component MainTradeOptUI {

    preferred_width: 100%;
    preferred_height: 100%;

    if AppCom.islocked: LockUI { }

    if !AppCom.islocked: VerticalLayout {
        TradeOptPage { }

        StatusStrip { }
    }

    if !AppCom.islocked && AppCom.show_set: SettingPage {
        z: 101;

        width: 700px;
        height: 420px;
    }

    if !AppCom.islocked && AppCom.show_about: AboutPage {
        z: 102;

        width: 380px;
        height: 250px;
    }
}
