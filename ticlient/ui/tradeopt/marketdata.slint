import { <PERSON>, <PERSON><PERSON>s, Radius, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, TableView} from "@zstk/lib.slint";
import { ColWidth, WidgetColor, AppCom } from "../struct.slint";
import { MarketDataDtl } from "../md/mdstruct.slint";
import { OptTrd_TabSelIdx, OptTrd_InputOrder } from "struct.slint";
import { LineEditEx, LineItem } from "../widgets/lineeditex.slint";

export global OptTrd_MarketData {

    in property <[[string]]> row_data;

    in property <[LineItem]> insid_model;
    in property <[LineItem]> all_insid_model;

    in_out property <string> insid;

    callback insid_changed(string);
    callback add_clicked();
    callback del_clicked(/* exchid */ string, /* insid */ string);
    callback del_all_clicked(/* row_cnt */ int);

    callback current_cell_changed(/* row_index */ int, /* col_index */ int);

    public function get_cur_sel_ins(row_index: int) -> { exchid: string, insid: string} {
        if row_index < row_data.length {
            return { exchid: row_data[row_index][0], insid: row_data[row_index][1] };
        }
        { exchid: "", insid: "" }
    }
}

export component MarketDataPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;
    private property <string> sel_del_exchid;
    private property <string> sel_del_insid;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }

            LineEditEx {
                width: 250px;
                model <=> OptTrd_MarketData.insid_model;
                current_text <=> OptTrd_MarketData.insid;
                placeholder_text: "请输入";

                edited => {
                    OptTrd_MarketData.insid_changed(self.current_text);
                }
            }

            Button {
                text: "加入自选";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                    self.focus();
                    OptTrd_MarketData.add_clicked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 2,
                fix_tail_count: 1,
                columns: [
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insid },
                    { title: @tr("合约名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insname },
                    { title: @tr("最新价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("买5量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("买5价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("买4量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("买4价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("买3量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("买3价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("买2量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("买2价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("买1量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("买1价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("卖1价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("卖1量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("卖2价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("卖2量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("卖3价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("卖3量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("卖4价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("卖4量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("卖5价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("卖5量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("开盘价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("最低价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("最高价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("跌停价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("涨停价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("昨收价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("昨结价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("成交量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("持仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("更新时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time_ms }
                ]
            };
            row_count: OptTrd_MarketData.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_MarketData.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            current_cell_changed(row_index, column_index) => {
                OptTrd_MarketData.current_cell_changed(row_index, column_index);
            }

            cell_double_clicked(row_index, column_index) => {
                OptTrd_TabSelIdx.input_selidx = 0;

                if OptTrd_MarketData.row_data.length > row-index {
                    OptTrd_InputOrder.offset = 0;
                    OptTrd_InputOrder.price = "";
                    OptTrd_InputOrder.price_has_error = true;
                    OptTrd_InputOrder.volume = "1";

                    if OptTrd_InputOrder.exchid != OptTrd_MarketData.row_data[row_index][0] {
                        OptTrd_InputOrder.exchid = OptTrd_MarketData.row_data[row_index][0];
                        OptTrd_InputOrder.exchid_changed();
                    }
                    if OptTrd_InputOrder.insid != OptTrd_MarketData.row_data[row_index][1] {
                        OptTrd_InputOrder.insid = OptTrd_MarketData.row_data[row_index][1];
                        OptTrd_InputOrder.insid_text_changed();
                    }
                }
            }

            cell_pointer_event(row_index, column_index, event, point) => {
                if PointerEventButton.right == event.button && PointerEventKind.up == event.kind {

                    self.select_full_row = true;
                    self.set_current_cell(row-index, column-index);

                    sel_del_exchid = OptTrd_MarketData.row_data[row_index][0];
                    sel_del_insid = OptTrd_MarketData.row_data[row_index][1];

                    right_up_point = point;
                    i_pw_menu.show();
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_MarketData.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_MarketData.row_data[row_index]);
                }
            }
        }
    }

    i_pw_menu := PopupWindow {

        x: right_up_point.x - root.absolute_position.x;
        y: right_up_point.y - root.absolute_position.y;

        Rectangle {

            width: 140px;

            border-width: 1px;
            border-color: Theme.border;
            border_radius: Radius.extra_small;

            drop_shadow_blur: 15px;
            drop_shadow_color: Theme.control_background.darker(0.5);

            background: Theme.control_background;

            VerticalLayout {
                padding: 5px;
                spacing: 10px;

                i_menu_del := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 14px;
                            colorize: Theme.foreground;
                            source: Icons.close;
                        }

                        Label {
                            text: "删除选中合约";
                            horizontal-alignment: left;
                        }
                    }

                    i_menu_del_ta := TouchArea {
                        clicked => {
                            if sel_del_insid == MarketDataDtl.insid && sel_del_exchid == MarketDataDtl.exchid {
                                MarketDataDtl.reset();
                            }
                            OptTrd_MarketData.del_clicked(sel_del_exchid, sel_del_insid);
                        }
                    }
                }

                i_menu_del_all := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 14px;
                            colorize: Theme.foreground;
                            source: Icons.close;
                        }

                        Label {
                            text: "删除所有合约";
                            horizontal_alignment: left;
                        }
                    }

                    i_menu_del_all_ta := TouchArea {
                        clicked => {
                            MarketDataDtl.reset();
                            OptTrd_MarketData.del_all_clicked(OptTrd_MarketData.row_data.length);
                        }
                    }
                }
            }
        }

        states [
            del_has_hover when i_menu_del_ta.has_hover: {
                i_menu_del.background: Theme.shadow;
            }
            del_all_has_hover when i_menu_del_all_ta.has_hover: {
                i_menu_del_all.background: Theme.shadow;
            }
        ]
    }
}
