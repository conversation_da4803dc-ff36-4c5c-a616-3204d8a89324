import { Theme, Label, ListView } from "@zstk/lib.slint";

export struct TimeItem {
    hour: int,
    minute: int,
    second: int
}

export component TimeSelect {
    in_out property <TimeItem> time;

    out property <int> time_int: time.hour * 10000 + time.minute * 100 + time.second;
    out property <string> time_str: @tr("{}:{}:{}", i_lb_hour.text, i_lb_minute.text, i_lb_second.text);

    height: 26px;
    width: 74px;

    forward_focus: i_focus_scope;

    Rectangle {
        border_color: !i_focus_scope.has_focus ? Theme.border : Theme.border_focus;
        border_width: 1px;
        border_radius: 4px;
        background: Theme.background;

        i_layout := HorizontalLayout {
            padding: 4px;
            spacing: 1px;

            i_lb_hour := Label {
                text: time.hour < 10 ? @tr("0{}", time.hour) : @tr("{}", time.hour);
                vertical_alignment: center;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        i_popup_hour.show();
                    }
                }
            }

            Label {
                text: ":";
                vertical_alignment: center;
            }

            i_lb_minute := Label {
                text: time.minute < 10 ? @tr("0{}", time.minute) : @tr("{}", time.minute);
                vertical_alignment: center;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        i_popup_minute.show();
                    }
                }
            }

            Label {
                text: ":";
                vertical_alignment: center;
            }

            i_lb_second := Label {
                text: time.second < 10 ? @tr("0{}", time.second) : @tr("{}", time.second);
                vertical_alignment: center;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        i_popup_second.show();
                    }
                }
            }
        }

        i_focus_scope := FocusScope {
        }
    }

    i_popup_hour := PopupWindow {
        x: i_lb_hour.x;
        y: i_lb_hour.y + i_lb_hour.height + 6px;
        width: 40px;
        height: 200px;

        Rectangle {
            border_color: Theme.foreground.with_alpha(0.5);
            border_width: 1px;
            background: Theme.background;
        }

        ListView {

            for i in 24 : Label {
                x: 3px;
                height: 30px;
                text: i < 10 ? @tr("0{}", i) : @tr("{}", i);

                TouchArea {
                    mouse_cursor: MouseCursor.pointer;
                    clicked => {
                        root.time.hour =  i;
                    }
                }
            }
        }
    }

    i_popup_minute := PopupWindow {
        x: i_lb_minute.x;
        y: i_lb_minute.y + i_lb_minute.height + 6px;
        width: 40px;
        height: 200px;

        Rectangle {
            border_color: Theme.foreground.with_alpha(0.5);
            border_width: 1px;
            background: Theme.background;
        }

        ListView {

            for i in 60 : Label {
                x: 3px;
                height: 30px;
                text: i < 10 ? @tr("0{}", i) : @tr("{}", i);

                TouchArea {
                    mouse_cursor: MouseCursor.pointer;
                    clicked => {
                        root.time.minute =  i;
                    }
                }
            }
        }
    }

    i_popup_second := PopupWindow {
        x: i_lb_second.x;
        y: i_lb_second.y + i_lb_second.height + 6px;
        width: 40px;
        height: 200px;

        Rectangle {
            border_color: Theme.foreground.with_alpha(0.5);
            border_width: 1px;
            background: Theme.background;
        }

        ListView {
            for i in 60 : Label {
                x: 3px;
                height: 30px;
                text: i < 10 ? @tr("0{}", i) : @tr("{}", i);

                TouchArea {
                    mouse_cursor: MouseCursor.pointer;
                    clicked => {
                        root.time.second =  i;
                    }
                }
            }
        }
    }
}