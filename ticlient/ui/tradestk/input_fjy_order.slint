import { <PERSON>, <PERSON>, <PERSON><PERSON>, ComboBox, <PERSON>Box, Radius } from "@zstk/lib.slint";
import { AppCom, TradeColor } from "../struct.slint";
import { LineEditEx } from "../widgets/lineeditex.slint";
import { StkTrd_Com, StkTrd_InputFjyOrder } from "struct.slint";
import { Fonts } from "@zstk/styling/font.slint";

export component InputFjyOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 2px;

        HorizontalLayout {
            spacing: 5px;

            Rectangle {
                background: transparent;
                width: 280px;

                GridLayout {
                    spacing-vertical: 7px;
                    spacing-horizontal: 20px;
                    padding: 5px;

                    Row {
                        Label {
                            width: 30px;
                            text: "账户";
                            vertical-alignment: center;
                        }

                        Label {
                            text: StkTrd_Com.accountid;
                            vertical-alignment: center;
                        }
                    }

                    Row {

                        Label {
                            text: "方向";
                            vertical-alignment: center;
                        }

                        Rectangle {
                            clip: true;
                            height: 26px;
                            border_width: 1px;
                            border_color: StkTrd_InputFjyOrder.get_dir_color();
                            border_radius: Radius.extra_small;

                            HorizontalLayout {
                                Rectangle {
                                    background: Theme.control_background_disabled;// StkTrd_InputFjyOrder.is_buy ? TradeColor.dir_buy : transparent;

                                    Label {
                                        text: "买入";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    // 目前只有逆回购,不支持买入
                                    // TouchArea {
                                    //     mouse_cursor: pointer;
                                    //     clicked => {
                                    //         StkTrd_InputFjyOrder.is_buy = true;
                                    //     }
                                    // }
                                }

                                Rectangle {
                                    background: StkTrd_InputFjyOrder.is_buy ? transparent : TradeColor.dir_sell;

                                    Label {
                                        text: "卖出";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            StkTrd_InputFjyOrder.is_buy = false;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "市场";
                            vertical-alignment: center;
                        }

                        Rectangle {
                            clip: true;
                            height: 26px;
                            border_color: TradeColor.exchid;
                            border_width: 1px;
                            border_radius: Radius.extra_small;

                            HorizontalLayout {

                                Rectangle {
                                    background: StkTrd_InputFjyOrder.is_sel_sse() ? TradeColor.exchid : transparent;

                                    Label {
                                        text: "上海";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            i_cbx_pricetype.model = StkTrd_InputFjyOrder.sse_price_type_model;
                                            i_cbx_pricetype.select(17);
                                            StkTrd_InputFjyOrder.set_market(1);
                                        }
                                    }
                                }

                                Rectangle {
                                    width: 1px;
                                    border_color: TradeColor.exchid;
                                    border_width: 1px;
                                }

                                Rectangle {
                                    background: StkTrd_InputFjyOrder.is_sel_szse() ? TradeColor.exchid : transparent;

                                    Label {
                                        text: "深圳";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            i_cbx_pricetype.model = StkTrd_InputFjyOrder.szse_price_type_model;
                                            i_cbx_pricetype.select(1);
                                            StkTrd_InputFjyOrder.set_market(2);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "类型";
                            vertical-alignment: center;
                        }

                        i_cbx_pricetype := ComboBox {
                            placeholder_text: "请先选择市场";
                            max_popup_height: 250px;
                            read_only: true;
                            current_text <=> StkTrd_InputFjyOrder.fjy_type_str;
                            model: 1 == StkTrd_InputFjyOrder.exchid ? StkTrd_InputFjyOrder.sse_price_type_model : (2 == StkTrd_InputFjyOrder.exchid ? StkTrd_InputFjyOrder.szse_price_type_model : []);
                            current_index <=> StkTrd_InputFjyOrder.fjy_type;
                        }
                    }

                    Row {

                        Label {
                            text: "代码";
                            vertical-alignment: center;
                        }

                        VerticalLayout {
                            spacing: !StkTrd_InputFjyOrder.insid_has_error ? 3px : 0px;

                            LineEditEx {
                                max_popup_height: 160px;
                                model <=> StkTrd_InputFjyOrder.insid_model;
                                has_error: StkTrd_InputFjyOrder.insid_has_error;
                                current_text <=> StkTrd_InputFjyOrder.insid;
                                placeholder_text: "请输入";

                                edited => {
                                    StkTrd_InputFjyOrder.insid_text_changed();
                                }
                            }

                            Label {
                                text <=> StkTrd_InputFjyOrder.insname;
                                vertical_alignment: top;
                                visible: !StkTrd_InputFjyOrder.insid_has_error;
                                height: !StkTrd_InputFjyOrder.insid_has_error ? 16px : 0px;
                                font_size: Fonts.small.size;
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "价格";
                            vertical-alignment: center;
                        }

                        SpinBox {
                            height: 26px;
                            placeholder_text: "请输入";
                            text <=> StkTrd_InputFjyOrder.price;
                            has_error <=> StkTrd_InputFjyOrder.price_has_error;

                            edited => {
                                StkTrd_InputFjyOrder.price_text_changed();
                            }

                            up_down_clicked(upflag) => {
                                StkTrd_InputFjyOrder.price_updown_changed(upflag);
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "质押式回购(逆回购)" == i_cbx_pricetype.current_text ? "金额" : "数量";
                            vertical-alignment: center;
                        }

                        SpinBox {
                            height: 26px;
                            placeholder_text: "请输入";
                            text <=> StkTrd_InputFjyOrder.volume;
                            has_error <=> StkTrd_InputFjyOrder.volume_has_error;

                            edited => {
                                StkTrd_InputFjyOrder.volume_text_changed();
                            }

                            up_down_clicked(upflag) => {
                                StkTrd_InputFjyOrder.volume_updown_changed(upflag);
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "重填";
                            vertical_alignment: center;
                            color: Theme.border_focus;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    StkTrd_InputFjyOrder.reset_sel_ins_info();
                                }
                            }
                        }

                        Button {
                            text: @tr("确定{}", StkTrd_InputFjyOrder.is_buy ? "买入" : "卖出");
                            background: StkTrd_InputFjyOrder.get_dir_color();

                            clicked => {
                                if AppCom.check_td_session_timeout() {
                                    return ;
                                }

                                StkTrd_InputFjyOrder.accountid = StkTrd_Com.accountid;
                                StkTrd_InputFjyOrder.ok_clicked(true);
                            }
                        }
                    }
                }
            }

            Rectangle {
                horizontal-stretch: 1;
            }
        }

        Rectangle {
            vertical_stretch: 1;
        }
    }
}
