use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
};

use crate::{
    common::{config::Config, global::TRADE_FUT_API, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::account_trade_ctp::{self, CtpAccountField};

type CtpAccountMap = dashmap::DashMap<String, CtpAccountField>;

/// 资金
pub(super) struct Account {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpAccountMap>,
}

/// 构建与初始化
impl Account {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpAccountMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_acc = app.global::<FutTrd_Account>();

        let app_weak = app.as_weak();
        app_acc.on_sort_ascending(move |column_index| Account::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_sort_descending(move |column_index| Account::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_get_row_data_color(move |row_index, column_index, data| {
            Account::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Account init completed");
    }
}

/// 与API的交互
impl Account {
    /// 请求查询资金信息
    pub fn qry_account(&self) {
        let req = account_trade_ctp::CtpReqQryTradingAccountField::default();

        let api = TRADE_FUT_API.get().unwrap().read().unwrap();

        if let Err(err) = api.req_qry_tradingaccount(0, &req) {
            log::warn!("req_qry_tradingaccount failed. {}", err);
        }

        let req = account_trade_ctp::CtpReqQryTradingAccountExField::default();
        if let Err(err) = api.req_qry_tradingaccountex(0, &req) {
            log::warn!("req_qry_tradingaccountex failed. {}", err);
        }
    }

    /// 响应查询资金信息
    pub fn on_rsp_qry_acc(&self, acc: CtpAccountField) {
        if acc.BrokerID.is_empty() && 1 == acc.SettlementID {
            return; // 汇总资金如果只由一条详情汇总而来则不用显示(显示详情即可, 为兼容老版本(无本字段即为0)判断条件为=1)
        }

        let mut insert_flag = true;
        let key = std::format!("{}{}", acc.BrokerID, acc.AccountID);

        if self.data_map.contains_key(&key) {
            let preacc = self.data_map.get(&key).unwrap();
            if acc == *preacc {
                insert_flag = false;
            }
        }

        if insert_flag {
            self.data_map.insert(key, acc);
            self.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

/// 事件
impl Account {
    /// 升序
    fn on_sort_ascending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<FutTrd_Account>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<FutTrd_Account>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 可用资金
        if 4 == column_index {
            if let Ok(var) = data.as_str().parse::<f64>() {
                if var >= 0. {
                    return nc.get_normal();
                }
                return nc.get_error();
            }
        }

        // 入金
        if 12 == column_index {
            return nc.get_normal();
        }

        // 出金
        if 13 == column_index {
            return nc.get_caution();
        }

        // 平仓盈亏, 持仓盈亏
        if 14 == column_index || 15 == column_index {
            if let Ok(var) = data.as_str().parse::<f64>() {
                if var >= 0. {
                    return nc.get_normal();
                }
                return nc.get_caution();
            }
        }

        nc.get_default()
    }
}

/// UI
impl Account {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                self.data_map.clone()
            } else {
                Arc::new(CtpAccountMap::new())
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            let app_com = app.global::<FutTrd_Com>();
            data_map_clone.iter().for_each(|acc| {
                let defaccid = app_com.get_accountid();
                if defaccid.is_empty() {
                    app_com.set_accountid(acc.AccountID.clone().into());
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 交易所
                items.push(acc.BrokerID.clone().into());

                // 资金账号
                items.push(acc.AccountID.clone().into());

                // 动态权益
                items.push(TIConvert::format_f(acc.Balance, Config::AMT_DIGITS).into());

                // 市值权益
                items.push(TIConvert::format_f(acc.ValueBalance, Config::AMT_DIGITS).into());

                // 可用资金
                items.push(TIConvert::format_f(acc.Available, Config::AMT_DIGITS).into());

                // 占用保证金
                items.push(TIConvert::format_f(acc.CurrMargin, Config::AMT_DIGITS).into());

                // 冻结保证金
                items.push(TIConvert::format_f(acc.FrozenMargin, Config::AMT_DIGITS).into());

                // 冻结资金
                items.push(TIConvert::format_f(acc.FrozenCash, Config::AMT_DIGITS).into());

                // 冻结手续费
                items.push(TIConvert::format_f(acc.FrozenCommission, Config::AMT_DIGITS).into());

                // 权利金收支
                items.push(TIConvert::format_f(acc.CashIn, Config::AMT_DIGITS).into());

                // 手续费
                items.push(TIConvert::format_f(acc.Commission, Config::AMT_DIGITS).into());

                // 申报费用
                items.push(TIConvert::format_f(acc.Mortgage, Config::AMT_DIGITS).into());

                // 入金
                items.push(TIConvert::format_f(acc.Deposit, Config::AMT_DIGITS).into());

                // 出金
                items.push(TIConvert::format_f(acc.Withdraw, Config::AMT_DIGITS).into());

                // 平仓盈亏
                items.push(TIConvert::format_f(acc.CloseProfit, Config::AMT_DIGITS).into());

                // 持仓盈亏
                items.push(TIConvert::format_f(acc.PositionProfit, Config::AMT_DIGITS).into());

                // 上次结算准备金
                items.push(TIConvert::format_f(acc.PreBalance, Config::AMT_DIGITS).into());

                row_data.push(items.into());
            });

            let app_acc = app.global::<FutTrd_Account>();

            let asc_column_index = app_acc.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_acc.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            app_acc.set_row_data(row_data.into());
        });
    }
}
