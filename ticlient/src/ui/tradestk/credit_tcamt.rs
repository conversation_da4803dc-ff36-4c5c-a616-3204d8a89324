use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{config::Config, global::TRADE_STK_BUF, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::position_trade_ctp::CtpCreditTcAmtField;

type CtpCreditTcAmtMap = dashmap::DashMap<String, CtpCreditTcAmtField>;

/// 资金头寸数据
struct CreditTcAmtDatas {
    /// 数据
    data_map: Arc<CtpCreditTcAmtMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl CreditTcAmtDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpCreditTcAmtMap::new()),
            data_changed: AtomicBool::new(false),

            filter_clicked: AtomicBool::new(false),
        }
    }
}
static CREDIT_TC_AMT_DATAS: OnceLock<CreditTcAmtDatas> = OnceLock::new();

/// 资金头寸
pub(super) struct CreditTcAmt {}

/// 构建与初始化
impl CreditTcAmt {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = CREDIT_TC_AMT_DATAS.set(CreditTcAmtDatas::new());

        let app_tc = app.global::<StkTrd_CreditTcAmt>();

        let app_weak = app.as_weak();
        app_tc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditTcAmt::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_tc.on_filter_changed(move || CreditTcAmt::on_filter_changed(&app_weak));

        let app_weak = app.as_weak();
        app_tc.on_export_clicked(move |etype| CreditTcAmt::on_export_clicked(&app_weak, etype));

        log::trace!("Credit tc amt init completed");
    }
}

/// 查询
impl CreditTcAmt {
    /// 查询资金头寸
    pub fn qry_credit_tcamt(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_credit_tcamt() };
        if ret.is_empty() {
            return;
        }

        let tc_datas = CREDIT_TC_AMT_DATAS.get().unwrap();

        for (key, cl) in ret {
            tc_datas.data_map.insert(key, cl);
        }
        tc_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditTcAmt {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 可用头寸
        if 4 == column_index {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data < 0.0 {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        CREDIT_TC_AMT_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("credit_tc_amt_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出资金头寸失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "头寸类型",
            "资金账户",
            "交易所",
            "授权头寸",
            "可用头寸",
            "使用头寸",
            "冻结头寸",
            "转入头寸",
            "转出头寸",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出资金头寸失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<StkTrd_CreditTcAmt>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let tc_datas = CREDIT_TC_AMT_DATAS.get().unwrap();
            let data_map_clone = tc_datas.data_map.clone();
            data_map_clone.iter().for_each(|cl| {
                let items = CreditTcAmt::get_row_items(&cl);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出资金头寸失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出资金头寸成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}

/// 更新UI
impl CreditTcAmt {
    /// 获取一行数据
    fn get_row_items(tc: &CtpCreditTcAmtField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 头寸类型
        items.push(
            match tc.TcType {
                1 => "普通头寸",
                2 => "专项头寸",
                _ => "未知头寸",
            }
            .into(),
        );

        // 资金账户
        items.push(slint::format!("{}", tc.AccID));

        // 交易所
        items.push(TIConvert::exch_name(tc.ExchID).into());

        // 授权头寸
        items.push(TIConvert::format_f(tc.Distrib, Config::AMT_DIGITS).into());

        // 可用头寸
        items.push(TIConvert::format_f(tc.Distrib - tc.Used - tc.Frozen + tc.In - tc.Out, Config::AMT_DIGITS).into());

        // 使用头寸
        items.push(TIConvert::format_f(tc.Used, Config::AMT_DIGITS).into());

        // 冻结头寸
        items.push(TIConvert::format_f(tc.Frozen, Config::AMT_DIGITS).into());

        // 转入头寸
        items.push(TIConvert::format_f(tc.In, Config::AMT_DIGITS).into());

        // 转出头寸
        items.push(TIConvert::format_f(tc.Out, Config::AMT_DIGITS).into());

        items
    }

    // 更新资金头寸
    async fn update_credit_contract(&self, app_weak: &Weak<App>) {
        let tc_datas = CREDIT_TC_AMT_DATAS.get().unwrap();

        let data_map_clone = {
            if tc_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                tc_datas.filter_clicked.store(false, Ordering::Relaxed);
                tc_datas.data_map.clone()
            } else {
                if tc_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    tc_datas.data_map.clone()
                } else {
                    Arc::new(CtpCreditTcAmtMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_tc = app.global::<StkTrd_CreditTcAmt>();

            let filter_tctype = app_tc.get_tctype();
            let filter_exchid = TIConvert::exch_id(app_tc.get_exchid().as_str());

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|tc| {
                if filter_tctype > 0 {
                    if tc.TcType != filter_tctype {
                        return;
                    }
                }

                if 0 != filter_exchid {
                    if tc.ExchID != filter_exchid {
                        return;
                    }
                }

                let items = CreditTcAmt::get_row_items(&tc);
                row_data.push(items.into());
            });

            app_tc.set_row_data(row_data.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新资金头寸
        self.update_credit_contract(&app_weak).await;
    }
}
