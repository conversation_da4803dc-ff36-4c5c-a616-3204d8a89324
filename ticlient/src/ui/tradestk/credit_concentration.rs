use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{config::Config, global::TRADE_STK_BUF, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::position_trade_ctp::CtpCreditConcentrationField;

type CtpCreditConcentMap = dashmap::DashMap<String, CtpCreditConcentrationField>;
type StringSet = dashmap::DashSet<String>;

struct CreditConcentDatas {
    /// 数据
    data_map: Arc<CtpCreditConcentMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 所有的组名称列表
    gname_set: Arc<StringSet>,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl CreditConcentDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpCreditConcentMap::new()),
            data_changed: AtomicBool::new(false),
            gname_set: Arc::new(StringSet::new()),

            filter_clicked: AtomicBool::new(false),
        }
    }
}
static CREDIT_CONCENT_DATAS: OnceLock<CreditConcentDatas> = OnceLock::new();

/// 集中度
pub(super) struct CreditConcent {}

/// 构建与初始化
impl CreditConcent {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = CREDIT_CONCENT_DATAS.set(CreditConcentDatas::new());

        let app_mon = app.global::<StkTrd_CreditConcentration>();

        let app_weak = app.as_weak();
        app_mon.on_get_row_data_color(move |row_index, column_index, data| {
            CreditConcent::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_mon.on_filter_changed(move || CreditConcent::on_filter_changed(&app_weak));

        let app_weak = app.as_weak();
        app_mon.on_export_clicked(move |etype| CreditConcent::on_export_clicked(&app_weak, etype));

        log::trace!("Credit concentration init completed");
    }
}

/// 查询
impl CreditConcent {
    /// 查询集中度
    pub fn qry_credit_concentration(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_credit_concentration() };
        if ret.is_empty() {
            return;
        }

        let cc_datas = CREDIT_CONCENT_DATAS.get().unwrap();

        for (key, cc) in ret {
            cc_datas.gname_set.insert(cc.GName.clone());
            cc_datas.data_map.insert(key, cc);
        }

        cc_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditConcent {
    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        CREDIT_CONCENT_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 是否异常
        if 12 == column_index {
            if "是" == data.as_str() {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("credit_concentration_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出集中度失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "组编码",
            "组名称",
            "类型",
            "资金账号",
            "交易所",
            "股东代码",
            "证券代码",
            "市值",
            "总资产",
            "维持担保比例",
            "集中度上限",
            "集中度比例",
            "是否异常",
            "证券市值",
            "总证券市值",
            "未成交市值",
            "总未成交市值",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出集中度失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<StkTrd_CreditConcentration>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter().take(rd.row_count() - 1));
            });
        }
        // 导出全部
        else {
            let cc_datas = CREDIT_CONCENT_DATAS.get().unwrap();
            let data_map_clone = cc_datas.data_map.clone();
            data_map_clone.iter().for_each(|cl| {
                let items = CreditConcent::get_row_items(&cl, false);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出集中度失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出集中度成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}

/// 更新UI
impl CreditConcent {
    /// 获取一行数据
    fn get_row_items(cc: &CtpCreditConcentrationField, is_show: bool) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 组编码
        items.push(slint::format!("{}", cc.GID));

        // 组名称
        items.push(slint::format!("{}", cc.GName));

        // 类型
        items.push(
            match cc.CType {
                1 => "单一证券",
                2 => "全组",
                _ => "未知",
            }
            .into(),
        );

        // 资金账号
        items.push(slint::format!("{}", cc.AccID));

        // 交易所
        items.push(TIConvert::exch_name(cc.ExchID).into());

        // 股东代码
        items.push(slint::format!("{}", cc.CliID));

        // 证券代码
        items.push(slint::format!("{}", cc.InsID));

        // 市值
        items.push(TIConvert::format_f(cc.Value, Config::AMT_DIGITS).into());

        // 总资产
        items.push(TIConvert::format_f(cc.AValue, Config::AMT_DIGITS).into());

        // 维持担保比例
        items.push(if -1.0 != cc.MRatio {
            slint::format!("{}%", TIConvert::format_f(cc.MRatio * 100.0, Config::AMT_DIGITS))
        } else {
            "无负债".into()
        });

        // 集中度上限
        items.push(slint::format!(
            "{}%",
            TIConvert::format_f(cc.ULimit * 100.0, Config::AMT_DIGITS)
        ));

        // 集中度比例
        items.push(slint::format!("{}%", TIConvert::format_f(cc.Ratio * 100.0, 4)));

        // 是否异常
        let yc = if cc.Ratio >= cc.ULimit { "是" } else { "否" };
        items.push(slint::format!("{}", yc));

        // 持仓市值
        items.push(TIConvert::format_f(cc.BuyValue, Config::AMT_DIGITS).into());

        // 总持仓市值
        items.push(TIConvert::format_f(cc.ABuyValue, Config::AMT_DIGITS).into());

        // 在途市值
        items.push(TIConvert::format_f(cc.BIValue, Config::AMT_DIGITS).into());

        // 在途总市值
        items.push(TIConvert::format_f(cc.ABIValue, Config::AMT_DIGITS).into());

        if is_show {
            // sort_col(仅用于排序)
            items.push(slint::format!(
                "{}{}{}{}",
                yc,
                200000000 - cc.GID,
                100000000 + cc.CType,
                cc.InsID
            ));
        }

        items
    }

    // 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let cc_datas = CREDIT_CONCENT_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mon = app.global::<StkTrd_CreditConcentration>();

            if cc_datas.gname_set.len() != app_mon.invoke_group_name_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                cc_datas.gname_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mon.set_group_name_model(id_arr.into());
            }
        });
    }

    // 更新集中度
    async fn update_credit_concent(&self, app_weak: &Weak<App>) {
        let cc_datas = CREDIT_CONCENT_DATAS.get().unwrap();

        let data_map_clone = {
            if cc_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                cc_datas.filter_clicked.store(false, Ordering::Relaxed);
                cc_datas.data_map.clone()
            } else {
                if cc_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    cc_datas.data_map.clone()
                } else {
                    Arc::new(CtpCreditConcentMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mon = app.global::<StkTrd_CreditConcentration>();

            let filter_gname = app_mon.get_sel_group_name().to_string();
            let filter_subindex = app_mon.get_sel_sub_index();
            let filter_exchid = TIConvert::exch_id(app_mon.get_sel_exchid().as_str());
            let filter_insid = app_mon.get_sel_insid().to_string();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|cc| {
                if !filter_gname.is_empty() {
                    if !cc.GName.contains(&filter_gname) {
                        return;
                    }
                }

                if 0 != filter_subindex {
                    if cc.CType != filter_subindex {
                        return;
                    }
                }

                if 0 != filter_exchid {
                    if cc.ExchID != filter_exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !cc.InsID.contains(&filter_insid) {
                        return;
                    }
                }

                let items = CreditConcent::get_row_items(&cc, true);
                row_data.push(items.into());
            });

            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(17).unwrap();
                let c_b = r_b.row_data(17).unwrap();
                c_b.cmp(&c_a)
            }));

            app_mon.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新集中度
        self.update_credit_concent(&app_weak).await;
    }
}
