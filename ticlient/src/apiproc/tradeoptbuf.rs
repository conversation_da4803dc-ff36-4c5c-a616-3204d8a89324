use std::sync::{
    atomic::{AtomicU64, Ordering},
    Arc, Mutex,
};

use tiapi::protocol_pub::{
    account_trade_ctp, app,
    exercise_trade_ctp::{self, CtpReqQryExec<PERSON>ombField, CtpReqQryExecField},
    instrument_trade_ctp,
    monitor_trade_ctp::{self, CtpReqQrySelfTradeDtlField},
    order_trade_ctp::{self, CtpReqQryOmlField},
    position_trade_ctp, trade_req_ctp,
    trade_trade_ctp::{self},
};

use crate::common::ticonvert::TIConvert;

use super::abnormalstate::IOnRtnAbnormalState;

pub type CtpOrderStatusMap = dashmap::DashMap<String, i32>;
pub type CtpOrderMap = dashmap::DashMap<String, order_trade_ctp::CtpOrderField>;
pub type CtpQuoteMap = dashmap::DashMap<String, order_trade_ctp::CtpQuoteField>;
pub type CtpOmlMap = dashmap::DashMap<String, order_trade_ctp::CtpOmlField>;
pub type CtpExecOrdMap = dashmap::DashMap<String, exercise_trade_ctp::CtpExecOrderField>;
pub type CtpExecCombOrdMap = dashmap::DashMap<String, exercise_trade_ctp::CtpExecCombineOrderField>;
pub type CtpWDMap = dashmap::DashMap<String, account_trade_ctp::CtpWithdrawDepositField>;

pub type CtpTradePositionMap = dashmap::DashMap<String, monitor_trade_ctp::CtpTradePositionField>;
pub type CtpOrderInsertCancelMap = dashmap::DashMap<String, monitor_trade_ctp::CtpOrderInsertCancelField>;
pub type CtpTradeSelfMap = dashmap::DashMap<String, monitor_trade_ctp::CtpTradeSelfField>;
pub type CtpTradeSelfDtlMap = dashmap::DashMap<String, monitor_trade_ctp::CtpTradeSelfDtlField>;
pub type CtpLimitAmountMap = dashmap::DashMap<String, monitor_trade_ctp::CtpLimitAmountField>;
pub type CtpLimitPositionMap = dashmap::DashMap<String, monitor_trade_ctp::CtpLimitPositionField>;

pub type CtpOrderVec = Vec<order_trade_ctp::CtpOrderField>;
pub type CtpQuoteVec = Vec<order_trade_ctp::CtpQuoteField>;
pub type CtpTradeVec = Vec<trade_trade_ctp::CtpTradeField>;
pub type CtpWDVec = Vec<account_trade_ctp::CtpWithdrawDepositField>;

pub type CtpOmlVec = Vec<order_trade_ctp::CtpOmlField>;
pub type CtpExecOrdVec = Vec<exercise_trade_ctp::CtpExecOrderField>;
pub type CtpExecCombOrdVec = Vec<exercise_trade_ctp::CtpExecCombineOrderField>;
pub type CtpTradeSelfDtlVec = Vec<monitor_trade_ctp::CtpTradeSelfDtlField>;

pub struct TradeOptBuf {
    key_index: AtomicU64,

    ////////////////////////////////////////////////////////////////////////////
    /// 异常状态回调
    rtn_abnormalstate_callback: Option<Box<dyn IOnRtnAbnormalState>>,

    /// 查询资金响应回调
    qry_acc_callback: Option<Arc<dyn Fn(account_trade_ctp::CtpAccountField) + Send + Sync>>,

    /// 查询持仓响应回调
    qry_pos_callback: Option<Arc<dyn Fn(position_trade_ctp::CtpPositionField) + Send + Sync>>,

    /// 查询持仓组合响应回调
    qry_pos_comb_callback: Option<Arc<dyn Fn(position_trade_ctp::CtpPositionCombField) + Send + Sync>>,

    /// 报单响应回调
    rsp_inputorder_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputOrderField) + Send + Sync>>,

    /// 撤单响应回调
    rsp_inputorderaction_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputOrderActionField) + Send + Sync>>,

    /// 组合响应回调
    rsp_combaction_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputCombActionField) + Send + Sync>>,

    /// 行权响应回调
    rsp_execorder_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputExecOrderField) + Send + Sync>>,

    /// 行权撤单响应回调
    rsp_execorderaction_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputExecOrderActionField) + Send + Sync>>,

    /// 组合行权响应回调
    rsp_execcomborder_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputExecCombineOrderField) + Send + Sync>>,

    /// 组合行权撤单响应回调
    rsp_execcomborderaction_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputExecCombineOrderActionField) + Send + Sync>>,

    /// 解锁仓响应回调
    rsp_inputstklock_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputStockLockField) + Send + Sync>>,

    /// 报价响应回调
    rsp_inputquote_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputQuoteField) + Send + Sync>>,

    /// 撤报价响应回调
    rsp_inputquoteaction_callback: Option<Arc<dyn Fn(trade_req_ctp::CtpRspInputQuoteActionField) + Send + Sync>>,

    ////////////////////////////////////////////////////////////////////////////
    /// 已经结束的交易所报单
    order_status_map: Arc<CtpOrderStatusMap>,

    /// 报单回报
    order_map: Arc<Mutex<CtpOrderMap>>,

    /// 在途报单回报
    in_order_map: Arc<Mutex<CtpOrderMap>>,

    /// 成交回报
    trade_vec: Arc<Mutex<CtpTradeVec>>,

    /// 组合回报
    oml_map: Arc<CtpOmlMap>,

    /// 行权回报
    execord_map: Arc<CtpExecOrdMap>,

    /// 在途行权回报
    in_execord_map: Arc<Mutex<CtpExecOrdMap>>,

    /// 组合行权回报
    execcombord_map: Arc<CtpExecCombOrdMap>,

    /// 在途组合行权回报
    in_execcombord_map: Arc<Mutex<CtpExecCombOrdMap>>,

    /// 已经结束的交易所报价
    quote_status_map: Arc<CtpOrderStatusMap>,

    /// 报价回报
    quote_map: Arc<Mutex<CtpQuoteMap>>,

    /// 在途报价回报
    in_quote_map: Arc<Mutex<CtpQuoteMap>>,

    /// 出入金回报
    wd_map: Arc<CtpWDMap>,

    ////////////////////////////////////////////////////////////////////////////
    /// 成交持仓比回报
    tradeposition_map: Arc<Mutex<CtpTradePositionMap>>,

    /// 报撤单回报
    orderinsertcancel_map: Arc<Mutex<CtpOrderInsertCancelMap>>,

    /// 自成交回报
    selftrade_map: Arc<Mutex<CtpTradeSelfMap>>,

    /// 自成交详情回报
    selftradedtl_map: Arc<CtpTradeSelfDtlMap>,

    /// 限额回报
    limitamount_map: Arc<Mutex<CtpLimitAmountMap>>,

    /// 限仓回报
    limitposition_map: Arc<Mutex<CtpLimitPositionMap>>,
}

impl TradeOptBuf {
    pub fn new() -> Self {
        Self {
            key_index: AtomicU64::new(1),

            rtn_abnormalstate_callback: None,
            qry_acc_callback: None,
            qry_pos_callback: None,
            qry_pos_comb_callback: None,
            rsp_inputorder_callback: None,
            rsp_inputorderaction_callback: None,
            rsp_combaction_callback: None,
            rsp_execorder_callback: None,
            rsp_execorderaction_callback: None,
            rsp_execcomborder_callback: None,
            rsp_execcomborderaction_callback: None,
            rsp_inputstklock_callback: None,
            rsp_inputquote_callback: None,
            rsp_inputquoteaction_callback: None,

            order_status_map: Arc::new(CtpOrderStatusMap::new()),
            order_map: Arc::new(Mutex::new(CtpOrderMap::new())),
            in_order_map: Arc::new(Mutex::new(CtpOrderMap::new())),
            trade_vec: Arc::new(Mutex::new(CtpTradeVec::new())),
            oml_map: Arc::new(CtpOmlMap::new()),
            execord_map: Arc::new(CtpExecOrdMap::new()),
            in_execord_map: Arc::new(Mutex::new(CtpExecOrdMap::new())),
            execcombord_map: Arc::new(CtpExecCombOrdMap::new()),
            in_execcombord_map: Arc::new(Mutex::new(CtpExecCombOrdMap::new())),
            quote_status_map: Arc::new(CtpOrderStatusMap::new()),
            quote_map: Arc::new(Mutex::new(CtpQuoteMap::new())),
            in_quote_map: Arc::new(Mutex::new(CtpQuoteMap::new())),
            wd_map: Arc::new(CtpWDMap::new()),

            tradeposition_map: Arc::new(Mutex::new(CtpTradePositionMap::new())),
            orderinsertcancel_map: Arc::new(Mutex::new(CtpOrderInsertCancelMap::new())),
            selftrade_map: Arc::new(Mutex::new(CtpTradeSelfMap::new())),
            selftradedtl_map: Arc::new(CtpTradeSelfDtlMap::new()),
            limitamount_map: Arc::new(Mutex::new(CtpLimitAmountMap::new())),
            limitposition_map: Arc::new(Mutex::new(CtpLimitPositionMap::new())),
        }
    }
}

impl TradeOptBuf {
    /// 获取在途报价
    pub fn pop_in_quote(&self) -> CtpQuoteMap {
        let in_quote_map = self.in_quote_map.lock().unwrap();
        let ret = in_quote_map.clone();
        in_quote_map.clear();
        ret
    }

    /// 获取报价
    pub fn pop_quote(&self) -> CtpQuoteMap {
        let quote_map = self.quote_map.lock().unwrap();
        let ret = quote_map.clone();
        quote_map.clear();
        ret
    }

    /// 获取在途报单
    pub fn pop_in_order(&self) -> CtpOrderMap {
        let in_order_map = self.in_order_map.lock().unwrap();
        let ret = in_order_map.clone();
        in_order_map.clear();
        ret
    }

    /// 获取报单
    pub fn pop_order(&self) -> CtpOrderMap {
        let order_map = self.order_map.lock().unwrap();
        let ret = order_map.clone();
        order_map.clear();
        ret
    }

    /// 获取成交
    pub fn pop_trade(&self) -> CtpTradeVec {
        let mut trade_vec = self.trade_vec.lock().unwrap();
        let ret = trade_vec.clone();
        trade_vec.clear();
        ret
    }

    /// 获取在途行权
    pub fn pop_in_execord(&self) -> CtpExecOrdMap {
        let in_execord_map = self.in_execord_map.lock().unwrap();
        let ret = in_execord_map.clone();
        in_execord_map.clear();
        ret
    }

    /// 获取在途行权组合
    pub fn pop_in_execcombord(&self) -> CtpExecCombOrdMap {
        let in_execcombord_map = self.in_execcombord_map.lock().unwrap();
        let ret = in_execcombord_map.clone();
        in_execcombord_map.clear();
        ret
    }

    /// 获取成交持仓比
    pub fn pop_tradeposition(&self) -> CtpTradePositionMap {
        let tpmap = self.tradeposition_map.lock().unwrap();
        let ret = tpmap.clone();
        tpmap.clear();
        ret
    }

    /// 获取报撤单
    pub fn pop_orderinsertcancel(&self) -> CtpOrderInsertCancelMap {
        let oicmap = self.orderinsertcancel_map.lock().unwrap();
        let ret = oicmap.clone();
        oicmap.clear();
        ret
    }

    /// 获取限仓
    pub fn pop_limitposition(&self) -> CtpLimitPositionMap {
        let lpmap = self.limitposition_map.lock().unwrap();
        let ret = lpmap.clone();
        lpmap.clear();
        ret
    }

    /// 获取限额
    pub fn pop_limitamount(&self) -> CtpLimitAmountMap {
        let lamap = self.limitamount_map.lock().unwrap();
        let ret = lamap.clone();
        lamap.clear();
        ret
    }

    /// 获取自成交
    pub fn pop_selftrade(&self) -> CtpTradeSelfMap {
        let stmap = self.selftrade_map.lock().unwrap();
        let ret = stmap.clone();
        stmap.clear();
        ret
    }

    // 查询组合
    pub fn qry_oml(&self, req: &CtpReqQryOmlField, page_index: i32, page_size: i32) -> (i32, CtpOmlVec) {
        log::debug!(
            "qry_oml: exch:{}, insid:{}, combdir:{}, combtradeid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.CombDir,
            req.CombTradeID,
            req.TransTime0,
            req.TransTime0_Int,
            req.TransTime1,
            req.TransTime1_Int,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut oml_vec = CtpOmlVec::new();
        if page_size <= 0 {
            return (cnt, oml_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_oml_vec: CtpOmlVec = self.oml_map.iter().map(|it| it.value().clone()).collect();
        sort_oml_vec.sort_by(|a, b| b.ActionTime.cmp(&a.ActionTime));
        sort_oml_vec.into_iter().for_each(|oml| {
            if !req.ExchID.is_empty() {
                if req.ExchID != oml.ExchangeID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != oml.InvestorID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !oml.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.CombDir {
                if req.CombDir != oml.CombDirection {
                    return;
                }
            }

            if !req.CombTradeID.is_empty() {
                if !oml.ComTradeID.contains(&req.CombTradeID) {
                    return;
                }
            }

            if 0 != req.TransTime0_Int {
                if oml.ActionTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1_Int {
                if oml.ActionTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                oml_vec.push(oml);
            }

            cnt += 1;
        });

        (cnt, oml_vec)
    }

    /// 查询组合(全部)
    pub fn qry_oml_all(&self) -> CtpOmlVec {
        let mut ret_vec: CtpOmlVec = self.oml_map.iter().map(|it| it.value().clone()).collect();
        ret_vec.sort_by(|a, b| b.ActionTime.cmp(&a.ActionTime));
        ret_vec
    }

    /// 查询行权(page_index从0开始)
    pub fn qry_exercise(&self, req: &CtpReqQryExecField, page_index: i32, page_size: i32) -> (i32, CtpExecOrdVec) {
        log::debug!(
            "qry_exercise: exch:{}, insid:{}, ordstatus:{}, ordsysid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.OrdStatus,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime0_Int,
            req.TransTime1,
            req.TransTime1_Int,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut exec_vec = CtpExecOrdVec::new();
        if page_size <= 0 {
            return (cnt, exec_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_exec_vec: CtpExecOrdVec = self.execord_map.iter().map(|it| it.value().clone()).collect();
        sort_exec_vec.sort_by(|a, b| b.InsertTime.cmp(&a.InsertTime));
        sort_exec_vec.into_iter().for_each(|eo| {
            if !req.ExchID.is_empty() {
                if req.ExchID != eo.ExchangeID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != eo.InvestorID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !eo.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.OrdStatus {
                if req.OrdStatus != eo.LastStatus {
                    return;
                }
            }

            if !req.OrderSysID.is_empty() {
                if !eo.ExecOrderSysID.contains(&req.OrderSysID) {
                    return;
                }
            }

            if 0 != req.TransTime0_Int {
                if eo.InsertTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1_Int {
                if eo.InsertTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                exec_vec.push(eo);
            }

            cnt += 1;
        });

        (cnt, exec_vec)
    }

    /// 查询行权(全部)
    pub fn qry_exercise_all(&self) -> CtpExecOrdVec {
        let mut ret_vec: CtpExecOrdVec = self.execord_map.iter().map(|it| it.value().clone()).collect();
        ret_vec.sort_by(|a, b| b.InsertTime.cmp(&a.InsertTime));
        ret_vec
    }

    /// 查询合并行权(page_index从0开始)
    pub fn qry_exercise_comb(&self, req: &CtpReqQryExecCombField, page_index: i32, page_size: i32) -> (i32, CtpExecCombOrdVec) {
        log::debug!(
                "qry_exercise_comb: exch:{}, insid:{}, ordstatus:{}, ordsysid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
                req.ExchID,
                req.InstrumentID,
                req.OrdStatus,
                req.OrderSysID,
                req.TransTime0,
                req.TransTime0_Int,
                req.TransTime1,
                req.TransTime1_Int,
                page_index,
                page_size
            );

        let mut cnt = 0;
        let mut exec_vec = CtpExecCombOrdVec::new();
        if page_size <= 0 {
            return (cnt, exec_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_exec_vec: CtpExecCombOrdVec = self.execcombord_map.iter().map(|it| it.value().clone()).collect();
        sort_exec_vec.sort_by(|a, b| b.InsertTime.cmp(&a.InsertTime));
        sort_exec_vec.into_iter().for_each(|eo| {
            if !req.ExchID.is_empty() {
                if req.ExchID != eo.ExchangeID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != eo.InvestorID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !eo.CallInstrumentID.contains(&req.InstrumentID) && !eo.PutInstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.OrdStatus {
                if req.OrdStatus != eo.LastStatus {
                    return;
                }
            }

            if !req.OrderSysID.is_empty() {
                if !eo.ExecCombineOrderSysID.contains(&req.OrderSysID) {
                    return;
                }
            }

            if 0 != req.TransTime0_Int {
                if eo.InsertTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1_Int {
                if eo.InsertTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                exec_vec.push(eo);
            }

            cnt += 1;
        });

        (cnt, exec_vec)
    }

    /// 查询合并行权(全部)
    pub fn qry_exercise_comb_all(&self) -> CtpExecCombOrdVec {
        let mut ret_vec: CtpExecCombOrdVec = self.execcombord_map.iter().map(|it| it.value().clone()).collect();
        ret_vec.sort_by(|a, b| b.InsertTime.cmp(&a.InsertTime));
        ret_vec
    }

    /// 查询自成交详情
    pub fn qry_selftradedtl(
        &self,
        req: &CtpReqQrySelfTradeDtlField,
        page_index: i32,
        page_size: i32,
    ) -> (i32, CtpTradeSelfDtlVec) {
        log::debug!(
            "qry_selftradedtl: exch:{}, insid:{}, tradeid:{}, ordsysid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.TradeID,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut selftrd_vec = CtpTradeSelfDtlVec::new();
        if page_size <= 0 {
            return (cnt, selftrd_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_selftrd_vec: CtpTradeSelfDtlVec = self.selftradedtl_map.iter().map(|it| it.value().clone()).collect();
        sort_selftrd_vec.sort_by(|a, b| b.TransTime.cmp(&a.TransTime));
        sort_selftrd_vec.into_iter().for_each(|trd| {
            if 0 != req.ExchID {
                if req.ExchID != trd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != trd.AccountID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !trd.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if !req.TradeID.is_empty() {
                if !trd.TradeID.contains(&req.TradeID) {
                    return;
                }
            }

            if !req.OrderSysID.is_empty() {
                if !trd.OrderSysID.contains(&req.OrderSysID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if trd.TransTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if trd.TransTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                selftrd_vec.push(trd);
            }

            cnt += 1;
        });

        (cnt, selftrd_vec)
    }

    /// 查询出入金(page_index从0开始)
    pub fn qry_wd(
        &self,
        req: &account_trade_ctp::CtpReqQryWithdrawDepositField,
        page_index: i32,
        page_size: i32,
    ) -> (i32, CtpWDVec) {
        log::debug!(
            "qry_wd: exch:{}, accountid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut wd_vec = CtpWDVec::new();
        if page_size <= 0 {
            return (cnt, wd_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_wd_vec: CtpWDVec = self.wd_map.iter().map(|it| it.value().clone()).collect();
        sort_wd_vec.sort_by(|a, b| b.DealTime.cmp(&a.DealTime));
        sort_wd_vec.into_iter().for_each(|wd| {
            if 0 != req.ExchID {
                if req.ExchID != wd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != wd.AccountID {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if wd.DealTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if wd.DealTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                wd_vec.push(wd);
            }

            cnt += 1;
        });

        (cnt, wd_vec)
    }
}

impl TradeOptBuf {
    // 注册异常状态回调
    pub fn register_rtn_abnormalstate_callback(&mut self, callback: Box<dyn IOnRtnAbnormalState>) {
        self.rtn_abnormalstate_callback = Some(callback);
    }

    /// 注册查询资金响应回调
    pub fn register_qry_acc_callback<F>(&mut self, callback: F)
    where
        F: Fn(account_trade_ctp::CtpAccountField) + Send + Sync + 'static,
    {
        self.qry_acc_callback = Some(Arc::new(callback));
    }

    /// 注册查询持仓响应回调
    pub fn register_qry_pos_callback<F>(&mut self, callback: F)
    where
        F: Fn(position_trade_ctp::CtpPositionField) + Send + Sync + 'static,
    {
        self.qry_pos_callback = Some(Arc::new(callback));
    }

    /// 注册查询持仓组合响应回调
    pub fn register_qry_pos_comb_callback<F>(&mut self, callback: F)
    where
        F: Fn(position_trade_ctp::CtpPositionCombField) + Send + Sync + 'static,
    {
        self.qry_pos_comb_callback = Some(Arc::new(callback));
    }

    /// 注册报单响应回调
    pub fn register_rsp_inputorder_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputOrderField) + Send + Sync + 'static,
    {
        self.rsp_inputorder_callback = Some(Arc::new(callback));
    }

    /// 注册撤单响应回调
    pub fn register_rsp_inputorderaction_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputOrderActionField) + Send + Sync + 'static,
    {
        self.rsp_inputorderaction_callback = Some(Arc::new(callback));
    }

    /// 注册组合响应回调
    pub fn register_rsp_combaction_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputCombActionField) + Send + Sync + 'static,
    {
        self.rsp_combaction_callback = Some(Arc::new(callback));
    }

    /// 注册行权响应回调
    pub fn register_rsp_execorder_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputExecOrderField) + Send + Sync + 'static,
    {
        self.rsp_execorder_callback = Some(Arc::new(callback));
    }

    /// 注册行权撤单响应回调
    pub fn register_rsp_execorderaction_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputExecOrderActionField) + Send + Sync + 'static,
    {
        self.rsp_execorderaction_callback = Some(Arc::new(callback));
    }

    /// 注册组合行权响应回调
    pub fn register_rsp_execcomborder_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputExecCombineOrderField) + Send + Sync + 'static,
    {
        self.rsp_execcomborder_callback = Some(Arc::new(callback));
    }

    /// 注册组合行权撤单响应回调
    pub fn register_rsp_execcomborderaction_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputExecCombineOrderActionField) + Send + Sync + 'static,
    {
        self.rsp_execcomborderaction_callback = Some(Arc::new(callback));
    }

    /// 注册解锁仓响应回调
    pub fn register_rsp_inputstklock_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputStockLockField) + Send + Sync + 'static,
    {
        self.rsp_inputstklock_callback = Some(Arc::new(callback));
    }

    /// 注册报价响应回调
    pub fn register_rsp_inputquote_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputQuoteField) + Send + Sync + 'static,
    {
        self.rsp_inputquote_callback = Some(Arc::new(callback));
    }

    /// 注册撤报价响应回调
    pub fn register_rsp_inputquoteaction_callback<F>(&mut self, callback: F)
    where
        F: Fn(trade_req_ctp::CtpRspInputQuoteActionField) + Send + Sync + 'static,
    {
        self.rsp_inputquoteaction_callback = Some(Arc::new(callback));
    }
}

impl TradeOptBuf {
    /// 断开连接
    pub fn on_rtn_disconnect(&self) {
        if let Some(rtn) = self.rtn_abnormalstate_callback.as_ref() {
            rtn.on_rtn_disconnect();
        }
    }

    /// 心跳消息
    pub fn on_rtn_heartbeat(&self, heartbeat: app::AppHeartbeatField) {}

    /// 交易服务端异常消息
    pub fn on_rtn_tdservererror(&self, tderr: app::AppTDServerErrorField) {
        if let Some(rtn) = self.rtn_abnormalstate_callback.as_ref() {
            rtn.on_rtn_tdservererror(tderr);
        }
    }

    /// 登录到交易服务端的Session超时
    pub fn on_rtn_tdsessiontimeout(&self, tdst: app::AppTDSessionTimeoutField) {
        if let Some(rtn) = self.rtn_abnormalstate_callback.as_ref() {
            rtn.on_rtn_tdsessiontimeout(tdst);
        }
    }

    /// 委托消息
    pub fn on_rtn_order(&self, ord: order_trade_ctp::CtpOrderField) {
        let key = {
            if !ord.OrderSysID.is_empty() {
                format!("{}{}{}", ord.InvestorID, ord.ExchangeID, ord.OrderSysID)
            } else {
                let index = self.key_index.fetch_add(1, Ordering::SeqCst);
                format!("{}{}", ord.InvestorID, index)
            }
        };

        let mut ord = ord;
        ord.LastStatus = TIConvert::ti_ordstatus(ord.OrderStatus);
        ord.Key = key;
        ord.TIOwnerType = {
            match ord.OwnerType.chars().next() {
                Some(c) => c as i32,
                None => -1,
            }
        };

        let mut insert_falg = false;
        if self.order_status_map.contains_key(&ord.Key) {
            let mut prev_order_status = self.order_status_map.get_mut(&ord.Key).unwrap();
            if *prev_order_status < ord.LastStatus {
                *prev_order_status = ord.LastStatus;
                insert_falg = true;
            }
        } else {
            insert_falg = true;
        }

        if insert_falg {
            self.order_map.lock().unwrap().insert(ord.Key.clone(), ord.clone());
            if 0 == ord.InstallID {
                // 用于存放报单类型. 0:正常报单; 1:报价产生的报单
                self.in_order_map.lock().unwrap().insert(ord.Key.clone(), ord);
            }
        }
    }

    /// 成交消息
    pub fn on_rtn_trade(&self, trd: trade_trade_ctp::CtpTradeField) {
        self.trade_vec.lock().unwrap().push(trd);
    }

    /// 组合消息
    pub fn on_rtn_oml(&self, oml: order_trade_ctp::CtpOmlField) {
        self.oml_map
            .insert(std::format!("{}{}{}", oml.ExchangeID, oml.ComTradeID, oml.SequenceNo), oml);
    }

    /// 行权消息
    pub fn on_rtn_execorder(&self, eo: exercise_trade_ctp::CtpExecOrderField) {
        let key = {
            if !eo.ExecOrderSysID.is_empty() {
                format!("{}{}{}", eo.InvestorID, eo.ExchangeID, eo.ExecOrderSysID)
            } else {
                let index = self.key_index.fetch_add(1, Ordering::SeqCst);
                format!("{}{}", eo.InvestorID, index)
            }
        };

        let mut eo = eo;
        eo.LastStatus = TIConvert::ti_execresault(eo.ExecResult);
        if eo.InsertTime.is_empty() {
            // 后续所有处理统一使用InsertTime
            eo.InsertTime = eo.CancelTime.clone();
        }

        let mut insert_falg = false;
        if self.execord_map.contains_key(&key) {
            let mut prev_eo = self.execord_map.get_mut(&key).unwrap();
            if prev_eo.LastStatus < eo.LastStatus {
                prev_eo.LastStatus = eo.LastStatus;
                prev_eo.StatusMsg = eo.StatusMsg.clone();
                prev_eo.InsertTime = eo.InsertTime.clone();
                insert_falg = true;
            }
        } else {
            insert_falg = true;
            self.execord_map.insert(key.clone(), eo.clone());
        }

        if insert_falg {
            self.in_execord_map.lock().unwrap().insert(key, eo);
        }
    }

    /// 行权指令合并消息
    pub fn on_rtn_execcombineorder(&self, eco: exercise_trade_ctp::CtpExecCombineOrderField) {
        let key = {
            if !eco.ExecCombineOrderSysID.is_empty() {
                format!("{}{}{}", eco.InvestorID, eco.ExchangeID, eco.ExecCombineOrderSysID)
            } else {
                let index = self.key_index.fetch_add(1, Ordering::SeqCst);
                format!("{}{}", eco.InvestorID, index)
            }
        };

        let mut eco = eco;
        eco.LastStatus = TIConvert::ti_execresault(eco.ExecResult);
        if eco.InsertTime.is_empty() {
            // 后续所有处理统一使用InsertTime
            eco.InsertTime = eco.CancelTime.clone();
        }

        let mut insert_falg = false;
        if self.execcombord_map.contains_key(&key) {
            let mut prev_eco = self.execcombord_map.get_mut(&key).unwrap();
            if prev_eco.LastStatus < eco.LastStatus {
                prev_eco.LastStatus = eco.LastStatus;
                prev_eco.StatusMsg = eco.StatusMsg.clone();
                prev_eco.InsertTime = eco.InsertTime.clone();
                insert_falg = true;
            }
        } else {
            insert_falg = true;
            self.execcombord_map.insert(key.clone(), eco.clone());
        }

        if insert_falg {
            self.in_execcombord_map.lock().unwrap().insert(key, eco);
        }
    }

    /// 报价消息
    pub fn on_rtn_quote(&self, quote: order_trade_ctp::CtpQuoteField) {
        let key = {
            if !quote.QuoteSysID.is_empty() {
                format!("{}{}{}", quote.InvestorID, quote.ExchangeID, quote.QuoteSysID)
            } else {
                let index = self.key_index.fetch_add(1, Ordering::SeqCst);
                format!("{}{}", quote.InvestorID, index)
            }
        };

        let mut quote = quote;
        quote.LastStatus = TIConvert::ti_ordstatus(quote.QuoteStatus);
        quote.BidLastStatus = quote.InstallID;
        quote.AskLastStatus = quote.SettlementID;
        quote.Key = key;

        let mut insert_falg = false;
        if self.quote_status_map.contains_key(&quote.Key) {
            let mut prev_status = self.order_status_map.get_mut(&quote.Key).unwrap();
            if *prev_status < quote.LastStatus {
                *prev_status = quote.LastStatus;
                insert_falg = true;
            }
        } else {
            insert_falg = true;
        }

        if insert_falg {
            self.quote_map.lock().unwrap().insert(quote.Key.clone(), quote.clone());
            self.in_quote_map.lock().unwrap().insert(quote.Key.clone(), quote);
        }
    }

    /// 限额消息
    pub fn on_rtn_limitamount(&self, la: monitor_trade_ctp::CtpLimitAmountField) {
        let mut la = la;
        la.Key = std::format!("{}{}", la.ExchID, la.AccountID);
        if 0f64 != la.MaxBuyPremium {
            la.Ratio = la.BuyPremium / la.MaxBuyPremium;
        }
        la.CanBuyPremium = la.MaxBuyPremium - la.BuyPremium;

        let lamap = self.limitamount_map.lock().unwrap();
        lamap.insert(la.Key.clone(), la);
    }

    /// 限持仓
    pub fn on_rtn_limitposition(&self, lp: monitor_trade_ctp::CtpLimitPositionField) {
        let lpmap = self.limitposition_map.lock().unwrap();
        lpmap.insert(
            std::format!("{}{}{}{}", lp.ExchID, lp.AccountID, lp.ClientID, lp.ProductID),
            lp,
        );
    }

    /// 自成交
    pub fn on_rtn_tradeself(&self, trdslf: monitor_trade_ctp::CtpTradeSelfField) {
        let stmap = self.selftrade_map.lock().unwrap();
        stmap.insert(
            std::format!(
                "{}{}{}{}",
                trdslf.ExchID,
                trdslf.AccountID,
                trdslf.ClientID,
                trdslf.InstrumentID
            ),
            trdslf,
        );
    }

    /// 自成交详情
    pub fn on_rtn_tradeselfdtl(&self, trdslf: monitor_trade_ctp::CtpTradeSelfDtlField) {
        self.selftradedtl_map.insert(
            std::format!("{}{}{}", trdslf.ExchID, trdslf.ClientID, trdslf.MsgSeqNum),
            trdslf,
        );
    }

    /// 成交持仓比例
    pub fn on_rtn_tradeposition(&self, tp: monitor_trade_ctp::CtpTradePositionField) {
        let tpmap = self.tradeposition_map.lock().unwrap();
        tpmap.insert(
            std::format!("{}{}{}{}", tp.ExchID, tp.AccountID, tp.ClientID, tp.ProductID),
            tp,
        );
    }

    /// 报撤单
    pub fn on_rtn_orderinsertcancel(&self, ordinsertcancel: monitor_trade_ctp::CtpOrderInsertCancelField) {
        let oicmap = self.orderinsertcancel_map.lock().unwrap();
        oicmap.insert(
            std::format!(
                "{}{}{}{}",
                ordinsertcancel.ExchID,
                ordinsertcancel.AccountID,
                ordinsertcancel.ClientID,
                ordinsertcancel.ProductID
            ),
            ordinsertcancel,
        );
    }

    /// 出入金
    pub fn on_rtn_wd(&self, wd: account_trade_ctp::CtpWithdrawDepositField) {
        self.wd_map.insert(format!("{}{}", wd.AccountID, wd.BizSeqNum), wd);
    }

    /// 报单录入响应消息
    pub fn on_rsp_inputorder(&self, rsp: trade_req_ctp::CtpRspInputOrderField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputorder_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 报单操作响应
    pub fn on_rsp_inputorderaction(&self, rsp: trade_req_ctp::CtpRspInputOrderActionField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputorderaction_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 行权响应
    pub fn on_rsp_inputexecorder(&self, rsp: trade_req_ctp::CtpRspInputExecOrderField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_execorder_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 行权操作响应
    pub fn on_rsp_inputexecorderaction(&self, rsp: trade_req_ctp::CtpRspInputExecOrderActionField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_execorderaction_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 组合录入响应
    pub fn on_rsp_inputcombaction(&self, rsp: trade_req_ctp::CtpRspInputCombActionField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_combaction_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 行权指令合并录入响应
    pub fn on_rsp_inputexeccombineorder(&self, rsp: trade_req_ctp::CtpRspInputExecCombineOrderField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_execcomborder_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 行权指令合并操作响应
    pub fn on_rsp_inputexeccombineorderaction(
        &self,
        rsp: trade_req_ctp::CtpRspInputExecCombineOrderActionField,
        reqid: i32,
        islast: bool,
    ) {
        if let Some(cb) = self.rsp_execcomborderaction_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 备兑解锁仓响应消息
    pub fn on_rsp_inputstocklock(&self, rsp: trade_req_ctp::CtpRspInputStockLockField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputstklock_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 报价录入响应消息
    pub fn on_rsp_inputquote(&self, rsp: trade_req_ctp::CtpRspInputQuoteField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputquote_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 报价操作响应
    pub fn on_rsp_inputquoteaction(&self, rsp: trade_req_ctp::CtpRspInputQuoteActionField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputquoteaction_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 查询合约响应
    pub fn on_rsp_qry_instrument(&self, ins: instrument_trade_ctp::CtpRspQryInstrumentField, reqid: i32, islast: bool) {
        let mut gins = crate::common::global::INS.get().unwrap().write().unwrap();

        if !ins.InstrumentID.is_empty() {
            gins.push_ins(ins.to_ins());
        }

        if islast {
            log::info!("Received the last instrument");
            gins.is_last = true;
        }
    }

    /// 查询资金响应消息
    pub fn on_rsp_qry_tradingaccount(&self, acc: account_trade_ctp::CtpAccountField, reqid: i32, islast: bool) {
        if let Some(cb) = self.qry_acc_callback.as_ref() {
            cb(acc);
        }
    }

    /// 查询持仓响应消息
    pub fn on_rsp_qry_investorposition(&self, pos: position_trade_ctp::CtpPositionField, reqid: i32, islast: bool) {
        if let Some(cb) = self.qry_pos_callback.as_ref() {
            cb(pos);
        }
    }

    /// 查询组合持仓明细响应消息
    pub fn on_rsp_qry_investorpositioncombdtl(&self, pos: position_trade_ctp::CtpPositionCombField, reqid: i32, islast: bool) {
        if let Some(cb) = self.qry_pos_comb_callback.as_ref() {
            cb(pos);
        }
    }
}
