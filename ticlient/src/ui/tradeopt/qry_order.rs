use std::rc::Rc;

use crate::{
    common::{config::Config, global::DB, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::order_trade_ctp::{CtpOrderField, CtpReqQryOrderField};

/// 查询 - 委托
pub(super) struct QueryOrder {}

impl QueryOrder {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_qryord = app.global::<OptTrd_QryOrder>();

        let app_weak = app.as_weak();
        app_qryord.on_qry_clieked(move || QueryOrder::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        app_qryord.on_page_index_changed(move |index| QueryOrder::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        app_qryord.on_page_size_changed(move |size| QueryOrder::on_page_size_changed(&app_weak, size));

        let app_weak = app.as_weak();
        app_qryord.on_export_clicked(move |etype| QueryOrder::on_export_clicked(&app_weak, etype));

        log::trace!("Query order init completed");
    }
}

impl QueryOrder {
    /// 获取一行数据
    fn get_row_items(ord: &CtpOrderField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        let exchid = {
            match ord.ExchangeID.as_str() {
                "SSE" => 1,
                "SZSE" => 2,
                _ => -1,
            }
        };
        let ti = TIConvert::ctp_opt_order_to_ti(exchid, ord.OrderPriceType, ord.TimeCondition, ord.VolumeCondition);

        // 资金账号
        items.push(ord.InvestorID.clone().into());

        // 交易所
        items.push(ord.ExchangeID.clone().into());

        // 交易编码
        items.push(ord.ClientID.clone().into());

        // 发生时间
        items.push(ord.InsertTime.clone().into());

        // 合约编码
        items.push(ord.InstrumentID.clone().into());

        // 买买方向
        items.push(TIConvert::ctp_side(ord.Direction).into());

        // 开平标志
        items.push(
            TIConvert::ctp_offsetflag({
                let ret = ord.CombOffsetFlag.parse::<i32>();
                if let Ok(of) = ret {
                    of + 48
                } else {
                    -1
                }
            })
            .into(),
        );

        // 数量
        items.push(slint::format!("{}", ord.VolumeTotalOriginal));

        // 价格
        items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, ord.LimitPrice));

        // 报单状态
        items.push(TIConvert::order_status(ord.LastStatus).into());

        // 交易所报单编号
        items.push(ord.OrderSysID.clone().into());

        // 本地报单编号
        items.push(ord.OrderLocalID.clone().into());

        // 报单价格条件
        items.push(TIConvert::pricetype(ti.0).into());

        // 有效期类型
        items.push(TIConvert::timeinforce(ti.1).into());

        // 成交量类型
        items.push(TIConvert::volume_condition(ti.2).into());

        // 投资者类型
        items.push(TIConvert::ownertype(ord.TIOwnerType).into());

        // 触发条件
        items.push(TIConvert::ctp_trigcondition(ord.ContingentCondition).into());

        // 最小成交量
        items.push(slint::format!("{}", ord.MinVolume));

        // 用户代码
        items.push(ord.UserID.clone().into());

        // 备兑标志
        items.push(
            TIConvert::ctp_hedgeflag2({
                let ret = ord.CombHedgeFlag.parse::<i32>();
                if let Ok(of) = ret {
                    of + 48
                } else {
                    -1
                }
            })
            .into(),
        );

        items
    }

    // 查询
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qryord = app.global::<OptTrd_QryOrder>();

        // 清空上一次结果
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qryord.set_row_data(row_data.into());

        let st = app_qryord.get_starttime();
        let et = app_qryord.get_endtime();
        let ordstatus = app_qryord.get_ordstatus();
        let req = CtpReqQryOrderField {
            ExchID: app_qryord.get_exchid().into(),
            AccountID: "".into(),
            InstrumentID: std::format!("%{}%", app_qryord.get_insid()),
            OrdStatus: TIConvert::order_status_id(&ordstatus),
            OrderSysID: std::format!("%{}%", app_qryord.get_ordersysid()),
            TransTime0: std::format!("{:02}:{:02}:{:02}", st.hour, st.minute, st.second),
            TransTime0_Int: st.hour * 10000 + st.minute * 100 + st.second,
            TransTime1: std::format!("{:02}:{:02}:{:02}", et.hour, et.minute, et.second),
            TransTime1_Int: et.hour * 10000 + et.minute * 100 + et.second,
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qryord.get_page_index()
            }
        };
        let page_size = app_qryord.get_page_size();

        let ret = { DB.get().unwrap().lock().unwrap().qry_trd_order(&req, page_index, page_size) };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qryord.set_page_index_model(page_index_data.into());
            app_qryord.set_item_total(ret.0);
            app_qryord.set_page_index(0);
            app_qryord.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|ord| {
            let items = QueryOrder::get_row_items(&ord);
            row_data.push(items.into());
        });

        app_qryord.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryOrder::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryOrder::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryOrder::on_query(app_weak, true);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("order_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出报单失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "交易编码",
            "发生时间",
            "合约编码",
            "买卖方向",
            "开平标志",
            "数量",
            "价格",
            "报单状态",
            "交易所报单编号",
            "本地报单编号",
            "报单价格条件",
            "有效期类型",
            "成交量类型",
            "投资者类型",
            "触发条件",
            "最小成交量",
            "用户代码",
            "备兑标志",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出报单失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<OptTrd_QryOrder>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let mut page_index = 0;
            let page_size = 2000;
            let req = CtpReqQryOrderField {
                TransTime1: "23:59:59".into(),
                TransTime1_Int: 235959,
                ..Default::default()
            };

            loop {
                let ret = { DB.get().unwrap().lock().unwrap().qry_trd_order(&req, page_index, page_size) };

                ret.1.iter().for_each(|ord| {
                    let items = QueryOrder::get_row_items(&ord);
                    let _ = writer.write_record(items.iter());
                });

                if (ret.1.len() as i32) < page_size {
                    break;
                }

                page_index += 1;
            }
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出报单失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出报单成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}
