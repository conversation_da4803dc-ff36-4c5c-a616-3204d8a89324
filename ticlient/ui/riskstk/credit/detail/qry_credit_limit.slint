import { <PERSON>, TableView, TVColumnHeader, TVCellType, TVVisible, Fonts, Label, Radius, Icons, ComboBox, Button } from "@zstk/lib.slint";
import { ColWidth, CommonModel, NotifyColor, WidgetColor, AppCom, AppInerArgs } from "../../../struct.slint";
import { TimeSelect, TimeItem } from "../../../widgets/time.slint";
import { PageItem } from "../../../widgets/page.slint";
import { StkRskCredit_QryCreditLimit } from "../struct.slint";

export component QryCreditLimitPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <length> header_exchid_width: AppInerArgs.show_detail ? ColWidth.exchid : 0.5px;
    private property <TVVisible> header_exchid_visible: AppInerArgs.show_detail ? TVVisible.Visible : TVVisible.Invisible;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkRskCredit_QryCreditLimit.exchid;
        i_cbx_ltype.current_text = StkRskCredit_QryCreditLimit.ltype_str;
        i_ts_start.time = StkRskCredit_QryCreditLimit.starttime;
        i_ts_end.time = StkRskCredit_QryCreditLimit.endtime;
    }

    private property <TVColumnHeader> header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        columns: [
            { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: header_exchid_width, visible: header_exchid_visible },
            { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_120 },
            { title: @tr("额度类型 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_110, tips: "融资额度\n融券额度" },
            { title: @tr("转入额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("转出额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("处理时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_120 },
        ]
    };

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            if AppInerArgs.show_detail: Label {
                text: "交易所";
                vertical_alignment: center;
            }
            i_cbx_exch := ComboBox {
                width: AppInerArgs.show_detail ? 70px : 0px;
                height: AppCom.widgets_height;
                visible: AppInerArgs.show_detail;
                placeholder_text: "请选择";
                current_text: StkRskCredit_QryCreditLimit.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "额度类型";
                vertical_alignment: center;
            }
            i_cbx_ltype := ComboBox {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_QryCreditLimit.ltype_str;
                model: [
                    { text: "", tag: 0, },
                    { text: "融资额度", tag: -100, },
                    { text: "融券额度", tag: -101, },
                ];
            }

            Label {
                text: "处理时间";
                vertical_alignment: center;
            }
            HorizontalLayout {
                i_ts_start := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryCreditLimit.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryCreditLimit.endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                height: AppCom.widgets_height;
                background: WidgetColor.btn_background;

                clicked => {
                    StkRskCredit_QryCreditLimit.exchid = i_cbx_exch.current_text;
                    StkRskCredit_QryCreditLimit.ltype_str = i_cbx_ltype.current_text;
                    StkRskCredit_QryCreditLimit.starttime = i_ts_start.time;
                    StkRskCredit_QryCreditLimit.endtime = i_ts_end.time;
                    StkRskCredit_QryCreditLimit.starttime_int = i_ts_start.time_int;
                    StkRskCredit_QryCreditLimit.endtime_int = i_ts_end.time_int;

                    if i_cbx_ltype.current_text == "融资额度" {
                        StkRskCredit_QryCreditLimit.ltype_int = -100;
                    } else if i_cbx_ltype.current_text == "融券额度" {
                        StkRskCredit_QryCreditLimit.ltype_int = -101;
                    } else {
                        StkRskCredit_QryCreditLimit.ltype_int = 0;
                    }

                    StkRskCredit_QryCreditLimit.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            sortable: true;
            column_header: header;
            row_count: StkRskCredit_QryCreditLimit.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_QryCreditLimit.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                if 2 == column_index {
                    if data == "融资额度" {
                        return #9232e1;
                    } else if data == "融券额度" {
                        return #e84add;
                    } else {
                        return Theme.foreground;
                    }
                }

                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_QryCreditLimit.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_QryCreditLimit.row_data[row_index]);
                }
            }
        }

        if StkRskCredit_QryCreditLimit.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkRskCredit_QryCreditLimit.page_index_model;
            item_total <=> StkRskCredit_QryCreditLimit.item_total;
            page_total <=> StkRskCredit_QryCreditLimit.page_total;
            page_index <=> StkRskCredit_QryCreditLimit.page_index;
            page_size <=> StkRskCredit_QryCreditLimit.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryCreditLimit.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryCreditLimit.page_size_changed(size);
            }
        }
    }
}