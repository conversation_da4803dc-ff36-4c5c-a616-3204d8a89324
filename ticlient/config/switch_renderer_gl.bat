@echo off
set "file=renderer.txt"
set "content=gl"

if exist "%file%" (
    del "%file%"
    if exist "%file%" (
        echo [Failed] Unable to delete %file%, please check if the file is occupied
        pause
        exit /b 1
    )
)

<nul set /p="%content%" > "%file%"
if exist "%file%" (
    echo [Success] Switch to %content% renderer successfully
    pause
    exit /b 0
) else (
    echo [Failed] Switch to %content% renderer failed, Unable to create %file%
    pause
    exit /b 1
)
