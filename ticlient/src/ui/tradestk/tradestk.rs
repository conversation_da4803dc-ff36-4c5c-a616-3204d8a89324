use crate::{
    common::global::{self, DB, MD_BUF, TOKIO_RT, TRADE_STK_BUF},
    slintui::*,
    ui::{
        tradecom::{abnormalstate::AbnormalState, statusstrip::StatusStrip},
        IApp,
    },
};
use slint::*;

use std::{
    sync::{Arc, RwLock},
    thread,
};
use tokio::time::{interval, Duration};

use super::{
    account::Account, credit_concentration::CreditConcent, credit_contract::CreditContract, credit_limitamt::CreditLimitAmt,
    credit_limitpos::CreditLimitPos, in_fjy_order::InFjyOrder, in_order::InOrder, input_fjy_order::InputFjyOrder,
    input_order::InputOrder, input_order_credit::InputOrderCredit, marketdata::Marketdata, mon_convertbonds::MonConvertBonds,
    mon_orderinsertcancel::MonOrderInsertCancel, mon_selftrade::MonSelfTrade, mon_selftradedtl::MonSelfTradeDtl,
    position::Position, qry_credit_reamtdtl::QueryCreditReAmtDtl, qry_fjy_order::QueryFjyOrder, qry_fjy_trade::QueryFjyTrade,
    qry_fundtransdtl::QueryFundTransDtl, qry_instrument::QueryInstrument, qry_order::QueryOrder,
    qry_positiontrans::QueryPositionTrans, qry_positiontransdtl::QueryPositionTransDtl, qry_trade::QueryTrade,
    qry_withdrawdeposit::QueryWithdrawDeposit, setting::Setting,
};

/// 显示报单消息对话框<br>state. 100:报单确认; 其他:错误
pub(super) fn show_input_msg_box(app: &crate::App, state: i32, title: SharedString, msg: SharedString) {
    app.global::<crate::StkTrd_InputTip>()
        .set_app_ret(crate::AppResult { state, title, msg });
}

/// 显示撤单消息对话框<br>state. 100:撤单笔确认; 101:撤全部确认; 102:撤买确认; 103:撤卖确认; 其他:错误
pub(super) fn show_cancel_msg_box(app: &crate::App, state: i32, title: SharedString, msg: SharedString) {
    app.global::<crate::StkTrd_CancelTip>()
        .set_app_ret(crate::AppResult { state, title, msg });
}

pub struct TradeStkApp {
    is_credit: bool,
    thread: Option<thread::JoinHandle<()>>,
    db_thread: Option<thread::JoinHandle<()>>,
    run_falg: Arc<RwLock<bool>>,

    abnormalstate: Arc<AbnormalState>,
    statusstrip: Arc<StatusStrip>,
    setting: Arc<Setting>,

    marketdata: Arc<Marketdata>,
    account: Arc<Account>,
    position: Arc<Position>,
    in_order: Arc<InOrder>,
    in_fjy_order: Arc<InFjyOrder>,
    qry_order: Arc<QueryOrder>,
    qry_trade: Arc<QueryTrade>,
    qry_fjy_order: Arc<QueryFjyOrder>,
    qry_fjy_trade: Arc<QueryFjyTrade>,
    qry_wd: Arc<QueryWithdrawDeposit>,
    qry_positiontrans: Arc<QueryPositionTrans>,
    qry_instrument: Arc<QueryInstrument>,
    qry_fundtransdtl: Arc<QueryFundTransDtl>,
    qry_positiontransdtl: Arc<QueryPositionTransDtl>,
    mon_orderinsertcancel: Arc<MonOrderInsertCancel>,
    mon_convertbonds: Arc<MonConvertBonds>,
    mon_selftrade: Arc<MonSelfTrade>,
    mon_selftradedtl: Arc<MonSelfTradeDtl>,
    input_fjy_ord: Arc<InputFjyOrder>,
    input_ord: Arc<InputOrder>,

    input_ord_credit: Arc<InputOrderCredit>,
    credit_limitamt: Arc<CreditLimitAmt>,
    credit_limitpos: Arc<CreditLimitPos>,
    credit_contract: Arc<CreditContract>,
    credit_concent: Arc<CreditConcent>,
    qry_credit_reamtdtl: Arc<QueryCreditReAmtDtl>,
}

impl TradeStkApp {
    pub fn new() -> Self {
        Self {
            is_credit: false,
            thread: None,
            db_thread: None,
            run_falg: Arc::new(RwLock::new(true)),

            abnormalstate: Arc::new(AbnormalState::new()),
            statusstrip: Arc::new(StatusStrip::new()),
            setting: Arc::new(Setting::new()),

            marketdata: Arc::new(Marketdata::new()),
            account: Arc::new(Account::new()),
            position: Arc::new(Position::new()),
            in_order: Arc::new(InOrder::new()),
            in_fjy_order: Arc::new(InFjyOrder::new()),
            qry_order: Arc::new(QueryOrder::new()),
            qry_trade: Arc::new(QueryTrade::new()),
            qry_fjy_order: Arc::new(QueryFjyOrder::new()),
            qry_fjy_trade: Arc::new(QueryFjyTrade::new()),
            qry_wd: Arc::new(QueryWithdrawDeposit::new()),
            qry_positiontrans: Arc::new(QueryPositionTrans::new()),
            qry_instrument: Arc::new(QueryInstrument::new()),
            qry_fundtransdtl: Arc::new(QueryFundTransDtl::new()),
            qry_positiontransdtl: Arc::new(QueryPositionTransDtl::new()),
            mon_orderinsertcancel: Arc::new(MonOrderInsertCancel::new()),
            mon_convertbonds: Arc::new(MonConvertBonds::new()),
            mon_selftrade: Arc::new(MonSelfTrade::new()),
            mon_selftradedtl: Arc::new(MonSelfTradeDtl::new()),
            input_fjy_ord: Arc::new(InputFjyOrder::new()),
            input_ord: Arc::new(InputOrder::new()),

            input_ord_credit: Arc::new(InputOrderCredit::new()),
            credit_limitamt: Arc::new(CreditLimitAmt::new()),
            credit_limitpos: Arc::new(CreditLimitPos::new()),
            credit_contract: Arc::new(CreditContract::new()),
            credit_concent: Arc::new(CreditConcent::new()),
            qry_credit_reamtdtl: Arc::new(QueryCreditReAmtDtl::new()),
        }
    }

    /// UI 更新任务
    pub fn start_ui_update_task(&self, app_weak: Weak<App>, run_flag: Arc<RwLock<bool>>) {
        let abnormalstate_clone = self.abnormalstate.clone();
        let status_strip_clone = self.statusstrip.clone();
        let md_clone = self.marketdata.clone();
        let md_1_clone = self.marketdata.clone();
        let account_clone = self.account.clone();
        let position_clone = self.position.clone();
        let in_order_clone = self.in_order.clone();
        let mon_orderinsertcancel_clone = self.mon_orderinsertcancel.clone();
        let mon_convertbonds_clone = self.mon_convertbonds.clone();
        let mon_selftrade_clone = self.mon_selftrade.clone();

        let in_fjy_order_clone = if !self.is_credit {
            Some(self.in_fjy_order.clone())
        } else {
            None
        };
        let input_fjy_ord_clone = if !self.is_credit {
            Some(self.input_fjy_ord.clone())
        } else {
            None
        };
        let input_ord_clone = if !self.is_credit { Some(self.input_ord.clone()) } else { None };
        let input_ord_credit_clone = if self.is_credit {
            Some(self.input_ord_credit.clone())
        } else {
            None
        };
        let credit_limitamt_clone = if self.is_credit {
            Some(self.credit_limitamt.clone())
        } else {
            None
        };
        let credit_limitpos_clone = if self.is_credit {
            Some(self.credit_limitpos.clone())
        } else {
            None
        };
        let credit_contract_clone = if self.is_credit {
            Some(self.credit_contract.clone())
        } else {
            None
        };
        let credit_concent_clone = if self.is_credit {
            Some(self.credit_concent.clone())
        } else {
            None
        };

        // 启动 tokio 异步任务
        TOKIO_RT.get().unwrap().spawn(async move {
            let mut input_timer = interval(Duration::from_millis(1000));
            let mut other_timer = interval(Duration::from_millis(500));

            loop {
                // 退出条件
                if !*run_flag.read().unwrap() {
                    break;
                }

                abnormalstate_clone.update(app_weak.clone()).await;
                if !abnormalstate_clone.is_connected() {
                    break;
                }

                tokio::select! {
                    _ = input_timer.tick() => {
                        md_1_clone.update_input(app_weak.clone()).await;
                        if let Some(x) = &input_fjy_ord_clone {
                            x.update_input(app_weak.clone()).await;
                        }
                        if let Some(x) = &input_ord_clone {
                            x.update_input(app_weak.clone()).await;
                        }
                        if let Some(x) = &input_ord_credit_clone {
                            x.update_input(app_weak.clone()).await;
                        }
                    }
                    _ = other_timer.tick() => {
                        status_strip_clone.update(app_weak.clone()).await;
                        md_clone.update(app_weak.clone()).await;
                        account_clone.update(app_weak.clone()).await;
                        position_clone.update(app_weak.clone()).await;
                        in_order_clone.update(app_weak.clone()).await;
                        mon_orderinsertcancel_clone.update(app_weak.clone()).await;
                        mon_convertbonds_clone.update(app_weak.clone()).await;
                        mon_selftrade_clone.update(app_weak.clone()).await;
                        if let Some(x) = &in_fjy_order_clone {
                            x.update(app_weak.clone()).await;
                        }
                        if let Some(x) = &input_fjy_ord_clone {
                            x.update(app_weak.clone()).await;
                        }
                        if let Some(x) = &input_ord_clone {
                            x.update(app_weak.clone()).await;
                        }
                        if let Some(x) = &input_ord_credit_clone {
                            x.update(app_weak.clone()).await;
                        }
                        if let Some(x) = &credit_limitamt_clone {
                            x.update(app_weak.clone()).await;
                        }
                        if let Some(x) = &credit_limitpos_clone {
                            x.update(app_weak.clone()).await;
                        }
                        if let Some(x) = &credit_contract_clone {
                            x.update(app_weak.clone()).await;
                        }
                        if let Some(x) = &credit_concent_clone {
                            x.update(app_weak.clone()).await;
                        }
                    }
                }
            }
            log::info!(
                "run_flag:{}, is_connected:{}. ui update task end....",
                *run_flag.read().unwrap(),
                abnormalstate_clone.is_connected()
            );
        });
    }
}

impl IApp for TradeStkApp {
    fn setup(&mut self, app: &crate::slintui::App) {
        // 初始化
        {
            app.global::<AppCom>()
                .set_svr_build_date(crate::common::global::CFG.get().unwrap().read().unwrap().com.SvrBuildDate);
            self.is_credit = app.global::<StkTrd_Com>().get_iscredit();
            log::info!("is_credit: {}", self.is_credit);

            self.abnormalstate.init(&app);
            self.statusstrip.init(&app);

            log::info!("App init started");
            global::set_app_init_msg("开始初始化组件...");

            self.setting.init(&app);
            self.marketdata.init(&app);
            self.account.init(&app);
            self.position.init(&app);
            self.in_order.init(&app);
            self.qry_order.init(&app);
            self.qry_trade.init(&app);
            self.qry_wd.init(&app);
            self.qry_positiontrans.init(&app);
            self.qry_instrument.init(&app);
            self.qry_fundtransdtl.init(&app);
            self.qry_positiontransdtl.init(&app);
            self.mon_orderinsertcancel.init(&app);
            self.mon_convertbonds.init(&app);
            self.mon_selftrade.init(&app);
            self.mon_selftradedtl.init(&app);

            if self.is_credit {
                self.input_ord_credit.init(&app);
                self.credit_limitamt.init(&app);
                self.credit_limitpos.init(&app);
                self.credit_contract.init(&app);
                self.credit_concent.init(&app);
                self.qry_credit_reamtdtl.init(&app);
            } else {
                self.in_fjy_order.init(&app);
                self.qry_fjy_order.init(&app);
                self.qry_fjy_trade.init(&app);
                self.input_ord.init(&app);
                self.input_fjy_ord.init(&app);
            }

            log::info!("App init completed");
            global::set_app_init_msg("组件初始化完成, 开始注册回调消息...");
        }

        // 异步初始化
        {
            let app_weak = app.as_weak();
            let is_credit = self.is_credit;
            let abnormalstate_clone = self.abnormalstate.clone();

            let md_clone = self.marketdata.clone();
            let input_fjy_ord_clone = if !is_credit { Some(self.input_fjy_ord.clone()) } else { None };
            let input_ord_clone = if !is_credit { Some(self.input_ord.clone()) } else { None };
            let input_ord_credit_clone = if is_credit {
                Some(self.input_ord_credit.clone())
            } else {
                None
            };

            global::TOKIO_RT.get().unwrap().spawn(async move {
                let start_time = std::time::Instant::now();
                let timeout = std::time::Duration::from_secs(60);
                let mut check_interval = tokio::time::Duration::from_millis(100);

                loop {
                    if !abnormalstate_clone.is_connected() {
                        return;
                    }

                    let is_completed = if is_credit {
                        let ins = crate::common::global::INS.get().unwrap().read().unwrap();
                        ins.is_last
                    } else {
                        let ins = crate::common::global::INS.get().unwrap().read().unwrap();
                        ins.is_last && ins.is_fjy_last
                    };

                    if is_completed {
                        break;
                    }

                    if start_time.elapsed() >= timeout {
                        log::warn!(
                            "登录成功后超过60秒都未获取到最后一条证券信息. 请检查网络, 可能是网络阻塞缓慢的原因.
                            如果之后一直接收不到最后一条证券信息, 可能的影响:使用证券代码时可能找不到该证券的信息"
                        );
                        break;
                    }

                    // 使用指数退避策略，逐渐增加检查间隔
                    tokio::time::sleep(check_interval).await;
                    if check_interval.as_millis() < 500 {
                        check_interval = tokio::time::Duration::from_millis((check_interval.as_millis() as u64 * 3 / 2).min(500));
                    }
                }

                let _ = app_weak.upgrade_in_event_loop(move |app| {
                    md_clone.init_async(&app);

                    if let Some(x) = input_fjy_ord_clone {
                        x.init_async(&app);
                    }
                    if let Some(x) = input_ord_clone {
                        x.init_async(&app);
                    }
                    if let Some(x) = input_ord_credit_clone {
                        x.init_async(&app);
                    }
                });
            });
        }

        // 注册回调
        {
            {
                log::info!("App set md callback started");

                let mut md_buf = MD_BUF.get().unwrap().write().unwrap();

                let md_clone = self.marketdata.clone();
                md_buf.register_on_subscribe_failed_callback(move |em| md_clone.on_sub_failed(em));

                let md_clone = self.marketdata.clone();
                md_buf.register_on_un_subscribe_failed_callback(move |em| md_clone.on_un_sub_failed(em));

                log::info!("App set md callback completed");
                global::set_app_init_msg("注册行情消息回调完成...");
            }

            {
                log::info!("App set callback started");

                let mut trd_stk_buf = TRADE_STK_BUF.get().unwrap().write().unwrap();

                let account_clone = self.account.clone();
                trd_stk_buf.register_qry_acc_callback(move |acc: tiapi::protocol_pub::account_trade_ctp::CtpAccountField| {
                    account_clone.on_rsp_qry_acc(acc)
                });

                let position_clone = self.position.clone();
                trd_stk_buf.register_qry_pos_callback(move |pos| position_clone.on_rsp_qry_acc(pos));

                let in_order_clone = self.in_order.clone();
                trd_stk_buf.register_rsp_inputorderaction_callback(move |rsp| in_order_clone.on_rsp_inputorderaction(rsp));

                let in_fjy_order_clone = self.in_fjy_order.clone();
                trd_stk_buf
                    .register_rsp_inputfjyorderaction_callback(move |rsp| in_fjy_order_clone.on_rsp_inputfjyorderaction(rsp));

                if self.is_credit {
                    let input_ord_credit_clone = self.input_ord_credit.clone();
                    trd_stk_buf.register_rsp_inputorder_callback(move |rsp| input_ord_credit_clone.on_rsp_inputorder(rsp));
                } else {
                    let input_ord_clone = self.input_ord.clone();
                    trd_stk_buf.register_rsp_inputorder_callback(move |rsp| input_ord_clone.on_rsp_inputorder(rsp));

                    let input_fjy_ord_clone = self.input_fjy_ord.clone();
                    trd_stk_buf.register_rsp_inpufjytorder_callback(move |rsp| input_fjy_ord_clone.on_rsp_businessorder(rsp));
                }

                log::info!("App set callback completed");
                global::set_app_init_msg("注册交易回调完成...");
            }
        }

        // 逻辑更新线程
        {
            log::info!("App start qyery thread started");
            global::set_app_init_msg("正在初始化逻辑更新线程...");

            let abnormalstate_clone = self.abnormalstate.clone();

            let account_clone = self.account.clone();
            let position_clone = self.position.clone();
            let in_order_clone = self.in_order.clone();
            let in_fjy_order_clone = self.in_fjy_order.clone();
            let mon_orderinsertcancel_clone = self.mon_orderinsertcancel.clone();
            let mon_convertbonds_clone = self.mon_convertbonds.clone();
            let mon_selftrade_clone = self.mon_selftrade.clone();

            let credit_limitamt_clone = if self.is_credit {
                Some(self.credit_limitamt.clone())
            } else {
                None
            };

            let credit_limitpos_clone = if self.is_credit {
                Some(self.credit_limitpos.clone())
            } else {
                None
            };

            let credit_contract_clone = if self.is_credit {
                Some(self.credit_contract.clone())
            } else {
                None
            };

            let credit_concent_clone = if self.is_credit {
                Some(self.credit_concent.clone())
            } else {
                None
            };

            let run_falg: Arc<RwLock<bool>> = self.run_falg.clone();
            self.thread = Some(thread::spawn(move || {
                loop {
                    if !*run_falg.read().unwrap() {
                        break;
                    }
                    if !abnormalstate_clone.is_connected() {
                        break;
                    }

                    account_clone.qry_account();
                    position_clone.qry_position();

                    in_order_clone.qry_in_order();
                    in_fjy_order_clone.qry_in_fjy_order();

                    mon_orderinsertcancel_clone.qry_orderinsertcancel();
                    mon_convertbonds_clone.qry_convertbonds();
                    mon_selftrade_clone.qry_selftrade();

                    if credit_limitamt_clone.is_some() {
                        credit_limitamt_clone.as_ref().unwrap().qry_credit_limit();
                    }

                    if credit_limitpos_clone.is_some() {
                        credit_limitpos_clone.as_ref().unwrap().qry_credit_limit();
                    }

                    if credit_contract_clone.is_some() {
                        credit_contract_clone.as_ref().unwrap().qry_credit_contract();
                    }

                    if credit_concent_clone.is_some() {
                        credit_concent_clone.as_ref().unwrap().qry_credit_concentration();
                    }

                    thread::sleep(std::time::Duration::from_secs(1));
                }

                log::info!(
                    "run_falg:{}, is_connected:{}. qry thread is end....",
                    *run_falg.read().unwrap(),
                    abnormalstate_clone.is_connected()
                );
            }));

            log::info!("App start qyery thread completed");
            global::set_app_init_msg("初始化逻辑更新线程完成...");
        }

        // UI更新线程
        {
            log::info!("App start ui thread started");
            global::set_app_init_msg("正在初始化UI更新线程...");

            self.start_ui_update_task(app.as_weak(), self.run_falg.clone());

            log::info!("App start ui update thread completed");
            global::set_app_init_msg("初始化UI更新线程完成...");
        }

        // DB线程
        {
            log::info!("App start db thread started");
            global::set_app_init_msg("正在初始化数据存储线程...");

            let run_falg: Arc<RwLock<bool>> = self.run_falg.clone();
            let abnormalstate_clone = self.abnormalstate.clone();

            self.db_thread = Some(thread::spawn(move || {
                loop {
                    if !*run_falg.read().unwrap() {
                        break;
                    }
                    if !abnormalstate_clone.is_connected() {
                        break;
                    }

                    let order_map = {
                        let trd_stk_buf = TRADE_STK_BUF.get().unwrap().read().unwrap();
                        trd_stk_buf.pop_order()
                    };
                    if !order_map.is_empty() {
                        let mut db = DB.get().unwrap().lock().unwrap();
                        db.write_trd_order(order_map);
                    }

                    let trade_vec = {
                        let trd_stk_buf = TRADE_STK_BUF.get().unwrap().read().unwrap();
                        trd_stk_buf.pop_trade()
                    };
                    if !trade_vec.is_empty() {
                        let mut db = DB.get().unwrap().lock().unwrap();
                        db.write_trd_trade(trade_vec);
                    }

                    thread::sleep(std::time::Duration::from_millis(500));
                }

                log::info!(
                    "run_falg:{}, is_connected:{}. db thread is end....",
                    *run_falg.read().unwrap(),
                    abnormalstate_clone.is_connected()
                );
            }));

            log::info!("App start db thread completed");
            global::set_app_init_msg("初始化数据存储线程完成...");
        }

        global::set_app_init_msg("主界面初始化完成...");
        global::set_app_init_complete();
    }

    fn teardown(&mut self) {
        log::info!("App stop started");

        *self.run_falg.write().unwrap() = false;

        if let Some(t) = self.thread.take() {
            let _ = t.join();
        }
        log::info!("App stop query thread completed");

        if let Some(t) = self.db_thread.take() {
            let _ = t.join();
        }
        log::info!("App stop db thread completed");
    }
}
