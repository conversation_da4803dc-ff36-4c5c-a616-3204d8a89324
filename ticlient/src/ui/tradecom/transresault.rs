/// 保存划转结果
#[derive(Debug)]
pub(in crate::ui) struct TransResault {
    /// 划入成功数
    pub in_success_cnt: i32,

    /// 划出成功数
    pub out_success_cnt: i32,

    /// 划入数
    pub in_cnt: i32,

    /// 划出数
    pub out_cnt: i32,
}
impl TransResault {
    /// 构建
    pub fn new() -> Self {
        Self {
            in_success_cnt: 0,
            out_success_cnt: 0,
            in_cnt: 0,
            out_cnt: 0,
        }
    }

    /// 划出是否可以继续划转
    pub fn in_can_trans(&self) -> bool {
        // 有划出成功, 划入数不等下划入成功数
        self.out_success_cnt > 0 && self.in_success_cnt != self.in_cnt
    }

    /// 划入是否可以继续划转
    pub fn out_can_trans(&self) -> bool {
        // 有划出成功, 划出数不等下划出成功数
        self.out_success_cnt > 0 && self.out_success_cnt != self.out_cnt
    }
}
pub type TransResaultMap = dashmap::DashMap<String, TransResault>;
