import { Fonts, TableView } from "@zstk/lib.slint";
import { ColWidth, AppCom } from "../struct.slint";

export global StkTrd_MonOrderInsertCancel {
    in property <[[string]]> row_data;

    pure callback get_row_data_color(int, int, string) -> brush;
}

export component MonOrderInsertCancelPage {

    preferred_width: 100%;
    preferred_height: 100%;

    TableView {
        height: 100%;
        width: 100%;

        column_header: {
            height: AppCom.table_header_height,
            background: Colors.transparent,
            font: Fonts.normal,
            columns: [
                { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                { title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                { title: @tr("是否异常"), alignment: TextHorizontalAlignment.center, width: ColWidth.monstatus },
                { title: @tr("总笔数"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                { title: @tr("报单笔数"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                { title: @tr("撤单笔数"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
            ]
        };
        row_count: StkTrd_MonOrderInsertCancel.row_data.length;

        get_cell_data(row_index, column_index) => {
            StkTrd_MonOrderInsertCancel.row_data[row_index][column_index]
        }

        get_cell_data_color(row_index, column_index, data) => {
            StkTrd_MonOrderInsertCancel.get_row_data_color(row_index, column_index, data)
        }

        cell_key_copy_pressed(row_index, column_index) => {
            if column_index >= 0 {
                AppCom.copy_str_to_clipboard(StkTrd_MonOrderInsertCancel.row_data[row_index][column_index]);
            } else {
                AppCom.copy_arr_to_clipboard(StkTrd_MonOrderInsertCancel.row_data[row_index]);
            }
        }
    }
}
