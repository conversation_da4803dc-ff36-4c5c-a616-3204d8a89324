import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";

export component RightBottomPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "可转债" },
        { text: "报撤单" },
        { text: "自成交" },
        { text: "自成交详情" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.right_bottom_selidx;
        }

        if 0 == StkRsk_TabSelIdx.right_bottom_selidx:  Rectangle { Text {text: "可转债";} }
        if 1 == StkRsk_TabSelIdx.right_bottom_selidx:  Rectangle { Text {text: "报撤单";} }
        if 2 == StkRsk_TabSelIdx.right_bottom_selidx:  Rectangle { Text {text: "自成交";} }
        if 3 == StkRsk_TabSelIdx.right_bottom_selidx:  Rectangle { Text {text: "自成交详情";} }
    }
}
