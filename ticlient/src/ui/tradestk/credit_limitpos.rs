use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{config::Config, global::TRADE_STK_BUF, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::position_trade_ctp::CtpCreditLimitField;

type CtpCreditLimitMap = dashmap::DashMap<String, CtpCreditLimitField>;

/// 融券授信额度数据
struct CreditLimitPosDatas {
    /// 数据
    data_map: Arc<CtpCreditLimitMap>,

    /// 汇总数据
    data_sumary_map: Arc<CtpCreditLimitMap>,

    /// 是否只显示汇总数据
    only_show_summary: AtomicBool,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl CreditLimitPosDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpCreditLimitMap::new()),
            data_sumary_map: Arc::new(CtpCreditLimitMap::new()),
            only_show_summary: AtomicBool::new(false),
            data_changed: AtomicBool::new(false),
            filter_clicked: AtomicBool::new(false),
        }
    }
}
static CREDIT_LIMIT_POS_DATAS: OnceLock<CreditLimitPosDatas> = OnceLock::new();

/// 融券授信额度
pub(super) struct CreditLimitPos {}

/// 构建与初始化
impl CreditLimitPos {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = CREDIT_LIMIT_POS_DATAS.set(CreditLimitPosDatas::new());

        let app_cl = app.global::<StkTrd_CreditLimitPos>();

        CREDIT_LIMIT_POS_DATAS
            .get()
            .unwrap()
            .only_show_summary
            .store(app_cl.get_only_show_summary(), Ordering::Relaxed);

        let app_weak = app.as_weak();
        app_cl.on_get_row_data_color(move |row_index, column_index, data| {
            CreditLimitPos::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_cl.on_filter_changed(move || CreditLimitPos::on_filter_changed(&app_weak));

        let app_weak = app.as_weak();
        app_cl.on_export_clicked(move |etype| CreditLimitPos::on_export_clicked(&app_weak, etype));

        log::trace!("Credit limit positon init completed");
    }
}

/// 查询
impl CreditLimitPos {
    /// 查询融券授信额度
    pub fn qry_credit_limit(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_credit_limit_pos() };
        if ret.is_empty() {
            return;
        }

        let cl_datas = CREDIT_LIMIT_POS_DATAS.get().unwrap();

        for (key, cl) in ret {
            cl_datas.data_map.insert(key, cl);
        }

        if cl_datas.only_show_summary.load(Ordering::Relaxed) {
            cl_datas.data_sumary_map.clear();
            cl_datas.data_map.iter().for_each(|la| {
                let key = la.AccID.clone();
                if let Some(mut exist) = cl_datas.data_sumary_map.get_mut(&key) {
                    exist.Distrib += la.Distrib;
                    exist.Used += la.Used;
                    exist.Frozen += la.Frozen;
                    exist.In += la.In;
                    exist.Out += la.Out;
                } else {
                    let mut new_la = la.clone();
                    new_la.ExchID = 0;
                    cl_datas.data_sumary_map.insert(key, new_la);
                }
            });
        }

        cl_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditLimitPos {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 可用额度
        if 3 == column_index {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data < 0.0 {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        CREDIT_LIMIT_POS_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("credit_limit_pos_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!(
                    "导出融券授信额度失败\n\n打开文件[{:?}失败]\n{:?}",
                    path,
                    writer.err().unwrap()
                ),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "授权额度",
            "可用额度",
            "使用额度",
            "冻结额度",
            "转入额度",
            "转出额度",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出融券授信额度失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<StkTrd_CreditLimitPos>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let cl_datas = CREDIT_LIMIT_POS_DATAS.get().unwrap();
            let data_map_clone = if cl_datas.only_show_summary.load(Ordering::Relaxed) {
                cl_datas.data_sumary_map.clone()
            } else {
                cl_datas.data_map.clone()
            };
            data_map_clone.iter().for_each(|cl| {
                let items = CreditLimitPos::get_row_items(&cl);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出融券授信额度失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出融券授信额度成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}

/// 更新UI
impl CreditLimitPos {
    /// 获取一行数据
    fn get_row_items(cl: &CtpCreditLimitField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 资金账户
        items.push(slint::format!("{}", cl.AccID));

        // 交易所
        items.push(TIConvert::exch_name(cl.ExchID).into());

        // 授权额度
        items.push(TIConvert::format_f(cl.Distrib, Config::AMT_DIGITS).into());

        // 可用额度
        items.push(TIConvert::format_f(cl.Distrib - cl.Used - cl.Frozen + cl.In - cl.Out, Config::AMT_DIGITS).into());

        // 使用额度
        items.push(TIConvert::format_f(cl.Used, Config::AMT_DIGITS).into());

        // 冻结额度
        items.push(TIConvert::format_f(cl.Frozen, Config::AMT_DIGITS).into());

        // 转入额度
        items.push(TIConvert::format_f(cl.In, Config::AMT_DIGITS).into());

        // 转出额度
        items.push(TIConvert::format_f(cl.Out, Config::AMT_DIGITS).into());

        items
    }

    // 更新融券授信额度
    async fn update_credit_contract(&self, app_weak: &Weak<App>) {
        let cl_datas = CREDIT_LIMIT_POS_DATAS.get().unwrap();

        let only_show_summary = cl_datas.only_show_summary.load(Ordering::Relaxed);

        let data_map_clone = {
            if cl_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                cl_datas.filter_clicked.store(false, Ordering::Relaxed);
                if only_show_summary {
                    cl_datas.data_sumary_map.clone()
                } else {
                    cl_datas.data_map.clone()
                }
            } else {
                if cl_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    if only_show_summary {
                        cl_datas.data_sumary_map.clone()
                    } else {
                        cl_datas.data_map.clone()
                    }
                } else {
                    Arc::new(CtpCreditLimitMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_cl = app.global::<StkTrd_CreditLimitPos>();

            let filter_exchid = TIConvert::exch_id(app_cl.get_exchid().as_str());

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|cl| {
                if 0 != filter_exchid {
                    if cl.ExchID != filter_exchid {
                        return;
                    }
                }

                let items = CreditLimitPos::get_row_items(&cl);
                row_data.push(items.into());
            });

            app_cl.set_row_data(row_data.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新融券授信额度
        self.update_credit_contract(&app_weak).await;
    }
}
