/// CTP类型
pub struct CTPType {}

// 买卖方向类型
impl CTPType {
    /// 买 '0'
    pub const SIDE_BUY: i32 = 48;

    /// 卖 '1'
    pub const SIDE_SELL: i32 = 49;

    /// 融资买入 3
    pub const SIDE_BUY_CREDIT: i32 = 3;

    /// 融券卖出 4
    pub const SIDE_SELL_CREDIT: i32 = 4;

    /// 买券还券 5
    pub const SIDE_BUY_RETURN: i32 = 5;

    /// 卖券还款 6
    pub const SIDE_SELL_RETURN: i32 = 6;

    /// 现券还券
    pub const SIDE_RETURN_STOCK: i32 = 7;

    /// 直接还款
    pub const SIDE_RETURN_FUND: i32 = 8;
}

// 单腿合约方向
impl CTPType {
    /// 权力仓 '0'
    pub const LEGSIDE_LONG: i32 = 48;

    /// 义务仓 '1'
    pub const LEGSIDE_SHORT: i32 = 49;
}

// 持仓方向类型
impl CTPType {
    /// 多头(买) '2'
    pub const POSDIR_LONG: i32 = 50;

    /// 空头(卖) '3'
    pub const POSDIR_SHORT: i32 = 51;
}

// 开平标志
impl CTPType {
    /// 开仓 '0'
    pub const OC_OPEN: i32 = 48;

    /// 平仓 '1'
    pub const OC_CLOSE: i32 = 49;

    /// 强平 '2'
    pub const OC_CLOSE_FORCE: i32 = 50;

    /// 平今 '3'
    pub const OC_CLOSE_TODAY: i32 = 51;

    /// 平昨 '4'
    pub const OC_CLOSE_YESTODAY: i32 = 52;

    /// 强减 '5'
    pub const OC_SUB_FORCE: i32 = 53;

    /// 本地强平 '6'
    pub const OC_CLOSE_FORCE_LOCAL: i32 = 54;
}

// 组合与拆分组合
impl CTPType {
    /// 组合 '0'
    pub const COMBED: i32 = 48;

    /// 拆分 '1'
    pub const COMBED_UN: i32 = 49;
}

// 投机套保标志
impl CTPType {
    /// 投机 '1'
    pub const HEDGE_SPECULATION: i32 = 49;

    /// 套利 '2'
    pub const HEDGE_ARBITRAGE: i32 = 50;

    /// 套保 '3'
    pub const HEDGE_HEDGE: i32 = 51;

    /// 备兑 '4'
    pub const HEDGE_COVERED: i32 = 52;

    /// 做市商 '5'
    pub const HEDGE_MARKETMAKER: i32 = 53;
}

// 报单价格条件
impl CTPType {
    /// 市价 '1'
    pub const PRICETYPE_MARKET: i32 = 49;

    /// 限价 '2'
    pub const PRICETYPE_LIMIT: i32 = 50;

    /// 最优价 '3'
    pub const PRICETYPE_BEST_3: i32 = 51;

    /// 最新价 '4'
    pub const PRICETYPE_LAST: i32 = 52;

    /// 五档价 'G'
    pub const PRICETYPE_FIVE: i32 = 71;

    /// 本方最优价格 'H'
    pub const PRICETYPE_BEST_H: i32 = 72;
}

// 订单有效时间类型
impl CTPType {
    /// 即时成交剩余撤销 '1'
    pub const TIMEINFORCE_IOC: i32 = 49;

    /// 本节有效 '2'
    pub const TIMEINFORCE_GIS: i32 = 50;

    /// 当日有效 '3'
    pub const TIMEINFORCE_GFD: i32 = 51;

    /// 指定日期前有效 '4'
    pub const TIMEINFORCE_GTD: i32 = 52;

    /// 撤销前有效 '5'
    pub const TIMEINFORCE_GTC: i32 = 53;

    /// 集合竞价有效 '6'
    pub const TIMEINFORCE_GFA: i32 = 54;
}

// 触发条件类型
impl CTPType {
    /// 立即 '1'
    pub const CC_IMMEDIATELY: i32 = 49;

    /// 止损 '2'
    pub const CC_TOUCH: i32 = 50;
}

// 成交量类型
impl CTPType {
    /// 任何数量 '1'
    pub const VC_AV: i32 = 49;

    /// 最小数量 '2'
    pub const VC_MV: i32 = 50;

    /// 全部数量 '3'
    pub const VC_CV: i32 = 51;
}

// 持仓日期类型
impl CTPType {
    /// 今日持仓 '1'
    pub const PSD_TODAY: i32 = 49;

    /// 历史持仓 '2'
    pub const PSD_HIS: i32 = 50;
}

// 订单状态类型
impl CTPType {
    /// 已报入 '3'
    pub const ORDER_STATUS_SUCCESS: i32 = 51;

    /// 部分成交 '1'
    pub const ORDER_STATUS_TRADE: i32 = 49;

    /// 全部成交 '0'
    pub const ORDER_STATUS_ALL: i32 = 48;

    /// 已撤单 '5'
    pub const ORDER_STATUS_CANCEL: i32 = 53;

    /// 已拒绝 'b'
    pub const ORDER_STATUS_REJECT: i32 = 96;

    /// 部成部撤 '2'
    pub const ORDER_STATUS_TRADECANCEL: i32 = 50;
}

impl CTPType {
    /// 未执行 'n'
    pub const EXEC_RESULT_NO: i32 = 110;

    /// 已取消 'c'
    pub const EXEC_RESULT_CANCEL: i32 = 99;

    /// 已拒绝 'a'
    pub const EXEC_RESULT_REJECT: i32 = 97;
}
