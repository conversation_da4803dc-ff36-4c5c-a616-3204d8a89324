[package]
name = "mdapi"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
comfun = { path = "../comfun" }

log = "=0.4.27"
url = "=2.5.4"
num_enum = "=0.7.3"
futures-util = "=0.3.31"
futures-channel = "=0.3.31"
native-tls = "=0.2.14"
tokio-tungstenite = {version = "=0.26.2", features = ["native-tls", "url"]}
tokio = { version = "=1.44.2", features = ["rt-multi-thread"] }
serde = { version = "=1.0.219", features = ["derive"] }
serde_json = "=1.0.140"
crossbeam-channel = "=0.5.15"
async-trait = "=0.1.88"