import { Theme, TabBarItem, TabView } from "@zstk/lib.slint";

import { InOrderPage } from "in_order.slint";
import { InExercisePage } from "in_exercise.slint";
import { InExercisePageComb } from "in_exercise_comb.slint";
import { InQuotePage } from "in_quote.slint";

import { OptTrd_Com, OptTrd_TabSelIdx } from "struct.slint";

export component InPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "委托" },
        { text: "行权" },
        { text: "组合行权" },
    ];

    private property <[TabBarItem]> tabview_market_items: [
        { text: "委托" },
        { text: "行权" },
        { text: "组合行权" },
        { text: "报价" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: OptTrd_Com.ismarket ? tabview_market_items : tabview_items;
            current_index <=> OptTrd_TabSelIdx.in_subselidx;
        }

        if 0 == OptTrd_TabSelIdx.in_subselidx: InOrderPage { }

        if 1 == OptTrd_TabSelIdx.in_subselidx: InExercisePage { }

        if 2 == OptTrd_TabSelIdx.in_subselidx: InExercisePageComb { }

        if 3 == OptTrd_TabSelIdx.in_subselidx: InQuotePage { }
    }
}
