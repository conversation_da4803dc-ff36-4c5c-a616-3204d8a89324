/// 持仓量信息
#[derive(Default)]
pub struct PositionInfoFutField {
    /// 昨持仓量中未用于组合的持仓量
    pub PrePosition: i32,

    /// 昨持仓量中用于组合的持仓量
    pub PreCombPosition: i32,

    /// 实际持仓量 = 昨持仓量(PrePosition+PreCombPosition) + 开仓量 - 平仓量 - 用于组合的持仓量
    pub Position: i32,

    /// 开仓量
    pub OpenVolume: i32,

    /// 平仓量
    pub CloseVolume: i32,

    /// 用于组合的持仓量
    pub CombPosition: i32,

    /// 冻结持仓量(在途平仓报单冻结)
    pub FrozenPosition: i32,

    /// 冻结保证金(计算值)
    pub CalcFrozenMargin: f64,

    /// 冻结保证金(实际值)
    pub FrozenMargin: f64,

    /// 占用保证金(计算值)
    pub CalcUseMargin: f64,

    /// 占用保证金(实际值)
    pub UseMargin: f64,

    /// 开仓金额
    pub OpenAmount: f64,

    /// 平仓金额
    pub CloseAmount: f64,

    /// 持仓均价
    pub AvgPrice: f64,

    /// 持仓市值
    pub PosValue: f64,

    /// 持仓市值计算价格
    pub PosValPrice: f64,

    /// 持仓盈亏
    pub PosProfit: f64,

    /// 持仓盈亏计算价格(最新价)
    pub PosProfitPrice: f64,

    /// 平仓盈亏
    pub CloseProfit: f64,

    /**********下面字段为非协议内容用于展示***************/
    /// Delta
    pub Delta: f64,

    /// Delta金额
    pub DeltaAmount: f64,

    /// Vega
    pub Vega: f64,

    /// Vega金额
    pub VegaAmount: f64,

    /// Gamma
    pub Gamma: f64,

    /// Gamma金额
    pub GammaAmount: f64,

    /// Theta
    pub Theta: f64,

    /// Theta金额
    pub ThetaAmount: f64,
}
impl PositionInfoFutField {
    fn has_position(&self) -> bool {
        0 != self.PreCombPosition
            || 0 != self.PrePosition
            || 0 != self.Position
            || 0 != self.OpenVolume
            || 0 != self.CloseVolume
            || 0 != self.CombPosition
            || 0 != self.FrozenPosition
    }
}

/// 持仓信息
#[derive(Default)]
pub struct PositionFutField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 投机套保标志
    pub HedgeFlag: i32,

    /// 持仓日期类型
    pub PosDate: i32,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 期权最新价
    pub OptLPrice: f64,

    /// 标的最新价(如果持仓合约不是期权,本字段表示合约的最新价)
    pub ObjLPrice: f64,

    /// 实虚值(仅期权有效)
    pub VirValue: f64,

    /// 多头持仓信息
    pub Long: PositionInfoFutField,

    /// 空头持仓信息
    pub Short: PositionInfoFutField,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
