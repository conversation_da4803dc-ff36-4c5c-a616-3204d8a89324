import { TabView, TabBarItem } from "@zstk/lib.slint";

import { InputOrderPage } from "input_order.slint";
import { InputOmlPage } from "input_oml.slint";
import { InputExercisePage } from "input_exercise.slint";
import { InputStockLockPage } from "input_stocklock.slint";
import { InputQuotePage } from "input_quote.slint";

import { OptTrd_Com, OptTrd_TabSelIdx } from "struct.slint";

export component InputPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "报单录入" },
        { text: "组合录入" },
        { text: "行权录入" },
        { text: "备兑解锁仓" },
    ];

    private property <[TabBarItem]> tabview_market_items: [
        { text: "报单录入" },
        { text: "组合录入" },
        { text: "行权录入" },
        { text: "备兑解锁仓" },
        { text: "报价录入" },
    ];

    VerticalLayout {
        padding_left: 1px;
        spacing: 2px;

        TabView {
            height: 30px;

            items: OptTrd_Com.ismarket ? tabview_market_items : tabview_items;
            current_index <=> OptTrd_TabSelIdx.input_selidx;
        }

        if 0 == OptTrd_TabSelIdx.input_selidx: InputOrderPage { }
        if 1 == OptTrd_TabSelIdx.input_selidx: InputOmlPage { }
        if 2 == OptTrd_TabSelIdx.input_selidx: InputExercisePage { }
        if 3 == OptTrd_TabSelIdx.input_selidx: InputStockLockPage { }
        if 4 == OptTrd_TabSelIdx.input_selidx: InputQuotePage { }
    }
}
