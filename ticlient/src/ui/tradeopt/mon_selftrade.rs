use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{apiproc::tradeoptbuf::CtpTradeSelfMap, common::global::CFG};

use crate::{
    common::{global::TRADE_OPT_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type IDSet = dashmap::DashSet<String>;

/// 监控 - 自成交 - 数据
struct MonSelfTradeDatas {
    /// 数据
    data_map: Arc<CtpTradeSelfMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 交易编码列表
    clientid_set: Arc<IDSet>,

    /// 品种编码列表
    productid_set: Arc<IDSet>,

    /// 是否需要过滤数据
    filter_changed: AtomicBool,
}
impl MonSelfTradeDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpTradeSelfMap::new()),
            data_changed: AtomicBool::new(false),

            clientid_set: Arc::new(IDSet::new()),
            productid_set: Arc::new(IDSet::new()),

            filter_changed: AtomicBool::new(false),
        }
    }
}
static MON_SELF_TRADE_DATAS: OnceLock<MonSelfTradeDatas> = OnceLock::new();

/// 监控 - 自成交
pub(super) struct MonSelfTrade {}

// 构建与初始化
impl MonSelfTrade {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = MON_SELF_TRADE_DATAS.set(MonSelfTradeDatas::new());

        let app_mon = app.global::<OptTrd_MonSelfTrade>();

        // 注册过滤条件改变事件
        let app_weak = app.as_weak();
        app_mon.on_filter_changed(move || MonSelfTrade::on_filter_changed(&app_weak));

        // 注册获取单元格颜色事件
        let app_weak = app.as_weak();
        app_mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonSelfTrade::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor self trade init completed");
    }
}

// 查询
impl MonSelfTrade {
    /// 查询自成交信息
    pub fn qry_selftrade(&self) {
        let ret = { TRADE_OPT_BUF.get().unwrap().read().unwrap().pop_selftrade() };
        if ret.is_empty() {
            return;
        }

        let mst_datas = MON_SELF_TRADE_DATAS.get().unwrap();

        let mut data_changed = false;
        for (key, ts) in ret {
            // 仅显示按品种统计
            if 2 == ts.TSType {
                if !mst_datas.clientid_set.contains(&ts.ClientID) {
                    mst_datas.clientid_set.insert(ts.ClientID.clone());
                }
                if !mst_datas.productid_set.contains(&ts.InstrumentID) {
                    mst_datas.productid_set.insert(ts.InstrumentID.clone());
                }

                mst_datas.data_map.insert(key, ts);
                data_changed = true;
            }
        }

        if data_changed {
            mst_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

/// 事件
impl MonSelfTrade {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 是否异常
        if 4 == column_index {
            if "是" == data.as_str() {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过虑条件有变动
    fn on_filter_changed(app_weak: &Weak<App>) {
        MON_SELF_TRADE_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

// 更新UI
impl MonSelfTrade {
    // 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let mst_datas = MON_SELF_TRADE_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mst = app.global::<OptTrd_MonSelfTrade>();

            if mst_datas.clientid_set.len() != app_mst.invoke_clientid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mst_datas.clientid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mst.set_clientid_model(id_arr.into());
            }

            if mst_datas.productid_set.len() != app_mst.invoke_productid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                mst_datas.productid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_mst.set_productid_model(id_arr.into());
            }
        });
    }

    // 更新限仓
    async fn update_limitpos(&self, app_weak: &Weak<App>) {
        let mst_datas = MON_SELF_TRADE_DATAS.get().unwrap();

        let data_map_clone = {
            if mst_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                mst_datas.filter_changed.store(false, Ordering::Relaxed);
                mst_datas.data_map.clone()
            } else {
                if mst_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    mst_datas.data_map.clone()
                } else {
                    Arc::new(CtpTradeSelfMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mst = app.global::<OptTrd_MonSelfTrade>();

            let filter_exchid = app_mst.get_exchid().to_string();
            let filter_cliid = app_mst.get_clientid().to_string();
            let filter_productid = app_mst.get_productid().to_string();

            // 最大自成交数易
            let max_selftrade_vol = { CFG.get().unwrap().read().unwrap().opt_mon.TrdSelfMonitor.TradeVol };

            // 填充数据
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|st| {


                let exchname = TIConvert::exch_name(st.ExchID);

                if !filter_exchid.is_empty() {
                    if exchname != filter_exchid {
                        return;
                    }
                }

                if !filter_cliid.is_empty() {
                    if !st.ClientID.contains(&filter_cliid) {
                        return;
                    }
                }

                if !filter_productid.is_empty() {
                    if !st.InstrumentID.contains(&filter_productid) {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(st.AccountID.clone().into());

                // 交易所
                items.push(exchname.into());

                // 交易编码
                items.push(st.ClientID.clone().into());

                // 合约编码
                items.push(st.InstrumentID.clone().into());

                // 是否异常
                items.push((if st.TradeVol >= max_selftrade_vol { "是" } else { "否" }).into());

                // 自成交数量
                items.push(slint::format!("{}", st.TradeVol));

                // 自成交笔数
                items.push(slint::format!("{}", st.TradeNum));

                // 发生时间
                items.push(TIConvert::transtime(st.TransTime).into());

                // sort_col(仅用于排序)
                let ratio = { ********* + st.TradeVol };
                items.push(ratio.to_string().into());

                row_data.push(items.into());
            });

            // 根据 sort_col 按降序排序
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(8).unwrap();
                let c_b = r_b.row_data(8).unwrap();
                c_b.cmp(&c_a)
            }));
            app_mst.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新限仓
        self.update_limitpos(&app_weak).await;
    }
}
