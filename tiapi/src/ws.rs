use crate::serverinfo::ServerInfo;

use futures_util::{future, pin_mut, StreamExt};
use tokio::net::TcpStream;
use tokio_tungstenite::{
    connect_async, connect_async_tls_with_config, tungstenite::protocol::Message, MaybeTlsStream, WebSocketStream,
};

/// WS客户端
pub(crate) struct WsClient {
    /// 服务端信息
    svrinfo: ServerInfo,

    /// 消息生产者
    msg_sender: Option<crossbeam_channel::Sender<Message>>,

    /// 发送通道
    chn_sx: Option<futures_channel::mpsc::UnboundedSender<Message>>,
}

impl WsClient {
    pub fn new() -> Self {
        Self {
            svrinfo: ServerInfo::default(),
            msg_sender: None,
            chn_sx: None,
        }
    }

    /// 释放资源
    pub fn release(&mut self) {
        self.chn_sx = None; // 触发接收线程退出
        if self.msg_sender.is_some() {
            self.msg_sender.take(); // 触发消息消费者退出
        }
    }

    /// 设置服务端信息
    pub fn set_svrinfo(&mut self, si: &ServerInfo) {
        self.svrinfo = si.clone();
    }

    /// 消息生产者
    pub fn set_msg_sender(&mut self, sender: crossbeam_channel::Sender<Message>) {
        self.msg_sender = Some(sender);
    }

    /// 请求登录
    ///
    /// 同步操作. 请求连接->创建接收线程->发送登录请求
    pub async fn req_login(&mut self, loginmsg: &str) -> crate::Result<()> {
        // 解析地址
        let hostaddr = self.svrinfo.get_ws_url();
        let url = url::Url::parse(&hostaddr);
        if url.is_err() {
            // 解析URL失败
            return Err(format!("{}", url.err().unwrap().to_string()));
        }

        // 连接服务端
        let ret_cnn: Result<
            (
                WebSocketStream<MaybeTlsStream<TcpStream>>,
                tokio_tungstenite::tungstenite::http::Response<Option<Vec<u8>>>,
            ),
            tokio_tungstenite::tungstenite::Error,
        >;
        if 1 == self.svrinfo.encon {
            let connector = native_tls::TlsConnector::builder()
                .danger_accept_invalid_certs(true)
                .build()
                .unwrap();

            ret_cnn = connect_async_tls_with_config(
                url.unwrap(),
                None,
                false,
                Some(tokio_tungstenite::Connector::NativeTls(connector)),
            )
            .await;
        } else {
            ret_cnn = connect_async(url.unwrap()).await;
        }
        if ret_cnn.is_err() {
            // 连接服务端失败
            return Err(ret_cnn.err().unwrap().to_string());
        }

        let (chn_tx, chn_rx) = futures_channel::mpsc::unbounded();

        // 注意: 如果参数2用self.msg_sender.take().unwrap()则会导致如果发送失败再次请求时会 panic
        tokio::spawn(Self::recv_run(chn_rx, ret_cnn.unwrap().0, self.msg_sender.clone().unwrap()));

        let ret = chn_tx.unbounded_send(Message::Text(loginmsg.into()));
        if ret.is_err() {
            return Err(format!("send to ws error. {:?}", ret.err().unwrap()));
        }
        self.chn_sx = Some(chn_tx); // 保存发送通道

        Ok(())
    }
}

impl WsClient {
    /// 消息接收函数
    async fn recv_run(
        chn_rx: futures_channel::mpsc::UnboundedReceiver<Message>,
        ws_stream: WebSocketStream<MaybeTlsStream<TcpStream>>,
        sender: crossbeam_channel::Sender<Message>,
    ) {
        let (write, read) = ws_stream.split();
        let write_to_ws = chn_rx.map(Ok).forward(write);
        let ws_to_recv = {
            read.for_each(|message| async {
                let mut errglag = false;

                if let Ok(msg) = message {
                    if msg.is_close() {
                        log::warn!("[in]: ws closed. {:?}", msg);
                    }

                    let _ = sender.send(msg);
                } else {
                    log::error!("ws rec error. {:?}", message.err().unwrap().to_string());
                    errglag = true;

                    // 读取失败后添加一条关闭消息到队列
                    let msg = Message::Close(None);
                    let _ = sender.send(msg);
                }

                // 已经发生错误，退出
                if errglag {
                    return;
                }
            })
        };

        pin_mut!(write_to_ws, ws_to_recv);
        future::select(write_to_ws, ws_to_recv).await;
        log::info!("receive thread closed");
    }
}
