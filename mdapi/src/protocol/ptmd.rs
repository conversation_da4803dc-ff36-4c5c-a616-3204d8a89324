use serde::{Deserialize, Serialize};

use super::ptstruct::EMessageType;
use comfun;

/// 行情登录信息
#[derive(Serialize)]
pub struct PTReqMdLogin {
    /// 用户名
    #[serde(skip_serializing_if = "String::is_empty")]
    pub uid: String,

    /// 密码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub pwd: String,

    /// 动态密码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OneTimePwd: String,

    /// appid
    #[serde(skip_serializing_if = "String::is_empty")]
    pub appid: String,

    /// 认证码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub authcode: String,

    /// 版本号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub version: String,

    /// mac地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub macaddress: String,

    /// 上报信息
    #[serde(skip_serializing_if = "String::is_empty")]
    pub exrepeatinfo: String,

    /// 上报信息
    #[serde(skip_serializing_if = "String::is_empty")]
    pub tirepeatinfo: String,

    /// 请求编码
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub reqid: i32,

    /// 消息类型
    mt: i32,
}
impl Default for PTReqMdLogin {
    fn default() -> Self {
        Self {
            uid: String::default(),
            pwd: String::default(),
            OneTimePwd: String::default(),
            appid: String::default(),
            authcode: String::default(),
            version: String::default(),
            macaddress: String::default(),
            exrepeatinfo: String::default(),
            tirepeatinfo: String::default(),
            reqid: 0,
            mt: EMessageType::TIMT_WS_Com_Req_Login.into(),
        }
    }
}

/// 请求行情登录响应
#[derive(Default, Deserialize)]
#[serde[default]]
pub struct PTRspMdLogin {
    /// 错误码
    pub ec: i32,

    /// 错误信息
    pub em: String,

    /// 是否最后一条
    pub lt: i32,

    /// 请求编号
    pub reqid: i32,

    /// user id
    pub uid: String,

    /// session id
    pub sid: String,
}

/// 请求行情登出
#[derive(Serialize)]
pub struct PTReqMdLogout {
    /// Request ID
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub reqid: i32,
    /// User ID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub uid: String,
    /// Session ID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub sid: String,

    /// 消息类型
    mt: i32,
}
impl Default for PTReqMdLogout {
    fn default() -> Self {
        Self {
            mt: EMessageType::TIMT_WS_Com_Req_Logout.into(),
            reqid: 0,
            uid: String::default(),
            sid: String::default(),
        }
    }
}

/// 请求行情登出响应
#[derive(Default, Deserialize)]
#[serde[default]]
pub struct PTRspMdLogout {
    /// 错误码
    pub ec: i32,

    /// 错误信息
    pub em: String,

    /// 是否最后一条
    pub lt: i32,

    /// 请求编号
    pub reqid: i32,
}

/// 行情订阅/退订阅字段
#[derive(Default)]
pub struct MdSubscribeField {
    /// 订阅的合约编码. 值不能为空
    pub IID: String,

    /// Match. 是否匹配。0:不匹配; 1:匹配
    /// 当匹配时, IID值为 * 时，匹配所有; 不为 * 时否按前向匹配规则匹配, 如IID为 1100, 可匹配 1100, 11001, 11002, 11008899 ...
    pub MTH: i32,
}
impl MdSubscribeField {
    pub fn equals(&self, rh: &MdSubscribeField) -> bool {
        self.IID == rh.IID && self.MTH == rh.MTH
    }
}

/// 请求行情订阅
#[derive(Serialize)]
pub struct PTReqMdSubscribe {
    /// Request ID
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub reqid: i32,
    /// User ID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub uid: String,
    /// Session ID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub sid: String,

    /// 请参考 MdSubscribeField 的 IID 字段说明
    #[serde(skip_serializing_if = "String::is_empty")]
    pub IID: String,

    /// 请参考 MdSubscribeField 的 MTH 字段说明
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub MTH: i32,

    mt: i32,
}
impl Default for PTReqMdSubscribe {
    fn default() -> Self {
        Self {
            IID: String::default(),
            MTH: i32::default(),
            mt: EMessageType::TIMT_WS_Md_Req_Subscribe.into(),
            reqid: 0,
            uid: String::default(),
            sid: String::default(),
        }
    }
}

/// 请求行情订阅响应
#[derive(Default, Deserialize)]
#[serde[default]]
pub struct PTRspMdSubscribe {
    /// 错误码
    pub ec: i32,

    /// 错误信息
    pub em: String,

    /// 是否最后一条
    pub lt: i32,

    /// 请求编号
    pub reqid: i32,

    /// 请参考 MdSubscribeField 的 IID 字段说明
    pub IID: String,

    /// 请参考 MdSubscribeField 的 MTH 字段说明
    pub MTH: i32,
}

/// 请求行情退订阅
#[derive(Serialize)]
pub struct PTReqMdUnSubscribe {
    /// Request ID
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub reqid: i32,
    /// User ID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub uid: String,
    /// Session ID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub sid: String,

    // public readonly int mt = (int);
    /// 请参考 MdSubscribeField 的 IID 字段说明
    pub IID: String,

    /// 请参考 MdSubscribeField 的 MTH 字段说明
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub MTH: i32,

    mt: i32,
}
impl Default for PTReqMdUnSubscribe {
    fn default() -> Self {
        Self {
            IID: String::default(),
            MTH: i32::default(),
            mt: EMessageType::TIMT_WS_Md_Req_UnSubscribe.into(),
            reqid: 0,
            uid: String::default(),
            sid: String::default(),
        }
    }
}

/// 请求行情退订阅响应
#[derive(Default, Deserialize)]
#[serde[default]]
pub struct PTRspMdUnSubscribe {
    /// 错误码
    pub ec: i32,

    /// 错误信息
    pub em: String,

    /// 是否最后一条
    pub lt: i32,

    /// 请求编号
    pub reqid: i32,

    /// 请参考 MdSubscribeField 的 IID 字段说明
    pub IID: String,

    /// 请参考 MdSubscribeField 的 MTH 字段说明
    pub MTH: i32,
}

/// L1行情消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct PTRtnMarketDataL1 {
    pub EID: String, // 交易所编码
    pub IID: String, // 合约编码
    pub ME: String,  // 行情类别
    pub UT: u32,     // 最新更新时间. HHMMSSsss
    pub PCP: f64,    // 昨收价
    pub NT: i32,     // 成交笔数
    pub VOL: f64,    // 成交数量
    pub TUR: f64,    // 成交总金额
    pub LP: f64,     // 最新价
    pub OP: f64,     // 开盘价
    pub HTP: f64,    // 最高价
    pub LTP: f64,    // 最低价
    pub CP: f64,     // 今收价
    pub SP: f64,     // 今结算价
    pub PSP: f64,    // 昨结算价
    pub IOPV: f64,   // IOPV
    pub PIOPV: f64,  // 昨IOPV
    pub AP: f64,     // 动态参考价
    pub AQ: i32,     // 虚拟匹配数量
    pub OPV: i32,    // 总持仓量
    pub TPC: String, // 产品所处的交易阶段代码

    pub BP1: f64, // 买1价
    pub BV1: i32, // 买1量
    pub BP2: f64, // 买2价
    pub BV2: i32, // 买2量
    pub BP3: f64, // 买3价
    pub BV3: i32, // 买3量
    pub BP4: f64, // 买4价
    pub BV4: i32, // 买4量
    pub BP5: f64, // 买5价
    pub BV5: i32, // 买5量

    pub AP1: f64, // 卖1价
    pub AV1: i32, // 卖1量
    pub AP2: f64, // 卖2价
    pub AV2: i32, // 卖2量
    pub AP3: f64, // 卖3价
    pub AV3: i32, // 卖3量
    pub AP4: f64, // 卖4价
    pub AV4: i32, // 卖4量
    pub AP5: f64, // 卖5价
    pub AV5: i32, // 卖5量
}
