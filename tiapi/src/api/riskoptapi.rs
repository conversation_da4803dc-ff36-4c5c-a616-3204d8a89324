use crate::protocol_pub::{
    account_risk_opt::*,
    app::*,
    exercise_risk_opt::ExerciseField,
    greeks::GreeksField,
    monitor_risk_opt::*,
    order_risk_opt::{OmlField, OrderField},
    position_risk_opt::*,
    trade_risk_opt::TradeField,
};

/// 个股期权风控回调类
pub trait IRiskOptSpi: Send + Sync {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {}

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {}

    /// 资金消息
    fn on_rtn_account(&self, acc: AccountsField) {}

    /// 出入金消息
    fn on_rtn_withdrawdeposit(&self, wd: WithdrawDepositField) {}

    /// 持仓消息
    fn on_rtn_position(&self, position: PositionField) {}

    /// 持仓组合消息
    fn on_rtn_positioncomb(&self, positioncomb: PositionCombField) {}

    /// 委托消息
    fn on_rtn_order(&self, ord: OrderField) {}

    /// 成交消息
    fn on_rtn_trade(&self, trd: TradeField) {}

    /// 期权组合详情消息
    fn on_rtn_oml(&self, oml: OmlField) {}

    /// 行权消息
    fn on_rtn_exercise(&self, ex: ExerciseField) {}

    /// 希腊字母
    fn on_rtn_greeks(&self, greeks: GreeksField) {}

    /// 自成交详情消息
    fn on_rtn_tradeselfdtl(&self, trd: TradeField) {}

    /// 自成交消息
    fn on_rtn_tradeself(&self, trdself: TradeSelfField) {}

    /// 限额消息
    fn on_rtn_limitaccount(&self, limitacc: LimitAccountField) {}

    /// 成交持仓比消息
    fn on_rtn_tradeposition(&self, tradepos: TradePositionField) {}

    /// 限仓消息
    fn on_rtn_limitposition(&self, limitpos: LimitPositionField) {}

    /// 报撤单消息
    fn on_rtn_orderinsertcancel(&self, ordinsertcancel: OrderInsertCancelField) {}
}
