import { Splitter, Label } from "@zstk/lib.slint";
import { StkRsk_Com } from "struct.slint";
import { RiskStkNormalPage } from "normal/riskstk.slint";
import { RiskStkCreditPage } from "credit/riskstkcredit.slint";

import {
    StkRskCredit_Com,
    StkRskCredit_Account, StkRskCredit_Position, StkRskCredit_GaCha,
    StkRskCredit_CreditContract, StkRskCredit_CreditLimitAmt, StkRskCredit_CreditLimitPos, StkRskCredit_CreditTcAmt, StkRskCredit_CreditTcPos, StkRskCredit_CreditConcentration,
    StkRskCredit_QryCreditReAmtDtl, StkRskCredit_QryWithdrawDeposit, StkRskCredit_QryCreditLimit, StkRskCredit_QryCreditTcAmt
} from "./credit/struct.slint";
import { RiskStkCreditDetailPage } from "./credit/riskstkcreditdetail.slint";

export {
    StkRsk_Com,
    RiskStkCreditDetailPage,
    StkRskCredit_Com,
    StkRskCredit_Account, StkRskCredit_Position, StkRskCredit_GaCha,
    StkRskCredit_CreditContract, StkRskCredit_CreditLimitAmt, StkRskCredit_CreditLimitPos, StkRskCredit_CreditTcAmt, StkRskCredit_CreditTcPos, StkRskCredit_CreditConcentration,
    StkRskCredit_QryCreditReAmtDtl, StkRskCredit_QryWithdrawDeposit, StkRskCredit_QryCreditLimit, StkRskCredit_QryCreditTcAmt
}

export component RiskStkPage {

    preferred_width: 100%;
    preferred_height: 100%;

    if StkRsk_Com.iscredit: RiskStkCreditPage {}
    if !StkRsk_Com.iscredit: RiskStkNormalPage {}
}
