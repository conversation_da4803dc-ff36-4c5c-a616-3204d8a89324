use std::process::Command;

#[cfg(target_os = "linux")]
use std::path::Path;

/// Platform-agnostic function to open and select a file in the system's file manager
pub fn open_and_select_file(path: &str) -> Result<(), String> {
    #[cfg(target_os = "macos")]
    {
        open_and_select_file_mac(path)
    }

    #[cfg(target_os = "windows")]
    {
        open_and_select_file_win(path)
    }

    #[cfg(target_os = "linux")]
    {
        open_and_select_file_linux(path)
    }

    #[cfg(not(any(target_os = "macos", target_os = "windows", target_os = "linux")))]
    {
        Err("Unsupported operating system".to_string())
    }
}

//#[cfg(target_os = "macos")]
fn open_and_select_file_mac(path: &str) -> Result<(), String> {
    let status = Command::new("open")
        .arg("-R")
        .arg(path)
        .stderr(std::process::Stdio::null())
        .spawn()
        .map_err(|e| format!("Failed to spawn open command: {}", e))?
        .wait()
        .map_err(|e| format!("Failed to wait for open command: {}", e))?;

    if !status.success() {
        return Err(format!("Failed to open and select file: {}", path));
    }

    Ok(())
}

#[cfg(target_os = "windows")]
fn open_and_select_file_win(path: &str) -> Result<(), String> {
    // Convert path separators to Windows style

    use std::os::windows::process::CommandExt;
    let windows_path = path.replace('/', "\\");

    let status = Command::new("explorer")
        .creation_flags(0x08000000) // CREATE_NO_WINDOW flag
        .arg("/select,")
        .arg(&windows_path)
        .stderr(std::process::Stdio::null())
        .spawn()
        .map_err(|e| format!("Failed to spawn explorer: {}", e))?
        .wait()
        .map_err(|e| format!("Failed to wait for explorer: {}", e))?;

    if !status.success() {
        return Err(format!("Explorer failed to open and select file: {}", windows_path));
    }

    Ok(())
}

#[cfg(target_os = "linux")]
fn open_and_select_file_linux(path: &str) -> Result<(), String> {
    let file_managers = [
        ("nautilus", Some("--select")),
        ("dolphin", None),
        ("thunar", Some("--select")),
        ("nemo", Some("--no-desktop")),
        ("caja", Some("--no-desktop")),
        ("pcmanfm", None),
    ];

    for (manager, select_flag) in file_managers {
        let mut command = Command::new(manager);
        if let Some(flag) = select_flag {
            command.arg(flag);
        }

        if let Ok(status) = command
            .arg(path)
            .stderr(std::process::Stdio::null())
            .spawn()
            .and_then(|mut child| child.wait())
        {
            if status.success() {
                return Ok(());
            }
        }
    }

    // Fallback to xdg-open for the parent directory
    let path = Path::new(path);
    if let Some(dir_path) = path.parent() {
        if let Ok(status) = Command::new("xdg-open")
            .arg(dir_path)
            .stderr(std::process::Stdio::null())
            .spawn()
            .and_then(|mut child| child.wait())
        {
            if status.success() {
                return Ok(());
            }
        }
    }

    Err(std::format!(
        "Failed to open and select file[{}] on Linux, No suitable file manager found",
        path.display()
    ))
}
