import { Fonts, TableView, TVVisible } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom, AppInerArgs } from "../../../struct.slint";
import { StkRskCredit_CreditLimitPos } from "../struct.slint";

export component CreditLimitPosPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <TVVisible> header_exchid_visible: AppInerArgs.show_detail ? TVVisible.Visible : TVVisible.Invisible;

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid, visible: header_exchid_visible },
                    { title: @tr("授权额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("可用额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("使用额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("冻结额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转入额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转出额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                ]
            };
            row_count: StkRskCredit_CreditLimitPos.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_CreditLimitPos.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkRskCredit_CreditLimitPos.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_CreditLimitPos.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_CreditLimitPos.row_data[row_index]);
                }
            }
        }
    }
}
