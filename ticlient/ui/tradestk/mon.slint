import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";
import { StkTrd_TabSelIdx, StkTrd_Com } from "struct.slint";

import { MonOrderInsertCancelPage } from "mon_orderinsertcancel.slint";
import { MonConvertBondsPage } from "mon_convertbonds.slint";
import { MonSelfTradePage } from "mon_selftrade.slint";
import { MonSelfTradeDtlPage } from "mon_selftradedtl.slint";

export component MonitorPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "报撤单" },
        { text: "可转债" },
        { text: "自成交" },
        { text: "自成交详情" },
    ];

    private property <[TabBarItem]> tabview_credit_items: [
        { text: "报撤单" },
        { text: "可转债" },
        { text: "自成交" },
        { text: "自成交详情" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: StkTrd_Com.iscredit ? tabview_credit_items : tabview_items;

            current_index <=> StkTrd_TabSelIdx.mon_subselidx;
        }

        if 0 == StkTrd_TabSelIdx.mon_subselidx && !StkTrd_Com.iscredit: MonOrderInsertCancelPage { }
        if 1 == StkTrd_TabSelIdx.mon_subselidx && !StkTrd_Com.iscredit: MonConvertBondsPage { }
        if 2 == StkTrd_TabSelIdx.mon_subselidx && !StkTrd_Com.iscredit: MonSelfTradePage { }
        if 3 == StkTrd_TabSelIdx.mon_subselidx && !StkTrd_Com.iscredit: MonSelfTradeDtlPage { }

        if 0 == StkTrd_TabSelIdx.mon_subselidx && StkTrd_Com.iscredit: MonOrderInsertCancelPage { }
        if 1 == StkTrd_TabSelIdx.mon_subselidx && StkTrd_Com.iscredit: MonConvertBondsPage { }
        if 2 == StkTrd_TabSelIdx.mon_subselidx && StkTrd_Com.iscredit: MonSelfTradePage { }
        if 3 == StkTrd_TabSelIdx.mon_subselidx && StkTrd_Com.iscredit: MonSelfTradeDtlPage { }
    }
}
