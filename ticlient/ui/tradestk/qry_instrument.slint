import { Theme, Fonts, Label, LineEdit, Button, ComboBox, TableView, TVColumnHeader, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { StkTrd_Com } from "struct.slint";

export global StkTrd_QryInstrument {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> insid;

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);
}

export component QryInstrumentPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <TVColumnHeader> ins_header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 2,
        columns: [
            { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
            { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
            { title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },
            { title: @tr("类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
            { title: @tr("T+1"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },

            { title: @tr("买单位数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
            { title: @tr("卖单位数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },

            { title: @tr("最小变动价"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
            { title: @tr("昨收盘价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("涨停板价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("跌停板价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

            { title: @tr("市价最大下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("市价最小下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("限价最大下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("限价最小下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
        ]
    };

    private property <TVColumnHeader> ins_credit_header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 2,
        columns: [
            { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
            { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
            { title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },
            { title: @tr("类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
            { title: @tr("T+1"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },

            { title: @tr("买单位数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
            { title: @tr("卖单位数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },

            { title: @tr("最小变动价"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
            { title: @tr("昨收盘价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("涨停板价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("跌停板价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

            { title: @tr("市价最大下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("市价最小下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("限价最大下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("限价最小下单量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

            { title: @tr("融资标的"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
            { title: @tr("融券标的"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
            { title: @tr("折算率"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            { title: @tr("融资保证金比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("融券保证金比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            { title: @tr("允许融资"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
            { title: @tr("允许融券"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
            { title: @tr("是否担保品"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_105 },
        ]
    };

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkTrd_QryInstrument.exchid;
        i_le_insid.text = StkTrd_QryInstrument.insid;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text: StkTrd_QryInstrument.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }

            i_le_insid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: StkTrd_QryInstrument.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Button {
                text: "查询";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                    StkTrd_QryInstrument.exchid = i_cbx_exch.current_text;
                    StkTrd_QryInstrument.insid = i_le_insid.text;

                    StkTrd_QryInstrument.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: StkTrd_Com.iscredit ? ins_credit_header : ins_header;
            row_count: StkTrd_QryInstrument.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_QryInstrument.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_QryInstrument.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_QryInstrument.row_data[row_index]);
                }
            }
        }

        if StkTrd_QryInstrument.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkTrd_QryInstrument.page_index_model;
            item_total <=> StkTrd_QryInstrument.item_total;
            page_total <=> StkTrd_QryInstrument.page_total;
            page_index <=> StkTrd_QryInstrument.page_index;
            page_size <=> StkTrd_QryInstrument.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryInstrument.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryInstrument.page_size_changed(size);
            }
        }
    }
}
