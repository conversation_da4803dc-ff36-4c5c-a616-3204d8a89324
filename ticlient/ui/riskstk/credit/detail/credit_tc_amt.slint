import { Fonts, TableView, TVVisible, Label, ComboBox } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom, AppInerArgs } from "../../../struct.slint";
import { StkRskCredit_CreditTcAmt } from "../struct.slint";

export component CreditTcAmtPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <TVVisible> header_exchid_visible: AppInerArgs.show_detail ? TVVisible.Visible : TVVisible.Invisible;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "头寸类型";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_CreditTcAmt.filter_tctype_str;
                model: [
                    { text: "", tag: 0, },
                    { text: "普通头寸", tag: 1, },
                    { text: "专项头寸", tag: 2, },
                ];

                selected => {
                    StkRskCredit_CreditTcAmt.filter_tctype = self.current_value.tag;
                    StkRskCredit_CreditTcAmt.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("头寸类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid, visible: header_exchid_visible },
                    { title: @tr("授权头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("可用头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("使用头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("冻结头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转入头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转出头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                ]
            };
            row_count: StkRskCredit_CreditTcAmt.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_CreditTcAmt.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkRskCredit_CreditTcAmt.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_CreditTcAmt.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_CreditTcAmt.row_data[row_index]);
                }
            }
        }
    }
}
