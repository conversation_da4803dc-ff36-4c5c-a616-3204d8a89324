use serde::Deserialize;

/// 持仓量信息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct PositionInfoStkField {
    /// 昨持仓量
    pub PrePosition: i32,

    /// 实际持仓量 = 昨持仓量 + 开仓量 - 平仓量
    pub Position: i32,

    /// 开仓量
    pub OpenVolume: i32,

    /// 平仓量
    pub CloseVolume: i32,

    /// 股份划转量
    pub TransVolume: i32,

    /// 冻结持仓量(在途平仓报单冻结)
    pub FrozenPosition: i32,

    /// 开仓金额
    pub OpenAmount: f64,

    /// 平仓金额
    pub CloseAmount: f64,

    /// 持仓均价
    pub AvgPrice: f64,

    /// 持仓市值
    pub PosValue: f64,

    /// 持仓盈亏
    pub PosProfit: f64,

    /**********下面字段为信用交易字段**********/
    /// 融券已还持仓量
    pub XYRqRePos: i32,

    /// 融资已还持仓量
    pub XYRzRePos: f64,

    /// 融资未还持仓量
    pub XYRzNdPos: f64,

    // 折算率
    pub XYDiscRatio: f64,

    /// 融资充抵保证金
    pub XYCdMargin: f64,
}
impl PositionInfoStkField {
    // 是否有持仓量
    pub fn has_position(&self) -> bool {
        0 != self.PrePosition || 0 != self.Position || 0 != self.OpenVolume || 0 != self.CloseVolume || 0 != self.FrozenPosition
    }
}

/// 持仓信息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct PositionStkField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 投机套保标志
    pub HedgeFlag: i32,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 最新价(LastPrice)
    pub LPrice: f64,

    /// 多头持仓信息
    pub Long: PositionInfoStkField,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 股份划转消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct PositionTransStkField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,
}

/// 信用交易 - 合约
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CreditContractField {
    pub ConNo: String, // 合约头寸

    pub AccID: String, // 资金账号
    pub CliID: String, // 交易编码

    pub OpenDate: i32,   // 开仓日期
    pub ExpireDate: i32, // 到期日期
    pub IsExpire: i32,   // 是否到期合约
    pub ExchID: i32,     // 交易所编码
    pub InsID: String,   // 合约编码
    pub Side: i32,       // 方向(融资买入,融券卖出)

    pub OrderSysID: String, // 交易所委托编码
    pub OrderVolume: i32,   // 委托数量
    pub OrderPrice: f64,    // 委托价格
    pub OrderAmount: f64,   // 委托金额

    pub Volume: i32, // 合约数量
    pub Amount: f64, // 合约金额
    pub Commi: f64,  // 合约费用

    pub Rate: f64,     // 合约利率
    pub Interest: f64, // 合约利息

    pub ReVol: i32,      // 已还数量
    pub ReAmt: f64,      // 已还金额
    pub ReInterest: f64, // 已还利息

    pub TdReVol: i32,      // 当日已经数量
    pub TdReAmt: f64,      // 当日已还金额(本金)
    pub TdReCommi: f64,    // 当日已还费用
    pub TdReInterest: f64, // 当日已还利息

    pub NdVol: i32,      // 未还数量
    pub NdAmt: f64,      // 未还金额
    pub NdInterest: f64, // 未还利息

    pub FreeInt: f64, // 合约罚息

    pub RzNdVol: f64,     // 融资未还数量(按还款金额的比例折算出来)
    pub XYBuy: f64,       // (融资买入证券市值 - 融资买入金额) * 折算率
    pub XYBuyMargin: f64, // 融资买入证券金额 * 融资保证金比例

    pub XYSellAmt: f64,    // 融券卖出金额
    pub XYSell: f64,       // (融券卖出金额 - 融券卖出证券市值) * 折算率
    pub XYSellMargin: f64, // 融券卖出证券市值 * 融券保证金比例

    pub LPrice: f64,      // 最新价
    pub DiscRatio: f64,   // 折算率
    pub MarginRatio: f64, // 保证金率
}

/// 信用交易 - 集中度
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CreditConcentrationField {
    pub GID: i32,      // 集中度组编码
    pub GName: String, // 集中度组名称
    pub CType: i32,    // 集中度类型. 1:单一证券集中度; 2:组合集中度

    pub AccID: String, // 资金账号
    pub CliID: String, // 交易编码
    pub ExchID: i32,   // 交易所编码

    pub InsID: String, // 合约编码(仅单一证券集中度有效)
    pub ULimit: f64,   // 集中度上限

    pub MRatio: f64, // 维持保证金比例(值为-1表示无负债)

    pub BuyValue: f64,  // 持仓市值
    pub ABuyValue: f64, // 总持仓市值
    pub BIValue: f64,   // 买方向在途量的市值
    pub ABIValue: f64,  // 买方向在途量的总市值

    pub Value: f64,  // 市值
    pub AValue: f64, // 总资产
    pub Ratio: f64,  // 集中度比例
}

/// 信用交易 - 归还明细
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CreditReturnDtlField {
    pub No: i32, // 编号

    pub AccID: String, // 资金账号
    pub CliID: String, // 交易编码
    pub ExchID: i32,   // 交易所编码

    pub InsID: String, // 证券编码
    pub Time: i32,     // 时间

    pub Type: i32,      // 总类型
    pub ReType: i32,    // 类型
    pub ReDtlType: i32, // 类型明细

    pub ReID: String,     //  归还编码
    pub ReIndex: i32,     //  归还序号
    pub OrdSydID: String, // 合约编码

    pub Amount: f64,  // 金额
    pub ReAmt: f64,   // 归还金额
    pub LeftAmt: f64, // 剩余金额

    pub Volume: i32,  // 数量
    pub ReVol: i32,   // 归还数量
    pub LeftVol: i32, // 剩余数量
}

/// 请求查询信用交易 - 归还明细
#[derive(Default)]
pub struct ReqQryCreditReDtlField {
    /// 资金账号
    pub AccID: String,

    /// 交易所代码
    pub ExchID: i32,

    /// 偿还类型
    pub ReType: i32,

    /// 偿还编码
    pub ReID: String,

    /// 委托编码
    pub OrdSysID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}