use crate::{
    protocol_pub::{
        account_trade_ctp::{
            CtpAccountField, CtpReqFundTransferField, CtpReqQryFundTransferField, CtpReqQryTradingAccountExField,
            CtpReqQryTradingAccountField, CtpReqReFundTransferField, CtpRspFundTransferField, CtpRspQryFundTransferField,
            CtpWithdrawDepositField,
        },
        app::*,
        exercise_trade_ctp::{CtpExecCombineOrderField, CtpExecOrderField},
        instrument_trade_ctp::{CtpReqQryInstrumentField, CtpRspQryInstrumentField},
        login_trade_ctp::{CtpLoginRetField, CtpReqLoginField},
        monitor_trade_ctp::*,
        order_trade_ctp::{CtpOmlField, Ctp<PERSON><PERSON><PERSON><PERSON><PERSON>, CtpQuoteField},
        position_trade_ctp::{CtpPositionCombField, CtpPositionField, CtpReqQryPositionCombDtlField, CtpReqQryPositionField},
        trade_req_ctp::*,
        trade_trade_ctp::CtpTradeField,
    },
    serverinfo::ServerInfo,
};

/// 个股期权交易回调类
pub trait ITradeOptSpi: Send + Sync {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {}

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {}

    /// 委托消息
    fn on_rtn_order(&self, ord: CtpOrderField) {}

    /// 成交消息
    fn on_rtn_trade(&self, trd: CtpTradeField) {}

    /// 组合消息
    fn on_rtn_oml(&self, oml: CtpOmlField) {}

    /// 执行宣告消息
    fn on_rtn_execorder(&self, eo: CtpExecOrderField) {}

    /// 行权指令合并消息
    fn on_rtn_execcombineorder(&self, eco: CtpExecCombineOrderField) {}

    /// 报价消息
    fn on_rtn_quote(&self, quote: CtpQuoteField) {}

    /// 限额消息
    fn on_rtn_limitamount(&self, la: CtpLimitAmountField) {}

    /// 限仓
    fn on_rtn_limitposition(&self, lp: CtpLimitPositionField) {}

    /// 自成交
    fn on_rtn_tradeself(&self, trdslf: CtpTradeSelfField) {}

    /// 自成交详情
    fn on_rtn_tradeselfdtl(&self, trd: CtpTradeSelfDtlField) {}

    /// 成交持仓比例
    fn on_rtn_tradeposition(&self, tp: CtpTradePositionField) {}

    /// 报撤单
    fn on_rtn_orderinsertcancel(&self, ordinsertcancel: CtpOrderInsertCancelField) {}

    /// 出入金消息
    fn on_rtn_wd(&self, wd: CtpWithdrawDepositField) {}

    /// 报单录入响应消息
    fn on_rsp_inputorder(&self, rsp: CtpRspInputOrderField, reqid: i32, islast: bool) {}

    /// 备兑解锁仓响应消息
    fn on_rsp_inputstocklock(&self, rsp: CtpRspInputStockLockField, reqid: i32, islast: bool) {}

    /// 报单操作响应
    fn on_rsp_inputorderaction(&self, rsp: CtpRspInputOrderActionField, reqid: i32, islast: bool) {}

    /// 行权响应
    fn on_rsp_inputexecorder(&self, rsp: CtpRspInputExecOrderField, reqid: i32, islast: bool) {}

    /// 行权操作响应
    fn on_rsp_inputexecorderaction(&self, rsp: CtpRspInputExecOrderActionField, reqid: i32, islast: bool) {}

    /// 组合录入响应
    fn on_rsp_inputcombaction(&self, rsp: CtpRspInputCombActionField, reqid: i32, islast: bool) {}

    /// 行权指令合并录入响应
    fn on_rsp_inputexeccombineorder(&self, rsp: CtpRspInputExecCombineOrderField, reqid: i32, islast: bool) {}

    /// 行权指令合并操作响应
    fn on_rsp_inputexeccombineorderaction(&self, rsp: CtpRspInputExecCombineOrderActionField, reqid: i32, islast: bool) {}

    /// 报价录入响应消息
    fn on_rsp_inputquote(&self, rsp: CtpRspInputQuoteField, reqid: i32, islast: bool) {}

    /// 报价操作响应
    fn on_rsp_inputquoteaction(&self, rsp: CtpRspInputQuoteActionField, reqid: i32, islast: bool) {}

    /// 查询合约响应
    fn on_rsp_qry_instrument(&self, ins: CtpRspQryInstrumentField, reqid: i32, islast: bool) {}

    /// 查询资金响应消息
    fn on_rsp_qry_tradingaccount(&self, acc: CtpAccountField, reqid: i32, islast: bool) {}

    /// 查询资金(分交易所)响应消息
    fn on_rsp_qry_tradingaccountex(&self, acc: CtpAccountField, reqid: i32, islast: bool) {}

    /// 查询持仓响应消息
    fn on_rsp_qry_investorposition(&self, pos: CtpPositionField, reqid: i32, islast: bool) {}

    /// 查询组合持仓明细响应消息
    fn on_rsp_qry_investorpositioncombdtl(&self, pos: CtpPositionCombField, reqid: i32, islast: bool) {}
}

/// 个股期权交易API
pub trait TradeOptApi: Send + Sync {
    /// 初始化
    fn init(&mut self);

    /// 释放资源
    fn release(&mut self);

    /// 设置服务端信息(请求登录前必须设置)
    fn set_svrinfo(&mut self, si: &ServerInfo);

    /// 请求登录<br>
    /// 同步接口. 连接->登录<br>
    /// 返回值说明. 成功发送登录请求前的错误均放在Err中, 成功发送请求后无论登录成功与否都放在OK中
    fn req_login(&self, req: &CtpReqLoginField) -> crate::Result<CtpLoginRetField>;

    /// 请求报单
    fn req_orderinput(&self, reqid: i32, req: &CtpReqInputOrderField) -> crate::Result<()>;

    /// 请求撤单
    fn req_orderaction(&self, reqid: i32, req: &CtpReqInputOrderActionField) -> crate::Result<()>;

    /// 请求申请组合操作
    fn req_combactioninput(&self, reqid: i32, req: &CtpReqInputCombActionField) -> crate::Result<()>;

    /// 请求行权
    fn req_exec_orderinput(&self, reqid: i32, req: &CtpReqInputExecOrderField) -> crate::Result<()>;

    /// 请求撤行权
    fn req_exec_orderaction(&self, reqid: i32, req: &CtpReqInputExecOrderActionField) -> crate::Result<()>;

    /// 请求合并行权
    fn req_exec_comb_orderinput(&self, reqid: i32, req: &CtpReqInputExecCombineOrderField) -> crate::Result<()>;

    /// 请求合并行权操作
    fn req_exec_comb_orderaction(&self, reqid: i32, req: &CtpReqInputExecCombineOrderActionField) -> crate::Result<()>;

    /// 请求报价
    fn req_quoteinput(&self, reqid: i32, req: &CtpReqInputQuoteField) -> crate::Result<()>;

    /// 请求报价操作
    fn req_quoteaction(&self, reqid: i32, req: &CtpReqInputQuoteActionField) -> crate::Result<()>;

    /// 请求查询合约
    fn req_qry_instrument(&self, reqid: i32, req: &CtpReqQryInstrumentField) -> crate::Result<()>;

    /// 请求查询资金账户
    fn req_qry_tradingaccount(&self, reqid: i32, req: &CtpReqQryTradingAccountField) -> crate::Result<()>;

    /// 请求查询资金账户(分交易所)
    fn req_qry_tradingaccountex(&self, reqid: i32, req: &CtpReqQryTradingAccountExField) -> crate::Result<()>;

    /// 请求查询持仓
    fn req_qry_investorposition(&self, reqid: i32, req: &CtpReqQryPositionField) -> crate::Result<()>;

    /// 请求查询组合持仓明细
    fn req_qry_investorposition_combdtl(&self, reqid: i32, req: &CtpReqQryPositionCombDtlField) -> crate::Result<()>;

    /// 请求备兑解锁仓
    fn req_stocklockinput(&self, reqid: i32, req: &CtpReqInputStocklockField) -> crate::Result<()>;

    /// 请求资金划转(同步函数)
    fn req_fundtrans(&self, req: &CtpReqFundTransferField, rsp: &mut CtpRspFundTransferField) -> crate::Result<()>;

    /// 请求资金继续划转(同步函数)
    fn req_re_fundtrans(&self, req: &CtpReqReFundTransferField, rsp: &mut Vec<CtpRspQryFundTransferField>) -> crate::Result<()>;

    /// 请求查询资金划转记录(同步函数)
    fn req_qry_fundtrans(&self, req: &CtpReqQryFundTransferField, rsp: &mut Vec<CtpRspQryFundTransferField>)
        -> crate::Result<()>;
}
