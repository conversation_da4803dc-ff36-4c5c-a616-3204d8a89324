use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        self,
        ctptype::CTPType,
        global::{CFG, INS, TRADE_OPT_API},
        instrument::Instrument,
        ticonvert::TIConvert,
        titype::TIType,
    },
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::{
    position_trade_ctp::CtpPositionField,
    trade_req_ctp::{CtpReqInputCombActionField, CtpRspInputCombActionField},
};

use super::tradeopt::show_input_msg_box;

#[derive(Default)]
struct PositionInfo {
    investorid: String,
    exchid: String,
    insid: String,
    dir: i32,
    hedge: i32,
    vol: i32,
}
type PositionMap = dashmap::DashMap<String, PositionInfo>;
type InsIDMap = dashmap::DashMap<String, String>;

/// 组合录入数据
struct InputOmlDatas {
    /******************************************************************************************************************/
    /// 在组合过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputoml: AtomicBool,

    /// 组合响应数据
    rsp_inputoml: Arc<Mutex<CtpRspInputCombActionField>>,

    /// 所有的期权合约列表
    all_opt_insid_map: Arc<InsIDMap>,

    /// 持仓信息
    pos_map: Arc<PositionMap>,

    /// 多头持仓合约列表
    long_pos_insid_map: Arc<InsIDMap>,

    /// 空头持仓合约列表
    short_pos_insid_map: Arc<InsIDMap>,

    /// 是否需要更新第1腿合约列表
    need_update_leg1_insid_model: AtomicBool,

    /// 是否需要更新第2腿合约列表
    need_update_leg2_insid_model: AtomicBool,

    /// 输入的合约是否有改变
    input_leg1_instid_changed: AtomicBool,

    /// 输入的合约是否有改变
    input_preview_leg1_instid_changed: AtomicBool,
}
impl InputOmlDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputoml: AtomicBool::new(false),
            rsp_inputoml: Arc::new(Mutex::new(CtpRspInputCombActionField::default())),
            all_opt_insid_map: Arc::new(InsIDMap::new()),
            pos_map: Arc::new(PositionMap::new()),
            long_pos_insid_map: Arc::new(InsIDMap::new()),
            short_pos_insid_map: Arc::new(InsIDMap::new()),
            need_update_leg1_insid_model: AtomicBool::new(false),
            need_update_leg2_insid_model: AtomicBool::new(false),
            input_leg1_instid_changed: AtomicBool::new(false),
            input_preview_leg1_instid_changed: AtomicBool::new(false),
        }
    }
}
static INPUTOML_DATAS: OnceLock<InputOmlDatas> = OnceLock::new();

/// 组合
pub(super) struct InputOml {}

/// 构建与初始化
impl InputOml {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化报单录入数据
        let _ = INPUTOML_DATAS.set(InputOmlDatas::new());

        let app_inputoml = app.global::<OptTrd_InputOml>();

        // 注册组合策略变动事件
        let app_weak = app.as_weak();
        app_inputoml.on_strategyid_changed(move || InputOml::on_strategyid_changed(&app_weak));

        // 注册第1腿合约改变事件
        let app_weak = app.as_weak();
        app_inputoml.on_leg1_insid_changed(move || InputOml::on_leg1_insid_changed(&app_weak));

        // 注册第2腿合约改变事件
        let app_weak = app.as_weak();
        app_inputoml.on_leg2_insid_changed(move || InputOml::on_leg2_insid_changed(&app_weak));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputoml.on_volume_text_changed(move || InputOml::on_volume_text_changed(&app_weak));

        // 注册用户双击了持仓事件
        let app_weak = app.as_weak();
        app_inputoml
            .on_sel_position(move |accid, exchid, insid, dir| InputOml::on_sel_position(&app_weak, accid, exchid, insid, dir));

        // 注册用户双击了组合持仓事件
        let app_weak = app.as_weak();
        app_inputoml.on_sel_comb_position(move || InputOml::on_sel_comb_position(&app_weak));

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputoml.on_volume_updown_changed(move |upflag| InputOml::on_volume_updown_changed(&app_weak, upflag));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputoml.on_req_tip_toggled(move |checked| InputOml::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputoml.on_ok_clicked(move |need_confirm| InputOml::on_ok_clicked(&app_weak, need_confirm));

        // 初始化组合预览
        self.init_comb_preview(app);

        log::trace!("Input oml init completed");
    }

    /// 初始化组合预览
    fn init_comb_preview(&self, app: &crate::slintui::App) {
        let app_inputoml = app.global::<OptTrd_InputOml>();
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();

        // 所获所有合约编码，填充到第1腿合约
        {
            let leg1_insid_items: Rc<VecModel<LineItem>> = Rc::new(VecModel::default());

            let all_opt_id = { common::global::INS.get().unwrap().read().unwrap().get_opt_id_all_with_name() };
            all_opt_id.iter().for_each(|it| {
                inputoml_datas
                    .all_opt_insid_map
                    .insert(it.key().clone(), it.value().1.clone());

                leg1_insid_items.push(LineItem {
                    text: slint::format!("{}", it.key()),
                    remark: slint::format!("{}", it.value().1),
                    ..Default::default()
                });
            });

            let sort_model = Rc::new(leg1_insid_items.sort_by(move |a, b| a.text.cmp(&b.text)));
            app_inputoml.set_op_leg1_insid_model(sort_model.clone().into());
            app_inputoml.set_all_op_leg1_insid_model(sort_model.into());
        }

        // 注册第1腿合约改变事件
        let app_weak = app.as_weak();
        app_inputoml.on_op_leg1_insid_changed(move || InputOml::on_op_leg1_insid_changed(&app_weak));

        // 注册第2腿合约改变事件
        let app_weak = app.as_weak();
        app_inputoml.on_op_leg2_insid_changed(move || InputOml::on_op_leg2_insid_changed(&app_weak));
    }

    /// 组合响应
    pub fn on_rsp_combaction(&self, rsp: CtpRspInputCombActionField) {
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();
        *inputoml_datas.rsp_inputoml.lock().unwrap() = rsp;
        inputoml_datas.is_err_inputoml.store(true, Ordering::Relaxed);
    }

    /// 更新持仓信息
    pub fn update_position(pos: &CtpPositionField) {
        if let Some(inputoml_datas) = INPUTOML_DATAS.get() {
            let key = std::format!("{}|{}", pos.InstrumentID, pos.ExchangeID);
            let pos_insid_map = if pos.PosiDirection == CTPType::POSDIR_LONG {
                &inputoml_datas.long_pos_insid_map
            } else {
                &inputoml_datas.short_pos_insid_map
            };

            if !pos_insid_map.contains_key(&key) {
                if let Some(ins) = INS
                    .get()
                    .unwrap()
                    .read()
                    .unwrap()
                    .get_ins_with_exchname(&pos.ExchangeID, &pos.InstrumentID)
                {
                    pos_insid_map.insert(key, ins.symbol);
                    inputoml_datas.need_update_leg1_insid_model.store(true, Ordering::Relaxed);
                    inputoml_datas.need_update_leg2_insid_model.store(true, Ordering::Relaxed);
                }
            }

            // 注意: 如果该投资者在同一个交易所有多个相同HedgeFlag的交易编码,更新组合中可用持仓数量时会出错
            // 目前仅测试环境手动添加上场数据时出现过这类错误,正常情况还没有上述情况
            let key = std::format!(
                "{}{}{}{}{}",
                pos.InvestorID,
                pos.ExchangeID,
                pos.InstrumentID,
                pos.PosiDirection,
                pos.HedgeFlag
            );

            let available_vol = pos.Position - pos.CombPosition - pos.LongFrozen - pos.ShortFrozen;

            inputoml_datas
                .pos_map
                .entry(key)
                .and_modify(|pos_info| pos_info.vol = available_vol)
                .or_insert(PositionInfo {
                    investorid: pos.InvestorID.clone(),
                    exchid: pos.ExchangeID.clone(),
                    insid: pos.InstrumentID.clone(),
                    dir: pos.PosiDirection,
                    hedge: pos.HedgeFlag,
                    vol: available_vol,
                });
        }
    }
}

/// 事件
impl InputOml {
    /// 组合策略变动
    fn on_strategyid_changed(app_weak: &Weak<App>) -> bool {
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();
        inputoml_datas.need_update_leg1_insid_model.store(true, Ordering::Relaxed);
        inputoml_datas.need_update_leg2_insid_model.store(true, Ordering::Relaxed);
        true
    }

    // 双击了持仓
    fn on_sel_position(app_weak: &Weak<App>, accid: SharedString, exchid: SharedString, insid: SharedString, dir: SharedString) {
        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        let dir = if "多" == dir.as_str() {
            CTPType::POSDIR_LONG
        } else {
            CTPType::POSDIR_SHORT
        };

        if app_inputoml.get_leg1_insid().is_empty() {
            if dir == app_inputoml.get_leg1_dir_i32() {
                app_inputoml.set_leg1_insid(insid);
                app_inputoml.invoke_leg1_insid_changed();
            }
        } else if app_inputoml.get_leg2_insid().is_empty() {
            if dir == app_inputoml.get_leg2_dir_i32() {
                let leg2_insid_set = InsIDMap::new();
                leg2_insid_set.insert(insid.to_string(), "".to_owned());

                let leg1_insid = app_inputoml.get_leg1_insid();

                let leg2_insid_vec = super::optcomb::OptComb::get_leg2_insid(
                    app_inputoml.get_strategyid_idx() + 1,
                    TIConvert::exch_id(&exchid),
                    &leg1_insid,
                    &leg2_insid_set,
                );
                if 1 == leg2_insid_vec.len() {
                    app_inputoml.set_leg2_insid(insid);
                    app_inputoml.invoke_leg2_insid_changed();
                }
            }
        }
    }

    /// 双击了组合持仓
    fn on_sel_comb_position(app_weak: &Weak<App>) {}

    /// 第1腿合约编码有变动
    fn on_leg1_insid_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();
        inputoml_datas.input_leg1_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        let leg1_insid = app_inputoml.get_leg1_insid();
        let ins = {
            common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins_by_insid(&leg1_insid)
                .unwrap_or_default()
        };

        app_inputoml.invoke_clear_leg2_info();

        if ins.insid.is_empty() {
            app_inputoml.set_leg1_vol("0".into());
            app_inputoml.set_leg1_insname(if leg1_insid.is_empty() {
                "".into()
            } else {
                "无效合约1".into()
            });
            app_inputoml.set_leg1_has_error(true);
            return false;
        }

        app_inputoml.set_leg1_insname(ins.symbol.clone().into());
        app_inputoml.set_leg1_has_error(false);

        let inputoml_datas = INPUTOML_DATAS.get().unwrap();
        inputoml_datas.need_update_leg2_insid_model.store(true, Ordering::Relaxed);

        true
    }

    /// 第2腿合约编码有变动
    fn on_leg2_insid_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        let leg2_insid = app_inputoml.get_leg2_insid();

        if let Some(ins) = {
            common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins_by_insid(&leg2_insid)
        } {
            app_inputoml.set_leg2_insname(ins.symbol.clone().into());
            app_inputoml.set_leg2_has_error(false);
        } else {
            app_inputoml.set_leg2_insname(if leg2_insid.is_empty() {
                "".into()
            } else {
                "无效合约2".into()
            });
            app_inputoml.set_leg2_vol("0".into());
            app_inputoml.set_leg2_has_error(true);
        }

        true
    }

    /// 报单数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        let volume = app_inputoml.get_volume().replace(",", "");
        if volume.is_empty() {
            app_inputoml.set_volume_has_error(true);
            return false;
        }

        if volume.trim().parse::<i32>().unwrap_or_default() <= 0 {
            app_inputoml.set_volume_has_error(true);
            return false;
        }

        app_inputoml.set_volume_has_error(false);

        true
    }

    // 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        if app_inputoml.get_volume_has_error() {
            app_inputoml.set_volume("1".into());
            app_inputoml.set_volume_has_error(false);
            return;
        }

        let volume_str = app_inputoml.get_volume().replace(",", "");
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            return;
        }

        if upflag {
            volume += 1;
        } else {
            volume -= 1;
            if volume <= 0 {
                return;
            }
        }

        app_inputoml.set_volume(slint::format!("{}", volume));
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();

        if app.global::<OptTrd_InputOml>().get_is_comb() {
            app_set.set_trdreqtip_combinsert(checked);
            CFG.get().unwrap().write().unwrap().com.ReqTradeTip.CombInsert = checked;
        } else {
            app_set.set_trdreqtip_combcancecl(checked);
            CFG.get().unwrap().write().unwrap().com.ReqTradeTip.CombAction = checked;
        }
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        // 资金账户
        let accid = {
            let accid = app_inputoml.get_accountid();
            if accid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "资金账户不能为空".into());
                return;
            }

            accid
        };

        // 是否组合
        let is_comb = app_inputoml.get_is_comb();

        // 组合编码(仅解除时有效)
        let combtradeid = {
            let combtradeid = app_inputoml.get_combtradeid();
            if !is_comb && combtradeid.is_empty() {
                show_input_msg_box(
                    &app,
                    1,
                    Default::default(),
                    "解组合时, 组合编码不能为空\n\n鼠标左键双击要解除的组合持仓自动填充".into(),
                );
                return;
            }
            combtradeid
        };

        // 组合策略
        let strategy = app_inputoml.get_strategyid_idx() + 1;

        // 单腿合约编码
        let (insid1, insid2) = {
            let leg1_insid = app_inputoml.get_leg1_insid();
            let leg2_insid = app_inputoml.get_leg2_insid();

            if leg1_insid.is_empty() || app_inputoml.get_leg1_has_error() {
                show_input_msg_box(&app, 1, Default::default(), "获取第1腿合约错误".into());
                return;
            }

            if leg2_insid.is_empty() || app_inputoml.get_leg2_has_error() {
                show_input_msg_box(&app, 1, Default::default(), "获取第2腿合约错误".into());
                return;
            }

            (Instrument::parse_id(&leg1_insid).0, Instrument::parse_id(&leg2_insid).0)
        };

        // 组合数量
        let volume = app_inputoml.get_volume().replace(",", "");
        let volume = volume.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            show_input_msg_box(&app, 1, Default::default(), "组合数量必须大于0".into());
            return;
        }

        if need_confirm {
            let need_confirm = {
                if is_comb {
                    CFG.get().unwrap().read().unwrap().com.ReqTradeTip.CombInsert
                } else {
                    CFG.get().unwrap().read().unwrap().com.ReqTradeTip.CombAction
                }
            };
            if need_confirm {
                if is_comb {
                    show_input_msg_box(
                        &app,
                        100,
                        "组合请求确认".into(),
                        slint::format!(
                            "资金账户:{}\n\n第1腿合约:{}({})    第2腿合约:{}({})\n数量:{}\n组合策略:{}\n指令方向:{}",
                            accid,
                            insid1,
                            app_inputoml.get_leg1_dir(),
                            insid2,
                            app_inputoml.get_leg2_dir(),
                            volume,
                            app_inputoml.get_strategyid(),
                            "组合"
                        ),
                    );
                } else {
                    show_input_msg_box(
                        &app,
                        100,
                        "解组合请求确认".into(),
                        slint::format!(
                            "资金账户:{}\n\n第1腿合约:{}({})    第2腿合约:{}({})\n数量:{}\n组合策略:{}\n指令方向:{}\n解组合编码:{}",
                            accid,
                            insid1, app_inputoml.get_leg1_dir(),
                            insid2, app_inputoml.get_leg2_dir(),
                            volume,
                            app_inputoml.get_strategyid(),
                            "解组合",
                            combtradeid,
                        ),
                    );
                }

                return;
            }
        }

        // 转换成ctp格式的
        let (insid, dir) = match strategy {
            TIType::STRATEGY_CNSJC => (std::format!("{insid1}&{insid2}"), CTPType::SIDE_BUY),
            TIType::STRATEGY_PXSJC => (std::format!("{insid1}&{insid2}"), CTPType::SIDE_BUY),
            TIType::STRATEGY_PNSJC => (std::format!("{insid2}&{insid1}"), CTPType::SIDE_SELL),
            TIType::STRATEGY_CXSJC => (std::format!("{insid2}&{insid1}"), CTPType::SIDE_SELL),
            TIType::STRATEGY_KS => (std::format!("{insid1}&{insid2}"), CTPType::SIDE_SELL),
            TIType::STRATEGY_KKS => (std::format!("{insid1}&{insid2}"), CTPType::SIDE_SELL),
            _ => {
                show_input_msg_box(&app, 1, Default::default(), "转换成CTP格式错误".into());
                return;
            }
        };

        let req = CtpReqInputCombActionField {
            HedgeFlag: CTPType::HEDGE_SPECULATION,
            InvestorID: accid.to_string(),
            Volume: volume,
            CombDirection: if is_comb { CTPType::COMBED } else { CTPType::COMBED_UN },
            ComTradeID: if is_comb { "".to_string() } else { combtradeid.to_string() },
            Direction: dir,
            InstrumentID: insid,
            ..Default::default()
        };
        if let Err(err) = TRADE_OPT_API.get().unwrap().read().unwrap().req_combactioninput(0, &req) {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("请求{}失败\n\n错误信息: {}", if is_comb { "组合" } else { "解除组合" }, err),
            );
        }
    }
}

/// 组合预览事件
impl InputOml {
    /// 第1腿合约编码有变动
    fn on_op_leg1_insid_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();
        inputoml_datas
            .input_preview_leg1_instid_changed
            .store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        app_inputoml.invoke_clear_op_leg2_info();

        let leg1_insid = app_inputoml.get_op_leg1_insid();
        let ins = {
            common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins_by_insid(&leg1_insid)
                .unwrap_or_default()
        };
        if ins.insid.is_empty() {
            app_inputoml.set_op_leg1_insname("".into());
            app_inputoml.set_op_leg1_has_error(true);
            return false;
        }

        app_inputoml.set_op_leg1_insname(ins.symbol.clone().into());
        app_inputoml.set_op_leg1_has_error(false);

        // 填充第2腿合约列表
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();
        let leg2_insid_vec = super::optcomb::OptComb::get_leg2_insid(
            app_inputoml.get_op_strategyid_idx() + 1,
            ins.exchid,
            &ins.insid,
            &inputoml_datas.all_opt_insid_map,
        );
        let leg2_insid_items: Rc<VecModel<LineItem>> = Rc::new(VecModel::default());
        leg2_insid_vec.iter().for_each(|ins_info| {
            leg2_insid_items.push(LineItem {
                text: ins_info.0.clone().into(),
                remark: ins_info.1.clone().into(),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(leg2_insid_items.sort_by(move |a, b| a.text.cmp(&b.text)));
        if 0 == sort_model.row_count() {
            app_inputoml.set_op_leg2_placeholder_text("没有满足条件的合约".into());
        } else {
            app_inputoml.set_op_leg2_placeholder_text("请选择".into());
        }
        app_inputoml.set_op_leg2_insid_model(sort_model.into());

        true
    }

    /// 第2腿合约编码有变动
    fn on_op_leg2_insid_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputoml = app.global::<OptTrd_InputOml>();

        let leg2_insid = app_inputoml.get_op_leg2_insid();

        if let Some(ins) = {
            common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins_by_insid(&leg2_insid)
        } {
            app_inputoml.set_op_leg2_insname(ins.symbol.clone().into());
            app_inputoml.set_op_leg2_has_error(false);
        } else {
            app_inputoml.set_op_leg2_insname("无效合约2".into());
            app_inputoml.set_op_leg2_has_error(true);
        }

        true
    }
}

/// 更新UI
impl InputOml {
    /// 更新错误信息
    async fn update_errmsg(&self, app_weak: &Weak<App>) {
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();

        let is_err_inputoml = inputoml_datas
            .is_err_inputoml
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputoml {
            return;
        }

        let rsp = { inputoml_datas.rsp_inputoml.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_input_msg_box(
                &app,
                1,
                if rsp.ComTradeID.is_empty() {
                    "响应失败"
                } else {
                    "解组合响应失败"
                }
                .into(),
                if rsp.ComTradeID.is_empty() {
                    slint::format!("错误码:{}\n错误信息:{}", rsp.ec, rsp.em)
                } else {
                    slint::format!("解组合编码:{}\n\n错误码:{}\n错误信息:{}", rsp.ComTradeID, rsp.ec, rsp.em)
                },
            );
        });
    }

    /// 更新输入信息
    async fn update_input_info(&self, app_weak: &Weak<App>) {
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();

        let need_update_leg1_insid_model = inputoml_datas
            .need_update_leg1_insid_model
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        let need_update_leg2_insid_model = inputoml_datas
            .need_update_leg2_insid_model
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        let (long_pos_insid_set_clone, short_pos_insid_set_clone) =
            if need_update_leg1_insid_model || need_update_leg2_insid_model {
                (
                    inputoml_datas.long_pos_insid_map.clone(),
                    inputoml_datas.short_pos_insid_map.clone(),
                )
            } else {
                (Arc::new(InsIDMap::new()), Arc::new(InsIDMap::new()))
            };

        let pos_map_clone = inputoml_datas.pos_map.clone();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_inputoml = app.global::<OptTrd_InputOml>();

            // 设置第1腿合约的下拉列表
            if need_update_leg1_insid_model {
                let leg1_insid_items: Rc<VecModel<LineItem>> = Rc::new(VecModel::default());
                if CTPType::POSDIR_LONG == app_inputoml.get_leg1_dir_i32() {
                    long_pos_insid_set_clone.iter().for_each(|it| {
                        leg1_insid_items.push(crate::slintui::LineItem {
                            text: slint::format!("{}", it.key()),
                            remark: slint::format!("{}", it.value()),
                            ..Default::default()
                        });
                    });
                } else {
                    short_pos_insid_set_clone.iter().for_each(|it| {
                        leg1_insid_items.push(crate::slintui::LineItem {
                            text: slint::format!("{}", it.key()),
                            remark: slint::format!("{}", it.value()),
                            ..Default::default()
                        });
                    });
                }
                let sort_model = Rc::new(leg1_insid_items.sort_by(move |a, b| a.text.cmp(&b.text)));
                app_inputoml.set_leg1_insid_model(sort_model.clone().into());
                app_inputoml.set_all_leg1_insid_model(sort_model.into());
            }

            // 第1腿合约信息
            let ins1 = {
                let leg1_insid = app_inputoml.get_leg1_insid();

                common::global::INS
                    .get()
                    .unwrap()
                    .read()
                    .unwrap()
                    .get_ins_by_insid(leg1_insid.as_str())
                    .unwrap_or_default()
            };
            if ins1.insid.is_empty() {
                return;
            }

            // 第2腿合约的下拉列表
            if need_update_leg2_insid_model {
                let strategy = app_inputoml.get_strategyid_idx() + 1;
                let leg2_insid_vec = super::optcomb::OptComb::get_leg2_insid(
                    strategy,
                    ins1.exchid,
                    &ins1.insid,
                    if CTPType::POSDIR_LONG == app_inputoml.get_leg2_dir_i32() {
                        &long_pos_insid_set_clone
                    } else {
                        &short_pos_insid_set_clone
                    },
                );

                let leg2_insid_items: Rc<VecModel<LineItem>> = Rc::new(VecModel::default());
                leg2_insid_vec.iter().for_each(|ins_info| {
                    leg2_insid_items.push(LineItem {
                        text: ins_info.0.clone().into(),
                        remark: ins_info.1.clone().into(),
                        ..Default::default()
                    });
                });
                let sort_model = Rc::new(leg2_insid_items.sort_by(move |a, b| a.text.cmp(&b.text)));
                let sort_model_cnt = sort_model.row_count() as i32;
                if 0 == sort_model_cnt {
                    app_inputoml.set_leg2_placeholder_text("没有满足条件的持仓合约".into());
                } else {
                    app_inputoml.set_leg2_placeholder_text("请选择".into());
                }
                //app_inputoml.set_visible_leg2_ins_cnt(sort_model_cnt);
                app_inputoml.set_leg2_insid_model(sort_model.into());
            }

            // 资金账户
            let accid = app.global::<OptTrd_Com>().get_accountid();

            // 查询第1腿合约的数量
            {
                let key = std::format!(
                    "{}{}{}{}{}",
                    accid,
                    ins1.exchname,
                    ins1.insid,
                    app_inputoml.get_leg1_dir_i32(),
                    CTPType::HEDGE_SPECULATION
                );
                if let Some(pos) = pos_map_clone.get(&key) {
                    app_inputoml.set_leg1_vol(pos.vol.to_string().into());
                } else {
                    app_inputoml.set_leg1_vol("0".into());
                }
            }

            // 查询第2腿合约的数量
            {
                let leg2_insid = app_inputoml.get_leg2_insid();
                let (insid, _) = Instrument::parse_id(&leg2_insid);

                let key = std::format!(
                    "{}{}{}{}{}",
                    accid,
                    ins1.exchname,
                    insid,
                    app_inputoml.get_leg2_dir_i32(),
                    CTPType::HEDGE_SPECULATION
                );
                if let Some(pos) = pos_map_clone.get(&key) {
                    app_inputoml.set_leg2_vol(pos.vol.to_string().into());
                } else {
                    app_inputoml.set_leg2_vol("0".into());
                }
            }
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新报单响应错误信息
        self.update_errmsg(&app_weak).await;

        // 更新输入信息
        self.update_input_info(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let inputoml_datas = INPUTOML_DATAS.get().unwrap();
        let is_changed = (
            inputoml_datas
                .input_leg1_instid_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok(),
            inputoml_datas
                .input_preview_leg1_instid_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok(),
        );
        if is_changed.0 || is_changed.1 {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_inputoml = app.global::<OptTrd_InputOml>();

                if is_changed.0 {
                    let leg1_insid = app_inputoml.get_leg1_insid();

                    if leg1_insid.len() > 8 + 5 {
                        // 目前个股期权单合约的编码长度还没有超过8位的
                        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                        app_inputoml.set_leg1_insid_model(items.into());
                        return;
                    }

                    let all_insid_data = app_inputoml.get_all_leg1_insid_model();

                    if leg1_insid.is_empty() {
                        app_inputoml.set_leg1_insid_model(all_insid_data);
                        return;
                    }

                    let mut find_flag = false;
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    for i in 0..all_insid_data.row_count() {
                        let li = all_insid_data.row_data(i).unwrap();
                        if li.text.starts_with(leg1_insid.as_str()) {
                            items.push(li);
                            find_flag = true;
                        } else {
                            if find_flag {
                                break;
                            }
                        }
                    }

                    app_inputoml.set_leg1_insid_model(items.into());
                }

                if is_changed.1 {
                    let leg1_insid = app_inputoml.get_op_leg1_insid();

                    if leg1_insid.len() > 8 + 5 {
                        // 目前个股期权单合约的编码长度还没有超过8位的
                        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                        app_inputoml.set_op_leg1_insid_model(items.into());
                        return;
                    }

                    let all_insid_data = app_inputoml.get_all_op_leg1_insid_model();

                    if leg1_insid.is_empty() {
                        app_inputoml.set_op_leg1_insid_model(all_insid_data);
                        return;
                    }

                    let mut find_flag = false;
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    for i in 0..all_insid_data.row_count() {
                        let li = all_insid_data.row_data(i).unwrap();
                        if li.text.starts_with(leg1_insid.as_str()) {
                            items.push(li);
                            find_flag = true;
                        } else {
                            if find_flag {
                                break;
                            }
                        }
                    }

                    app_inputoml.set_op_leg1_insid_model(items.into());
                }
            });
        }
    }
}
