use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{self, config::Config, ctptype::CTPType, global::TRADE_OPT_API, openselfile, ticonvert::TIConvert, titype::TIType},
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::{
    position_trade_ctp::{CtpPositionField, CtpReqQryPositionField},
    trade_req_ctp::CtpReqInputCombActionField,
};

type CtpPositionMap = dashmap::DashMap<String, CtpPositionField>;
type PosInsIDSet = dashmap::DashSet<String>;

/// 持仓数据
struct PositionDatas {
    /// 数据
    data_map: Arc<CtpPositionMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 所有的持仓合约列表
    insid_set: Arc<PosInsIDSet>,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,

    /// 合约编码是否有编辑过 - 如果编辑过,需要重新更新下拉列表
    insid_edited: AtomicBool,

    /// 是否有新的持仓合约 - 如果有,需要重新更新下拉列表
    new_pos_insid: AtomicBool,
}
impl PositionDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpPositionMap::new()),
            data_changed: AtomicBool::new(false),
            insid_set: Arc::new(PosInsIDSet::new()),

            filter_clicked: AtomicBool::new(false),
            insid_edited: AtomicBool::new(false),
            new_pos_insid: AtomicBool::new(false),
        }
    }
}
static POSITION_DATAS: OnceLock<PositionDatas> = OnceLock::new();

// 持仓
pub(super) struct Position {}

/// 构建与初始化
impl Position {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = POSITION_DATAS.set(PositionDatas::new());

        let app_pos = app.global::<OptTrd_Position>();

        let app_weak = app.as_weak();
        app_pos.on_sort_ascending(move |column_index| Position::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_sort_descending(move |column_index| Position::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_insid_text_changed(move || Position::on_insid_text_changed(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_filter_changed(move || Position::on_filter_changed(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_export_clicked(move |etype| Position::on_export_clicked(&app_weak, etype));

        let app_weak = app.as_weak();
        app_pos.on_get_row_data_color(move |row_index, column_index, data| {
            Position::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_pos.on_pre_deal_covered_convert(move || Position::on_pre_deal_covered_convert(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_corvered_convert_clicked(move || Position::on_corvered_convert_clicked(&app_weak));

        log::trace!("Position init completed");
    }
}

/// 与API的交互
impl Position {
    /// 请求查询持仓信息
    pub fn qry_position(&self) {
        let req = CtpReqQryPositionField::default();

        let api = TRADE_OPT_API.get().unwrap().read().unwrap();
        if let Err(err) = api.req_qry_investorposition(0, &req) {
            log::warn!("req_qry_investorposition failed. {}", err);
        }
    }

    /// 响应查询持仓信息
    pub fn on_rsp_qry_pos(&self, pos: CtpPositionField) {
        super::input_oml::InputOml::update_position(&pos);

        let pos_datas = POSITION_DATAS.get().unwrap();

        if !pos_datas.insid_set.contains(&pos.InstrumentID) {
            pos_datas.insid_set.insert(pos.InstrumentID.clone());
            POSITION_DATAS.get().unwrap().new_pos_insid.store(true, Ordering::Relaxed);
        }

        let mut insert_flag = true;
        let key = std::format!(
            "{}{}{}{}{}{}",
            pos.InvestorID,
            pos.ExchangeID,
            pos.InstrumentID,
            pos.PosiDirection,
            pos.HedgeFlag,
            pos.InvestUnitID,
        );

        if pos_datas.data_map.contains_key(&key) {
            let prepos = pos_datas.data_map.get(&key).unwrap();
            if pos == *prepos {
                insert_flag = false;
            }
        }

        if insert_flag {
            pos_datas.data_map.insert(key, pos);
            pos_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

/// 事件
impl Position {
    /// 升序
    fn on_sort_ascending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_pos = app.global::<OptTrd_Position>();
        let row_data = app_pos.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_pos.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_pos = app.global::<OptTrd_Position>();
        let row_data = app_pos.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_pos.set_row_data(sort_model.into());
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        match column_index {
            // 持仓方向
            4 => {
                if data.as_str().contains("多") || data.as_str().contains("Long") {
                    return nc.get_error();
                }
                nc.get_normal()
            }
            // 转入量
            15 => match data.parse::<i32>() {
                Ok(num) if num > 0 => nc.get_normal(),
                _ => nc.get_default(),
            },
            // 转出量
            16 => match data.parse::<i32>() {
                Ok(num) if num > 0 => nc.get_caution(),
                _ => nc.get_default(),
            },
            // 备兑类型
            17 => {
                if "备兑" == data.as_str() {
                    return nc.get_error();
                }
                nc.get_default()
            }
            _ => nc.get_default(),
        }
    }

    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) {
        POSITION_DATAS.get().unwrap().insid_edited.store(true, Ordering::Relaxed);
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        POSITION_DATAS.get().unwrap().filter_clicked.store(true, Ordering::Relaxed);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("position_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出持仓失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "合约编码",
            "合约名称",
            "持仓方向",
            "持仓量",
            "实收保证金",
            "交易所保证金",
            "组合冻结",
            "在途冻结",
            "可平仓量",
            "今日持仓量",
            "上日持仓量",
            "开仓量",
            "平仓量",
            "转入量",
            "转出量",
            "备兑标志",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出持仓失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<OptTrd_Position>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let pos_datas = POSITION_DATAS.get().unwrap();
            let data_map_clone = pos_datas.data_map.clone();
            data_map_clone.iter().for_each(|pos| {
                let items = Position::get_row_items(&pos);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出持仓失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出持仓成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }

    /// 预处理备兑转换
    fn on_pre_deal_covered_convert(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_pos = app.global::<OptTrd_Position>();

        let sel_exchid = app_pos.get_sel_cc_exchid();
        let sel_insid = app_pos.get_sel_cc_insid();

        let ins = {
            common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins(TIConvert::exch_id(&sel_exchid), &sel_insid)
                .unwrap_or_default()
        };
        if TIType::PRODUCT_OPT == ins.producttype && TIType::OPTIONS_CALL == ins.optionstype {
            let covered = app_pos.get_sel_cc_covered();
            if "非备兑" == covered.as_str() {
                app_pos.set_show_covered_type(1); // 转备兑仓
                return true;
            } else if "备兑" == covered.as_str() {
                #[cfg(not(debug_assertions))]
                return false;

                app_pos.set_show_covered_type(2); // 转普通仓(目前生产上没有这个功能)
                return true;
            }
        }

        false
    }

    /// 备兑转换
    fn on_corvered_convert_clicked(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();

        let app_pos = app.global::<OptTrd_Position>();

        let vol = app_pos.get_sel_cc_volume().trim().parse::<i32>().unwrap_or_default();
        if vol <= 0 {
            show_msg_box(&app, 1, Default::default(), "请输入有效的转换数量".into());
            return;
        }

        let mut req = CtpReqInputCombActionField::default();

        let covered_type = app_pos.get_show_covered_type();
        req.HedgeFlag = if 1 == covered_type {
            CTPType::HEDGE_SPECULATION
        } else {
            CTPType::HEDGE_COVERED
        };

        req.InvestorID = app_pos.get_sel_cc_accid().into();
        req.InstrumentID = app_pos.get_sel_cc_insid().into();
        req.CombDirection = CTPType::COMBED;
        req.Direction = CTPType::SIDE_SELL;
        req.Volume = vol;

        if let Err(err) = TRADE_OPT_API.get().unwrap().read().unwrap().req_combactioninput(0, &req) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!(
                    "请求{}失败\n\n错误信息: {}",
                    if 1 == covered_type { "转备兑仓" } else { "转普通仓" },
                    err
                ),
            );
        }
    }
}

/// 更新UI
impl Position {
    /// 获取一行数据
    fn get_row_items(pos: &CtpPositionField) -> Rc<VecModel<SharedString>> {
        let mut insname = "".to_owned();
        {
            if let Some(ins) = common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins(TIConvert::exch_id(&pos.ExchangeID), &pos.InstrumentID)
            {
                insname = ins.symbol.clone();
            }
        };

        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 资金账号
        items.push(pos.InvestorID.clone().into());

        // 交易所
        items.push(pos.ExchangeID.clone().into());

        // 合约编码
        items.push(pos.InstrumentID.clone().into());

        // 合约名称
        items.push(insname.into());

        // 持仓方向
        items.push(TIConvert::ctp_posdir(pos.PosiDirection).into());

        // 持仓量
        items.push(TIConvert::format_i(pos.Position).into());

        // 实收保证金
        items.push(TIConvert::format_f(pos.UseMargin, Config::AMT_DIGITS).into());

        // 交易所保证金
        items.push(TIConvert::format_f(pos.ExchangeMargin, Config::AMT_DIGITS).into());

        // 组合冻结
        items.push(TIConvert::format_i(pos.CombPosition).into());

        // 在途冻结
        let mut covered = TIType::COVERED_UN;
        if CTPType::POSDIR_LONG == pos.PosiDirection {
            items.push(TIConvert::format_i(pos.LongFrozen).into());
        } else if CTPType::POSDIR_SHORT == pos.PosiDirection {
            items.push(TIConvert::format_i(pos.ShortFrozen).into());

            if CTPType::HEDGE_COVERED == pos.HedgeFlag {
                covered = TIType::COVERED;
            }
        } else {
            items.push("".into());
        }

        // 可平仓量
        items.push(TIConvert::format_i(pos.Position - pos.CombPosition - pos.LongFrozen - pos.ShortFrozen).into());

        // 今日持仓量
        items.push(TIConvert::format_i(pos.TodayPosition).into());

        // 上日持仓量
        items.push(TIConvert::format_i(pos.YdPosition).into());

        // 开仓量
        items.push(TIConvert::format_i(pos.OpenVolume).into());

        // 平仓量
        items.push(TIConvert::format_i(pos.CloseVolume).into());

        // 转入量
        items.push(TIConvert::format_i(pos.PosInVol).into());

        // 转出量
        items.push(TIConvert::format_i(pos.PosOutVol).into());

        // 备兑标志
        items.push(
            if TIType::COVERED_UN == covered {
                "非备兑"
            } else {
                "备兑"
            }
            .into(),
        );

        items
    }

    // 更新合约下拉列表
    async fn update_insid(&self, app_weak: &Weak<App>) {
        let pos_datas = POSITION_DATAS.get().unwrap();

        let insid_edited = pos_datas
            .insid_edited
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        let new_pos_insid = pos_datas
            .new_pos_insid
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        if !insid_edited && !new_pos_insid {
            return;
        }

        let insid_set_clone = pos_datas.insid_set.clone();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let pos = app.global::<OptTrd_Position>();
            let filter = pos.get_insid().trim().to_owned();
            if insid_edited || new_pos_insid {
                let insid_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                insid_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                insid_set_clone.iter().for_each(|insid| {
                    if insid.contains(&filter) {
                        insid_arr.push(ListViewItem {
                            text: insid.clone().into(),
                            ..Default::default()
                        });
                    }
                });

                pos.set_insid_model(insid_arr.into());
            }
        });
    }

    // 更新持仓
    async fn update_position(&self, app_weak: &Weak<App>) {
        let pos_datas = POSITION_DATAS.get().unwrap();

        let data_map_clone = {
            if pos_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                pos_datas.filter_clicked.store(false, Ordering::Relaxed);
                pos_datas.data_map.clone()
            } else {
                if pos_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    pos_datas.data_map.clone()
                } else {
                    Arc::new(CtpPositionMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_pos = app.global::<OptTrd_Position>();

            let filter_exchid = app_pos.get_exchid().as_str().to_owned();
            let filter_insid = app_pos.get_insid().trim().to_owned();
            let filter_posdir = {
                let dir = app_pos.get_posdir_idx();
                if dir <= 0 {
                    0
                } else if 1 == dir {
                    CTPType::POSDIR_LONG // 多头
                } else {
                    CTPType::POSDIR_SHORT // 空头
                }
            };

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|pos| {
                if !filter_exchid.is_empty() {
                    if pos.ExchangeID != filter_exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !pos.InstrumentID.contains(&filter_insid) {
                        return;
                    }
                }

                if 0 != filter_posdir {
                    if pos.PosiDirection != filter_posdir {
                        return;
                    }
                }

                let items = Position::get_row_items(&pos);
                row_data.push(items.into());
            });

            let asc_column_index = app_pos.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_pos.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            app_pos.set_row_data(row_data.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新合约下拉列表
        self.update_insid(&app_weak).await;

        // 更新持仓
        self.update_position(&app_weak).await;
    }
}
