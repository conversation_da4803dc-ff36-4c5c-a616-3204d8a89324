use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::PositionStkMap,
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

/// 持仓数据
struct PositionDatas {
    filter_changed: AtomicBool,
}
impl PositionDatas {
    pub fn new() -> Self {
        Self {
            filter_changed: AtomicBool::new(false),
        }
    }
}
static POSITION_DATAS: OnceLock<PositionDatas> = OnceLock::new();

/// 持仓
pub(in crate::ui::riskstk::credit) struct Position {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<PositionStkMap>,
}

impl Position {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(PositionStkMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let _ = POSITION_DATAS.set(PositionDatas::new());

        let app_pos = app.global::<StkRskCredit_Position>();

        let app_weak = app.as_weak();
        app_pos.on_get_row_data_color(move |row_index, column_index, data| {
            Position::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_pos.on_sort_ascending(move |column_index| Position::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_sort_descending(move |column_index| Position::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_filter_changed(move || Position::on_filter_changed(&app_weak));

        log::trace!("Position init completed");
    }
}

impl Position {
    /// 请求查询持仓信息
    pub fn qry_position(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_position() };
        if ret.is_empty() {
            return;
        }

        for (key, acc) in ret {
            self.data_map.insert(key, acc);
        }
        self.data_changed.store(true, Ordering::Relaxed);
    }
}

impl Position {
    /// 过滤
    fn on_filter_changed(app_weak: &Weak<RiskStkCreditDetailPage>) {
        POSITION_DATAS.get().unwrap().filter_changed.store(true, Ordering::Relaxed);
    }

    /// 升序
    fn on_sort_ascending(app_weak: &Weak<RiskStkCreditDetailPage>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<StkRskCredit_Position>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<RiskStkCreditDetailPage>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<StkRskCredit_Position>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(
        app_weak: &Weak<RiskStkCreditDetailPage>,
        row_index: i32,
        column_index: i32,
        data: SharedString,
    ) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        nc.get_default()
    }
}

impl Position {
    /// 更新持仓信息
    pub async fn update(&self, app_weak: Weak<RiskStkCreditDetailPage>) {
        let pos_datas = POSITION_DATAS.get().unwrap();

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                pos_datas.filter_changed.store(false, Ordering::Relaxed);
                self.data_map.clone()
            } else {
                if pos_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    self.data_map.clone()
                } else {
                    Arc::new(PositionStkMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            let app_pos = app.global::<StkRskCredit_Position>();

            let filter_accid = app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned();
            if filter_accid.is_empty() {
                app_pos.set_row_data(row_data.into());
                return;
            }

            let filter_exchid = app_pos.get_filter_exchid().to_string();
            let filter_insid = app_pos.get_filter_insid().to_string();

            data_map_clone.iter().for_each(|pos| {
                if filter_accid != pos.AccountID {
                    return;
                }

                let exchid = TIConvert::exch_name(pos.ExchID);
                if !filter_exchid.is_empty() {
                    if exchid != filter_exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !pos.InstrumentID.contains(&filter_insid) {
                        return;
                    }
                }

                let mut tp1 = 1;
                let mut insname = "".to_owned();
                let mut digits = Config::STK_PRICE_DIGITS;
                {
                    if let Some(ins) = crate::common::global::INS
                        .get()
                        .unwrap()
                        .read()
                        .unwrap()
                        .get_ins(pos.ExchID, &pos.InstrumentID)
                    {
                        tp1 = ins.tp1;
                        insname = ins.name.clone();
                        digits = ins.ptw as usize;
                    }
                };

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账号
                items.push(pos.AccountID.clone().into());

                // 合约编码
                items.push(pos.InstrumentID.clone().into());

                // 持仓量
                items.push(TIConvert::format_i(pos.Long.Position).into());

                // 当前可用
                let mut apos = pos.Long.PrePosition - pos.Long.CloseVolume - pos.Long.FrozenPosition + pos.Long.TransVolume
                    - pos.Long.XYRqRePos;
                if 0 == tp1 {
                    apos += pos.Long.OpenVolume;
                }
                items.push(TIConvert::format_i(apos).into());

                // 上日持仓量
                items.push(TIConvert::format_i(pos.Long.PrePosition).into());

                // 当日买入
                items.push(TIConvert::format_i(pos.Long.OpenVolume).into());

                // 当日卖出
                items.push(TIConvert::format_i(pos.Long.CloseVolume).into());

                // 划转量
                items.push(TIConvert::format_i(pos.Long.TransVolume).into());

                // 冻结持仓量
                items.push(TIConvert::format_i(pos.Long.FrozenPosition).into());

                // 持仓均价
                items.push(TIConvert::format_f(pos.Long.AvgPrice, digits).into());

                // 持仓成本
                items.push(TIConvert::format_f(pos.Long.Position as f64 * pos.Long.AvgPrice, Config::AMT_DIGITS).into());

                // 最新价
                items.push(TIConvert::format_f(pos.LPrice, Config::AMT_DIGITS).into());

                // 持仓市值
                items.push(TIConvert::format_f(pos.Long.PosValue, Config::AMT_DIGITS).into());

                // 持仓盈亏
                items.push(TIConvert::format_f(pos.Long.PosProfit, Config::AMT_DIGITS).into());

                // 现券还券量
                items.push(TIConvert::format_i(pos.Long.XYRqRePos).into());

                // 融资已还折算量
                items.push(TIConvert::format_i(pos.Long.XYRzRePos as i32).into());

                // 融资未还折算量
                items.push(TIConvert::format_i(pos.Long.XYRzNdPos as i32).into());

                // 折算率
                items.push(TIConvert::format_f(pos.Long.XYDiscRatio, Config::AMT_DIGITS).into());

                // 充抵保证金
                items.push(TIConvert::format_f(pos.Long.XYCdMargin, Config::AMT_DIGITS).into());

                // 交易所
                items.push(exchid.into());

                // 合约名称
                items.push(insname.into());

                row_data.push(items.into());
            });

            let asc_column_index = app_pos.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_pos.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            app_pos.set_row_data(row_data.into());
        });
    }
}
