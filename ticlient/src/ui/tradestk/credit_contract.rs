use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{
        self,
        config::Config,
        global::{TOKIO_RT, TRADE_STK_API, TRADE_STK_BUF},
        openselfile,
        ticonvert::TIConvert,
    },
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::{
    position_trade_ctp::CtpCreditContractField,
    trade_req_ctp::{
        CtpReqInputOrderField,
        TradeType::{EContingentCondition, EDirection, EForceCloseReason, EOrderPriceType, ETimeCondition, EVolumeCondition},
    },
};

type CtpCreditContractMap = dashmap::DashMap<String, CtpCreditContractField>;
type StringSet = dashmap::DashSet<String>;

/// 信用合约数据
struct CreditContractDatas {
    /// 数据
    data_map: Arc<CtpCreditContractMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 所有的持仓合约列表
    insid_set: Arc<StringSet>,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl CreditContractDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpCreditContractMap::new()),
            data_changed: AtomicBool::new(false),
            insid_set: Arc::new(StringSet::new()),

            filter_clicked: AtomicBool::new(false),
        }
    }
}
static CREDIT_CONTRACT_DATAS: OnceLock<CreditContractDatas> = OnceLock::new();

/// 信用合约
pub(super) struct CreditContract {}

/// 构建与初始化
impl CreditContract {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = CREDIT_CONTRACT_DATAS.set(CreditContractDatas::new());

        let app_cc = app.global::<StkTrd_CreditContract>();

        let app_weak = app.as_weak();
        app_cc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditContract::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        // 过滤
        let app_weak = app.as_weak();
        app_cc.on_filter_changed(move || CreditContract::on_filter_changed(&app_weak));

        // 导出
        let app_weak = app.as_weak();
        app_cc.on_export_clicked(move |etype| CreditContract::on_export_clicked(&app_weak, etype));

        // 直接还款
        let app_weak = app.as_weak();
        app_cc.on_reamt_clicked(move || CreditContract::on_reamt_clicked(&app_weak));

        // 现券还券
        let app_weak = app.as_weak();
        app_cc.on_repos_clicked(move || CreditContract::on_repos_clicked(&app_weak));

        log::trace!("Credit contract init completed");
    }
}

/// 查询
impl CreditContract {
    /// 查询信用合约
    pub fn qry_credit_contract(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_credit_contract() };
        if ret.is_empty() {
            return;
        }

        let cc_datas = CREDIT_CONTRACT_DATAS.get().unwrap();

        for (key, cc) in ret {
            if !cc_datas.insid_set.contains(&cc.InsID) {
                cc_datas.insid_set.insert(cc.InsID.clone());
            }

            cc_datas.data_map.insert(key, cc);
        }
        cc_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditContract {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();

        let nc = app.global::<NotifyColor>();

        if 4 == column_index {
            if "融资买入" == data.as_str() {
                return nc.get_caution();
            } else {
                return nc.get_normal();
            }
        }

        // 已还
        if 10 <= column_index && column_index <= 16 {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data > 0.0 {
                return nc.get_normal();
            }
        }

        // 未还
        if 17 <= column_index && column_index <= 20 {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data > 0.0 {
                return nc.get_error();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        CREDIT_CONTRACT_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("credit_contract_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出合约失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "开仓日期",
            "到期日期",
            "股东代码",
            "证券代码",
            "合约类型",
            "委托编号",
            "合约数量",
            "合约金额",
            "合约费用",
            "合约利息",
            "已还数量",
            "已还金额",
            "已还利息",
            "当日已还数量",
            "当日已还本金",
            "当日已还费用",
            "当日已还利息",
            "未还数量",
            "未还金额",
            "未还利息",
            "合约负债",
            "最新价",
            "折算率",
            "保证金率",
            "融资折算未还量",
            "融资盈亏",
            "融资保证金",
            "融券卖出金额",
            "融券盈亏",
            "融券保证金",
            "委托价格",
            "委托数量",
            "委托金额",
            "交易所",
            "证券名称",
            "资金账户",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出合约失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<StkTrd_CreditContract>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let cc_datas = CREDIT_CONTRACT_DATAS.get().unwrap();
            let data_map_clone = cc_datas.data_map.clone();
            data_map_clone.iter().for_each(|pos| {
                let items = CreditContract::get_row_items(&pos);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出合约失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出合约成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }

    /// 直接还款
    fn on_reamt_clicked(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_cc = app.global::<StkTrd_CreditContract>();
        app_cc.set_reamt_req_ret("".into());

        let accid = app_cc.get_reamt_accid();
        let exchid = app_cc.get_reamt_exchid();
        let insid = app_cc.get_reamt_insid();
        let amt = app_cc.get_reamt_amt().replace(",", "").trim().parse::<f64>().unwrap_or(0.0);

        if accid.is_empty() {
            show_msg_box(&app, 3, Default::default(), "资金账号不能为空".into());
            app_cc.set_reamt_ctrls_enabled(true);
            return;
        }
        if amt < 0.01 {
            show_msg_box(&app, 3, Default::default(), "还款金额不能小于0.01".into());
            app_cc.set_reamt_ctrls_enabled(true);
            return;
        }

        if !insid.is_empty() {
            if !common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .has_ins_with_exchname(&exchid, &insid)
            {
                show_msg_box(&app, 3, Default::default(), "指定的证券代码不存在".into());
                app_cc.set_reamt_ctrls_enabled(true);
                return;
            }
        }

        let mut req = CtpReqInputOrderField::default();
        req.CombOffsetFlag = "0".to_owned();
        req.CombHedgeFlag = "1".to_owned();
        req.ContingentCondition = EContingentCondition::Immediately.into();
        req.ForceCloseReason = EForceCloseReason::NotForceClose.into();
        req.InvestorID = accid.to_string();
        req.InstrumentID = insid.to_string();
        req.ExchangeID = exchid.to_string();
        req.LimitPrice = amt;
        req.VolumeTotalOriginal = 0;
        req.OrderPriceType = EOrderPriceType::LimitPrice.into();
        req.TimeCondition = ETimeCondition::GFD.into();
        req.VolumeCondition = EVolumeCondition::AV.into();
        req.Direction = EDirection::ReturnFund.into();

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let api = TRADE_STK_API.get().unwrap().read().await;
            let result = api.req_orderinput(0, &req).await;
            let err_msg = result.as_ref().err().map(|e| e.to_string());

            let _ = wapp.upgrade_in_event_loop(move |app| {
                let appcc = app.global::<StkTrd_CreditContract>();
                if let Some(err) = err_msg {
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("请求直接还款失败\n\n错误信息: {}", err),
                    );
                } else {
                    appcc.set_reamt_req_ret("发送请求成功".into());
                }

                appcc.set_reamt_ctrls_enabled(true);
            });
        });
    }

    /// 现券还券
    fn on_repos_clicked(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_cc = app.global::<StkTrd_CreditContract>();
        app_cc.set_repos_req_ret("".into());

        let accid = app_cc.get_repos_accid();
        let insid = app_cc.get_repos_insid();
        let vol = app_cc.get_repos_pos().replace(",", "").trim().parse::<i32>().unwrap_or(0);

        if accid.is_empty() {
            show_msg_box(&app, 3, Default::default(), "资金账号不能为空".into());
            app_cc.set_repos_ctrls_enabled(true);
            return;
        }
        if insid.is_empty() {
            show_msg_box(&app, 3, Default::default(), "证券代码不能为空".into());
            app_cc.set_repos_ctrls_enabled(true);
            return;
        }
        if vol <= 0 {
            show_msg_box(&app, 3, Default::default(), "还券数量必须大于0".into());
            app_cc.set_repos_ctrls_enabled(true);
            return;
        }

        if !insid.is_empty() {
            if !common::global::INS.get().unwrap().read().unwrap().has_ins_only_id(&insid) {
                show_msg_box(&app, 3, Default::default(), "证券代码不存在".into());
                app_cc.set_repos_ctrls_enabled(true);
                return;
            }
        }

        let mut req = CtpReqInputOrderField::default();
        req.CombOffsetFlag = "0".to_owned();
        req.CombHedgeFlag = "1".to_owned();
        req.ContingentCondition = EContingentCondition::Immediately.into();
        req.ForceCloseReason = EForceCloseReason::NotForceClose.into();
        req.InvestorID = accid.to_string();
        req.InstrumentID = insid.to_string();
        req.ExchangeID = "".to_string(); // 根据jinyi要求,界面上不出现市场(只展示合约编码),如果合约编码重复服务端会拒绝
        req.LimitPrice = 0.0;
        req.VolumeTotalOriginal = vol;
        req.OrderPriceType = EOrderPriceType::LimitPrice.into();
        req.TimeCondition = ETimeCondition::GFD.into();
        req.VolumeCondition = EVolumeCondition::AV.into();
        req.Direction = EDirection::ReturnStock.into();

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let api = TRADE_STK_API.get().unwrap().read().await;
            let result = api.req_orderinput(0, &req).await;
            let err_msg = result.as_ref().err().map(|e| e.to_string());

            let _ = wapp.upgrade_in_event_loop(move |app| {
                let appcc = app.global::<StkTrd_CreditContract>();
                if let Some(err) = err_msg {
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("请求现券还券失败\n\n错误信息: {}", err),
                    );
                } else {
                    appcc.set_repos_req_ret("发送请求成功".into());
                }

                appcc.set_repos_ctrls_enabled(true);
            });
        });
    }
}

/// 更新UI
impl CreditContract {
    /// 获取一行数据
    fn get_row_items(cc: &CtpCreditContractField) -> Rc<VecModel<SharedString>> {
        let mut insname = "".to_owned();
        let mut digits = Config::STK_PRICE_DIGITS;
        {
            if let Some(ins) = common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins(cc.ExchID, &cc.InsID)
            {
                insname = ins.name.clone();
                digits = ins.ptw as usize;
            }
        };

        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 开仓日期
        items.push(slint::format!("{}", cc.OpenDate));

        // 到期日期
        items.push(slint::format!("{}", cc.ExpireDate));

        // 股东代码
        items.push(cc.CliID.clone().into());

        // 证券代码
        items.push(cc.InsID.clone().into());

        // 合约类型
        items.push(TIConvert::side(cc.Side).into());

        // 委托编码
        items.push(cc.OrderSysID.clone().into());

        // 合约数量
        items.push(TIConvert::format_i(cc.Volume).into());

        // 合约金额
        items.push(TIConvert::format_f(cc.Amount, Config::AMT_DIGITS).into());

        // 合约手续费
        items.push(TIConvert::format_f(cc.Commi, Config::AMT_DIGITS).into());

        // 合约利息
        items.push(TIConvert::format_f(cc.Interest, Config::AMT_DIGITS).into());

        // 已还数量
        items.push(TIConvert::format_i(cc.ReVol).into());

        // 已还金额
        items.push(TIConvert::format_f(cc.ReAmt, Config::AMT_DIGITS).into());

        // 已还利息
        items.push(TIConvert::format_f(cc.ReInterest, Config::AMT_DIGITS).into());

        // 当日已还数量
        items.push(TIConvert::format_i(cc.TdReVol).into());

        // 当日已还金额
        items.push(TIConvert::format_f(cc.TdReAmt, Config::AMT_DIGITS).into());

        // 当日已还费用
        items.push(TIConvert::format_f(cc.TdReCommi, Config::AMT_DIGITS).into());

        // 当日已还利息
        items.push(TIConvert::format_f(cc.TdReInterest, Config::AMT_DIGITS).into());

        // 未还数量
        items.push(TIConvert::format_i(cc.NdVol).into());

        // 未还金额
        items.push(TIConvert::format_f(cc.NdAmt, Config::AMT_DIGITS).into());

        // 未还利息
        items.push(TIConvert::format_f(cc.NdInterest, Config::AMT_DIGITS).into());

        // 合约负债
        items.push(TIConvert::format_f(cc.NdAmt + cc.NdInterest, Config::AMT_DIGITS).into());

        // 最新价
        items.push(TIConvert::format_f(cc.LPrice, digits).into());

        // 折算率
        items.push(TIConvert::format_f(cc.DiscRatio, Config::AMT_DIGITS).into());

        // 保证金率
        items.push(TIConvert::format_f(cc.MarginRatio, Config::AMT_DIGITS).into());

        // 融资折算未还量
        items.push(TIConvert::format_i(cc.RzNdVol as i32).into());

        // 融资浮盈
        items.push(TIConvert::format_f(cc.RzBuy, Config::AMT_DIGITS).into());

        // 融资保证金
        items.push(TIConvert::format_f(cc.RzBuyMargin, Config::AMT_DIGITS).into());

        // 融券卖出金额
        items.push(TIConvert::format_f(cc.RqSellAmt, Config::AMT_DIGITS).into());

        // 融券浮盈
        items.push(TIConvert::format_f(cc.RqSell, Config::AMT_DIGITS).into());

        // 融券保证金
        items.push(TIConvert::format_f(cc.RqSellMargin, Config::AMT_DIGITS).into());

        // 委托价格
        items.push(TIConvert::format_f(cc.OrderPrice, digits).into());

        // 委托数量
        items.push(TIConvert::format_i(cc.OrderVolume).into());

        // 委托金额
        items.push(TIConvert::format_f(cc.OrderAmount, Config::AMT_DIGITS).into());

        // 交易所
        items.push(TIConvert::exch_name(cc.ExchID).into());

        // 合约名称
        items.push(insname.into());

        // 资金账号
        items.push(cc.AccID.clone().into());

        items
    }

    // 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let cc_datas = CREDIT_CONTRACT_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_cc = app.global::<StkTrd_CreditContract>();

            if cc_datas.insid_set.len() != app_cc.invoke_insid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                cc_datas.insid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_cc.set_insid_model(id_arr.into());
            }
        });
    }

    // 更新信用合约
    async fn update_credit_contract(&self, app_weak: &Weak<App>) {
        let cc_datas = CREDIT_CONTRACT_DATAS.get().unwrap();

        let data_map_clone = {
            if cc_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                cc_datas.filter_clicked.store(false, Ordering::Relaxed);
                cc_datas.data_map.clone()
            } else {
                if cc_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    cc_datas.data_map.clone()
                } else {
                    Arc::new(CtpCreditContractMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_cc = app.global::<StkTrd_CreditContract>();

            let filter_side = app_cc.get_side();
            let filter_exchid = TIConvert::exch_id(app_cc.get_exchid().as_str());
            let filter_insid = app_cc.get_insid().to_string();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|cc| {
                if filter_side > 0 {
                    if cc.Side != filter_side {
                        return;
                    }
                }

                if 0 != filter_exchid {
                    if cc.ExchID != filter_exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !cc.InsID.contains(&filter_insid) {
                        return;
                    }
                }

                let items = CreditContract::get_row_items(&cc);
                row_data.push(items.into());
            });

            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = slint::format!("{:>8}{:>8}{:>20}", r_a.row_data(1).unwrap(), r_a.row_data(0).unwrap(), r_a.row_data(5).unwrap());
                let c_b = slint::format!("{:>8}{:>8}{:>20}", r_b.row_data(1).unwrap(), r_b.row_data(0).unwrap(), r_b.row_data(5).unwrap());
                c_a.cmp(&c_b)
            }));

            app_cc.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新信用合约
        self.update_credit_contract(&app_weak).await;
    }
}
