import { Theme, Icons, GroupListView, GroupListViewItem, VerticalSideBar, Switch, Label, LineEdit, ComboBox } from "@zstk/lib.slint";
import { FutTrd_Setting } from "struct.slint";
import { AppCom } from "../struct.slint";
import { WndRec } from "../widgets/wndrec.slint";

// 设置 - 公共 - 主题
component SetComTheme {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        FutTrd_Setting.set_txt = "主题";
    }

    VerticalLayout {
        padding: 20px;

        HorizontalLayout {
            spacing: 20px;

            Label {
                text: "外观";
                vertical_alignment: center;
            }

            ComboBox {
                width: 90px;
                height: 24px;
                model: [
                    { text: "跟随系统" }, { text: "浅色" }, { text: "深色" }
                ];

                init => {
                    self.select(AppCom.theme_style);
                }

                selected(index) => {
                    AppCom.set_theme_style(index);
                }
            }

            Rectangle {}
        }

        Rectangle {}
    }
}

// 设置 - 交易 - 交易请求确认
component SetTrdReqConfirm {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        FutTrd_Setting.set_txt = "交易请求确认";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        GridLayout {
            spacing: 10px;

            Row {
                Label {
                    width: 120px;
                    text: "报单请求确认";
                }

                Switch {
                    checked <=> FutTrd_Setting.trdreqtip_inputorder;
                    toggled => {
                        FutTrd_Setting.trdreqtip_inputorder_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "撤单请求确认";
                }

                Switch {
                    checked <=> FutTrd_Setting.trdreqtip_ordercancel;
                    toggled => {
                        FutTrd_Setting.trdreqtip_ordercancel_changed();
                    }
                }
            }

            Row {
                Rectangle {
                    height: 1px;
                 }
            }

            Row {
                Label {
                    width: 120px;
                    text: "行权请求确认";
                }

                Switch {
                    checked <=> FutTrd_Setting.trdreqtip_inputexec;
                    toggled => {
                        FutTrd_Setting.trdreqtip_inputexec_changed();
                    }
                }
            }

            Row {
                Label {
                    text: "撤销行权请求确认";
                }

                Switch {
                    checked <=> FutTrd_Setting.trdreqtip_execcancel;
                    toggled => {
                        FutTrd_Setting.trdreqtip_execcancel_changed();
                    }
                }
            }
        }

        Label {
            text: "提示: 本页选项每次登录均默认开启, 设置仅用于本次登录";
        }
    }
}

// 设置 - 异常监控 - 自成交
component SetMonitorTradeSelf {
    preferred_width: 100%;
    preferred_height: 100%;

    init => {
        FutTrd_Setting.set_txt = "异常监控 - 自成交";
    }

    VerticalLayout {
        padding: 20px;
        spacing: 20px;

        Label {
            height: 26px;
            text: "满足以下条件, 则认为自成交异常";
        }

        HorizontalLayout {
            height: 26px;

            Label {
                text: "自成交数量大于等于  ";
                vertical_alignment: center;
            }

            i_le_trdvol := LineEdit {
                width: 120px;
                text: FutTrd_Setting.mon_tradeself.trade_vol;
                placeholder_text: "值为大于0的整数";
                has_error: 1 == FutTrd_Setting.mon_tradeself_err_status;

                edited => {
                    FutTrd_Setting.mon_tradeself.trade_vol = self.text;
                    FutTrd_Setting.mon_tradeself_changed();
                }
            }

            Rectangle { }
        }

        HorizontalLayout {
            height: 26px;

            Label {
                width: 70px;
                text: "默认值";
                color: Theme.border_focus;

                TouchArea {
                    mouse_cursor: pointer;
                    clicked => {
                        FutTrd_Setting.reset_mon_tradeself();
                        i_le_trdvol.text = FutTrd_Setting.mon_tradeself.trade_vol;
                    }
                }
            }

            Label {
                text: "提示: 本页数据设置后, 重启后全部生效...";
            }
        }

        Label {
            text: FutTrd_Setting.mon_tradeself_err_tips;
            color: red;
        }
    }
}

component SideBarView inherits VerticalSideBar {
    in_out property <[GroupListViewItem]> items <=> navigation.model;
    in_out property <{ parent: int, item: int}> current_item <=> navigation.current_item;

    callback current_item_changed <=> navigation.current_item_changed;

    forward_focus: navigation;

    navigation := GroupListView {
        vertical_stretch: 1;
        current_item: { item: 0, parent: 0 };
    }
}

component SetPages {
    in property <{ parent: int, item: int}> sel_item;

    preferred_width: 100%;
    preferred_height: 100%;

    if (0 == root.sel_item.parent && 0 == root.sel_item.item): SetComTheme { }

    if (1 == root.sel_item.parent && 0 == root.sel_item.item): SetTrdReqConfirm { }

    if (2 == root.sel_item.parent && 0 == root.sel_item.item): SetMonitorTradeSelf {
    }
}

export component SettingPage {

    private property <bool> hover_close_btn;

    private property <GroupListViewItem> widgets1: { text: "公共", items: [
        { leading_icon: Icons.list, text: "主题" },
    ] };

    private property <GroupListViewItem> widgets2: { text: "交易", items: [
        { leading_icon: Icons.list, text: "交易请求确认" },
    ] };

    private property <GroupListViewItem> resources: {
        text: "异常监控",
        items: [
            { leading_icon: Icons.list, text: "自成交" },
        ]
    };

    preferred-width: 100%;
    preferred-height: 100%;

    WndRec {
        title: @tr("{}", FutTrd_Setting.set_txt);
        btn_close_clicked => {
            AppCom.show_set = false;
        }

        Rectangle {
            HorizontalLayout {
                SideBarView {
                    resizable: true;
                    title: @tr("设置");
                    items: [root.widgets1, root.widgets2, root.resources];
                    current_item <=> FutTrd_Setting.sel_item;
                }

                Rectangle {
                    SetPages {
                        sel_item: FutTrd_Setting.sel_item;
                    }
                }
            }
        }
    }
}
