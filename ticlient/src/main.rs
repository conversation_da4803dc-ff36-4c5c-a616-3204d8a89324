#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]
#![allow(unreachable_code)]
#![allow(dead_code)]
#![allow(unused_variables)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]

mod apiproc;
mod app;
mod common;
mod error;
mod login;
mod ui;
mod version;
mod winit_helper;

pub mod slintui {
    slint::include_modules!();
}
use clap::Parser;
use i_slint_backend_winit::winit::raw_window_handle::HasWindowHandle;
use i_slint_backend_winit::WinitWindowAccessor;
use slintui::*;

use crate::common::global;

#[derive(Parser, Debug)]
#[command[version]]
struct Args {
    /// Renderer name. Values ​​can only be `skia` `femtovg` `software`
    #[arg(short, long, default_value = "")]
    renderer: String,

    /// Graphics API interface, only valid on `Windows` platform and when the renderer value is `skia`. The value can only be empty or d3d
    #[arg(short, long, default_value = "")]
    gapi: String,

    /* 以下参数为内部调试的启动参数, 不主动对外提供(即使用 help 时不显示的参数, 客户如果有需要可以告知) */
    /// 交易里资金明细是否显示(目前仅针对信用交易)
    #[arg(long, default_value = "false", hide = true)]
    trd_detail_amt: bool,

    /// 交易里的0持仓是否显示
    #[arg(long, default_value = "false", hide = true)]
    trd_zero_pos: bool,
}

struct AppGuard;
impl Drop for AppGuard {
    fn drop(&mut self) {
        let rt = common::global::TOKIO_RT.get().unwrap();
        rt.block_on(async {
            common::global::release().await;
        });
    }
}

/// 显示普通对话框
/// - `state`: 对话框类型 (1:提示; 2:警告; 3:错误; 4:None)
/// - `title`: 对话框标题
/// - `msg`: 对话框内容
pub fn show_msg_box(app: &crate::App, state: i32, title: slint::SharedString, msg: slint::SharedString) {
    app.global::<crate::AppCom>()
        .set_app_ret(crate::AppResult { state, title, msg });
}

/// 获取导出文件的路径
/// - `app`: slint应用
/// - `default_name`: 默认文件名
pub fn get_expor_path(app: &crate::App, default_name: &str) -> Option<std::path::PathBuf> {
    // windows下目前打开对话框如果选择了重复的文件会卡住,是slint本身的问题,未解决前不再打开对话框直接使用默认路径
    if cfg!(target_os = "windows") {
        return Some(
            std::path::PathBuf::from(match std::env::var("USERPROFILE") {
                Ok(dir) => dir,
                Err(_) => common::global::CFG.get().unwrap().read().unwrap().com.FPath.ExeDir.clone(),
            })
            .join(default_name),
        );
    }

    let default_dir = if cfg!(target_os = "macos") {
        match std::env::var("HOME") {
            Ok(home) => std::format!("{}/Documents", home),
            Err(_) => common::global::CFG.get().unwrap().read().unwrap().com.FPath.WorkDir.clone(),
        }
    } else {
        common::global::CFG.get().unwrap().read().unwrap().com.FPath.ExeDir.clone()
    };

    let ret = app.window().with_winit_window(|window| {
        let dialog = native_dialog::DialogBuilder::file()
            .set_title("请选择保存路径...")
            .set_location(&default_dir)
            //.add_filter("CSV", &["csv"])
            .set_filename(default_name);

        let dialog = match window.window_handle() {
            Ok(win) => dialog.set_owner(&win),
            Err(err) => {
                // 未获取到主窗口句柄, 此时打开对话框主窗口会被隐藏
                log::warn!("get_export_path: Failed to get window handle. {}", err);
                dialog
            }
        }
        .save_single_file();

        match dialog.show() {
            Ok(path) => match path {
                Some(path) => Some((true, path)),                     // 选择了正确路径
                None => Some((false, std::path::PathBuf::default())), // 主动放弃的
            },
            Err(err) => {
                log::warn!("get_export_path: Failed to show SaveSingleFile dialog. {}", err);
                None
            }
        }
    });

    if let Some(path) = ret {
        if let Some(path) = path {
            match path.0 {
                true => return Some(path.1),
                false => return None,
            };
        }
    }

    // 获取失败(包括调用with_winit_window失败和打开save_single_file失败)使用默认路径
    let path = std::path::PathBuf::from(&default_dir).join(default_name);
    log::warn!("get_export_path: Failed. use default path. {:?}", path);
    Some(path)
}

/// 从启动参数解析要使用的渲染器
/// - `args`: 通过命令行传递过来的启动参数
///
/// 如果参数有传递则使用传递的渲染器, 否则读取上一次运行成功时使用的渲染器; 如果读取再失败使用默认的渲染器
fn parse_renderer_from_args(args: Args) -> common::tistruct::RendererInfo {
    if args.renderer.is_empty() {
        let (r, g) = match common::global::deserialize_user_data::<common::tistruct::RendererInfo>(
            &common::global::read_user_data_from_file(&std::format!(
                "{}/{}",
                common::global::CFG.get().unwrap().read().unwrap().com.FPath.WorkDir,
                "drg.json"
            )),
        ) {
            // 再次验证防止人为修改过配置文件
            Some(var) => match (var.renderer.as_str(), var.gapi.as_str()) {
                ("skia", "d3d") => ("skia", "d3d"),
                ("femtovg", _) => ("femtovg", ""),
                ("software", _) => ("software", ""),
                _ => ("skia", ""),
            },
            None => ("skia", ""),
        };

        common::tistruct::RendererInfo {
            renderer: r.into(),
            gapi: g.into(),
        }
    } else {
        common::tistruct::RendererInfo {
            renderer: args.renderer,
            gapi: args.gapi,
        }
    }
}

/// 选择渲染器
/// - `renderer_name`: 渲染器名称
/// - `graphics_api`: 图形接口API
fn selector_renderer(renderer_name: &str, graphics_api: &str) -> bool {
    let renderer_name = match renderer_name {
        "skia" => "skia",
        "femtovg" => "femtovg",
        "software" => "software",
        _ => {
            log::error!("selector_renderer failed. error renderer_name=[{}] ", renderer_name);
            return false;
        }
    };

    let selector = slint::BackendSelector::new()
        .backend_name("winit".into())
        .renderer_name(renderer_name.to_owned());
    let selector = match (renderer_name, graphics_api) {
        ("skia", "d3d") => {
            if cfg!(target_os = "windows") {
                // d3d只支持Windows平台
                selector.require_d3d()
            } else {
                selector
            }
        }
        ("femtovg", _) => selector.require_opengl_with_version(3, 0),
        _ => selector,
    };

    match selector.select() {
        Ok(_) => true,
        Err(e) => {
            println!("selector_renderer [{}, {}] failed. {:?}", renderer_name, graphics_api, e);
            false
        }
    }
}

/// 居中登录窗口
fn center_login_window(login: &LoginForm) {
    winit_helper::center_window(login.window());
}

/// 等待主界面初始化
async fn on_wait_for_app_show<F>(mut progress: F)
where
    F: FnMut(String) + Send + 'static,
{
    // 记录上一次显示的消息，避免重复显示相同消息
    let mut last_msg = String::new();

    loop {
        let tips = global::get_app_init_tips();

        if tips.0 {
            // 最后一条消息也要确保显示
            if last_msg != tips.1 {
                progress(tips.1.clone().into());
            }
            break;
        }

        // 只有当消息变化时才更新UI
        if last_msg != tips.1 {
            progress(tips.1.clone().into());
            last_msg = tips.1.clone();
        }

        tokio::time::sleep(std::time::Duration::from_millis(10)).await;
    }
    global::drop_app_init_tips();
}

fn main() {
    // 解析对外提供的启动参数
    let args = Args::parse();

    if let Err(err) = common::global::prepare_init() {
        println!("prepare_init failed. {}", err);
        return;
    }

    match tokio::runtime::Builder::new_multi_thread().enable_all().build() {
        Ok(rt) => {
            let _ = common::global::TOKIO_RT.set(rt);
        }
        Err(err) => {
            log::error!("New runtime failed. {:?}", err);
            return;
        }
    };

    log::info!("The program started successfully. {}", version::CRATE_VERSION);

    let iner_args = (args.trd_detail_amt, args.trd_zero_pos);
    let renderer_info = parse_renderer_from_args(args);
    if selector_renderer(&renderer_info.renderer, &renderer_info.gapi) {
        log::info!("selector_renderer successfully. {:?}", renderer_info);
    } else {
        log::error!("selector_renderer failed");
        panic!("selector_renderer failed");
    }

    #[cfg(target_os = "macos")]
    common::macos::set_dock_menu();

    let _app_guard = AppGuard;

    let renderer = match renderer_info.renderer.as_str() {
        "skia" => slintui::ERenderer::Skia,
        "femtovg" => slintui::ERenderer::Femtovg,
        _ => slintui::ERenderer::Software,
    };

    let os = {
        #[cfg(target_os = "windows")]
        let os = slintui::EOS::Win;

        #[cfg(target_os = "macos")]
        let os = slintui::EOS::Mac;

        #[cfg(target_os = "linux")]
        let os = slintui::EOS::Linux;

        os
    };

    #[cfg(feature = "risk")]
    let rsk_stk_credit_app = std::rc::Rc::new(std::cell::RefCell::new(None));

    #[cfg(feature = "risk")]
    let rsk_stk_credit_app = rsk_stk_credit_app.clone();

    let login = LoginForm::new().unwrap();
    login.invoke_set_font(os, renderer);

    let wlogin = login.as_weak();
    login.on_login_success(move || {
        let app = App::new().unwrap();
        app.invoke_set_font(os, renderer);
        {
            app.global::<AppInerArgs>().invoke_set_args(iner_args.0, iner_args.1);
            let cfg = global::CFG.get().unwrap().read().unwrap();
            if crate::common::tistruct::EClientType::StkCredit == cfg.com.ClientType
                || crate::common::tistruct::EClientType::StkCreditTrd == cfg.com.ClientType
            {
                app.global::<AppInerArgs>().set_is_all_in_one(1 == cfg.com.SvrOther);
            }
        }

        #[cfg(feature = "risk")]
        {
            let clitype = { common::global::CFG.get().unwrap().read().unwrap().com.ClientType };
            if common::tistruct::EClientType::StkCredit == clitype {
                *rsk_stk_credit_app.borrow_mut() = Some(RiskStkCreditDetailPage::new().unwrap());

                let rsk_stk_app_ref = rsk_stk_credit_app.borrow();
                let rsk_stk_app = rsk_stk_app_ref.as_ref().unwrap();
                rsk_stk_app.invoke_set_font(os, renderer);
                {
                    let cfg = global::CFG.get().unwrap().read().unwrap();
                    rsk_stk_app.global::<AppInerArgs>().invoke_set_args(iner_args.0, iner_args.1);
                    rsk_stk_app.global::<AppInerArgs>().set_is_all_in_one(1 == cfg.com.SvrOther);
                }

                app::run_rsk_stk_credit(&app, rsk_stk_app);
            }
        }

        // 显示主界面, 关闭登录界面
        let app_weak = app.as_weak();
        let login_weak = wlogin.clone();
        global::TOKIO_RT.get().unwrap().spawn(async move {
            let app_weak_proc = login_weak.clone();
            let ret = global::TOKIO_RT
                .get()
                .unwrap()
                .spawn_blocking(move || {
                    let rt = tokio::runtime::Handle::current();
                    rt.block_on(on_wait_for_app_show(move |msg| {
                        let _ = app_weak_proc.upgrade_in_event_loop(move |app| {
                            app.set_process_tips(msg.into());
                        });
                    }))
                })
                .await;

            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let _ = app.show();

                #[cfg(not(debug_assertions))]
                {
                    let app_weak_inner = app.as_weak();
                    global::TOKIO_RT.get().unwrap().spawn(async move {
                        tokio::time::sleep(std::time::Duration::from_millis(1)).await;
                        let _ = app_weak_inner.upgrade_in_event_loop(|app| {
                            if !app.window().is_maximized() {
                                app.window().set_maximized(true);
                            }
                        });
                    });
                }
            });

            let _ = login_weak.upgrade_in_event_loop(move |login| {
                let _ = login.hide();
            });
        });

        app::run(&app);
    });

    login::run(&login);
    login.show().unwrap();

    #[cfg(not(target_os = "windows"))]
    center_login_window(&login);

    let wlogin = login.as_weak();
    let _ = std::thread::spawn(move || {
        std::thread::sleep(std::time::Duration::from_secs(1));

        // 保存使用成功的渲染器
        common::global::serialize_write_user_data(
            &std::format!(
                "{}/{}",
                common::global::CFG.get().unwrap().read().unwrap().com.FPath.WorkDir,
                "drg.json"
            ),
            &renderer_info,
            false,
        );

        let _ = wlogin.upgrade_in_event_loop(move |login| {
            login.invoke_ginit();
        });
    });

    slint::run_event_loop().unwrap();
}
