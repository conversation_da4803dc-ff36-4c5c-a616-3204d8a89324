import { Theme, ThemeStyle, ListViewItem } from "@zstk/lib.slint";

// 程序类型
export enum EAppType {
    // 未知
    Unknown,

    // 个股期权风控
    StkOpt,

    // 个股期权交易
    StkOptTrd,

    // 现货风控
    Stk,

    // 现货交易
    StkTrd,

    // 现货风控(信用)
    StkCredit,

    // 现货交易(信用)
    StkCreditTrd,

    // 期货期权风控
    Fut,

    // 期货期权交易
    FutTrd,
}

// 操作系统
export enum EOS {
    // Windows
    Win,

    // macOS
    Mac,

    // Linux
    Linux,
}

// 渲染器
export enum ERenderer {
    Skia,
    Femtovg,
    Software,
}

// 通知类型
export enum ENotifyType {

    // 错误(异常)
    Error,

    // 警示(逼近错误)
    Caution,

    // 提示
    Prompt,

    // 正常
    Normal
}

// 结果
export struct AppResult {
    // 状态码. 取值请参考 MsgBoxID
    state: int,

    // 标题
    title: string,

    // 消息
    msg: string,
}

// 公共
export global AppCom {
    in_out property <int> theme_style;                      // 主题外观. 0:跟随系统; 1:浅色; 2:深色

    in_out property <bool> islocked: false;                 // 界面锁定
    in_out property <bool> show_set;                        // 显示设置界面
    in_out property <bool> show_about;                      // 显示关于界面

    in_out property <bool> td_session_timeout;              // 交易Session是否超时
    in_out property <int> svr_build_date;                   // 服务端编译日期

    out property <length> table_header_height: 26px;        // 表格控件列头高度
    out property <length> widgets_height: 22px;             // 小部件(ComboBox, LineEdit, Button 等)高度

    in_out property <AppResult> app_ret;
    in_out property <AppResult> app_close;

    private property <string> font_family: "Noto Serif SC";

    callback unlocked(/** passwd */ string) -> bool;
    callback close(/** state */ int);   // state: 100直接关闭; 101:重启

    callback copy_str_to_clipboard(/** data */ string);
    callback copy_arr_to_clipboard(/** arr */ [string]);

    callback theme_style_changed(/** style */ int);

    // 检查交易Session是否超时
    public function check_td_session_timeout() -> bool {
        if td_session_timeout {
            app_ret.state = 3;
            app_ret.msg = "交易Session超时, 禁止使用交易功能";
        }
        td_session_timeout
    }

    // 设置主题外观
    public function set_theme_style(style: int) {
        if style == theme_style {
            return;
        }

        if 0 == style {
            Theme.set_theme_with_system();
            theme_style = 0;
            theme_style_changed(AppCom.theme_style);
        } else if 1 == style {
            Theme.set_theme_style(ThemeStyle.Light);
            theme_style = 1;
            theme_style_changed(AppCom.theme_style);
        } else if 2 == style {
            Theme.set_theme_style(ThemeStyle.Dark);
            theme_style = 2;
            theme_style_changed(AppCom.theme_style);
        }
    }

    // 获取窗体的默认字体
    public function get_default_font(os: EOS, renderer: ERenderer) -> {family: string, size: length, weight: int} {
        if EOS.Win == os {
            if ERenderer.Skia == renderer {
                return {family: font_family, size: 13px, weight: 610};
            }else if ERenderer.Femtovg == renderer {
                return {family: font_family, size: 13px, weight: 600};
            } else {
                return {family: font_family, size: 13px, weight: 400};
            }
        } else if EOS.Mac == os{
            if ERenderer.Skia == renderer {
                return {family: font_family, size: 13px, weight: 600};
            }
            else if ERenderer.Femtovg == renderer {
                return {family: font_family, size: 13px, weight: 600};
            } else {
                return {family: font_family, size: 13px, weight: 400};
            }
        } else {
            return {family: font_family, size: 13px, weight: 600};
        }
    }
}

// 内部参数
export global AppInerArgs {
    in_out property <bool> trd_detail_amt: false;   // 交易里资金明细是否显示(目前仅针对信用交易)
    in_out property <bool> trd_zero_pos: false;     // 交易里的0持仓是否显示
    public function set_args(a1: bool, a2: bool) {
        trd_detail_amt = a1;
        trd_zero_pos = a2;
    }

    in_out property <bool> is_all_in_one: false;    // 是否所有交易所布置在同一个节点(目前仅针对信用交易, 不通过参数设置, 通过服务端返回的信息设置本字段)
    out property <bool> show_detail: is_all_in_one ? trd_detail_amt : true; // 是否显示客户在各交易所的明细
}

// 异常监控 - 可转债
export struct MonConvertbonds {
    trade_amount: string,
    ratio: string
}

// 异常监控 - 报撤单
export struct MonOrderinsertcancel {
    greater_than_num: string,
    cancel_ratio: string
}

// 异常监控 - 自成交
export struct MonTradeself {
    trade_vol: string
}

// 异常监控 - 成交持仓比
export struct MonTradePosition {
    ratio: string,
    trade_vol: string
}

// 状态栏显示
export global AppStatusStrip {
    in property <{level: int, msg: string }> msg;
    in property <string> uid;
    in property <string> time;
    in property <int> login_status;
    in property <string> login_tips;
}

// 关于
export global AppAbout {
    in_out property <int> icon: 0;
    in_out property <string> name: "ticlient";
    in_out property <string> version: "Version";
    in_out property <string> copyright: "Copyright © TachyonInfo. All rights reserved.";

    in-out property <bool> show_slint_about: true;
}

// 对话框使用的ID
export global MsgBoxID {
    // 常用
    out property <int> info: 1;
    out property <int> warn: 2;
    out property <int> error: 3;
    out property <int> none: 4;

    // APP控制
    out property <int> app_close: 100;        // 主动关闭APP
    out property <int> app_abnormal: 101;     // 异常(如与服务端断开等)

    // 交易
    out property <int> order_insert_confirm: 100;         // 报单确认
    out property <int> order_cancel_confirm: 100;         // 撤单笔确认
    out property <int> order_cancel_all_confirm: 101;     // 撤所有单确认
    out property <int> order_cancel_buy_confirm: 102;     // 撤所有买单确认
    out property <int> order_cancel_sell_confirm: 103;    // 撤所有卖单确认
}

// 通知颜色
export global NotifyColor {
    // 错误(异常)
    out property <brush> error: Colors.red;

    // 警示(逼近错误)
    out property <brush> caution: Colors.deeppink;

    // 提示
    out property <brush> prompt: #c85014;

    // 正常
    out property <brush> normal: #48a854;

    // 默认
    out property <brush> default: Theme.foreground;
}

// 交易相关的颜色
export global TradeColor {
    // 交易所
    out property <brush> selected: #4a7cb19a;

    // 交易所
    out property <brush> exchid: #4a7cb19a;

    // 价格较昨不变动
    out property <brush> price_eq: Theme.foreground;

    // 价格较昨上涨
    out property <brush> price_up: #f34547e8;

    // 价格较昨下跌
    out property <brush> price_down: #44a951ed;

    // 方向 - 买入
    out property <brush> dir_buy: #f34547e8;

    // 方向 - 卖出
    out property <brush> dir_sell: #44a951ed;

    // 方向 - 其他
    out property <brush> dir_other: #44a951ed;
}

// 小部件颜色
export global WidgetColor {
    out property <brush> btn_background: #4a7cb19a;
}

// 默认列宽
export global ColWidth {
    out property <length> len_250: 250px;   // 宽度250px
    out property <length> len_240: 240px;   // 宽度240px
    out property <length> len_230: 230px;   // 宽度230px
    out property <length> len_220: 220px;   // 宽度220px
    out property <length> len_210: 210px;   // 宽度210px
    out property <length> len_200: 200px;   // 宽度200px
    out property <length> len_190: 190px;   // 宽度190px
    out property <length> len_180: 180px;   // 宽度180px
    out property <length> len_170: 170px;   // 宽度170px
    out property <length> len_160: 160px;   // 宽度160px
    out property <length> len_150: 150px;   // 宽度150px
    out property <length> len_145: 145px;   // 宽度145px
    out property <length> len_140: 140px;   // 宽度140px
    out property <length> len_135: 135px;   // 宽度135px
    out property <length> len_130: 130px;   // 宽度130px
    out property <length> len_125: 125px;   // 宽度125px
    out property <length> len_120: 120px;   // 宽度120px
    out property <length> len_115: 115px;   // 宽度115px
    out property <length> len_110: 110px;   // 宽度110px
    out property <length> len_105: 105px;   // 宽度105px
    out property <length> len_100: 100px;   // 宽度100px
    out property <length> len_95:  95px;    // 宽度95px
    out property <length> len_90:  90px;    // 宽度90px
    out property <length> len_85:  85px;    // 宽度85px
    out property <length> len_80:  80px;    // 宽度80px
    out property <length> len_75:  75px;    // 宽度75px
    out property <length> len_70:  70px;    // 宽度70px
    out property <length> len_65:  65px;    // 宽度65px
    out property <length> len_60:  60px;    // 宽度60px

    out property <length> exchid: len_65;       // 交易所
    out property <length> accountid: len_105;   // 资金账号
    out property <length> clientid: len_105;    // 交易编码
    out property <length> userid: len_105;      // 用户代码
    out property <length> opt_insid: len_80;    // 合约编码
    out property <length> opt_insname: len_160; // 合约名称
    out property <length> stk_insid: len_80;    // 合约编码
    out property <length> stk_insname: len_100; // 合约名称
    out property <length> fut_insid: len_120;   // 合约编码
    out property <length> fut_insname: len_160; // 合约名称
    out property <length> time: len_85;         // 发生时间
    out property <length> time_ms: len_105;     // 发生时间(ms)
    out property <length> buyside: len_80;      // 买卖
    out property <length> openclose: len_80;    // 开平标志
    out property <length> hedge: len_80;        // 投保标志
    out property <length> price: len_80;        // 价格
    out property <length> volume: len_105;      // 数量

    out property <length> tradeid: len_150;     // 成交编码
    out property <length> monstatus: len_80;    // 监控状态,是否异常

    out property <length> ord_vol: len_95;              // 委托数量
    out property <length> ord_price: len_80;            // 委托价格
    out property <length> ord_sysid: len_150;           // 交易所报单编号
    out property <length> ord_localid: len_130;         // 本地报单编号
    out property <length> ord_status: len_80;           // 报单状态
    out property <length> ord_pricetype: len_220;       // 报单价格条件
    out property <length> ord_timecondition: len_150;   // 有效期类型
    out property <length> ord_fjytype: len_120;         // 非交易业务类型
    out property <length> ord_volumecondition: len_90;  // 成交量类型
}

// 公共的下拉列表
export global CommonModel {

    // 证券交易所
    out property <[ListViewItem]> exch_stk_model: [
        { text: "" },
        { text: "SSE" },
        { text: "SZSE" },
        { text: "BSE" },
    ];

    // 证券交易所(个股期权)
    out property <[ListViewItem]> exch_opt_model: [
        { text: "" },
        { text: "SSE" },
        { text: "SZSE" },
    ];

    // 期货交易所
    out property <[ListViewItem]> exch_fut_model: [
        { text: "" },
        { text: "CFFEX" },
        { text: "SHFE" },
        { text: "INE" },
        { text: "DCE" },
        { text: "CZCE" },
        { text: "GFEX" },
    ];

    // 报单状态
    out property <[ListViewItem]> orderstatus_model: [
        { text: "" },
        { text: "已报入" },
        { text: "部分成交" },
        { text: "全部成交" },
        { text: "部成部撤" },
        { text: "已撤单" },
        { text: "已拒绝" },
    ];

    // 报单状态(个股期权的委托状态没有模拟的部成部撤)
    out property <[ListViewItem]> orderstatus_opt_model: [
        { text: "" },
        { text: "已报入" },
        { text: "部分成交" },
        { text: "全部成交" },
        { text: "已撤单" },
        { text: "已拒绝" },
    ];

    // 未设置交易所时的报单价格条件
    out property <[ListViewItem]> price_type_init_model: [
        { text: "普通限价"}
    ];
}
