use std::fmt::Debug;

use crate::error::Result;
use comfun;
use num_enum::TryFromPrimitive;
use serde::{Deserialize, Serialize};

/// 发布时对应的公司
pub enum EPubCompany {
    Ti,
    <PERSON><PERSON><PERSON>,
}

/// 服务端类型
#[derive(Default, Debug, TryFromPrimitive)]
#[repr(i32)]
pub enum EServerType {
    /// 个股期权风控端
    StkOpt = 0,

    /// 个股期权交易端(Ctp)
    StkOptTradeCtp = 1,

    /// 现货风控端
    Stk = 10,

    /// 现货交易端(Ctp)
    StkTradeCtp = 11,

    /// 期货风控端
    Fut = 12,

    /// 期货交易端(Ctp)
    FutTradeCtp = 13,

    /// 未知错误的服务端类型
    #[default]
    Unknown = 255,
}

/// 客户端类型
#[derive(Default, Clone, Copy, PartialEq, TryFromPrimitive)]
#[repr(i32)]
pub enum EClientType {
    /// 未知
    #[default]
    Unknown = 0,

    /// 个股期权
    StkOpt = 100,

    /// 个股期权交易端
    StkOptTrd = 101,

    /// 现货
    Stk = 200,

    /// 现货交易端
    StkTrd = 201,

    /// 现货(信用)
    StkCredit = 202,

    /// 现货交易端(信用)
    StkCreditTrd = 203,

    /// 期货
    Fut = 300,

    /// 期货交易端
    FutTrd = 301,
}
impl Debug for EClientType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::StkOpt => write!(f, "StkOpt"),
            Self::StkOptTrd => write!(f, "StkOptTrd"),
            Self::Stk => write!(f, "Stk"),
            Self::StkTrd => write!(f, "StkTrd"),
            Self::StkCredit => write!(f, "StkCredit"),
            Self::StkCreditTrd => write!(f, "StkCreditTrd"),
            Self::Fut => write!(f, "Fut"),
            Self::FutTrd => write!(f, "FutTrd"),
            Self::Unknown => write!(f, "Unknown"),
        }
    }
}

/// 服务端配置信息
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde[default]]
pub struct ServerInfoField {
    /// 是否使用代理. 1是
    #[serde(skip_serializing_if = "comfun::is_one_i32")]
    pub proxy: i32,

    /// 是否使用加密连接. 1是
    #[serde(skip_serializing_if = "comfun::is_one_i32")]
    pub encon: i32,

    /// 客户端类型
    #[serde(rename = "Tag")]
    pub CliType: i32,

    /// 服务端名称
    pub Name: String,

    /// 服务端地址
    pub Host: String,

    /// 服务端端口
    pub Port: i32,

    /// 服务端地址(仅在风控端调用交易端使用)
    #[serde(skip_serializing_if = "String::is_empty")]
    pub TrdHost: String,

    /// 服务端端口(仅在风控端调用交易端使用)
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub TrdPort: i32,
}
impl Default for ServerInfoField {
    fn default() -> Self {
        Self {
            proxy: 1,
            encon: 1,
            CliType: 0,
            Name: String::default(),
            Host: String::default(),
            Port: 0,
            TrdHost: String::default(),
            TrdPort: 0,
        }
    }
}
impl ServerInfoField {
    /// 检查客户端类型
    pub fn check_clitype(&self) -> bool {
        let ret = EClientType::try_from(self.CliType);
        if let Ok(ct) = ret {
            return ct != EClientType::Unknown;
        }

        false
    }

    /// 获取url
    pub fn get_url(&self) -> Result<String> {
        let uri = self.get_uri()?;
        let http = if 1 == self.encon { "https" } else { "http" };
        Ok(format!("{}://{}:{}/{}", http, self.Host, self.Port, uri))
    }

    /// 获取交易url
    pub fn get_trd_url(&self) -> Result<String> {
        if let Ok(ct) = EClientType::try_from(self.CliType) {
            if EClientType::StkOpt == ct || EClientType::Stk == ct || EClientType::Fut == ct {
                let uri = self.get_uri()?;
                let http = if 1 == self.encon { "https" } else { "http" };
                return Ok(format!("{}://{}:{}/{}", http, self.TrdHost, self.TrdPort, uri));
            }
        }

        return Err(format!("must be risk can get get_trd_url"));
    }
}
impl ServerInfoField {
    fn get_uri(&self) -> Result<&'static str> {
        let ret = EClientType::try_from(self.CliType);
        if ret.is_err() {
            return Err("Unknown client type".to_owned());
        }

        let ct = EClientType::try_from(self.CliType).ok().unwrap();
        match ct {
            EClientType::StkOpt => Ok("5025373852ec4fb186ae461aa3bfc7df"),
            EClientType::StkOptTrd => Ok("4f0c4a705093449e9f1662a7162acff8"),
            EClientType::Stk => Ok("9553acfc718d4e779fb9449130a48f53"),
            EClientType::StkTrd => Ok("2dfbe187bc6c45d594bd51b10d3dd385"),
            EClientType::StkCredit => Ok("f6e8153d5e804d9085fd2740161b4c85"),
            EClientType::StkCreditTrd => Ok("c0582bf70de8412cb9e6a19402bfb182"),
            EClientType::Fut => Ok("d25f2ee42bfc4c6a8548879250771130"),
            EClientType::FutTrd => Ok("d4ea2a183fba4cc89d9e4f02e3917448"),
            EClientType::Unknown => Err("Unknown client type".to_owned()),
        }
    }
}

/// 模块日志配置信息
#[derive(Deserialize, Debug, Clone)]
pub struct LogModuleField {
    pub name: String,
    pub level: i32,
}
impl Default for LogModuleField {
    fn default() -> Self {
        Self {
            name: Default::default(),
            level: 3,
        }
    }
}

/// 日志配置信息
#[derive(Deserialize, Debug, Clone)]
#[serde[default]]
pub struct LogInfoFiled {
    pub level: i32,
    pub module: Vec<LogModuleField>,
}
impl Default for LogInfoFiled {
    fn default() -> Self {
        Self {
            level: 3,
            module: Default::default(),
        }
    }
}

/// 获取LevelFilter. 如果参数错误,返回Off级别
pub fn get_log_level(level: i32) -> log::LevelFilter {
    match level {
        0 => log::LevelFilter::Off,
        1 => log::LevelFilter::Error,
        2 => log::LevelFilter::Warn,
        3 => log::LevelFilter::Info,
        4 => log::LevelFilter::Debug,
        5 => log::LevelFilter::Trace,
        _ => log::LevelFilter::Off,
    }
}

/// 查询到的服务端配置
#[derive(Serialize, Deserialize, Default, Debug, Clone)]
#[serde[default]]
pub struct QrySvrInfoField {
    /// 服务端类型
    pub st: i32,

    /// 服务端具体类型
    pub std: i32,

    /// 服务端编译时间
    pub bd: i32,

    /// 原本协议是表示是否最后一条的(因用不到本字段, 服务端借用本字段传递其他信息)
    pub tc: i32,

    /// 请求Uri
    pub requri: String,

    /// 发布Uri
    pub puburi: String,

    /// 行情Uri
    pub mduri: String,

    /// 请求端口
    pub reqport: i32,

    /// 发布端口
    pub pubport: i32,

    /// 发布端口
    pub mdport: i32,
}

/// 文件路径
#[derive(Default, Debug)]
pub struct FilePathField {
    /// 程序所在目录
    pub ExeDir: String,

    /// 程序工作目录
    pub WorkDir: String,

    /// 资源文件目录
    pub ResDir: String,

    /// 创建登录用户的目录状态
    /// 只要其中一个创建失败, 所有配置均使用默认值
    pub CreateLoginUserDirStatus: bool,

    /// 当前登录用户的目录(同登录用户名)
    pub LoginUserDir: String,

    /// 当前登录用户用于保存配置文件的目录
    pub LoginUserCfgDir: String,

    /// 当前登录用户用于保存界面布局的目录
    pub LoginUserLayoutDir: String,

    /// 当前登录用户用于保存数据文件的目录
    pub LoginUserDataDir: String,

    /// 当前登录用户用于保存数据库文件的目录
    pub LoginUserDataDbDir: String,
}
impl FilePathField {
    /// 配置文件路径
    pub const SERVER_INFO_PATH: &'static str = "si.json";

    /// 配置日志路径
    pub const LOG_INFO_PATH: &'static str = "log.json";

    /// 用户目录
    pub const USER_DIR: &'static str = "userdatas";

    /// 历史用户信息
    pub const HIS_USER_INFO_PATH: &'static str = "userdatas/hu";

    /// 历史用户信息
    pub const HIS_RISK_USER_INFO_PATH: &'static str = "userdatas/rhu";

    /// 主题文件路径
    pub const THEME_PATH: &'static str = "theme.json";

    /// 用户自定义的布局文件路径
    pub const LAYOUT_PATH: &'static str = "layout.json";

    /// 用户自定义的显示实时消息条数文件路径
    pub const SHOW_REAL_MSG_COUNT_PATH: &'static str = "realmsgcount.json";

    /// 监控数据显示格式文件路径
    pub const MON_SHOW_FORMAT_PATH: &'static str = "monshowformat.json";

    /// 成交持仓比文件路径
    pub const TRD_POS_MONITOR_PATH: &'static str = "trdposmonitor.json";

    /// 可转债监控文件路径
    pub const COVRT_BOND_MONITOR_PATH: &'static str = "covrtbondmonitor.json";

    /// 成交监控文件路径
    pub const TRADE_MONITOR_PATH: &'static str = "trademonitor.json";

    /// 自成交文件路径
    pub const TRD_SELF_MONITOR_PATH: &'static str = "trdselfmonitor.json";

    /// 报撤单文件路径
    pub const ORD_INSERT_CANCEL_MONITOR_PATH: &'static str = "ordinsertcancel.json";

    /// 用户自定义的值显示格式的文件路径
    pub const SHOW_VALUE_FORMAT_PATH: &'static str = "valueformat.json";

    /// 用户自选行情现货合约文件路径
    /// 默认分组保存的文件
    /// 如果以后要扩展分组，可以一个文件保存分组名（默认的不保存），每个分组名对应一个文件去保存数据
    pub const SELF_MD_STK_INS_ID: &'static str = "defmdstkins.json";

    /// 用户自选行情个股期权合约文件路径
    /// 默认分组保存的文件
    /// 如果以后要扩展分组，可以一个文件保存分组名（默认的不保存），每个分组名对应一个文件去保存数据
    pub const SELF_MD_OPT_INS_ID: &'static str = "defmdoptins.json";

    /// 用户自选行情期货期权合约文件路径
    /// 默认分组保存的文件
    /// 如果以后要扩展分组，可以一个文件保存分组名（默认的不保存），每个分组名对应一个文件去保存数据
    pub const SELF_MD_FUT_INS_ID: &'static str = "defmdfutins.json";
}

/// 登录用户信息
#[derive(Default)]
pub struct LoginUserInfoField {
    /// 用户名
    pub UserID: String,

    /// 解除锁定的字符串
    pub UNLockStr: Vec<u8>,
}

/// 显示实时消息的条数
/// 有些消息(如委托)非常多, 实时显示窗口中仅显示指定条数
/// 可以通过专门的查询窗口查询具体的信息
pub struct ShowRealMsgCountField {
    /// 出入金条数
    pub WithdrawCount: i32,

    /// 委托条数
    pub OrderCount: i32,

    /// 成交条数
    pub TradeCount: i32,

    /// 组合详情条数
    pub OmlCount: i32,

    /// 行权条数
    pub ExerciseCount: i32,
}
impl ShowRealMsgCountField {
    /// 最少的消息条数

    pub const MIN_COUNT: i32 = 50;

    /// 最大的消息条数

    pub const MAX_COUNT: i32 = 200;
}
impl Default for ShowRealMsgCountField {
    fn default() -> Self {
        Self {
            WithdrawCount: 100,
            OrderCount: 100,
            TradeCount: 100,
            OmlCount: 100,
            ExerciseCount: 100,
        }
    }
}

/// 值显示格式
#[derive(Default)]
pub struct ShowValueFormatFiled;
impl ShowValueFormatFiled {
    /// 数量的显示格式
    pub const VOLUME: &'static str = "N0";

    /// 金额的显示格式. #,##0.00
    pub const AMOUNT: &'static str = "N2";

    /// 价格的显示格式
    pub const PRICE: &'static str = "N4";

    /// 百分比的显示格式
    pub const PERCENT: &'static str = "P2";
}

/// 主题
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde[default]]
pub struct ThemeField {
    /// 样式. 0:跟随系统; 1:浅色; 2:深色
    pub Style: i32,

    /// 调色板
    pub Palette: String,
}
impl ThemeField {
    /// 默认的样式
    pub const STYLE: i32 = 0;

    /// 默认的调色板
    pub const PALETTE: &'static str = "";
}
impl Default for ThemeField {
    fn default() -> Self {
        Self {
            Style: ThemeField::STYLE,
            Palette: ThemeField::PALETTE.to_owned(),
        }
    }
}

/// 成交持仓比例监控
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde[default]]
pub struct TradePositionMonitorField {
    /// 成交量
    pub TradeVolume: i32,

    /// 比例(配置时一定要限制其最小值为1)
    pub Ratio: f64,
}
impl TradePositionMonitorField {
    /// 默认的成交量
    pub const TRADEVOLUME: i32 = 1000;

    /// 默认的比例
    pub const RATIO: f64 = 3.0;
}
impl Default for TradePositionMonitorField {
    fn default() -> Self {
        Self {
            TradeVolume: Self::TRADEVOLUME,
            Ratio: Self::RATIO,
        }
    }
}

/// 可转债监控信息
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde[default]]
pub struct ConvertBondsMonitorField {
    /// 成交金额阀值
    pub TradeAmount: f64,

    /// 比例(配置时一定要限制其最小值为1)
    pub Ratio: f64,
}
impl ConvertBondsMonitorField {
    /// 默认的成交金额阀值
    pub const TRADEAMOUNT: f64 = 50000000.0;

    /// 默认的比例
    pub const RATIO: f64 = 5.0;
}
impl Default for ConvertBondsMonitorField {
    fn default() -> Self {
        Self {
            TradeAmount: Self::TRADEAMOUNT,
            Ratio: Self::RATIO,
        }
    }
}

/// 成交监控信息
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde[default]]
pub struct TradeMonitorField {
    /// 成交金额阀值
    pub TradeAmount: f64,
}
impl TradeMonitorField {
    /// 默认的成交金额阀值
    pub const TRADEAMOUNT: f64 = 50000000.0;
}
impl Default for TradeMonitorField {
    fn default() -> Self {
        Self {
            TradeAmount: Self::TRADEAMOUNT,
        }
    }
}

/// 自成交监控
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde[default]]
pub struct TradeSelfMonitorField {
    /// 成交量
    pub TradeVol: i32,
}
impl TradeSelfMonitorField {
    /// 默认的成交量
    pub const TRADEVOL: i32 = 50;
}
impl Default for TradeSelfMonitorField {
    fn default() -> Self {
        Self {
            TradeVol: Self::TRADEVOL,
        }
    }
}

/// 报撤单监控
#[derive(Serialize, Deserialize, Debug, Clone)]
#[serde[default]]
pub struct OrderInsertCancelMonitorField {
    /// 大于数量
    pub GreaterThanNum: i32,

    /// 撤单比例倍率
    pub CancelRatio: f64,
}
impl OrderInsertCancelMonitorField {
    /// 默认的大于数量
    pub const GREATERTHANNUM: i32 = 20000;

    /// 默认的撤单比例倍率
    pub const CANCELRATIO: f64 = 1.0;
}
impl Default for OrderInsertCancelMonitorField {
    fn default() -> Self {
        Self {
            GreaterThanNum: Self::GREATERTHANNUM,
            CancelRatio: Self::CANCELRATIO,
        }
    }
}

/// 颜色结构
pub struct Color(u8, u8, u8);
impl Color {
    /// 白色
    const WHITE: Color = Color(255, 255, 255);

    /// 黑色
    const BLACK: Color = Color(0, 0, 0);

    /// 红色
    const RED: Color = Color(255, 0, 0);

    /// 绿色
    const GREEN: Color = Color(0, 255, 0);

    /// 蓝色
    const BLUE: Color = Color(0, 0, 255);

    /// 深紫色
    const DEEP_PINK: Color = Color(255, 20, 147);

    /// 深绿色
    const DARK_GREEN: Color = Color(0, 100, 0);
}

/// 数据的显示格式
pub struct DataShowFormatField {
    /// 字体颜色
    pub Color: Color,

    /// 是否加粗
    pub Bold: bool, // = false;
}
impl Default for DataShowFormatField {
    fn default() -> Self {
        Self {
            Color: Color::BLACK,
            Bold: false,
        }
    }
}

/// 监控数据显示格式
#[derive(Default)]
pub struct MonitorShowFormatField;
impl MonitorShowFormatField {
    /// 错误(异常)
    pub const ERROR: DataShowFormatField = DataShowFormatField {
        Color: Color::RED,
        Bold: true,
    };

    /// 警示(逼近错误)
    pub const CAUTION: DataShowFormatField = DataShowFormatField {
        Color: Color::DEEP_PINK,
        Bold: false,
    };

    /// 提示
    pub const PROMPT: DataShowFormatField = DataShowFormatField {
        Color: Color(200, 80, 20),
        Bold: false,
    };

    /// 提示
    pub const NORMAL: DataShowFormatField = DataShowFormatField {
        Color: Color(200, 80, 20),
        Bold: false,
    };

    /// 正常
    pub const DATA_SHOW_FORMAT: DataShowFormatField = DataShowFormatField {
        Color: Color::DARK_GREEN,
        Bold: false,
    };
}

/// 查询类型
pub enum EQueryType {
    /// 委托
    Order = 0,

    /// 成交
    Trade,

    /// 自成交
    TradeSelf,

    /// 组合
    Oml,

    /// 行权
    Exercise,

    /// 出入金
    WithdrawDeposit,

    /// 非交易业务委托
    BusinessOrder,

    /// 非交易业务成交
    BusinessTrade,
}

/// 交易类型
pub enum ETradeType {
    /// 报单录入请求
    InputOrder,

    /// 报单操作请求
    InputOrderAction,

    /// 申请组合录入请求
    CombInsert,

    /// 申请组合录入请求
    CombAction,

    /// 执行宣告录入请求
    ExecOrderInsert,

    /// 执行宣告操作请求
    ExecOrderAction,

    /// 行权指令合并录入请求
    ExecCombineOrderInsert,

    /// 行权指令合并操作请求
    ExecCombineOrderAction,

    /// 报价录入请求
    InputQuote,

    /// 报价操作请求
    InputQuoteAction,

    /// 询价录入请求
    InputForQuote,
}
impl ETradeType {
    fn get_text(&self) -> &'static str {
        match self {
            ETradeType::InputOrder => "报单请求",
            ETradeType::InputOrderAction => "撤单请求",
            ETradeType::CombInsert => "组合请求",
            ETradeType::CombAction => "解组合请求",
            ETradeType::ExecOrderInsert => "行权请求",
            ETradeType::ExecOrderAction => "撤销行权请求",
            ETradeType::ExecCombineOrderInsert => "组合行权请求",
            ETradeType::ExecCombineOrderAction => "撤销组合行权请求",
            ETradeType::InputQuote => "报价请求",
            ETradeType::InputQuoteAction => "撤报价请求",
            ETradeType::InputForQuote => "询价请求",
        }
    }
}

/// 使用交易功能时是否提示
pub struct RequestTradeTipField {
    /// 报单录入请求
    pub InputOrder: bool,

    /// 报单操作请求
    pub InputOrderAction: bool,

    /// 申请组合录入请求
    pub CombInsert: bool,

    /// 申请组合操作请求
    pub CombAction: bool,

    /// 执行宣告录入请求
    pub ExecOrderInsert: bool,

    /// 执行宣告操作请求
    pub ExecOrderAction: bool,

    /// 行权指令合并录入请求
    pub ExecCombineOrderInsert: bool,

    /// 行权指令合并操作请求
    pub ExecCombineOrderAction: bool,

    /// 报价录入请求
    pub InputQuote: bool,

    /// 报价操作请求
    pub InputQuoteAction: bool,

    /// 备兑解锁仓请求
    pub InputStockLock: bool,

    /// 询价录入请求
    pub InputForQuote: bool,

    /// 非交易业务报单录入请求
    pub FjyInputOrder: bool,

    /// 非交易业务报单操作请求
    pub FjyInputOrderAction: bool,
}
impl Default for RequestTradeTipField {
    fn default() -> Self {
        Self {
            InputOrder: true,
            InputOrderAction: true,
            CombInsert: true,
            CombAction: true,
            ExecOrderInsert: true,
            ExecOrderAction: true,
            ExecCombineOrderInsert: true,
            ExecCombineOrderAction: true,
            InputQuote: true,
            InputQuoteAction: true,
            InputStockLock: true,
            InputForQuote: true,
            FjyInputOrder: true,
            FjyInputOrderAction: true,
        }
    }
}

/// 所选择的类型
#[derive(Default)]
pub enum ESelectType {
    /// 合约号
    #[default]
    InstrumentID = 1,

    /// 资金账户
    AccountID,

    /// 价格
    Price,

    /// 行情
    MarketData,

    /// 持仓信息
    Position,

    /// 组合持仓信息
    PositionComb,
}

/// 选择项
#[derive(Default)]
pub struct SelectObject {
    /// 类型
    pub Type: ESelectType,

    /// 具体数据
    pub Obj: String,
}

/// 渲染器
#[derive(serde::Deserialize, serde::Serialize, Debug, Clone, Default)]
#[serde[default]]
pub struct RendererInfo {
    pub renderer: String,
    pub gapi: String,
}
