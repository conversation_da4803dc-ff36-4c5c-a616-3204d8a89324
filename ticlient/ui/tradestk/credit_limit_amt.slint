import { Fonts, TableView, TVVisible, Label, ComboBox } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";
import { StkTrd_CreditLimitAmt } from "struct.slint";

export component CreditLimitAmtPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <TVVisible> header_exchid_visible: StkTrd_CreditLimitAmt.only_show_summary ? TVVisible.Invisible : TVVisible.Visible;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            if !StkTrd_CreditLimitAmt.only_show_summary : HorizontalLayout {
                spacing: 10px;

                Label {
                    text: "交易所";
                    vertical_alignment: center;
                }

                i_cbx_exch := ComboBox {
                    width: 100px;
                    placeholder_text: "请选择";
                    current_text <=> StkTrd_CreditLimitAmt.exchid;
                    model: CommonModel.exch_stk_model;

                    selected => {
                        StkTrd_CreditLimitAmt.filter_changed();
                    }
                }

                Rectangle {
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    StkTrd_CreditLimitAmt.export_clicked(type);
                }
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid, visible: header_exchid_visible },
                    { title: @tr("授权额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("可用额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("使用额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("冻结额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转入额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转出额度"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                ]
            };
            row_count: StkTrd_CreditLimitAmt.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_CreditLimitAmt.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkTrd_CreditLimitAmt.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_CreditLimitAmt.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_CreditLimitAmt.row_data[row_index]);
                }
            }
        }
    }
}
