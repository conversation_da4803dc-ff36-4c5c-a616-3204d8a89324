use serde::{Deserialize, Serialize};

use super::instrument::*;

/// 请求查询合约消息
#[derive(Default, Debug, Serialize)]
pub struct ReqQryInstrumentField {}

/// 查询的合约响应消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct RspQryInstrumentField {
    pub array: Vec<InstrumentField>,
}

/// 请求查询非交易业务合约消息
#[derive(Default, Debug, Serialize)]
pub struct ReqQryFJYInstrumentField {}

/// 查询的非交易业务合约响应消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct RspQryFJYInstrumentField {
    pub array: Vec<FJYInstrumentField>,
}
