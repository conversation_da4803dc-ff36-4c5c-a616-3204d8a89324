import { Theme, Button, Icons, IconSettings, ListView, Label } from "@zstk/lib.slint";
import { WidgetColor } from "../struct.slint";

export component ExportButton {
    private property <int> sel_type: 0;

    callback clicked(/* type */ int);

    height: 26px;
    width: 60px;

    i_container := Rectangle {
        border_radius: 4px;
        background: WidgetColor.btn_background;

        HorizontalLayout {
            i_btn := Button {
                text: "导出";
                has_border: false;
                background: WidgetColor.btn_background.with-alpha(0.2);
                clicked => {
                    root.clicked(sel_type);
                }
            }

            icon := Image {
                y: (parent.height - self.height) / 2;
                image_fit: contain;
                colorize: Theme.foreground;
                width: IconSettings.small;
                source: Icons.arrow_down;

                icon_touch_area := TouchArea {
                    mouse_cursor: MouseCursor.pointer;

                    clicked => {
                        i_popup.show();
                    }
                }
            }
        }
    }

    i_popup := PopupWindow {
        x:0;
        y: root.height + 2px;
        width: root.width;
        height: 50px;

        Rectangle {
            background: i_container.background;
            border_radius: i_container.border_radius;
            drop-shadow-color: i_container.background.mix(white, 0.5);
            drop-shadow-blur: 2px;

            ListView {
                for index in 2 : Rectangle {
                    height: 25px;

                    HorizontalLayout {
                        padding: 1px;
                        spacing: 2px;

                        Image {
                            y: (parent.height - self.height) / 2;
                            image_fit: contain;
                            colorize: Theme.foreground;
                            width: IconSettings.extra_small;
                            visible: index == sel_type;
                            source: Icons.check;
                        }

                        Label {
                            horizontal-alignment: left;
                            vertical-alignment: center;
                            text: @tr("{}", 0 == index ? "当前页" : "全部");
                        }
                    }

                    TouchArea {
                        mouse-cursor: pointer;
                        clicked => {
                            sel_type = index;
                        }
                    }
                }
            }
        }
    }
}
