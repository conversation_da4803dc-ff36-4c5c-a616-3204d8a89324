import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";
import { GaChaPage } from "gacha.slint";

export component TopPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "轧差信息" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.left_top_selidx;
        }

        if 0 == StkRsk_TabSelIdx.left_top_selidx: Rectangle { GaChaPage {} }
    }
}
