import { ListViewItem } from "@zstk/lib.slint";
import { TimeSelect, TimeItem } from "../../widgets/time.slint";

/// 提示信息
export global StkRskCredit_Com {
    in_out property <string> accountid;                 // 资金账户
    callback trd_accountid_changed(/* accid */ string); // 主界面选择打开交易的资金账户有变化时的回调

    in_out property <[ListViewItem]> accid_model: [];
    callback accid_model_changed(/* accid_model */ [ListViewItem]);

    in_out property <string> trd_login_tips;
}

// 资金信息
export global StkRskCredit_Account {
    in_out property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <string> sel_accid;

    // 默认以维保比例(6, 但6使用的是不可见的第31列)进行升序排序
    in_out property <int> sort_asc_column_index: 31;
    in_out property <int> sort_dec_column_index: -1;
    callback sort_ascending(int);
    callback sort_descending(int);

    in_out property <string> filter_accid;
    callback filter_changed();
}

// 资金轧差消息
export global StkRskCredit_GaCha {
    in_out property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <string> sel_accid;

    // 默认以差额进行升序排序
    in_out property <int> sort_asc_column_index: 6;
    in_out property <int> sort_dec_column_index: -1;
    callback sort_ascending(int);
    callback sort_descending(int);

    in_out property <string> filter_accid;
    callback filter_changed();
}

// 持仓信息
export global StkRskCredit_Position {
    in_out property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <int> sort_asc_column_index: 1;
    in_out property <int> sort_dec_column_index: -1;
    callback sort_ascending(int);
    callback sort_descending(int);

    in_out property <string> filter_accid;
    in_out property <string> filter_exchid;
    in_out property <string> filter_insid;
    in_out property <[ListViewItem]> insid_model: [];
    callback filter_changed();

     callback export_clicked(/* type */ int);
}

// 信用交易 - 信用合约
export global StkRskCredit_CreditContract {
    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <int> filter_side;
    in_out property <string> filter_side_str;
    in_out property <string> filter_accid;
    in_out property <string> filter_exchid;
    in_out property <string> filter_insid;
    in_out property <string> filter_ordsysid;
    callback filter_changed();
}

// 信用交易 - 融资授信额度
export global StkRskCredit_CreditLimitAmt {
    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    callback filter_changed();
}

// 信用交易 - 融券授信额度
export global StkRskCredit_CreditLimitPos {
    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    callback filter_changed();
}

// 信用交易 - 资金头寸
export global StkRskCredit_CreditTcAmt {
    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <int> filter_tctype;
    in_out property <string> filter_tctype_str;

    callback filter_changed();
}

// 信用交易 - 股份头寸
export global StkRskCredit_CreditTcPos {
    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in property <[ListViewItem]> insid_model;

    in_out property <int> filter_tctype;
    in_out property <string> filter_tctype_str;
    in_out property <string> filter_exchid;
    in_out property <string> filter_insid;

    callback filter_changed();
}

// 信用交易 - 集中度
export global StkRskCredit_CreditConcentration {
    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in property <[ListViewItem]> group_name_model;
    in_out property <string> filter_group_name;
    in_out property <int> filter_sub_index;
    in_out property <string> filter_sub_str;
    in_out property <string> filter_exchid;
    in_out property <string> filter_insid;

    callback filter_changed();

    public function group_name_model_len() -> int {
        group_name_model.length
    }
}

// 信用交易 - 查询融资还款明细
export global StkRskCredit_QryCreditReAmtDtl {
    in_out property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <int> retype;
    in_out property <string> retype_str;
    in_out property <string> reid;
    in_out property <string> ordsysid;
    in_out property <string> insid;
    in_out property <int> starttime_int;
    in_out property <int> endtime_int;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in_out property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in_out property <int> item_total: 0;
    in_out property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);

    public function reset_datas() {
        row_data = [];

        exchid = "";
        retype = 0;
        retype_str = "";
        reid = "";
        ordsysid = "";
        insid = "";
        starttime_int = 0;
        endtime_int = 0;
        starttime = {hour: 0, minute: 0, second: 0};
        endtime = {hour: 23, minute: 59, second: 59};

        page_index_model = [ { text: "1 / 1" } ];
        item_total = 0;
        page_total = 1;
        page_index = 0;
        page_size = 50;
    }
}

// 信用交易 - 查询授信额度调整
export global StkRskCredit_QryCreditLimit {
    in_out property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <int> ltype_int;
    in_out property <string> ltype_str;
    in_out property <int> starttime_int;
    in_out property <int> endtime_int;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in_out property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in_out property <int> item_total: 0;
    in_out property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);

    public function reset_datas() {
        row_data = [];

        exchid = "";
        ltype_int = 0;
        ltype_str = "";
        starttime_int = 0;
        endtime_int = 0;
        starttime = {hour: 0, minute: 0, second: 0};
        endtime = {hour: 23, minute: 59, second: 59};

        page_index_model = [ { text: "1 / 1" } ];
        item_total = 0;
        page_total = 1;
        page_index = 0;
        page_size = 50;
    }
}

// 信用交易 - 查询资金头寸调整
export global StkRskCredit_QryCreditTcAmt {
    in_out property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <int> starttime_int;
    in_out property <int> endtime_int;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in_out property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in_out property <int> item_total: 0;
    in_out property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);

    public function reset_datas() {
        row_data = [];

        exchid = "";
        starttime_int = 0;
        endtime_int = 0;
        starttime = {hour: 0, minute: 0, second: 0};
        endtime = {hour: 23, minute: 59, second: 59};

        page_index_model = [ { text: "1 / 1" } ];
        item_total = 0;
        page_total = 1;
        page_index = 0;
        page_size = 50;
    }
}

// 查询出入金
export global StkRskCredit_QryWithdrawDeposit {
    in_out property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <int> starttime_int;
    in_out property <int> endtime_int;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in_out property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in_out property <int> item_total: 0;
    in_out property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);

    public function reset_datas() {
        row_data = [];

        exchid = "";
        starttime_int = 0;
        endtime_int = 0;
        starttime = {hour: 0, minute: 0, second: 0};
        endtime = {hour: 23, minute: 59, second: 59};

        page_index_model = [ { text: "1 / 1" } ];
        item_total = 0;
        page_total = 1;
        page_index = 0;
        page_size = 50;
    }
}