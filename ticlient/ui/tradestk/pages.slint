import { AccountPage } from "account.slint";
import { MarketDataPage } from "marketdata.slint";
import { InputOrderPage } from "input_order.slint";
import { InputFjyOrderPage } from "input_fjy_order.slint";
import { MonitorPage } from "mon.slint";

import { SettingPage } from "setting.slint";

export component Pages {
    in property <int> selidx;

    preferred_width: 100%;
    preferred_height: 100%;
    horizontal_stretch: 1;
    vertical_stretch: 1;

    if 0 == selidx: AccountPage { }
    if 1 == selidx: MarketDataPage { }
    if 2 == selidx: InputOrderPage { }
    if 3 == selidx: InputFjyOrderPage { }
    if 4 == selidx: MonitorPage {}

    if 5 == selidx: SettingPage { }
}
