/// 系统消息类型
pub enum EAppMessageType {
    TI_AMT_Min = 100,

    /// 心跳
    TI_AMT_Heartbeat,

    /// 服务端异常(重启或心跳中断等)
    TI_AMT_ServerError,

    TI_AMT_Max,
}

/// 系统消息
#[derive(Default)]
pub struct AppField {
    /// 消息类型 - 行情消息中只有心跳消息
    pub at: i32,

    /// 消息
    pub msg: String,
}

// 心跳
#[derive(Default)]
pub struct AppHeartbeat {
    /// APPID
    pub APPID: i32,

    /// 服务端时间(s)
    pub Time: i64,
}

/// 系统消息
#[derive(Default)]
pub struct PTWSRtnApp {
    pub base: AppField,
}
