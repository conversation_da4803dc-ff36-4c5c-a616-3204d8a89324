use serde::{Deserialize, Serialize};

/// 请求登录响应
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpRspLoginField {
    pub ec: i32,      // 错误码
    pub em: String,   // 错误消息
    pub exchid: i32,  // 交易所编码
    pub tdsvrid: i32, // TD交易服务ID

    // CThostFtdcRspUserLoginField
    pub TradingDay: String,  //交易日
    pub LoginTime: String,   //登录成功时间
    pub BrokerID: String,    //经纪公司代码
    pub UserID: String,      //用户代码
    pub SystemName: String,  //交易系统名称
    pub FrontID: i32,        //前置编号
    pub SessionID: i32,      //会话编号
    pub MaxOrderRef: String, //最大报单引用
    pub SHFETime: String,    //上期所时间
    pub DCETime: String,     //大商所时间
    pub CZCETime: String,    //郑商所时间
    pub FFEXTime: String,    //中金所时间
    pub INETime: String,     //能源中心时间
}
impl CtpRspLoginField {
    /// 获取登录结果描述
    pub fn to_string(&self) -> String {
        let exchID = match self.exchid {
            1 => "SSE",
            2 => "SZSE",
            3 => "CFFEX",
            4 => "SHFE",
            5 => "INE",
            6 => "DCE",
            7 => "CZCE",
            8 => "GFEX",
            _ => "未知",
        };

        if 0 == self.ec {
            format!("交易服务: {}({}), 登录成功", self.tdsvrid, exchID)
        } else {
            format!(
                "交易服务: {}({}), 登录失败, 错误信息: [{}, {}]",
                self.tdsvrid, exchID, self.ec, self.em
            )
        }
    }
}

/// HTTP请求登录(CTP)
#[derive(Default, Serialize, Debug)]
pub struct CtpReqLoginField {
    pub reqid: i32,
    pub exrepeatinfo: String,
    pub tirepeatinfo: String,
    pub cliver: String,
    pub apiver: String, // = "20230523 ti_trade";

    /// 验证码序号
    pub vcseqnum: i32,

    /// 验证码内容
    pub vcdata: String,

    /// 客户端类型. 1:终端;0:API
    ///
    /// 两者区别:
    ///
    /// 1. 类型为API时服务端不校验验证码
    /// 2. 类型为终端时,查询的持仓包含持仓量为0的持仓(全平),为API时仅包含有持仓量大于0的持仓
    /// 3. 类型为终端时,查询的合约信息包含合约结构体中的扩展字段
    /// 4. 类型为终端时使用使用服务端默认的AppID/AuthCode, 为API时需要在登录时传递
    ///
    pub clienttype: i32, // = 1;

    /// 私有流序号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub prvseqeno: i32,

    /// 公有流序号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub pubseqeno: i32,

    /// 交易日. 本字段用于判定私有/公有流序号是否有效
    #[serde(skip_serializing_if = "String::is_empty")]
    pub tradingDay: String,

    // CThostFtdcReqAuthenticateField
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AuthCode: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AppID: String,

    // CThostFtdcReqUserLoginField
    #[serde(skip_serializing_if = "String::is_empty")]
    pub TradingDay: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,
    pub UserID: String,
    pub Password: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub UserProductInfo: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InterfaceProductInfo: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ProtocolInfo: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub MacAddress: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub OneTimePassword: String,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub LoginRemark: String,
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ClientIPPort: i32,
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ClientIPAddress: String,
}

/// HTTP请求登录响应(CTP)
#[derive(Default, Debug)]
pub struct CtpLoginRetField {
    /// 错误码. 0表示成功
    pub ec: i32,

    /// 错误信息
    pub em: String,

    /// 用户具有的权限
    pub func: i32,

    /// 登录的节点数. 最多5处
    pub cnt: i32,

    /// 每个节点的登录详情
    pub arr: Vec<CtpRspLoginField>,
}

/// HTTP请求登出(CTP)
#[derive(Default)]
pub struct PTCtpReqLogout {
    pub BrokerID: String,
    pub UserID: String,
}

/// HTTP请求登出响应(CTP)
#[derive(Default)]
pub struct PTCtpRspLogout {
    pub BrokerID: String,
    pub UserID: String,
}

/// 风控用户请求登录(CTP扩展)
#[derive(Default)]
pub struct PTCtpExReqRiskLogin {
    pub reqid: i32,
    pub ssid: String,
    pub clienttype: i32, // = 1;
}

/// 风控用户请求订阅(CTP扩展)
#[derive(Default)]
pub struct PTCtpExReqRiskSubscribe {
    pub aid: String,
}

/// 风控用户请求退订阅(CTP扩展)
#[derive(Default)]
pub struct PTCtpExReqRiskUnSubscribe {
    pub unall: i32,  // = 1;      // 是否退订阅所有.1:是;0:否
    pub aid: String, //         // unall不是1时，退订阅具体的aid
}
