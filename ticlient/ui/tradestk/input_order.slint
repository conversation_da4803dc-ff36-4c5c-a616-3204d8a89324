import { <PERSON>, <PERSON>, <PERSON><PERSON>, ComboBox, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@zstk/lib.slint";
import { AppCom, TradeColor, CommonModel, NotifyColor } from "../struct.slint";
import { LineEditEx } from "../widgets/lineeditex.slint";
import { StkTrd_Com, StkTrd_InputOrder, StkTrd_OrdPriceType } from "struct.slint";

export component InputOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    // 处理合约变动
    function do_show_insid_text_changed() {
        StkTrd_InputOrder.price = "";
        StkTrd_InputOrder.volume = "100";
        StkTrd_InputOrder.insid_text_changed();
    }

    VerticalLayout {
        spacing: 2px;

        HorizontalLayout {
            spacing: 5px;

            Rectangle {
                width: 280px;

                GridLayout {
                    spacing-vertical: 7px;
                    spacing-horizontal: 20px;
                    padding: 5px;

                    Row {
                        Label {
                            width: 30px;
                            text: "账户";
                            vertical-alignment: center;
                        }

                        Label {
                            text: StkTrd_Com.accountid;
                            vertical-alignment: center;
                        }
                    }

                    Row {

                        Label {
                            text: "方向";
                            vertical-alignment: center;
                        }

                        Rectangle {
                            clip: true;
                            height: 26px;
                            border_color: StkTrd_InputOrder.get_dir_color();
                            border_width: 1px;
                            border_radius: Radius.extra_small;

                            HorizontalLayout {
                                Rectangle {
                                    background: StkTrd_InputOrder.is_buy ? TradeColor.dir_buy : transparent;

                                    Label {
                                        text: "买入";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            if (!StkTrd_InputOrder.is_buy) {
                                                StkTrd_InputOrder.dir_changed(true);
                                            }
                                            StkTrd_InputOrder.is_buy = true;
                                        }
                                    }
                                }

                                Rectangle {
                                    background: StkTrd_InputOrder.is_buy ? transparent : TradeColor.dir_sell;

                                    Label {
                                        text: "卖出";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            if (StkTrd_InputOrder.is_buy) {
                                                StkTrd_InputOrder.dir_changed(false);
                                            }
                                            StkTrd_InputOrder.is_buy = false;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "市场";
                            vertical-alignment: center;
                        }

                        Rectangle {
                            clip: true;
                            height: 26px;
                            border_color: TradeColor.exchid;
                            border_width: 1px;
                            border_radius: Radius.extra_small;

                            HorizontalLayout {

                                Rectangle {
                                    background: StkTrd_InputOrder.is_sel_sse() ? TradeColor.exchid : transparent;

                                    Label {
                                        text: "上海";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            StkTrd_InputOrder.set_market("SSE");
                                            i_cbx_pricetype.select(0);
                                        }
                                    }
                                }

                                Rectangle {
                                    width: 1px;
                                    border_color: TradeColor.exchid;
                                    border_width: 1px;
                                }

                                Rectangle {
                                    background: StkTrd_InputOrder.is_sel_szse() ? TradeColor.exchid : transparent;

                                    Label {
                                        text: "深圳";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            StkTrd_InputOrder.set_market("SZSE");
                                            i_cbx_pricetype.select(0);
                                        }
                                    }
                                }

                                Rectangle {
                                    width: 1px;
                                    border_color: TradeColor.exchid;
                                    border_width: 1px;
                                }

                                Rectangle {
                                    background: StkTrd_InputOrder.is_sel_bse() ? TradeColor.exchid : transparent;

                                    Label {
                                        text: "北京";
                                        vertical_alignment: center;
                                        horizontal_alignment: center;
                                    }

                                    TouchArea {
                                        mouse_cursor: pointer;
                                        clicked => {
                                            StkTrd_InputOrder.set_market("BSE");
                                            i_cbx_pricetype.select(0);
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "报价";
                            vertical-alignment: center;
                        }

                        i_cbx_pricetype := ComboBox {
                            placeholder_text: "请选择";
                            current_text <=> StkTrd_InputOrder.price_type;
                            max_popup_height: 200px;
                            model: "SSE" == StkTrd_InputOrder.exchid ? StkTrd_OrdPriceType.sse_price_type_model :
                                    ("SZSE" == StkTrd_InputOrder.exchid ? StkTrd_OrdPriceType.szse_price_type_model :
                                    ("BSE" == StkTrd_InputOrder.exchid ? StkTrd_OrdPriceType.bse_price_type_model :
                                    CommonModel.price_type_init_model));
                        }
                    }

                    Row {

                        Label {
                            text: "代码";
                            vertical-alignment: center;
                        }

                        LineEditEx {
                            max_popup_height: 160px;
                            model <=> StkTrd_InputOrder.insid_model;
                            has_error: StkTrd_InputOrder.insid_has_error;
                            current_text <=> StkTrd_InputOrder.insid;
                            placeholder_text: "请输入";

                            edited => {
                                do_show_insid_text_changed();
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "价格";
                            vertical-alignment: center;
                        }

                        SpinBox {
                            height: 26px;
                            placeholder_text: "请输入";
                            text <=> StkTrd_InputOrder.price;
                            has_error: StkTrd_InputOrder.price_has_error;

                            edited => {
                                StkTrd_InputOrder.price_text_changed();
                            }

                            up_down_clicked(upflag) => {
                                StkTrd_InputOrder.price_updown_changed(upflag);
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "数量";
                            vertical-alignment: center;
                        }

                        SpinBox {
                            height: 26px;
                            placeholder_text: "请输入";
                            text <=> StkTrd_InputOrder.volume;
                            has_error: StkTrd_InputOrder.volume_has_error;

                            edited => {
                                StkTrd_InputOrder.volume_text_changed();
                            }

                            up_down_clicked(upflag) => {
                                StkTrd_InputOrder.volume_updown_changed(upflag);
                            }
                        }
                    }

                    Row {

                        Label {
                            text: "重填";
                            vertical_alignment: center;
                            color: Theme.border_focus;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    StkTrd_InputOrder.insid = "";
                                    StkTrd_InputOrder.insid_has_error = true;
                                    StkTrd_InputOrder.price_type = "普通限价";
                                    StkTrd_InputOrder.price = "";
                                    StkTrd_InputOrder.price_has_error = true;
                                    StkTrd_InputOrder.volume = 100;
                                    StkTrd_InputOrder.volume_has_error = false;
                                }
                            }
                        }

                        HorizontalLayout {
                            spacing: 10px;

                            CheckBox {
                                text: "预埋单";
                                checked <=> StkTrd_InputOrder.is_yumai;
                            }

                            Button {
                                text: @tr("确定{}", StkTrd_InputOrder.is_buy ? "买入" : "卖出");
                                background: StkTrd_InputOrder.get_dir_color();

                                clicked => {
                                    if AppCom.check_td_session_timeout() {
                                        return ;
                                    }

                                    StkTrd_InputOrder.accountid = StkTrd_Com.accountid;
                                    StkTrd_InputOrder.ok_clicked(true);
                                }
                            }
                        }
                    }

                    Row {

                        Label { }

                        HorizontalLayout {

                            i_lb_yumai_tips := Label {
                                height: 24px;
                                text: StkTrd_InputOrder.is_yumai ? "预埋单仅在收盘前非交易时间有效" : "";
                                color: NotifyColor.prompt;
                                font_size: Fonts.normal.size;
                            }

                            Rectangle {
                                horizontal_stretch: 1;
                            }
                        }
                    }
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        Rectangle {
            vertical_stretch: 1;
        }
    }
}
