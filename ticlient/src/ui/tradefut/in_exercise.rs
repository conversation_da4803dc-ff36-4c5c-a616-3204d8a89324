use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    apiproc::tradefutbuf::CtpExecOrdMap,
    common::global::{CFG, TRADE_FUT_API, TRADE_FUT_BUF},
    slintui::*,
};
use dashmap::DashMap;
use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{
    CtpReqInputExecOrderActionField, CtpRspInputExecCombineOrderActionField, CtpRspInputExecOrderActionField,
};

use super::tradefut::show_cancel_msg_box;

/// 在途 - 行权 - 数据
struct InExecDatas {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpExecOrdMap>,

    /// 撤单响应数据
    rsp_cancel: Arc<Mutex<CtpRspInputExecOrderActionField>>,

    /// 撤单响应数据
    rsp_comb_cancel: Arc<Mutex<CtpRspInputExecCombineOrderActionField>>,

    /// 在撤单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_in_cancel: AtomicBool,

    /// 用于全撤时, 当在撤单过程中发生错误停止继续撤单
    need_stop_cancel: AtomicBool,
}
impl InExecDatas {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpExecOrdMap::new()),

            rsp_cancel: Arc::new(Mutex::new(CtpRspInputExecOrderActionField::default())),
            rsp_comb_cancel: Arc::new(Mutex::new(CtpRspInputExecCombineOrderActionField::default())),
            is_err_in_cancel: AtomicBool::new(false),

            need_stop_cancel: AtomicBool::new(false),
        }
    }
}
static IN_EXEC_DATAS: OnceLock<InExecDatas> = OnceLock::new();

/// 在途 - 行权
pub(super) struct InExercise {}

// 构建与初始化
impl InExercise {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化行权数据
        let _ = IN_EXEC_DATAS.set(InExecDatas::new());

        let app_inexec = app.global::<FutTrd_InExercise>();

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inexec.on_req_tip_toggled(move |checked| InExercise::on_req_tip_toggled(&app_weak, checked));

        // 注册撤单笔事件
        let app_weak: Weak<App> = app.as_weak();
        app_inexec
            .on_cancel_exec_clieked(move |ord, need_confirm| InExercise::on_cancel_exec_clieked(&app_weak, ord, need_confirm));

        // 注册撤全部事件
        let app_weak = app.as_weak();
        app_inexec.on_cancel_exec_all_clieked(move |row_cnt, need_confirm| {
            InExercise::on_cancel_exec_all_clieked(&app_weak, row_cnt, need_confirm)
        });

        log::trace!("InExercise init completed");
    }
}

// 查询与响应
impl InExercise {
    /// 请求查询行权信息
    pub fn qry_in_exercise(&self) {
        let ret = { TRADE_FUT_BUF.get().unwrap().read().unwrap().pop_in_execord() };
        if ret.is_empty() {
            return;
        }

        let inexec_datas = IN_EXEC_DATAS.get().unwrap();

        let mut data_changed = false;

        ret.iter().for_each(|it| {
            let key = it.key();
            let ord = it.value();

            if inexec_datas.data_map.contains_key(key) {
                if 1 != ord.LastStatus {
                    inexec_datas.data_map.remove(key);
                    data_changed = true;
                } else {
                    let mut pre_order = inexec_datas.data_map.get_mut(key).unwrap();
                    pre_order.LastStatus = ord.LastStatus;
                    pre_order.InsertTime = ord.InsertTime.clone();
                    pre_order.CancelTime = ord.CancelTime.clone();
                    data_changed = true;
                }
            } else {
                if 1 == ord.LastStatus {
                    inexec_datas.data_map.insert(key.clone(), ord.clone());
                    data_changed = true;
                }
            }
        });

        if data_changed {
            inexec_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }

    /// 撤行权响应
    pub fn on_rsp_inputexecaction(&self, rsp: CtpRspInputExecOrderActionField) {
        let inexec_datas = IN_EXEC_DATAS.get().unwrap();

        inexec_datas.need_stop_cancel.store(true, Ordering::Relaxed);

        *inexec_datas.rsp_cancel.lock().unwrap() = rsp;
        inexec_datas.is_err_in_cancel.store(true, Ordering::Relaxed);
    }

    /// 撤组合行权响应
    pub fn on_rsp_inputexeccombaction(&self, rsp: CtpRspInputExecCombineOrderActionField) {
        let inexec_datas = IN_EXEC_DATAS.get().unwrap();

        inexec_datas.need_stop_cancel.store(true, Ordering::Relaxed);

        *inexec_datas.rsp_cancel.lock().unwrap() = CtpRspInputExecOrderActionField {
            ec: rsp.ec,
            em: rsp.em,
            ExecOrderSysID: rsp.ExecCombineOrderSysID,
        };
        inexec_datas.is_err_in_cancel.store(true, Ordering::Relaxed);
    }
}

// 事件
impl InExercise {
    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<FutTrd_Setting>();
        app_set.set_trdreqtip_execcancel(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.ExecOrderAction = checked;
    }

    /// 撤单笔
    fn on_cancel_exec_clieked(app_weak: &Weak<App>, ord: CancelExerciseField, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inexec = app.global::<FutTrd_InExercise>();

        if ord.exchid.is_empty() {
            show_cancel_msg_box(&app, 1, Default::default(), "请先选中要撤销的行权".into());
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.ExecOrderAction } {
            show_cancel_msg_box(
                &app,
                100,
                "撤销行权请求确认".into(),
                slint::format!(
                    "资金账户: {}\n\n交易所: {}\n交易所执行宣告编码: [{}]",
                    ord.accountid,
                    ord.exchid,
                    ord.ordersysid
                ),
            );
            return;
        }

        let mut req = CtpReqInputExecOrderActionField::default();
        req.ExchangeID = ord.exchid.as_str().into();
        req.InvestorID = ord.accountid.as_str().into();
        req.ExecOrderSysID = ord.ordersysid.as_str().into();

        let api = TRADE_FUT_API.get().unwrap().read().unwrap();
        if let Err(err) = api.req_exec_orderaction(0, &req) {
            show_cancel_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("请求撤销行权失败\n\n错误信息: {}", err),
            );
        }
    }

    /// 撤全部
    fn on_cancel_exec_all_clieked(app_weak: &Weak<App>, row_cnt: i32, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inexec = app.global::<FutTrd_InExercise>();

        if need_confirm {
            let need_confirm = { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.ExecOrderAction };
            if need_confirm {
                show_cancel_msg_box(&app, 101, Default::default(), slint::format!("是否撤销当前所有的有效行权单?"));
                return;
            }
        }

        if 0 == row_cnt {
            show_cancel_msg_box(&app, 1, Default::default(), "没有行权单可撤".into());
            return;
        }

        // 获取当前所有的在途报单
        let mut exec_vec = Vec::new();
        for row_index in 0..row_cnt {
            exec_vec.push(app_inexec.invoke_get_cancel_exec(row_index))
        }

        // 开始撤单
        let inexec_datas = IN_EXEC_DATAS.get().unwrap();
        inexec_datas.need_stop_cancel.store(false, Ordering::Relaxed);
        let api = TRADE_FUT_API.get().unwrap().read().unwrap();
        for exec in exec_vec {
            if inexec_datas.need_stop_cancel.load(Ordering::Relaxed) {
                // 撤单过程中遇到错误, 不再继续撤单
                return;
            }

            let mut req = CtpReqInputExecOrderActionField::default();
            req.ExchangeID = exec.exchid.as_str().into();
            req.InvestorID = exec.accountid.as_str().into();
            req.ExecOrderSysID = exec.ordersysid.as_str().into();
            if let Err(err) = api.req_exec_orderaction(0, &req) {
                show_cancel_msg_box(
                    &app,
                    1,
                    Default::default(),
                    slint::format!(
                        "请求撤行权单失败\n\n资金账户: {}\n\n交易所: {}\n交易所执行宣告编码: [{}]\n\n错误信息:{}",
                        exec.accountid,
                        exec.exchid,
                        exec.ordersysid,
                        err
                    ),
                );
                return;
            }
        }
    }
}

// 更新UI
impl InExercise {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let inexec_datas = IN_EXEC_DATAS.get().unwrap();

        let is_err_in_cancel = inexec_datas
            .is_err_in_cancel
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        let rsp_cancel: CtpRspInputExecOrderActionField;
        let data_map_clone: Arc<CtpExecOrdMap>;

        if !is_err_in_cancel {
            let data_changed = inexec_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok();
            if !data_changed {
                return;
            }

            rsp_cancel = CtpRspInputExecOrderActionField::default();
            data_map_clone = inexec_datas.data_map.clone();
        } else {
            rsp_cancel = inexec_datas.rsp_cancel.lock().unwrap().clone();
            data_map_clone = Arc::new(DashMap::new());
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            if is_err_in_cancel {
                show_cancel_msg_box(
                    &app,
                    1,
                    "行权撤单失败".into(),
                    slint::format!(
                        "交易所执行宣告编码:{}\n错误信息:[{}, {}]",
                        rsp_cancel.ExecOrderSysID,
                        rsp_cancel.ec,
                        rsp_cancel.em
                    ),
                );
                return;
            }

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            data_map_clone.iter().for_each(|ord| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账号
                items.push(ord.InvestorID.clone().into());

                // 交易所
                items.push(ord.ExchangeID.clone().into());

                // 交易编码
                items.push(ord.ClientID.clone().into());

                // 发生时间
                if !ord.InsertTime.is_empty() {
                    items.push(ord.InsertTime.clone().into());
                } else if !ord.CancelTime.is_empty() {
                    items.push(ord.CancelTime.clone().into());
                } else {
                    items.push("".into());
                }

                // 合约编码
                items.push(ord.InstrumentID.clone().into());

                // 数量
                items.push(slint::format!("{}", ord.Volume));

                // 执行结果
                items.push(ord.StatusMsg.clone().into());

                // 执行编码
                items.push(ord.ExecOrderSysID.clone().into());

                // 本地执行编码
                items.push(ord.ExecOrderLocalID.clone().into());

                // 用户代码
                items.push(ord.UserID.clone().into());

                // 序号(用于排序)
                items.push(slint::format!("{:>13}", ord.SequenceNo));

                row_data.push(items.into());
            });

            // 按时间列降序显示
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(10 as usize).unwrap();
                let c_b = r_b.row_data(10 as usize).unwrap();
                c_b.cmp(&c_a)
            }));

            app.global::<FutTrd_InExercise>().set_row_data(sort_model.into());
        });
    }
}
