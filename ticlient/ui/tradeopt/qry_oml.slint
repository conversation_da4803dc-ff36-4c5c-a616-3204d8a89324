import { <PERSON>, <PERSON>onts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { TimeSelect, TimeItem } from "../widgets/time.slint";
import { ExportButton } from "../widgets/export.slint";

export global OptTrd_QryOml {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> insid;
    in_out property <string> combdir;
    in_out property <string> combid;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);
    callback export_clicked(/* type */ int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

export component QryOmlPage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = OptTrd_QryOml.exchid;
        i_le_insid.text = OptTrd_QryOml.insid;
        i_cbx_comdir.current_text = OptTrd_QryOml.combdir;
        i_le_combid.text = OptTrd_QryOml.combid;
        i_ts_start.time = OptTrd_QryOml.starttime;
        i_ts_end.time = OptTrd_QryOml.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            i_cbx_exch := ComboBox {
                width: 80px;
                placeholder_text: "请选择";
                current_text: OptTrd_QryOml.exchid;
                model: CommonModel.exch_opt_model;
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }
            i_le_insid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: OptTrd_QryOml.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "指令方向";
                vertical_alignment: center;
            }
            i_cbx_comdir := ComboBox {
                width: 80px;
                placeholder_text: "请选择";
                current_text: OptTrd_QryOml.combdir;
                model: [
                    { text: "" },
                    { text: "组合" },
                    { text: "拆分" },
                ];
            }

            Label {
                text: "组合编码";
                vertical_alignment: center;
            }
            i_le_combid := LineEdit {
                width: 120px;
                placeholder_text: "请输入";
                text: OptTrd_QryOml.combid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }
            Label {
                text: "发生时间";
                vertical_alignment: center;
            }

            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: OptTrd_QryOml.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: OptTrd_QryOml.endtime;
                }
            }

            Button {
                text: "查询";
                background: WidgetColor.btn_background;
                width: 60px;

                clicked => {
                    OptTrd_QryOml.exchid = i_cbx_exch.current_text;
                    OptTrd_QryOml.insid = i_le_insid.text;
                    OptTrd_QryOml.combdir = i_cbx_comdir.current_text;
                    OptTrd_QryOml.combid = i_le_combid.text;
                    OptTrd_QryOml.starttime = i_ts_start.time;
                    OptTrd_QryOml.endtime = i_ts_end.time;

                    OptTrd_QryOml.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    OptTrd_QryOml.export_clicked(type);
                }
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 5,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("组合策略"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("第1腿合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("第1腿方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("第2腿合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("第2腿方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("指令方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("交易所组合编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地申请组合编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                    { title: @tr("备兑标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.hedge },
                ]
            };
            row_count: OptTrd_QryOml.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_QryOml.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_QryOml.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_QryOml.row_data[row_index]);
                }
            }
        }

        if OptTrd_QryOml.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> OptTrd_QryOml.page_index_model;
            item_total <=> OptTrd_QryOml.item_total;
            page_total <=> OptTrd_QryOml.page_total;
            page_index <=> OptTrd_QryOml.page_index;
            page_size <=> OptTrd_QryOml.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                OptTrd_QryOml.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                OptTrd_QryOml.page_size_changed(size);
            }
        }
    }
}
