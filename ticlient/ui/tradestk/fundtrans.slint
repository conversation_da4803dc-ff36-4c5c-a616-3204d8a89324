import { <PERSON>ton, LineEdit, ComboBox, ListViewItem, Icons, Theme, Label } from "@zstk/lib.slint";
import { WndRec } from "../widgets/wndrec.slint";
import { FundTransField, StkTrd_Com, StkTrd_Account, StkTrd_TabSelIdx } from "struct.slint";
import { WidgetColor } from "../struct.slint";

export component FundTransPage {

    private property <FundTransField> fund_trans;

    private property <[ListViewItem]> sys_model: [
        { text: "主席", tag:0 },
        { text: "SSE", tag:1 },
        { text: "SZSE", tag:2 },
        { text: "BSE", tag:9 }
    ];

    i_wnd := WndRec {
        title: @tr("资金划转");
        can_close: StkTrd_Account.ctrls_enabled;

        btn_close_clicked => {
            StkTrd_Account.show_fund_trans = false;
        }

        GridLayout {
            spacing: 10px;
            padding: 10px;

            Row {
                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "资金账号";
                }

                i_le_aid := LineEdit {
                    width: 150px;
                    read_only: true;
                    text: StkTrd_Account.sel_accid;
                }
            }

            Row {
                Label {
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "出金系统";
                }

                i_cbx_out := ComboBox {
                    enabled: StkTrd_Account.ctrls_enabled;
                    placeholder_text: "请选择";
                    model: sys_model;
                }
            }

            Row {
                Label {
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "入金系统";
                }

                i_cbx_in := ComboBox {
                    enabled: StkTrd_Account.ctrls_enabled;
                    placeholder_text: "请选择";
                    model: sys_model;
                }
            }

            Row {
                Label {
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "划转金额";
                }

                i_le_amount := LineEdit {
                    enabled: StkTrd_Account.ctrls_enabled;
                    placeholder_text: "金额不能小于0.005";
                    action_icon: Icons.close;

                    action => {
                        self.text = "";
                    }
                }
            }

            Row {
                Label {
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "密码";
                }

                i_le_passwd := LineEdit {
                    enabled: StkTrd_Account.ctrls_enabled;
                    placeholder_text: "划转密码";
                    input_type: password;
                    action_icon: Icons.close;
                    text <=> StkTrd_Com.transpwd;

                    action => {
                        self.text = "";
                    }
                }
            }

            Row {
                Rectangle {
                    height: 2px;
                }
            }

            Row {
                Label {
                    height: 26px;
                    vertical_alignment: center;
                    text: "划转记录";
                    color: Theme.accent_background;

                    TouchArea {
                        mouse_cursor: pointer;
                        clicked => {
                            StkTrd_TabSelIdx.selidx = StkTrd_Com.iscredit ? 4 : 2;
                            StkTrd_TabSelIdx.rtn_subselidx = StkTrd_Com.iscredit ? 5 : 7;
                        }
                    }
                }

                HorizontalLayout {
                    Rectangle {
                        horizontal_stretch: 1;
                    }

                    i_btn_trans := Button {
                        width: 50px;
                        height: 25px;
                        background: WidgetColor.btn_background;
                        enabled: StkTrd_Account.ctrls_enabled;
                        text: "划转";
                        clicked => {
                            StkTrd_Account.ctrls_enabled = false;

                            fund_trans.accountid = i_le_aid.text;
                            fund_trans.password = i_le_passwd.text;
                            fund_trans.outsys = i_cbx_out.current_index >= 0 ? i_cbx_out.current_value.tag : -1;
                            fund_trans.insys = i_cbx_in.current_index >= 0 ? i_cbx_in.current_value.tag : -1;
                            fund_trans.amount = i-le-amount.text;
                            StkTrd_Account.req_fund_trans(fund_trans);
                        }
                    }
                }
            }
        }
    }
}
