import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, ComboBox, <PERSON>View, ListViewItem } from "@zstk/lib.slint";
import { NotifyColor, ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { TimeSelect, TimeItem } from "../widgets/time.slint";

export global StkTrd_QryWithdrawDeposit {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <int> starttime_int;
    in_out property <int> endtime_int;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);
}

export component QryWithdrawDepositPage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkTrd_QryWithdrawDeposit.exchid;
        i_ts_start.time = StkTrd_QryWithdrawDeposit.starttime;
        i_ts_end.time = StkTrd_QryWithdrawDeposit.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text: StkTrd_QryWithdrawDeposit.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "处理时间";
                vertical_alignment: center;
            }

            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: StkTrd_QryWithdrawDeposit.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: StkTrd_QryWithdrawDeposit.endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                    StkTrd_QryWithdrawDeposit.exchid = i_cbx_exch.current_text;
                    StkTrd_QryWithdrawDeposit.starttime = i_ts_start.time;
                    StkTrd_QryWithdrawDeposit.endtime = i_ts_end.time;
                    StkTrd_QryWithdrawDeposit.starttime_int = i_ts_start.time_int;
                    StkTrd_QryWithdrawDeposit.endtime_int = i_ts_end.time_int;

                    StkTrd_QryWithdrawDeposit.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("处理时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("入金金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("出金金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                ]
            };
            row_count: StkTrd_QryWithdrawDeposit.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_QryWithdrawDeposit.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                if 1 == column_index && data.to_float() > 0.0 {
                    return NotifyColor.normal;
                }

                if 2 == column_index && data.to_float() > 0.0 {
                    return NotifyColor.caution;
                }

                NotifyColor.default
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_QryWithdrawDeposit.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_QryWithdrawDeposit.row_data[row_index]);
                }
            }
        }

        if StkTrd_QryWithdrawDeposit.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkTrd_QryWithdrawDeposit.page_index_model;
            item_total <=> StkTrd_QryWithdrawDeposit.item_total;
            page_total <=> StkTrd_QryWithdrawDeposit.page_total;
            page_index <=> StkTrd_QryWithdrawDeposit.page_index;
            page_size <=> StkTrd_QryWithdrawDeposit.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryWithdrawDeposit.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryWithdrawDeposit.page_size_changed(size);
            }
        }
    }
}
