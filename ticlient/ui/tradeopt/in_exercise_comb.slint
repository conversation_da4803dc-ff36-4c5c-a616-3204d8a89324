import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TableView } from "@zstk/lib.slint";
import { AppC<PERSON>, ColWidth, WidgetColor } from "../struct.slint";
import { OptTrd_InExerciseComb } from "struct.slint";

export component InExercisePageComb {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <int> select_row_index: -1;
    function req_cancel_exec(need_confirm: bool) {
        OptTrd_InExerciseComb.sel_cancel_exec.exchid = "";
        if select_row_index >= 0 && OptTrd_InExerciseComb.row_data.length > select_row_index {
            OptTrd_InExerciseComb.sel_cancel_exec.exchid = OptTrd_InExerciseComb.row_data[select_row_index][1];
            OptTrd_InExerciseComb.sel_cancel_exec.accountid = OptTrd_InExerciseComb.row_data[select_row_index][0];
            OptTrd_InExerciseComb.sel_cancel_exec.ordersysid = OptTrd_InExerciseComb.row_data[select_row_index][8];
        }
        OptTrd_InExerciseComb.cancel_exec_clieked(OptTrd_InExerciseComb.sel_cancel_exec, need_confirm);
    }

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 6,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("看涨合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_95 },
                    { title: @tr("看跌合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_95 },
                    { title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("执行结果"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("执行组合编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地执行组合编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },

                ]
            };
            row_count: OptTrd_InExerciseComb.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_InExerciseComb.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_clicked(row_index, column_index) => {
                self.select_full_row = true;
            }

            cell_double_clicked(row_index, column_index) => {
                req_cancel_exec(true);
            }

            current_cell_changed(row_index, column_index) => {
                select_row_index = row_index;
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_InExerciseComb.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_InExerciseComb.row_data[row_index]);
                }
            }
        }

        HorizontalLayout {
            padding: 6px;
            spacing: 30px;
            height: 36px;

            Button {
                text: "撤单";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    req_cancel_exec(true);
                }
            }

            Button {
                text: "全撤";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    OptTrd_InExerciseComb.cancel_exec_all_clieked(OptTrd_InExerciseComb.row_data.length, true);
                }
            }
        }
    }
}
