use std::{
    fs,
    io::{Read, Write},
    path::Path,
    sync::{<PERSON>, <PERSON>te<PERSON>, OnceLock, RwLock},
};

use crate::{
    apiproc::{
        mdbuf::MdBuf, mdspi::MdSpi, riskstkbuf::RiskStkBuf, riskstkspi::RiskStkSpi, tradefutbuf::TradeFutBuf,
        tradefutspi::TradeFutSpi, tradeoptbuf::TradeOptBuf, tradeoptspi::TradeOptSpi, tradestkbuf::TradeStkBuf,
        tradestkspi::TradeStkSpi,
    },
    common::{ticode, tistruct},
    ui::IApp,
    version,
};
use crate::{common::tistruct::QrySvrInfoField, error::Result};
use mdapi::mdapi::MdApi;
use std::{collections::HashMap, io::Cursor};
use tiapi::{
    api::{riskstkapi::RiskStkApi, tradefutapi::TradeFutApi, tradeoptapi::TradeOptApi, tradestkapi::TradeStkApi},
    create_risk_stk_api, create_trade_fut_api, create_trade_opt_api, create_trade_stk_api,
    protocol_pub::{
        account_trade_ctp::CtpReqQryTradingCodeField,
        instrument_trade_ctp::{CtpReqQryFJYInstrumentField, CtpReqQryInstrumentField},
        login_risk::ReqLoginField,
        login_trade_ctp::CtpReqLoginField,
    },
};

use super::{
    config::Config,
    db::DbSqlite,
    instrument::Instrument,
    tistruct::{
        ConvertBondsMonitorField, EClientType, EServerType, FilePathField, LogInfoFiled, OrderInsertCancelMonitorField,
        ServerInfoField, ThemeField, TradePositionMonitorField, TradeSelfMonitorField,
    },
};

pub static TOKIO_RT: OnceLock<tokio::runtime::Runtime> = OnceLock::new();

pub static IAPP: OnceLock<Arc<RwLock<Box<dyn IApp>>>> = OnceLock::new();
pub static IRSKPAGE: OnceLock<Arc<RwLock<Box<dyn IApp>>>> = OnceLock::new();

pub static CFG: OnceLock<Arc<RwLock<Config>>> = OnceLock::new();
pub static INS: OnceLock<Arc<RwLock<Instrument>>> = OnceLock::new();
pub static DB: OnceLock<Arc<Mutex<DbSqlite>>> = OnceLock::new();

pub static MD_API: OnceLock<Arc<tokio::sync::RwLock<Box<dyn MdApi>>>> = OnceLock::new();
pub static MD_BUF: OnceLock<Arc<RwLock<MdBuf>>> = OnceLock::new();

pub static TRADE_STK_API: OnceLock<Arc<tokio::sync::RwLock<Box<dyn TradeStkApi>>>> = OnceLock::new();
pub static TRADE_STK_BUF: OnceLock<Arc<RwLock<TradeStkBuf>>> = OnceLock::new();

pub static TRADE_FUT_API: OnceLock<Arc<RwLock<Box<dyn TradeFutApi>>>> = OnceLock::new();
pub static TRADE_FUT_BUF: OnceLock<Arc<RwLock<TradeFutBuf>>> = OnceLock::new();

pub static TRADE_OPT_API: OnceLock<Arc<RwLock<Box<dyn TradeOptApi>>>> = OnceLock::new();
pub static TRADE_OPT_BUF: OnceLock<Arc<RwLock<TradeOptBuf>>> = OnceLock::new();

pub static RISK_STK_API: OnceLock<Arc<tokio::sync::RwLock<Box<dyn RiskStkApi>>>> = OnceLock::new();
pub static RISK_STK_BUF: OnceLock<Arc<RwLock<RiskStkBuf>>> = OnceLock::new();

/// 程序启动的准备工作(初始化)
pub fn prepare_init() -> Result<()> {
    prepare()?;
    setup_logger()?;
    Ok(())
}

/// 初始化
pub fn init() -> Result<()> {
    init_cfg()?;
    Ok(())
}

/// 释放资源
pub async fn release() {
    if let Some(iapp) = IRSKPAGE.get() {
        iapp.write().unwrap().teardown();
    }
    log::info!("iapp rsk release successfully");

    if let Some(iapp) = IAPP.get() {
        iapp.write().unwrap().teardown();
    }
    log::info!("iapp release successfully");

    release_trdstkapi().await;
    release_trdfutapi().await;
    release_trdoptapi().await;
    log::info!("api release successfully");

    let mdapi = MD_API.get();
    if let Some(api) = mdapi {
        let mut api = api.write().await;
        let _ = api.req_logout().await;
        api.release().await;
    }
    log::info!("md release successfully");

    let db = DB.get();
    if let Some(db) = db {
        db.lock().unwrap().release();
    }
    log::info!("db release successfully");
    log::info!("app release successfully");
}

///登录前切换服务端
pub async fn change_server(svr_name: &str) -> Result<()> {
    let mut cfg = CFG.get().unwrap().write().unwrap();

    if !cfg.com.SIFMap.contains_key(svr_name) {
        return Err(format!("not found server config for {}", svr_name));
    }

    let sif = cfg.com.SIFMap.get(svr_name).unwrap().clone();
    if sif.Name == cfg.com.SIF.Name {
        return Ok(());
    }

    // 释放已经初始化的API
    if EClientType::StkTrd == cfg.com.ClientType || EClientType::StkCreditTrd == cfg.com.ClientType {
        release_trdstkapi().await;
    }
    if EClientType::FutTrd == cfg.com.ClientType {
        release_trdfutapi().await;
    }
    if EClientType::StkOptTrd == cfg.com.ClientType {
        release_trdoptapi().await;
    }
    if EClientType::Stk == cfg.com.ClientType || EClientType::StkCredit == cfg.com.ClientType {
        release_riskstkapi().await;
    }

    // 初始化对应的API
    let clitype = EClientType::try_from(sif.CliType).unwrap();
    if EClientType::StkTrd == clitype || EClientType::StkCreditTrd == clitype {
        init_trade_stk().await?;
        cfg.com.SIF = sif;
        cfg.com.ClientType = clitype;
    } else if EClientType::FutTrd == clitype {
        init_trade_fut()?;
        cfg.com.SIF = sif;
        cfg.com.ClientType = clitype;
    } else if EClientType::StkOptTrd == clitype {
        init_trade_opt()?;
        cfg.com.SIF = sif;
        cfg.com.ClientType = clitype;
    } else if EClientType::Stk == clitype || EClientType::StkCredit == clitype {
        init_risk_stk().await?;
        cfg.com.SIF = sif;
        cfg.com.ClientType = clitype;
    } else {
        cfg.com.SIF = ServerInfoField::default();
        cfg.com.ClientType = EClientType::Unknown;
        return Err(format!("ClientType[{:?}] not implemented, ServerName:{}", clitype, svr_name));
    }

    Ok(())
}

/// 获取校验码
pub async fn get_vercode() -> Result<(i32, image::DynamicImage)> {
    let cfg = CFG.get().unwrap().read().unwrap();

    let ret = cfg.com.SIF.get_url();
    if let Err(err) = ret {
        return Err(err);
    }

    let url = format!("{}/vercode", ret.ok().unwrap());
    let ret = tiapi::get_vercode(1 == cfg.com.SIF.encon, &url).await;
    if let Ok(si) = ret {
        let ret = image::ImageReader::new(Cursor::new(si.1)).with_guessed_format();
        if let Ok(ret) = ret {
            let ret = ret.decode();
            if let Ok(img) = ret {
                Ok((si.0, img))
            } else {
                Err(ret.err().unwrap().to_string())
            }
        } else {
            Err(ret.err().unwrap().to_string())
        }
    } else {
        Err(ret.err().unwrap())
    }
}

/// 请求登录
pub async fn req_login(vc_seqnum: i32, vc_code: &str, userid: &str, passwd: &str) -> Result<()> {
    let col = collect::get_info()?;
    log::info!("req_login: collect info successfully");

    let ipmac = collect::get_default_ipmac()?;
    log::info!("req_login: collect get_default_ipmac successfully");

    get_svrinfo().await?;
    log::info!("req_login: get_svrinfo successfully");

    let (clitype, sif) = {
        let cfg = CFG.get().unwrap().read().unwrap();
        (cfg.com.ClientType, cfg.com.SvrInfo.clone())
    };

    #[cfg(not(debug_assertions))]
    {
        if EClientType::StkTrd != clitype
            && EClientType::StkCreditTrd != clitype
            && EClientType::FutTrd != clitype
            && EClientType::StkOptTrd != clitype
            && EClientType::StkCredit != clitype
        {
            return Err(std::format!("当前类型的客户端功能还不支持Release版本"));
        }
    }

    if EClientType::StkTrd == clitype
        || EClientType::StkCreditTrd == clitype
        || EClientType::FutTrd == clitype
        || EClientType::StkOptTrd == clitype
    {
        let mut req = CtpReqLoginField::default();
        req.exrepeatinfo = col.exinfo.clone();
        req.tirepeatinfo = col.tiinfo.clone();
        req.UserID = userid.to_owned();
        req.Password = passwd.to_owned();
        req.cliver = std::format!("{}|{}", version::CRATE_VERSION, tiapi::get_version());
        req.apiver = std::format!("{} ti_trade(r)", version::CRATE_CLIENT_VER);
        req.vcseqnum = vc_seqnum;
        req.vcdata = vc_code.to_owned();
        req.clienttype = 1;
        req.MacAddress = ipmac.mac.to_owned();

        let log_ret: tiapi::protocol_pub::login_trade_ctp::CtpLoginRetField;

        if EClientType::StkTrd == clitype || EClientType::StkCreditTrd == clitype {
            let mut stkapi = TRADE_STK_API.get().unwrap().write().await;
            log::info!("req_login: get stkapi successfully");

            stkapi.set_svrinfo(&sif).await;
            log::info!("req_login: set_svrinfo successfully");

            log::info!("req_login: req_login");
            log_ret = stkapi.req_login(&req).await?;
        } else if EClientType::FutTrd == clitype {
            let mut futapi = TRADE_FUT_API.get().unwrap().write().unwrap();
            log::info!("req_login: get futapi successfully");

            futapi.set_svrinfo(&sif);
            log::info!("req_login: set_svrinfo successfully");

            log::info!("req_login: req_login");
            log_ret = futapi.req_login(&req)?;
        } else if EClientType::StkOptTrd == clitype {
            let mut optapi = TRADE_OPT_API.get().unwrap().write().unwrap();
            log::info!("req_login: get optapi successfully");

            optapi.set_svrinfo(&sif);
            log::info!("req_login: set_svrinfo successfully");

            log::info!("req_login: req_login");
            log_ret = optapi.req_login(&req)?;

            if 0 == log_ret.ec {
                let mut cfg = CFG.get().unwrap().write().unwrap();
                cfg.com.SvrTypeDtl = log_ret.func; // 借用服务端具体类型保存用户权限(0：普通交易; 1:做市商交易)
            }
        } else {
            log_ret = Default::default();
        }

        if 0 != log_ret.ec {
            let mut errmsg = log_ret.em + "\n\n";
            for rt in log_ret.arr {
                errmsg += &rt.to_string();
                errmsg += "\n";
            }
            return Err(errmsg);
        }
        log::info!("req_login: successfully");

        // 保存登录的详细信息,用于界面展示
        {
            let mut cfg = CFG.get().unwrap().write().unwrap();
            cfg.run.CtpLoginVec = log_ret.arr;
            cfg.run.TradingDay = cfg
                .run
                .CtpLoginVec
                .iter()
                .find(|lf| lf.ec == 0 && !lf.TradingDay.is_empty())
                .map(|lf| lf.TradingDay.parse::<i32>().unwrap_or_default())
                .unwrap_or_default();

            log::info!("req_login: TradingDay:{}", cfg.run.TradingDay);

            #[cfg(debug_assertions)]
            {
                cfg.run.TradingDay = 20250403;
            }
        }
    } else if EClientType::Stk == clitype || EClientType::StkCredit == clitype {
        let mut req = ReqLoginField::default();
        req.uid = userid.to_owned();
        req.passwd = passwd.to_owned();
        req.exrepeatinfo = col.exinfo.clone();
        req.tirepeatinfo = col.tiinfo.clone();
        req.cliver = std::format!("{}|{}", version::CRATE_VERSION, tiapi::get_version());
        req.apiver = std::format!("{} ti_trade(r)", version::CRATE_CLIENT_VER);
        req.vcseqnum = vc_seqnum;
        req.vcdata = vc_code.to_owned();
        req.macaddress = ipmac.mac.to_owned();

        if EClientType::Stk == clitype || EClientType::StkCredit == clitype {
            let mut stkapi = RISK_STK_API.get().unwrap().write().await;
            log::info!("req_login: get stkapi successfully");

            stkapi.set_svrinfo(&sif).await;
            log::info!("req_login: set_svrinfo successfully");

            log::info!("req_login: req_login");
            let log_ret = stkapi.req_login(&req).await?;
        }
    } else {
        return Err(std::format!("当前类型[{:?}]的客户端功能还不支持", clitype));
    }

    Ok(())
}

/// 请求查询交易编码
pub async fn req_qry_tradingcode() -> Result<()> {
    let clitype: EClientType;
    {
        clitype = CFG.get().unwrap().read().unwrap().com.ClientType;
    }

    let mut errmsg = String::new();

    if EClientType::StkTrd == clitype || EClientType::StkCreditTrd == clitype {
        let stkapi = TRADE_STK_API.get().unwrap().read().await;

        if let Err(err) = stkapi.req_qry_tradingcode(0, &CtpReqQryTradingCodeField::default()).await {
            errmsg = errmsg + &format!("请求查询交易编码失败.{}", err);
        }
    }

    if !errmsg.is_empty() {
        return Err(errmsg);
    }

    Ok(())
}

/// 请求查询合约
pub async fn req_qry_instrument() -> Result<()> {
    let clitype: EClientType;
    {
        clitype = CFG.get().unwrap().read().unwrap().com.ClientType;
    }

    let mut errmsg = String::new();

    if EClientType::StkTrd == clitype || EClientType::StkCreditTrd == clitype {
        let stkapi = TRADE_STK_API.get().unwrap().read().await;

        if let Err(err) = stkapi.req_qry_instrument(0, &CtpReqQryInstrumentField::default()).await {
            errmsg = errmsg + &format!("请求查询证券信息失败.{}", err);
        }

        if EClientType::StkTrd == clitype {
            if let Err(err) = stkapi
                .req_qry_fjy_instrument(0, &CtpReqQryFJYInstrumentField::default())
                .await
            {
                errmsg = errmsg + &format!("\n请求查询非交易证券信息失败.{}", err);
            }
        }
    } else if EClientType::FutTrd == clitype {
        let futapi = TRADE_FUT_API.get().unwrap().write().unwrap();

        if let Err(err) = futapi.req_qry_instrument(0, &CtpReqQryInstrumentField::default()) {
            errmsg = errmsg + &format!("请求查询合约失败.{}", err);
        }
    } else if EClientType::StkOptTrd == clitype {
        let optapi = TRADE_OPT_API.get().unwrap().write().unwrap();
        if let Err(err) = optapi.req_qry_instrument(0, &CtpReqQryInstrumentField::default()) {
            errmsg = errmsg + &format!("请求查询合约失败.{}", err);
        }
    } else if EClientType::Stk == clitype || EClientType::StkCredit == clitype {
        let stkapi = RISK_STK_API.get().unwrap().read().await;

        if let Err(err) = stkapi.req_qry_instrument(0).await {
            errmsg = errmsg + &format!("请求查询证券信息失败.{}", err);
        }

        if EClientType::Stk == clitype {
            if let Err(err) = stkapi.req_qry_fjy_instrument(0).await {
                errmsg = errmsg + &format!("\n请求查询非交易证券信息失败.{}", err);
            }
        }
    }

    if !errmsg.is_empty() {
        return Err(errmsg);
    }

    Ok(())
}

/// 初始化行情
pub async fn init_md() -> Result<()> {
    if MD_BUF.get().is_none() {
        let _ = MD_BUF.set(Arc::new(RwLock::new(MdBuf::new())));
    }

    if MD_API.get().is_none() {
        let mut spi = MdSpi::new();
        spi.register_buf(MD_BUF.get().unwrap().clone());
        let _ = MD_API.set(Arc::new(tokio::sync::RwLock::new(mdapi::create_mdapi(spi).unwrap())));
    }

    let si: mdapi::serverinfo::MDServerInfo;
    {
        let cfg = CFG.get().unwrap().read().unwrap();
        si = cfg.com.MdSrvInfo.clone();
    }

    let mut mdapi = MD_API.get().unwrap().write().await;

    mdapi.set_svrinfo(&si).await;

    let ret = mdapi.init().await;
    if let Err(err) = ret {
        return Err(format!("mdapi init failed. {}", err));
    }

    let req = mdapi::protocol::ptmd::PTReqMdLogin::default();
    let ret = mdapi.req_login(&req).await;
    if let Err(err) = ret {
        return Err(format!("mdapi req_login failed. {}", err));
    }

    Ok(())
}

/// 解锁
pub fn unlock(passwd: &str) -> bool {
    let mut passwd = passwd.to_owned().into_bytes();
    ticode::SimpleCode::ecb_encode(&mut passwd);

    let cfg = CFG.get().unwrap().read().unwrap();
    cfg.run.LoginUser.UNLockStr == passwd
}

/// 获取用于在交易终端状态栏上显示的登录信息<br><br>
/// 返回值: (登录用户名, 登录状态, 登录提示信息)
/// 登录状态: 1:正常; 2:异常: 3:部分正常; 4:未设置
pub fn get_ss_trd_longinfo() -> (String, i32, String) {
    let cfg = CFG.get().unwrap().read().unwrap();

    let mut login_status = 4;
    let mut login_tips = "登录详情\n\n".to_owned();

    let mut success_cnt = 0;
    for lr in &cfg.run.CtpLoginVec {
        if 0 == lr.ec {
            success_cnt += 1;
        }

        let tmp = lr.to_string();
        login_tips += tmp.as_str();
        login_tips += "\n";
    }
    login_tips.remove(login_tips.len() - 1);

    if success_cnt == cfg.run.CtpLoginVec.len() {
        login_status = 1;
    } else if login_status > 0 {
        login_status = 3;
    } else {
        login_status = 2;
    }

    (cfg.run.LoginUser.UserID.clone(), login_status, login_tips)
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/// 读取si.json
fn read_si(path: &str) -> Result<HashMap<String, ServerInfoField>> {
    let mut si_map: HashMap<String, ServerInfoField> = HashMap::new();

    match fs::read_to_string(&path) {
        Ok(si_datas) => {
            let ret: std::result::Result<Vec<ServerInfoField>, serde_json::Error> = serde_json::from_str(&si_datas);
            match ret {
                Ok(si_vec) => {
                    si_vec.iter().for_each(|si| {
                        if si.Name.is_empty() || !si.check_clitype() {
                            return;
                        }
                        si_map.insert(si.Name.clone(), si.clone());
                    });
                }
                Err(err) => {
                    return Err(std::format!("parse server_info failed. {}", err));
                }
            }
        }
        Err(err) => {
            return Err(std::format!("read server_info failed. {}", err));
        }
    };

    Ok(si_map)
}

/// 程序启动的准备工作
fn prepare() -> Result<()> {
    let _ = CFG.set(Arc::new(RwLock::new(Default::default())));

    let mut cfg = CFG.get().unwrap().write().unwrap();

    // 获取程序所在目录
    {
        cfg.com.FPath.ExeDir = "./".to_owned(); // 默认为当前目录
        let ret = std::env::current_exe();
        if ret.is_ok() {
            if let Some(parent) = ret.unwrap().parent() {
                cfg.com.FPath.ExeDir = parent.display().to_string();
            }
        }
    }

    // 获取工作目录
    {
        if cfg!(target_os = "macos") {
            let ret = std::env::var("HOME");
            if ret.is_ok() {
                cfg.com.FPath.WorkDir = format!("{}/Library/Application Support/ti/ticlient", ret.unwrap());
            } else {
                cfg.com.FPath.WorkDir = cfg.com.FPath.ExeDir.clone();
            }
        }

        if cfg!(target_os = "linux") {
            let ret = std::env::var("HOME");
            if ret.is_ok() {
                cfg.com.FPath.WorkDir = format!("{}/.ti/ticlient", ret.unwrap());
            } else {
                cfg.com.FPath.WorkDir = cfg.com.FPath.ExeDir.clone();
            }
        }

        if cfg!(target_os = "windows") {
            let ret = std::env::var("USERPROFILE");
            if ret.is_ok() {
                cfg.com.FPath.WorkDir = format!("{}/.ti/ticlient", ret.unwrap());
            } else {
                cfg.com.FPath.WorkDir = cfg.com.FPath.ExeDir.clone();
            }
        }

        if cfg.com.FPath.WorkDir.is_empty() {
            return Err("Get work directory failed. Unsupported operating system".to_owned());
        }
    }

    // 获取资源目录
    {
        if cfg!(target_os = "macos") {
            cfg.com.FPath.ResDir = cfg.com.FPath.WorkDir.clone();
        } else {
            cfg.com.FPath.ResDir = cfg.com.FPath.ExeDir.clone();
        }
    }

    // 创建目录用于保存数据
    {
        // 日志目录
        let logdir = if cfg!(target_os = "macos") {
            format!("{}/logs", cfg.com.FPath.WorkDir)
        } else {
            format!("{}/logs", cfg.com.FPath.ExeDir)
        };
        let ret = create_dir(logdir.clone());
        if let Err(err) = ret {
            return Err("Create log dir failed".to_owned());
        }

        // 用户目录
        let ret = create_dir(format!("{}/{}", cfg.com.FPath.WorkDir, FilePathField::USER_DIR));
        if let Err(err) = ret {
            return Err("Create user data dir failed".to_owned());
        }
    }

    Ok(())
}

/// 初始化配置
fn init_cfg() -> Result<()> {
    let _ = INS.set(Arc::new(RwLock::new(Instrument::new())));
    let _ = DB.set(Arc::new(Mutex::new(DbSqlite::new())));

    let mut cfg = CFG.get().unwrap().write().unwrap();

    // 读取配置文件
    let path = format!("{}/{}", cfg.com.FPath.ResDir, FilePathField::SERVER_INFO_PATH);
    let mut si_map = match read_si(&path) {
        Ok(si_map) => si_map,
        Err(err) => {
            #[cfg(debug_assertions)]
            {
                log::info!("init_cfg: path:{}. {}", path, err);
            }

            HashMap::new()
        }
    };

    // 如果是 macOS, 当没有读取到配置时, 再读取一次包中的配置文件
    if cfg!(target_os = "macos") {
        if si_map.is_empty() {
            let path = std::format!("{}/../Resources/{}", cfg.com.FPath.ExeDir, FilePathField::SERVER_INFO_PATH);
            match read_si(&path) {
                Ok(tmp_map) => si_map = tmp_map,
                Err(err) => {
                    #[cfg(debug_assertions)]
                    {
                        log::info!("init_cfg: read_si in macOS failed again. path:{}. {}", path, err);
                    }
                }
            };
        }
    }

    si_map.iter_mut().for_each(|it| {
        let si = it.1;

        let clitype = EClientType::try_from(si.CliType).ok().unwrap();
        if EClientType::StkOpt == clitype || EClientType::Stk == clitype || EClientType::Fut == clitype {
            if si.TrdHost.is_empty() {
                si.TrdHost = si.Host.clone();
                si.TrdPort = si.Port;
            }
        }

        cfg.com.SIFMap.insert(si.Name.clone(), si.clone());
    });

    #[cfg(debug_assertions)]
    {
        log::info!("SIFMap: {:?}", cfg.com.SIFMap);
    }

    Ok(())
}

/// 创建日志
fn setup_logger() -> Result<()> {
    // 读取日志配置
    {
        let mut cfg = CFG.get().unwrap().write().unwrap();

        let path = std::format!("{}/{}", cfg.com.FPath.ResDir, FilePathField::LOG_INFO_PATH);
        let ret = fs::read_to_string(&path);
        if ret.is_ok() {
            let datas = ret.unwrap();
            let ret: std::result::Result<LogInfoFiled, serde_json::Error> = serde_json::from_str(&datas);
            if ret.is_ok() {
                cfg.com.LogF = ret.unwrap();

                #[cfg(debug_assertions)]
                {
                    println!("LogF: {:?}", cfg.com.LogF);
                }
            }
        }
    }

    let file_path = {
        let cfg = CFG.get().unwrap().read().unwrap();
        let logdir = if cfg!(target_os = "macos") {
            format!("{}/logs", cfg.com.FPath.WorkDir)
        } else {
            format!("{}/logs", cfg.com.FPath.ExeDir)
        };

        std::format!("{}/ti_{}.log", logdir, chrono::Local::now().format("%Y-%m-%d_%H_%M_%S"))
    };

    let f = fern::log_file(file_path.clone());
    if let Err(err) = f {
        return Err(std::format!(
            "create log file failed. path:{}, {}",
            file_path,
            err.to_string()
        ));
    }

    let mut loginfo = {
        let cfg = CFG.get().unwrap().read().unwrap();
        cfg.com.LogF.clone()
    };
    let mut has_set_reqwest = false;
    for m in &mut loginfo.module {
        if "reqwest" == m.name {
            if m.level > 3 {
                m.level = 3;
            }
            has_set_reqwest = true;
        }
    }
    if !has_set_reqwest {
        loginfo.module.push(tistruct::LogModuleField {
            name: "reqwest".to_owned(),
            level: 3,
        });
    }

    let mut dispatch = fern::Dispatch::new().format(|out, message, record| {
        out.finish(format_args!(
            "[{} {} {}:{}] {}",
            chrono::Local::now().format("%Y%m%d %H:%M:%S.%3f"),
            record.level(),
            record.target(),
            record.line().or_else(|| { Some(0) }).unwrap(),
            message
        ))
    });

    for ml in &loginfo.module {
        dispatch = dispatch.level_for(ml.name.clone(), tistruct::get_log_level(ml.level));
    }
    dispatch = dispatch.level(tistruct::get_log_level(loginfo.level)).chain(f.unwrap());

    #[cfg(debug_assertions)]
    {
        dispatch = dispatch.chain(std::io::stdout());
    }

    let apply = dispatch.apply();
    if let Err(err) = apply {
        return Err(std::format!("apply log dispatch failed. {}", err.to_string()));
    }

    Ok(())
}

/// 初始化现货交易
async fn init_trade_stk() -> Result<()> {
    if TRADE_STK_BUF.get().is_none() {
        let _ = TRADE_STK_BUF.set(Arc::new(RwLock::new(TradeStkBuf::new())));
    }

    if TRADE_STK_API.get().is_none() {
        let mut spi = TradeStkSpi::new();
        spi.register_buf(TRADE_STK_BUF.get().unwrap().clone());
        let _ = TRADE_STK_API.set(Arc::new(tokio::sync::RwLock::new(create_trade_stk_api(spi).unwrap())));
    }

    let mut stkapi = TRADE_STK_API.get().unwrap().write().await;
    stkapi.init().await;

    Ok(())
}

/// 初始化现货风控
async fn init_risk_stk() -> Result<()> {
    if RISK_STK_BUF.get().is_none() {
        let _ = RISK_STK_BUF.set(Arc::new(RwLock::new(RiskStkBuf::new())));
    }

    if RISK_STK_API.get().is_none() {
        let mut spi = RiskStkSpi::new();
        spi.register_buf(RISK_STK_BUF.get().unwrap().clone());
        let _ = RISK_STK_API.set(Arc::new(tokio::sync::RwLock::new(create_risk_stk_api(spi).unwrap())));
    }

    let mut stkapi = RISK_STK_API.get().unwrap().write().await;
    stkapi.init().await;

    Ok(())
}

/// 初始化期货交易
fn init_trade_fut() -> Result<()> {
    if TRADE_FUT_BUF.get().is_none() {
        let _ = TRADE_FUT_BUF.set(Arc::new(RwLock::new(TradeFutBuf::new())));
    }

    if TRADE_FUT_API.get().is_none() {
        let mut spi = TradeFutSpi::new();
        spi.register_buf(TRADE_FUT_BUF.get().unwrap().clone());
        let _ = TRADE_FUT_API.set(Arc::new(RwLock::new(create_trade_fut_api(spi).unwrap())));
    }

    let mut futapi = TRADE_FUT_API.get().unwrap().write().unwrap();
    futapi.init();

    Ok(())
}

/// 初始化个股期权交易
fn init_trade_opt() -> Result<()> {
    if TRADE_OPT_BUF.get().is_none() {
        let _ = TRADE_OPT_BUF.set(Arc::new(RwLock::new(TradeOptBuf::new())));
    }

    if TRADE_OPT_API.get().is_none() {
        let mut spi = TradeOptSpi::new();
        spi.register_buf(TRADE_OPT_BUF.get().unwrap().clone());
        let _ = TRADE_OPT_API.set(Arc::new(RwLock::new(create_trade_opt_api(spi).unwrap())));
    }

    let mut optapi = TRADE_OPT_API.get().unwrap().write().unwrap();
    optapi.init();

    Ok(())
}

/// 释放现货交易API
async fn release_trdstkapi() {
    let trdstkapi = TRADE_STK_API.get();
    if let Some(api) = trdstkapi {
        api.write().await.release().await;
    }
}

/// 释放现货风控API
async fn release_riskstkapi() {
    let stkapi = RISK_STK_API.get();
    if let Some(api) = stkapi {
        api.write().await.release().await;
    }
}

/// 释放期货交易API
async fn release_trdfutapi() {
    let trdfutapi = TRADE_FUT_API.get();
    if let Some(api) = trdfutapi {
        api.write().unwrap().release();
    }
}

/// 释放个股期权交易API
async fn release_trdoptapi() {
    let trdoptapi = TRADE_OPT_API.get();
    if let Some(api) = trdoptapi {
        api.write().unwrap().release();
    }
}

/// 创建目录
fn create_dir(dirpath: String) -> Result<()> {
    let metadata = fs::metadata(dirpath.clone());
    if metadata.is_ok() && metadata.unwrap().is_dir() {
        return Ok(());
    }

    let ret = fs::create_dir_all(dirpath.clone());
    if let Some(err) = ret.err() {
        return Err(err.to_string());
    }

    Ok(())
}

/// 获取服务端信息
async fn get_svrinfo() -> Result<()> {
    let (encon, url) = {
        let cfg = CFG.get().unwrap().read().unwrap();
        let url = cfg.com.SIF.get_url()?;
        (1 == cfg.com.SIF.encon, url)
    };

    let si = tiapi::get_svrinfo(encon, &url).await?;
    #[cfg(debug_assertions)]
    {
        log::info!("get svrinfo success: {}", si);
    }

    let ret: std::result::Result<QrySvrInfoField, serde_json::Error> = serde_json::from_str(&si);
    if ret.is_err() {
        return Err(format!("Parse SvrInfo failed. {}", ret.err().unwrap().to_string()));
    }
    let qsi = ret.ok().unwrap();
    #[cfg(debug_assertions)]
    {
        log::info!("parse svrinfo success: {}", si);
    }

    let mut cfg = CFG.get().unwrap().write().unwrap();
    cfg.com.SvrType = EServerType::try_from(qsi.st).unwrap_or_default();
    cfg.com.SvrTypeDtl = qsi.std;
    cfg.com.SvrBuildDate = qsi.bd;
    cfg.com.SvrOther = qsi.tc;

    cfg.com.MdSrvInfo.encon = cfg.com.SIF.encon;
    cfg.com.MdSrvInfo.host = cfg.com.SIF.Host.clone();
    cfg.com.MdSrvInfo.uri = qsi.mduri.clone();

    cfg.com.SvrInfo.encon = cfg.com.SIF.encon;
    cfg.com.SvrInfo.host = cfg.com.SIF.Host.clone();
    cfg.com.SvrInfo.req_uri = qsi.requri.clone();
    cfg.com.SvrInfo.pub_uri = qsi.puburi.clone();

    if 1 == cfg.com.SIF.proxy {
        cfg.com.MdSrvInfo.port = cfg.com.SIF.Port;
        cfg.com.SvrInfo.req_port = cfg.com.SIF.Port;
        cfg.com.SvrInfo.pub_port = cfg.com.SIF.Port;
    } else {
        cfg.com.MdSrvInfo.port = qsi.mdport;
        cfg.com.SvrInfo.req_port = qsi.reqport;
        cfg.com.SvrInfo.pub_port = qsi.pubport;
    }

    #[cfg(debug_assertions)]
    {
        log::info!(
            "svrtype: {:?}, svrtypedtl:{}, SvrBuildDate:{}",
            cfg.com.SvrType,
            cfg.com.SvrTypeDtl,
            cfg.com.SvrBuildDate
        );
        log::info!("svrinfo: {:?}", cfg.com.SvrInfo);
        log::info!("mdsvrinfo: {:?}", cfg.com.MdSrvInfo);
    }

    Ok(())
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// 配置

/// 更新登录用户信息
pub fn update_login_user_info(uid: &str, passwd: &str) {
    let mut cfg = CFG.get().unwrap().write().unwrap();

    // 保存登录信息
    cfg.run.LoginUser.UserID = uid.to_owned();
    cfg.run.LoginUser.UNLockStr = passwd.to_owned().into_bytes();
    ticode::SimpleCode::ecb_encode(&mut cfg.run.LoginUser.UNLockStr);

    // 创建用户目录
    cfg.com.FPath.LoginUserDir = std::format!("{}/{}/{}", cfg.com.FPath.WorkDir, FilePathField::USER_DIR, uid);
    cfg.com.FPath.LoginUserCfgDir = std::format!("{}/cfg", cfg.com.FPath.LoginUserDir);
    cfg.com.FPath.LoginUserDataDir = std::format!("{}/data", cfg.com.FPath.LoginUserDir);
    cfg.com.FPath.LoginUserDataDbDir = std::format!("{}/db", cfg.com.FPath.LoginUserDataDir);
    cfg.com.FPath.LoginUserLayoutDir = std::format!("{}/layout", cfg.com.FPath.LoginUserDir);
    let _ = create_dir(cfg.com.FPath.LoginUserCfgDir.clone());
    let _ = create_dir(cfg.com.FPath.LoginUserDataDir.clone());
    let _ = create_dir(cfg.com.FPath.LoginUserDataDbDir.clone());
    let _ = create_dir(cfg.com.FPath.LoginUserLayoutDir.clone());

    // 主题
    {
        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::THEME_PATH);
        if let Some(var) = deserialize_user_data::<tistruct::ThemeField>(&read_user_data_from_file(&path)) {
            cfg.com.Theme = var;
        }
        if 0 != cfg.com.Theme.Style && 1 != cfg.com.Theme.Style && 2 != cfg.com.Theme.Style {
            cfg.com.Theme.Style = ThemeField::STYLE;
            cfg.com.Theme.Palette = ThemeField::PALETTE.to_owned();
        }
    }

    // 读取用户自定义配置
    if EClientType::StkTrd == cfg.com.ClientType {
        // 异常监控-可转债
        {
            let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::COVRT_BOND_MONITOR_PATH);
            if let Some(var) = deserialize_user_data::<ConvertBondsMonitorField>(&read_user_data_from_file(&path)) {
                cfg.stk_mon.CovrtBondMonitor = var;
            }
        }

        // 异常监控-报撤单
        {
            let path = std::format!(
                "{}/{}",
                cfg.com.FPath.LoginUserCfgDir,
                FilePathField::ORD_INSERT_CANCEL_MONITOR_PATH
            );
            if let Some(var) = deserialize_user_data::<OrderInsertCancelMonitorField>(&read_user_data_from_file(&path)) {
                cfg.stk_mon.OrdInsertCancelMonitor = var;
            }
        }

        // 异常监控-自成交
        {
            let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
            if let Some(var) = deserialize_user_data::<TradeSelfMonitorField>(&read_user_data_from_file(&path)) {
                cfg.stk_mon.TrdSelfMonitor = var;
            }
        }
    } else if EClientType::FutTrd == cfg.com.ClientType {
        // 异常监控-自成交
        {
            let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
            if let Some(var) = deserialize_user_data::<TradeSelfMonitorField>(&read_user_data_from_file(&path)) {
                cfg.fut_mon.TrdSelfMonitor = var;
            }
        }
    } else if EClientType::StkOptTrd == cfg.com.ClientType {
        // 异常监控-成交持仓比
        {
            let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_POS_MONITOR_PATH);
            if let Some(var) = deserialize_user_data::<TradePositionMonitorField>(&read_user_data_from_file(&path)) {
                cfg.opt_mon.TrdPosMonitor = var;
            }
        }

        // 异常监控-报撤单
        {
            let path = std::format!(
                "{}/{}",
                cfg.com.FPath.LoginUserCfgDir,
                FilePathField::ORD_INSERT_CANCEL_MONITOR_PATH
            );
            if let Some(var) = deserialize_user_data::<OrderInsertCancelMonitorField>(&read_user_data_from_file(&path)) {
                cfg.opt_mon.OrdInsertCancel = var;
            }
        }

        // 异常监控-自成交
        {
            let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
            if let Some(var) = deserialize_user_data::<TradeSelfMonitorField>(&read_user_data_from_file(&path)) {
                cfg.opt_mon.TrdSelfMonitor = var;
            }
        }
    }

    let mut db = DB.get().unwrap().lock().unwrap();
    let _ = db.init(&cfg.com.FPath.LoginUserDataDbDir, cfg.com.ClientType);
}

/// 保存默认的服务器名称
pub fn set_default_svr_name(svrname: &str) {
    let path = {
        let cfg = CFG.get().unwrap().read().unwrap();
        std::format!("{}/{}/sn", cfg.com.FPath.WorkDir, FilePathField::USER_DIR)
    };

    let file = std::fs::File::create(path);
    if let Ok(mut file) = file {
        let _ = file.write_all(svrname.to_owned().as_bytes());
    }
}

/// 获取默认的服务器名称
pub fn get_default_svr_name() -> Option<String> {
    let path = {
        let cfg = CFG.get().unwrap().read().unwrap();
        std::format!("{}/{}/sn", cfg.com.FPath.WorkDir, FilePathField::USER_DIR)
    };

    let file = std::fs::File::open(path);
    if let Ok(mut file) = file {
        let mut buf = String::new();
        let ret = file.read_to_string(&mut buf);
        if ret.is_ok() {
            return Some(buf);
        }
    }

    None
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/// 序列化用户数据并写入文件
pub fn serialize_write_user_data<T: ?Sized + serde::Serialize>(path: &str, var: &T, pretty: bool) {
    let ret = match pretty {
        true => serde_json::to_string_pretty(&var),
        false => serde_json::to_string(&var),
    };

    match ret {
        Ok(datas) => {
            match fs::write(path, &datas) {
                Err(err) => log::warn!(
                    "serialize_write_user_data: write failed. file:{:?}. {}",
                    Path::new(path).file_name().unwrap_or_default(),
                    err
                ),
                Ok(_) => {}
            };
        }
        Err(err) => {
            log::warn!(
                "serialize_write_user_data: serialize failed. file:{:?}. {}",
                Path::new(path).file_name().unwrap_or_default(),
                err
            );
        }
    }
}

/// 从文件中读取用户数据
pub fn read_user_data_from_file(path: &str) -> Option<String> {
    if let Ok(datas) = fs::read_to_string(&path) {
        return Some(datas);
    }
    None
}

/// 反序列化用户数据
pub fn deserialize_user_data<'a, T: serde::Deserialize<'a>>(datas: &'a Option<String>) -> Option<T> {
    if let Some(datas) = datas {
        if let Ok(var) = serde_json::from_str(datas) {
            return Some(var);
        }
    }

    None
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

/// 主界面初始化时的提示
#[derive(Default)]
pub struct AppInitTips {
    pub Status: bool,
    pub Msg: String,
}
pub static APP_INIT_TIPS: OnceLock<Arc<RwLock<AppInitTips>>> = OnceLock::new();

pub fn set_app_init_msg(msg: &str) {
    let tips = APP_INIT_TIPS.get_or_init(|| Arc::new(RwLock::new(AppInitTips::default())));
    let mut tips = tips.write().unwrap();
    tips.Msg = msg.to_string();
}
pub fn set_app_init_complete() {
    let tips = APP_INIT_TIPS.get_or_init(|| Arc::new(RwLock::new(AppInitTips::default())));
    let mut tips = tips.write().unwrap();
    tips.Status = true;
}
pub fn get_app_init_tips() -> (bool, String) {
    let tips = APP_INIT_TIPS.get();
    if tips.is_none() {
        return (false, "主界面正在初始化...".to_string());
    }
    let tips = tips.unwrap().read().unwrap();
    (tips.Status, tips.Msg.clone())
}
pub fn drop_app_init_tips() {
    if let Some(tips) = APP_INIT_TIPS.get() {
        let mut tips = tips.write().unwrap();
        tips.Status = false;
        tips.Msg.clear();
    }
}
