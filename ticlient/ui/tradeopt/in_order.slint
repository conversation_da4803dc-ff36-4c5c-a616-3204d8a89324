import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TableView } from "@zstk/lib.slint";
import { AppCom, ColWidth, WidgetColor } from "../struct.slint";
import { OptTrd_InOrder } from "struct.slint";

export component InOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <int> select_row_index: -1;
    function req_cancel_order(need_confirm: bool) {
        OptTrd_InOrder.sel_cancel_order.exchid = "";
        if select_row_index >= 0 && OptTrd_InOrder.row_data.length > select_row_index {
            OptTrd_InOrder.sel_cancel_order.exchid = OptTrd_InOrder.row_data[select_row_index][1];
            OptTrd_InOrder.sel_cancel_order.accountid = OptTrd_InOrder.row_data[select_row_index][0];
            OptTrd_InOrder.sel_cancel_order.ordersysid = OptTrd_InOrder.row_data[select_row_index][10];
        }
        OptTrd_InOrder.cancel_order_clieked(OptTrd_InOrder.sel_cancel_order, need_confirm);
    }

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 5,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insid },
                    { title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    { title: @tr("开平标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.openclose },
                    { title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    { title: @tr("报单状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("报单价格条件"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_pricetype },
                    { title: @tr("有效期类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_timecondition },
                    { title: @tr("成交量类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_90 },
                    { title: @tr("投资者类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_115 },
                    { title: @tr("触发条件"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("最小成交量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_90 },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                    { title: @tr("备兑标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.hedge },
                ]
            };
            row_count: OptTrd_InOrder.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_InOrder.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_clicked(row_index, column_index) => {
                self.select_full_row = true;
            }

            cell_double_clicked(row_index, column_index) => {
                req_cancel_order(true);
            }

            current_cell_changed(row_index, column_index) => {
                select_row_index = row_index;
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_InOrder.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_InOrder.row_data[row_index]);
                }
            }
        }

        HorizontalLayout {
            padding: 6px;
            spacing: 30px;
            height: 36px;

            Button {
                text: "撤单";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    req_cancel_order(true);
                }
            }

            Button {
                text: "全撤";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    OptTrd_InOrder.cancel_order_all_clieked(OptTrd_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤买";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    OptTrd_InOrder.cancel_order_bs_clieked(0, OptTrd_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤卖";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    OptTrd_InOrder.cancel_order_bs_clieked(1, OptTrd_InOrder.row_data.length, true);
                }
            }
        }
    }
}
