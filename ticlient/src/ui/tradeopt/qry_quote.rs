use std::rc::Rc;

use crate::{
    common::{config::Config, global::DB, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::order_trade_ctp::{CtpQuoteField, CtpReqQryQuoteField};

/// 查询 - 报价
pub(super) struct QueryQuote {}

impl QueryQuote {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_qryquote = app.global::<OptTrd_QryQuote>();

        let app_weak = app.as_weak();
        app_qryquote.on_qry_clieked(move || QueryQuote::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        app_qryquote.on_page_index_changed(move |index| QueryQuote::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        app_qryquote.on_page_size_changed(move |size| QueryQuote::on_page_size_changed(&app_weak, size));

        let app_weak = app.as_weak();
        app_qryquote.on_export_clicked(move |etype| QueryQuote::on_export_clicked(&app_weak, etype));

        log::trace!("Query quote init completed");
    }
}

impl QueryQuote {
    /// 获取一行数据
    fn get_row_items(quote: &CtpQuoteField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 资金账号
        items.push(quote.InvestorID.clone().into());

        // 交易所
        items.push(quote.ExchangeID.clone().into());

        // 交易编码
        items.push(quote.ClientID.clone().into());

        // 发生时间
        items.push(quote.InsertTime.clone().into());

        // 合约编码
        items.push(quote.InstrumentID.clone().into());

        // 报价状态
        items.push(TIConvert::order_status(quote.LastStatus).into());

        // 交易所报价编号
        items.push(quote.QuoteSysID.clone().into());

        // 本地报价编号
        items.push(quote.QuoteLocalID.clone().into());

        // 买报价状态
        items.push(TIConvert::order_status(quote.BidLastStatus).into());

        // 买报单编号
        items.push(quote.BidOrderSysID.clone().into());

        // 买开平标志
        items.push(if 0 != quote.BidVolume {
            TIConvert::ctp_offsetflag(quote.BidOffsetFlag).into()
        } else {
            "".into()
        });

        // 买价格
        items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, quote.BidPrice));

        // 买数量
        items.push(slint::format!("{}", quote.BidVolume));

        // 卖报价状态
        items.push(TIConvert::order_status(quote.AskLastStatus).into());

        // 卖报单编号
        items.push(quote.AskOrderSysID.clone().into());

        // 卖开平标志
        items.push(if 0 != quote.AskVolume {
            TIConvert::ctp_offsetflag(quote.AskOffsetFlag).into()
        } else {
            "".into()
        });

        // 卖价格
        items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, quote.AskPrice));

        // 卖数量
        items.push(slint::format!("{}", quote.AskVolume));

        // 用户代码
        items.push(quote.UserID.clone().into());

        items
    }

    // 查询
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qryquote = app.global::<OptTrd_QryQuote>();

        // 清空上一次结果
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qryquote.set_row_data(row_data.into());

        let st = app_qryquote.get_starttime();
        let et = app_qryquote.get_endtime();
        let ordstatus = app_qryquote.get_ordstatus();
        let req = CtpReqQryQuoteField {
            ExchID: app_qryquote.get_exchid().into(),
            AccountID: "".into(),
            InstrumentID: std::format!("%{}%", app_qryquote.get_insid()),
            OrdStatus: TIConvert::order_status_id(&ordstatus),
            OrderSysID: std::format!("%{}%", app_qryquote.get_ordersysid()),
            TransTime0: std::format!("{:02}:{:02}:{:02}", st.hour, st.minute, st.second),
            TransTime0_Int: st.hour * 10000 + st.minute * 100 + st.second,
            TransTime1: std::format!("{:02}:{:02}:{:02}", et.hour, et.minute, et.second),
            TransTime1_Int: et.hour * 10000 + et.minute * 100 + et.second,
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qryquote.get_page_index()
            }
        };
        let page_size = app_qryquote.get_page_size();

        let ret = { DB.get().unwrap().lock().unwrap().qry_trd_quote(&req, page_index, page_size) };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qryquote.set_page_index_model(page_index_data.into());
            app_qryquote.set_item_total(ret.0);
            app_qryquote.set_page_index(0);
            app_qryquote.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|ord| {
            let items = QueryQuote::get_row_items(&ord);
            row_data.push(items.into());
        });

        app_qryquote.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryQuote::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryQuote::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryQuote::on_query(app_weak, true);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("quote_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出报价失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "交易编码",
            "发生时间",
            "合约编码",
            "报价状态",
            "交易所报价编号",
            "本地报价编号",
            "买报价状态",
            "买报单编号",
            "买开平标志",
            "买价格",
            "买数量",
            "卖报价状态",
            "卖报单编号",
            "卖开平标志",
            "卖价格",
            "卖数量",
            "用户代码",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出报价失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<OptTrd_QryQuote>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let mut page_index = 0;
            let page_size = 2000;
            let req = CtpReqQryQuoteField {
                TransTime1: "23:59:59".into(),
                TransTime1_Int: 235959,
                ..Default::default()
            };

            loop {
                let ret = { DB.get().unwrap().lock().unwrap().qry_trd_quote(&req, page_index, page_size) };

                ret.1.iter().for_each(|ord| {
                    let items = QueryQuote::get_row_items(&ord);
                    let _ = writer.write_record(items.iter());
                });

                if (ret.1.len() as i32) < page_size {
                    break;
                }

                page_index += 1;
            }
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出报价失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出报价成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}
