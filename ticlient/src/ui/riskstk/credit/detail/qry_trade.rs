use std::rc::Rc;

use crate::{
    common::{config::Config, global::DB, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::trade_risk_stk::ReqQryTradeStkField;

/// 查询 - 成交
pub(in crate::ui::riskstk::credit) struct QueryTrade {}

impl QueryTrade {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let qry = app.global::<StkRskCredit_QryTrade>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryTrade::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryTrade::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryTrade::on_page_size_changed(&app_weak, size));

        log::trace!("Query trade detail init completed");
    }
}

impl QueryTrade {
    /// 查询事件
    fn on_query(app_weak: &Weak<RiskStkCreditDetailPage>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();

        let app_qry = app.global::<StkRskCredit_QryTrade>();

        let req = ReqQryTradeStkField {
            ExchID: TIConvert::exch_id(&app_qry.get_exchid()),
            AccountID: app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned(),
            InstrumentID: std::format!("%{}%", app_qry.get_insid()),
            TradeID: std::format!("%{}%", app_qry.get_tradeid()),
            OrderSysID: std::format!("%{}%", app_qry.get_ordersysid()),
            TransTime0: app_qry.get_starttime_int(),
            TransTime1: app_qry.get_endtime_int(),
            ..Default::default()
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qry.get_page_index()
            }
        };
        let page_size = app_qry.get_page_size();

        let ret = {
            DB.get()
                .unwrap()
                .lock()
                .unwrap()
                .qry_rsk_trade_stk(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            //show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
            println!("查询结果为空");
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qry.set_page_index_model(page_index_data.into());
            app_qry.set_item_total(ret.0);
            app_qry.set_page_index(0);
            app_qry.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|trd| {
            let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

            // 发生时间
            items.push(TIConvert::transtime(trd.TransTime).into());

            // 资金账号
            items.push(trd.AccountID.clone().into());

            // 合约编码
            items.push(trd.InstrumentID.clone().into());

            // 成交数量
            items.push(slint::format!("{}", trd.TradeVolume));

            // 成交价格
            items.push(slint::format!("{:.*}", Config::STK_PRICE_DIGITS, trd.TradePrice));

            // 买买方向
            items.push(TIConvert::side(trd.Side).into());

            // 成交编码
            items.push(trd.TradeID.clone().into());

            // 交易所报单编号
            items.push(trd.OrderSysID.clone().into());

            // 本地报单编号
            items.push(slint::format!("{}", trd.LocalOrderNo));

            // 交易所
            items.push(TIConvert::exch_name(trd.ExchID).into());

            row_data.push(items.into());
        });

        app_qry.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<RiskStkCreditDetailPage>) {
        QueryTrade::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<RiskStkCreditDetailPage>, page_index: i32) {
        QueryTrade::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<RiskStkCreditDetailPage>, page_size: i32) {
        QueryTrade::on_query(app_weak, true);
    }
}
