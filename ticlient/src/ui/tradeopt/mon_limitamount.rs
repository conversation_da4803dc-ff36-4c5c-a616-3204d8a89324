use std::{
    cmp,
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{apiproc::tradeoptbuf::CtpLimitAmountMap, common::config::Config};

use crate::{
    common::{global::TRADE_OPT_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type IDSet = dashmap::DashSet<String>;

/// 监控 - 限额 - 数据
struct MonLimitAmountDatas {
    /// 数据
    data_map: Arc<CtpLimitAmountMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 是否需要过滤数据
    filter_changed: AtomicBool,
}
impl MonLimitAmountDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpLimitAmountMap::new()),
            data_changed: AtomicBool::new(false),
            filter_changed: AtomicBool::new(false),
        }
    }
}
static MON_LIMIT_AMOUNT_DATAS: OnceLock<MonLimitAmountDatas> = OnceLock::new();

/// 监控 - 限额
pub(super) struct MonLimitAmount {}

// 构建与初始化
impl MonLimitAmount {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = MON_LIMIT_AMOUNT_DATAS.set(MonLimitAmountDatas::new());

        let app_mon = app.global::<OptTrd_MonLimitAmount>();

        // 注册过滤条件改变事件
        let app_weak = app.as_weak();
        app_mon.on_filter_changed(move || MonLimitAmount::on_filter_changed(&app_weak));

        // 注册获取单元格颜色事件
        let app_weak = app.as_weak();
        app_mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonLimitAmount::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor limit amount init completed");
    }
}

// 查询
impl MonLimitAmount {
    /// 查询限额信息
    pub fn qry_limitamount(&self) {
        let ret = { TRADE_OPT_BUF.get().unwrap().read().unwrap().pop_limitamount() };
        if ret.is_empty() {
            return;
        }

        let mla_datas = MON_LIMIT_AMOUNT_DATAS.get().unwrap();

        for (key, lp) in ret {
            mla_datas.data_map.insert(key, lp);
        }
        mla_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl MonLimitAmount {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 权利仓当前权利金, 剩余额度, 占用比例
        if 3 <= column_index && column_index <= 5 {
            let app_mla = app.global::<OptTrd_MonLimitAmount>();

            let ratio = app_mla.invoke_get_cell_data(row_index, 5);
            if let Ok(ratio) = ratio[..ratio.len() - 1].parse::<f64>() {
                if ratio < 80. {
                    return nc.get_normal();
                } else {
                    if ratio >= 100.0 {
                        return nc.get_error();
                    } else if ratio >= 90. {
                        return nc.get_caution();
                    } else {
                        return nc.get_prompt();
                    }
                }
            }
        }

        nc.get_default()
    }

    /// 过虑条件有变动
    fn on_filter_changed(app_weak: &Weak<App>) {
        MON_LIMIT_AMOUNT_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

// 更新UI
impl MonLimitAmount {
    // 更新限额
    async fn update_limitpos(&self, app_weak: &Weak<App>) {
        let mla_datas = MON_LIMIT_AMOUNT_DATAS.get().unwrap();

        let data_map_clone = {
            if mla_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                mla_datas.filter_changed.store(false, Ordering::Relaxed);
                mla_datas.data_map.clone()
            } else {
                if mla_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    mla_datas.data_map.clone()
                } else {
                    Arc::new(CtpLimitAmountMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_mla = app.global::<OptTrd_MonLimitAmount>();

            let filter_exchid = app_mla.get_exchid().to_string();

            // 填充数据
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|lp| {
                let exchname = TIConvert::exch_name(lp.ExchID);

                if !filter_exchid.is_empty() {
                    if exchname != filter_exchid {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(lp.AccountID.clone().into());

                // 交易所
                items.push(exchname.into());

                // 权利仓最大权利金
                items.push(TIConvert::format_f(lp.MaxBuyPremium, Config::AMT_DIGITS).into());

                // 权利仓当前权利金
                items.push(TIConvert::format_f(lp.BuyPremium, Config::AMT_DIGITS).into());

                // 剩余额度
                items.push(TIConvert::format_f(lp.CanBuyPremium, Config::AMT_DIGITS).into());

                // 占用比例
                items.push((TIConvert::format_f(lp.Ratio * 100.0, Config::AMT_DIGITS) + "%").into());

                row_data.push(items.into());
            });

            // 根据 ratio 按降序排序
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(5).unwrap();
                let c_b = r_b.row_data(5).unwrap();

                let c_a = &c_a[..c_a.len() - 1];
                let c_b = &c_b[..c_a.len() - 1];

                let c_a_ = c_a.parse::<f64>();
                let c_b_ = c_b.parse::<f64>();
                if c_a_.is_ok() && c_b_.is_ok() {
                    let c_a = c_a_.unwrap();
                    let c_b = c_b_.unwrap();

                    if c_b < c_a {
                        cmp::Ordering::Less
                    } else if c_b > c_a {
                        cmp::Ordering::Greater
                    } else {
                        cmp::Ordering::Equal
                    }
                } else {
                    c_b.cmp(&c_a)
                }
            }));
            app_mla.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新限额
        self.update_limitpos(&app_weak).await;
    }
}
