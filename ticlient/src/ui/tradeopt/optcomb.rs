use std::cmp::Ordering;

use crate::common::{self, instrument::Instrument, titype::TIType};

pub(super) struct OptComb {}

impl OptComb {
    ///根据组合策略与第1腿合约信息，计算所有满足的第二腿合约编码<br>
    ///
    /// `strategy`: 组合策略<br>
    /// `exchid`: 交易所编码<br>
    /// `leg1_insid`: 第1腿合约编码<br>
    /// `leg2_insid_map`: 所有可能的第2腿合约编码<br>
    ///
    /// 返回值: (insid|exchid,insname)
    pub fn get_leg2_insid(
        strategy: i32,
        exchid: i32,
        leg1_insid: &str,
        leg2_insid_map: &dashmap::DashMap<String, String>,
    ) -> Vec<(String, String)> {
        let mut ret = Vec::new();

        // 第1腿合约信息
        let leg1_ins = {
            common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins(exchid, leg1_insid)
                .unwrap_or_default()
        };
        if leg1_ins.insid.is_empty() {
            return ret;
        }

        leg2_insid_map.iter().for_each(|it| {
            let leg2_insid = it.key();

            if leg1_insid == leg2_insid {
                return;
            }

            // 第2腿合约信息
            let leg2_ins = {
                let (insid, _) = Instrument::parse_id(leg2_insid.as_str());
                common::global::INS
                    .get()
                    .unwrap()
                    .read()
                    .unwrap()
                    .get_ins(exchid, insid.as_str())
                    .unwrap_or_default()
            };
            if leg2_ins.insid.is_empty() {
                return;
            }

            // 相同的标的、相同的到期日、相同的合约单位
            if Ordering::Equal == leg1_ins.underlyingid.cmp(&leg2_ins.underlyingid)
                && Ordering::Equal == leg1_ins.expiredate.cmp(&leg2_ins.expiredate)
                && leg1_ins.multiple == leg2_ins.multiple
            {
                if TIType::STRATEGY_CNSJC == strategy || TIType::STRATEGY_CXSJC == strategy {
                    if TIType::OPTIONS_CALL == leg1_ins.optionstype && TIType::OPTIONS_CALL == leg2_ins.optionstype {
                        if TIType::STRATEGY_CNSJC == strategy && leg1_ins.strikeprice < leg2_ins.strikeprice {
                            ret.push((leg2_insid.clone(), leg2_ins.symbol.clone()));
                            return;
                        }

                        if TIType::STRATEGY_CXSJC == strategy && leg1_ins.strikeprice > leg2_ins.strikeprice {
                            ret.push((leg2_insid.clone(), leg2_ins.symbol.clone()));
                            return;
                        }
                    }
                }

                if TIType::STRATEGY_PNSJC == strategy || TIType::STRATEGY_PXSJC == strategy {
                    if TIType::OPTIONS_PUT == leg1_ins.optionstype && TIType::OPTIONS_PUT == leg2_ins.optionstype {
                        if TIType::STRATEGY_PNSJC == strategy && leg1_ins.strikeprice < leg2_ins.strikeprice {
                            ret.push((leg2_insid.clone(), leg2_ins.symbol.clone()));
                            return;
                        }

                        if TIType::STRATEGY_PXSJC == strategy && leg1_ins.strikeprice > leg2_ins.strikeprice {
                            ret.push((leg2_insid.clone(), leg2_ins.symbol.clone()));
                            return;
                        }
                    }
                }

                if TIType::STRATEGY_KS == strategy || TIType::STRATEGY_KKS == strategy {
                    if TIType::OPTIONS_CALL == leg1_ins.optionstype && TIType::OPTIONS_PUT == leg2_ins.optionstype {
                        if TIType::STRATEGY_KS == strategy && leg1_ins.strikeprice == leg2_ins.strikeprice {
                            ret.push((leg2_insid.clone(), leg2_ins.symbol.clone()));
                            return;
                        }

                        if TIType::STRATEGY_KKS == strategy && leg1_ins.strikeprice > leg2_ins.strikeprice {
                            ret.push((leg2_insid.clone(), leg2_ins.symbol.clone()));
                            return;
                        }
                    }
                }
            }
        });

        ret
    }
}
