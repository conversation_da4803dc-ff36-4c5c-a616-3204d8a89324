use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
};

use crate::{
    apiproc::tradestkbuf::CtpOrderInsertCancelMap,
    common::{
        global::{CFG, TRADE_STK_BUF},
        ticonvert::TIConvert,
    },
    slintui::*,
};
use slint::*;

/// 监控 - 报撤单
pub(super) struct MonOrderInsertCancel {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpOrderInsertCancelMap>,
}

impl MonOrderInsertCancel {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpOrderInsertCancelMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let mon = app.global::<StkTrd_MonOrderInsertCancel>();

        let app_weak = app.as_weak();
        mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonOrderInsertCancel::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor order insert cancel init completed");
    }
}

impl MonOrderInsertCancel {
    /// 查询报撤单信息
    pub fn qry_orderinsertcancel(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_orderinsertcancel() };
        if ret.is_empty() {
            return;
        }

        for (key, oic) in ret {
            self.data_map.insert(key, oic);
        }
        self.data_changed.store(true, Ordering::Relaxed);
    }
}

impl MonOrderInsertCancel {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 是否异常
        if 3 == column_index {
            if "是" == data.as_str() {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }
}

impl MonOrderInsertCancel {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                self.data_map.clone()
            } else {
                Arc::new(CtpOrderInsertCancelMap::new())
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            let greaterthannum = {
                CFG.get()
                    .unwrap()
                    .read()
                    .unwrap()
                    .stk_mon
                    .OrdInsertCancelMonitor
                    .GreaterThanNum
            };

            data_map_clone.iter().for_each(|oic| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                let allnum = oic.InsertNum + oic.CancelNum;
                let abnormal = {
                    if allnum < greaterthannum {
                        "否"
                    } else {
                        "是"
                    }
                };

                // 资金账户
                items.push(oic.AccountID.clone().into());

                // 交易所
                items.push(TIConvert::exch_name(oic.ExchID).into());

                // 交易编码
                items.push(oic.ClientID.clone().into());

                // 是否异常
                items.push(abnormal.into());

                // 总笔数
                items.push(slint::format!("{}", allnum));

                // 报单笔数
                items.push(slint::format!("{}", oic.InsertNum));

                // 撤单笔数
                items.push(slint::format!("{}", oic.CancelNum));

                // 发生时间
                items.push(TIConvert::transtime(oic.TransTime).into());

                row_data.push(items.into());
            });

            app.global::<StkTrd_MonOrderInsertCancel>().set_row_data(row_data.into());
        });
    }
}
