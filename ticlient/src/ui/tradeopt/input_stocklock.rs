use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        global::{CFG, INS, TRADE_OPT_API},
        instrument::Instrument,
        titype::TIType,
    },
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{CtpReqInputStocklockField, CtpRspInputStockLockField};

use super::tradeopt::show_input_msg_box;

/// 解锁仓录入数据
struct InputStockLockDatas {
    /******************************************************************************************************************/
    /// 在解锁仓过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputstklock: AtomicBool,

    /// 报单响应数据
    rsp_inputstklock: Arc<Mutex<CtpRspInputStockLockField>>,
}
impl InputStockLockDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputstklock: AtomicBool::new(false),
            rsp_inputstklock: Arc::new(Mutex::new(CtpRspInputStockLockField::default())),
        }
    }
}
static INPUTSTKLOCK_DATAS: OnceLock<InputStockLockDatas> = OnceLock::new();

/// 备兑解锁仓
pub(super) struct InputStockLock {}

/// 构建与初始化
impl InputStockLock {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化解锁仓录入数据
        let _ = INPUTSTKLOCK_DATAS.set(InputStockLockDatas::new());

        let app_inputstklock = app.global::<OptTrd_InputStockLock>();

        // 添加合约
        let insid_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());
        let inids_set = { INS.get().unwrap().read().unwrap().get_sse_stk_id_for_lock() };
        inids_set.iter().for_each(|insid| {
            insid_arr.push(ListViewItem {
                text: insid.clone().into(),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(insid_arr.sort_by(move |a, b| a.text.cmp(&b.text)));
        app_inputstklock.set_insid_model(sort_model.into());

        // 注册合约改变事件
        let app_weak = app.as_weak();
        app_inputstklock.on_insid_text_changed(move || InputStockLock::on_insid_text_changed(&app_weak));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputstklock.on_volume_text_changed(move || InputStockLock::on_volume_text_changed(&app_weak));

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputstklock.on_volume_updown_changed(move |upflag| InputStockLock::on_volume_updown_changed(&app_weak, upflag));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputstklock.on_req_tip_toggled(move |checked| InputStockLock::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputstklock.on_ok_clicked(move |need_confirm| InputStockLock::on_ok_clicked(&app_weak, need_confirm));

        log::trace!("Input stock lock init completed");
    }

    /// 报单响应
    pub fn on_rsp_inputstklock(&self, rsp: CtpRspInputStockLockField) {
        let inputstklock_datas = INPUTSTKLOCK_DATAS.get().unwrap();
        *inputstklock_datas.rsp_inputstklock.lock().unwrap() = rsp;
        inputstklock_datas.is_err_inputstklock.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl InputStockLock {
    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputstklock = app.global::<OptTrd_InputStockLock>();

        let ins = {
            let id = app_inputstklock.get_insid();
            INS.get().unwrap().read().unwrap().get_ins_by_insid(&id)
        };

        // 合约名称
        let mut insname = "".to_owned();

        if let Some(ins) = ins {
            app_inputstklock.set_insid_has_error(false);

            insname = if !ins.symbol.is_empty() {
                ins.symbol.clone()
            } else {
                ins.name.clone()
            };

            // 设置交易所编码
            let eid = app_inputstklock.get_exchid();
            if eid.as_str() != ins.exchname.as_str() {
                app_inputstklock.set_exchid(ins.exchname.clone().into());
            }
        } else {
            app_inputstklock.set_exchid("".into());
            app_inputstklock.set_insid_has_error(true);
        }

        if *insname != *app_inputstklock.get_insname() {
            app_inputstklock.set_insname(insname.into());
        }

        true
    }

    /// 报单数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputstklock = app.global::<OptTrd_InputStockLock>();

        let volume = app_inputstklock.get_volume().replace(",", "");
        if volume.is_empty() {
            app_inputstklock.set_volume_has_error(true);
            return false;
        }

        if volume.trim().parse::<i32>().unwrap_or_default() <= 0 {
            app_inputstklock.set_volume_has_error(true);
            return false;
        }

        app_inputstklock.set_volume_has_error(false);

        true
    }

    // 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputstklock = app.global::<OptTrd_InputStockLock>();

        if app_inputstklock.get_volume_has_error() {
            app_inputstklock.set_volume("10000".into());
            app_inputstklock.set_volume_has_error(false);
            return;
        }

        let volume_str = app_inputstklock.get_volume().replace(",", "");
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            return;
        }

        if upflag {
            volume += 10000;
        } else {
            volume -= 10000;
            if volume <= 0 {
                return;
            }
        }

        app_inputstklock.set_volume(slint::format!("{}", volume));
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();
        app_set.set_trdreqtip_inputstklock(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputStockLock = checked;
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputstklock = app.global::<OptTrd_InputStockLock>();

        // 获取参数
        let accid = app_inputstklock.get_accountid();
        let is_lock = app_inputstklock.get_is_lock();
        let exchid = app_inputstklock.get_exchid();
        let insid = {
            let id = app_inputstklock.get_insid();
            Instrument::parse_id(&id).0
        };
        let volume = app_inputstklock.get_volume().replace(",", "");

        // 输入参数检查
        {
            if accid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "账户不能为空".into());
                return;
            }

            if insid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约信息失败".into());
                return;
            }

            if exchid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约所在交易所失败".into());
                return;
            }

            if volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "数量不能为空".into());
                return;
            }
        }

        let volume = volume.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            show_input_msg_box(&app, 1, Default::default(), "请输入有效的数量".into());
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputStockLock } {
            show_input_msg_box(
                &app,
                100,
                slint::format!("{}确认", if is_lock { "锁仓" } else { "解锁仓" }),
                slint::format!(
                    "资金账户:{}\n\n交易所:{}    合约:{}\n\n数量:{}",
                    accid,
                    exchid,
                    insid,
                    volume
                ),
            );

            return;
        }

        let req = CtpReqInputStocklockField {
            ExchID: exchid.to_string(),
            InvestorID: accid.to_string(),
            InstrumentID: insid.to_string(),
            LocalOrderNo: 0,
            Locked: if is_lock {
                TIType::COVERED_STOCK_LOCK
            } else {
                TIType::COVERED_STOCK_UNLOCK
            },
            Volume: volume,
        };
        if let Err(err) = TRADE_OPT_API.get().unwrap().read().unwrap().req_stocklockinput(0, &req) {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("备兑解锁仓失败\n\n错误信息: {}", err),
            );
        }
    }
}

/// 更新UI
impl InputStockLock {
    // 更新响应信息
    async fn update_rspmsg(&self, app_weak: &Weak<App>) {
        let inputstklock_datas = INPUTSTKLOCK_DATAS.get().unwrap();

        let is_err_inputstklock = inputstklock_datas
            .is_err_inputstklock
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputstklock {
            return;
        }

        let rsp = { inputstklock_datas.rsp_inputstklock.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let lock_type = if 1 == rsp.Locked { "锁仓" } else { "解锁" };
            let txt = slint::format!("交易所:{}, 合约:{}, 数量:{}", rsp.ExchID, rsp.InstrumentID, rsp.Volume);

            if 0 == rsp.ec {
                show_msg_box(&app, 1, slint::format!("{}成功", lock_type), txt);
            } else {
                show_msg_box(
                    &app,
                    3,
                    slint::format!("{}失败", lock_type),
                    slint::format!("错误码:{}; 错误信息:{}\n\n{}", rsp.ec, rsp.em, txt),
                );
            }
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新响应信息
        self.update_rspmsg(&app_weak).await;
    }
}
