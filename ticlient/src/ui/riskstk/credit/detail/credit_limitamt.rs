use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::CreditLimitMap,
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

/// 融资授信额度数据
struct CreditLimitAmtDatas {
    filter_changed: AtomicBool,
}
impl CreditLimitAmtDatas {
    pub fn new() -> Self {
        Self {
            filter_changed: AtomicBool::new(false),
        }
    }
}
static CREDIT_LIMIT_AMT_DATAS: OnceLock<CreditLimitAmtDatas> = OnceLock::new();

/// 融资授信额度
pub(in crate::ui::riskstk::credit) struct CreditLimitAmt {
    /// 显示按交易所的明细
    show_detail_amt: AtomicBool,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CreditLimitMap>,

    /// 数据
    data_sumary_map: Arc<CreditLimitMap>,
}

/// 构建与初始化
impl CreditLimitAmt {
    pub fn new() -> Self {
        Self {
            show_detail_amt: AtomicBool::new(false),
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CreditLimitMap::new()),
            data_sumary_map: Arc::new(CreditLimitMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let _ = CREDIT_LIMIT_AMT_DATAS.set(CreditLimitAmtDatas::new());

        let app_cl = app.global::<StkRskCredit_CreditLimitAmt>();

        self.show_detail_amt
            .store(app.global::<AppInerArgs>().get_show_detail(), Ordering::Relaxed);

        let app_weak = app.as_weak();
        app_cl.on_get_row_data_color(move |row_index, column_index, data| {
            CreditLimitAmt::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_cl.on_filter_changed(move || CreditLimitAmt::on_filter_changed(&app_weak));

        log::trace!("Credit limit amt init completed");
    }
}

/// 查询
impl CreditLimitAmt {
    /// 查询融资授信额度
    pub fn qry_credit_limit(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_credit_limit_amt() };
        if ret.is_empty() {
            return;
        }

        let show_detail_amt = self.show_detail_amt.load(Ordering::Relaxed);

        for (key, cl) in ret {
            if let Some(mut exist) = self.data_map.get_mut(&key) {
                if !show_detail_amt {
                    let distrib = cl.Distrib - exist.Distrib;
                    let used = cl.Used - exist.Used;
                    let frozen = cl.Frozen - exist.Frozen;
                    let in_ = cl.In - exist.In;
                    let out = cl.Out - exist.Out;

                    // 根据先添加汇总,再添加明细的规则,这里肯定存在
                    let mut sum = self.data_sumary_map.get_mut(&cl.AccID).unwrap();
                    sum.Distrib += distrib;
                    sum.Used += used;
                    sum.Frozen += frozen;
                    sum.In += in_;
                    sum.Out += out;
                }

                exist.Distrib = cl.Distrib;
                exist.Used = cl.Used;
                exist.Frozen = cl.Frozen;
                exist.In = cl.In;
                exist.Out = cl.Out;
            } else {
                if !show_detail_amt {
                    if let Some(mut sum) = self.data_sumary_map.get_mut(&cl.AccID) {
                        sum.Distrib += cl.Distrib;
                        sum.Used += cl.Used;
                        sum.Frozen += cl.Frozen;
                        sum.In += cl.In;
                        sum.Out += cl.Out;
                    } else {
                        let mut new_cl = cl.clone();
                        new_cl.ExchID = 0;
                        self.data_sumary_map.insert(new_cl.AccID.clone(), new_cl);
                    }
                }

                self.data_map.insert(key, cl);
            }
        }

        self.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditLimitAmt {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(
        app_weak: &Weak<RiskStkCreditDetailPage>,
        row_index: i32,
        column_index: i32,
        data: SharedString,
    ) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 可用额度
        if 3 == column_index {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data < 0.0 {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<RiskStkCreditDetailPage>) {
        CREDIT_LIMIT_AMT_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

/// 更新UI
impl CreditLimitAmt {
    /// 更新
    pub async fn update(&self, app_weak: Weak<RiskStkCreditDetailPage>) {
        let cl_datas = CREDIT_LIMIT_AMT_DATAS.get().unwrap();

        let show_detail_amt = self.show_detail_amt.load(Ordering::Relaxed);

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                cl_datas.filter_changed.store(false, Ordering::Relaxed);
                if show_detail_amt {
                    self.data_map.clone()
                } else {
                    self.data_sumary_map.clone()
                }
            } else {
                if cl_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    if show_detail_amt {
                        self.data_map.clone()
                    } else {
                        self.data_sumary_map.clone()
                    }
                } else {
                    Arc::new(CreditLimitMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            let app_cl = app.global::<StkRskCredit_CreditLimitAmt>();

            let filter_accid = app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned();
            if filter_accid.is_empty() {
                app_cl.set_row_data(row_data.into());
                return;
            }

            data_map_clone.iter().for_each(|cl| {
                if filter_accid != cl.AccID {
                    return;
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(slint::format!("{}", cl.AccID));

                // 交易所
                items.push(TIConvert::exch_name(cl.ExchID).into());

                // 授权额度
                items.push(TIConvert::format_f(cl.Distrib, Config::AMT_DIGITS).into());

                // 可用额度
                items.push(TIConvert::format_f(cl.Distrib - cl.Used - cl.Frozen + cl.In - cl.Out, Config::AMT_DIGITS).into());

                // 使用额度
                items.push(TIConvert::format_f(cl.Used, Config::AMT_DIGITS).into());

                // 冻结额度
                items.push(TIConvert::format_f(cl.Frozen, Config::AMT_DIGITS).into());

                // 转入额度
                items.push(TIConvert::format_f(cl.In, Config::AMT_DIGITS).into());

                // 转出额度
                items.push(TIConvert::format_f(cl.Out, Config::AMT_DIGITS).into());

                row_data.push(items.into());
            });

            app_cl.set_row_data(row_data.into());
        });
    }
}
