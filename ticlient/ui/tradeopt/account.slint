import { Theme, TableView, <PERSON><PERSON><PERSON>Type, Fonts, Label, Radius, Icons } from "@zstk/lib.slint";
import { ColWidth, NotifyColor, AppCom } from "../struct.slint";
import { OptTrd_Com, OptTrd_Account } from "struct.slint";

export component AccountPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;

    TableView {
        height: 100%;
        width: 100%;
        sortable: true;

        column_header: {
            height: AppCom.table_header_height,
            background: Colors.transparent,
            font: Fonts.normal,
            fix_head_count: 2,
            fix_tail_count: 1,
            columns: [
                { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                { title: @tr("动态权益"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                { title: @tr("市值权益"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                { title: @tr("可用资金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                { title: @tr("实收保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("交易所保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("冻结保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("冻结资金 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100, tips: "冻结的权利金 + 行权冻结资金" },
                { title: @tr("冻结手续费 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100, tips: "冻结手续费 + 行权冻结手续费" },
                { title: @tr("权利金收支"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("入金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("出金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("平仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("上次结算准备金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                { title: @tr(""), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80, cell_type: TVCellType.HyperlinkText },
            ]
        };
        row_header: { visible: false };
        row_count: OptTrd_Account.row_data.length;

        sort_canceled => {
            OptTrd_Account.sort_asc_column_index = 0;
            OptTrd_Account.sort_dec_column_index = -1;
            OptTrd_Account.sort_ascending(OptTrd_Account.sort_asc_column_index);
        }

        sort_ascending(index) => {
            OptTrd_Account.sort_ascending(index);
            OptTrd_Account.sort_asc_column_index = index;
            OptTrd_Account.sort_dec_column_index = -1;
            true
        }

        sort_descending(index) => {
            OptTrd_Account.sort_descending(index);
            OptTrd_Account.sort_asc_column_index = -1;
            OptTrd_Account.sort_dec_column_index = index;
            true
        }

        get_cell_data(row_index, column_index) => {
            OptTrd_Account.row_data[row_index][column_index]
        }

        get_cell_data_color(row_index, column_index, data) => {
            // 入金
            if 12 == column_index {
                return NotifyColor.normal;
            }

            // 出金
            if 13 == column_index {
                return NotifyColor.caution;
            }

            // 资金划转
            if 16 == column_index {
                return Theme.accent_background;
            }

            // 这几列数据中小数含有逗号, ui处理不了, 交给回调处理
            // 可用资金, 平仓盈亏
            if 4 == column-index || 14 == column-index {
                return OptTrd_Account.get_row_data_color(row_index, column_index, data);
            }

            NotifyColor.default
        }

        cell_double_clicked(row_index, column_index) => {
            OptTrd_Com.accountid = OptTrd_Account.row_data[row_index][1];
        }

        cell_pointer_event(row_index, column_index, event, point) => {
            if PointerEventKind.up == event.kind {
                if PointerEventButton.left == event.button {
                    if 16 == column_index && "" != OptTrd_Account.row_data[row_index][16]{
                        OptTrd_Account.sel_accid = OptTrd_Account.row_data[row_index][1];
                        if "" != OptTrd_Account.sel_accid {
                            self.select_full_row = true;
                            self.set_current_row(row_index);
                            if !OptTrd_Account.show_fund_trans {
                                OptTrd_Account.show_fund_trans = true;
                            }
                        }
                    }
                } else if PointerEventButton.right == event.button {
                    OptTrd_Account.sel_accid = OptTrd_Account.row_data[row_index][1];
                    if "" != OptTrd_Account.sel_accid {
                        self.select_full_row = true;
                        self.set_current_row(row_index);

                        right_up_point = point;
                        if !OptTrd_Account.show_fund_trans {
                            i_pw_menu.show();
                        }
                    }
                }
            }
        }

        cell_key_copy_pressed(row_index, column_index) => {
            if column_index >= 0 {
                AppCom.copy_str_to_clipboard(OptTrd_Account.row_data[row_index][column_index]);
            } else {
                AppCom.copy_arr_to_clipboard(OptTrd_Account.row_data[row_index]);
            }
        }
    }

    i_pw_menu := PopupWindow {

        x: right_up_point.x - root.absolute_position.x;
        y: right_up_point.y - root.absolute_position.y;

        Rectangle {

            width: 100px;

            border-width: 1px;
            border-color: Theme.border;
            border_radius: Radius.extra_small;

            drop_shadow_blur: 15px;
            drop_shadow_color: Theme.control_background.darker(0.5);

            background: Theme.control_background;

            VerticalLayout {
                padding: 5px;
                spacing: 10px;

                i_menu_fundtrans := Rectangle {
                    border_radius: Radius.extra_small;

                    HorizontalLayout {
                        padding: 5px;
                        spacing: 5px;
                        Image {
                            image_fit: contain;
                            width: 14px;
                            colorize: Theme.foreground;
                            source: Icons.list;
                        }

                        Label {
                            text: "资金划转";
                            horizontal-alignment: left;
                        }
                    }

                    i_menu_fundtrans_ta := TouchArea {
                        clicked => {
                            OptTrd_Account.show_fund_trans = true;
                        }
                    }
                }
            }
        }

        states [
            fundtrans_has_hover when i_menu_fundtrans_ta.has_hover: {
                i_menu_fundtrans.background: Theme.shadow;
            }
        ]
    }
}