import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TableView } from "@zstk/lib.slint";
import { AppCom, ColWidth, WidgetColor } from "../struct.slint";
import { OptTrd_InQuote } from "struct.slint";

export component InQuotePage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <int> select_row_index: -1;
    function req_cancel_order(need_confirm: bool) {
        OptTrd_InQuote.sel_cancel_order.exchid = "";
        if select_row_index >= 0 && OptTrd_InQuote.row_data.length > select_row_index {
            OptTrd_InQuote.sel_cancel_order.exchid = OptTrd_InQuote.row_data[select_row_index][1];
            OptTrd_InQuote.sel_cancel_order.accountid = OptTrd_InQuote.row_data[select_row_index][0];
            OptTrd_InQuote.sel_cancel_order.ordersysid = OptTrd_InQuote.row_data[select_row_index][6];
        }
        OptTrd_InQuote.cancel_order_clieked(OptTrd_InQuote.sel_cancel_order, need_confirm);
    }

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 5,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insid },
                    { title: @tr("报价状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("交易所报价编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报价编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("买报价状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("买报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("买开平标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.openclose },
                    { title: @tr("买价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    { title: @tr("买数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("卖报价状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("卖报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("卖开平标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.openclose },
                    { title: @tr("卖价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    { title: @tr("卖数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                ]
            };
            row_count: OptTrd_InQuote.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_InQuote.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_clicked(row_index, column_index) => {
                self.select_full_row = true;
            }

            cell_double_clicked(row_index, column_index) => {
                req_cancel_order(true);
            }

            current_cell_changed(row_index, column_index) => {
                select_row_index = row_index;
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_InQuote.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_InQuote.row_data[row_index]);
                }
            }
        }

        HorizontalLayout {
            padding: 6px;
            spacing: 30px;
            height: 36px;

            Button {
                text: "撤单";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    req_cancel_order(true);
                }
            }

            Button {
                text: "全撤";
                background: WidgetColor.btn_background;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    OptTrd_InQuote.cancel_order_all_clieked(OptTrd_InQuote.row_data.length, true);
                }
            }
        }
    }
}
