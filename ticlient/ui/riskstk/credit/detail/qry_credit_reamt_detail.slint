import { Theme, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom, NotifyColor } from "../../../struct.slint";
import { PageItem } from "../../../widgets/page.slint";
import { TimeSelect, TimeItem } from "../../../widgets/time.slint";
import { StkRskCredit_QryCreditReAmtDtl } from "../struct.slint";

export component QryCreditReAmtDtlPage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkRskCredit_QryCreditReAmtDtl.exchid;
        i_cbx_retype.current_text = StkRskCredit_QryCreditReAmtDtl.retype_str;
        i_le_reid.text = StkRskCredit_QryCreditReAmtDtl.reid;
        i_le_ordsysid.text = StkRskCredit_QryCreditReAmtDtl.ordsysid;
        i_le_insid.text = StkRskCredit_QryCreditReAmtDtl.insid;
        i_ts_start.time = StkRskCredit_QryCreditReAmtDtl.starttime;
        i_ts_end.time = StkRskCredit_QryCreditReAmtDtl.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            i_cbx_exch := ComboBox {
                width: 70px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text: StkRskCredit_QryCreditReAmtDtl.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "偿还类型";
                vertical_alignment: center;
            }
            i_cbx_retype := ComboBox {
                width: 80px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text: StkRskCredit_QryCreditReAmtDtl.retype_str;
                model: [
                    { text: "" },
                    { text: "直接还款" },
                    { text: "卖券还款" },
                ];
            }

            Label {
                text: "偿还编号";
                vertical_alignment: center;
            }
            i_le_reid := LineEdit {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text: StkRskCredit_QryCreditReAmtDtl.reid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "合约委托编号";
                vertical_alignment: center;
            }
            i_le_ordsysid := LineEdit {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text: StkRskCredit_QryCreditReAmtDtl.ordsysid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }
            i_le_insid := LineEdit {
                width: 85px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text: StkRskCredit_QryCreditReAmtDtl.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "发生时间";
                vertical_alignment: center;
            }
            HorizontalLayout {
                i_ts_start := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryCreditReAmtDtl.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryCreditReAmtDtl.endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                height: AppCom.widgets_height;
                background: WidgetColor.btn_background;

                clicked => {
                    StkRskCredit_QryCreditReAmtDtl.exchid = i_cbx_exch.current_text;
                    StkRskCredit_QryCreditReAmtDtl.retype_str = i_cbx_retype.current_text;
                    StkRskCredit_QryCreditReAmtDtl.reid = i_le_reid.text;
                    StkRskCredit_QryCreditReAmtDtl.ordsysid = i_le_ordsysid.text;
                    StkRskCredit_QryCreditReAmtDtl.insid = i_le_insid.text;
                    StkRskCredit_QryCreditReAmtDtl.starttime = i_ts_start.time;
                    StkRskCredit_QryCreditReAmtDtl.endtime = i_ts_end.time;
                    StkRskCredit_QryCreditReAmtDtl.starttime_int = i_ts_start.time_int;
                    StkRskCredit_QryCreditReAmtDtl.endtime_int = i_ts_end.time_int;

                    if i_cbx_retype.current_text == "直接还款" {
                        StkRskCredit_QryCreditReAmtDtl.retype = 1;
                    } else if i_cbx_retype.current_text == "卖券还款" {
                        StkRskCredit_QryCreditReAmtDtl.retype = 2;
                    } else if i_cbx_retype.current_text == "平仓还款" {
                        StkRskCredit_QryCreditReAmtDtl.retype = 3;
                    } else {
                        StkRskCredit_QryCreditReAmtDtl.retype = 0;
                    }

                    StkRskCredit_QryCreditReAmtDtl.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 2,
                columns: [
                    /*  0 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    /*  1 */{ title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    /*  2 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    /*  3 */{ title: @tr("偿还类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_90 },
                    /*  4 */{ title: @tr("偿还编号 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid, tips: "直接还款时为TITD系统模拟的委托编号\n其他类型为交易所的委托编号" },
                    /*  5 */{ title: @tr("偿还序号 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_90, tips: "本笔偿还编号的偿还顺序" },
                    /*  6 */{ title: @tr("合约委托编号 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid, tips: "合约信息中的委托编号" },
                    /*  7 */{ title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    /*  8 */{ title: @tr("还前金额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110, tips: "本条明细偿还前的金额" },
                    /*  9 */{ title: @tr("类型明细"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_90 },
                    /* 10 */{ title: @tr("偿还金额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110, tips: "本条明细偿还的金额" },
                    /* 11 */{ title: @tr("剩余金额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110, tips: "本条明细偿还后的金额\n如果不是通过直接还款, 本笔偿还编号偿还结束后仍有剩余则放入可用" },
                    /* 12 */{ title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                ]
            };
            row_count: StkRskCredit_QryCreditReAmtDtl.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_QryCreditReAmtDtl.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                if 9 == column_index {
                    if data == "利息" {
                        return #9232e1;
                    } else if data == "本金及费用" {
                        return #e84add;
                    } else if data == "罚息" {
                        return #e31616;
                    } else {
                        return Theme.foreground;
                    }
                }

                if 10 == column_index {
                    return NotifyColor.normal;
                }

                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_QryCreditReAmtDtl.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_QryCreditReAmtDtl.row_data[row_index]);
                }
            }
        }

        if StkRskCredit_QryCreditReAmtDtl.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkRskCredit_QryCreditReAmtDtl.page_index_model;
            item_total <=> StkRskCredit_QryCreditReAmtDtl.item_total;
            page_total <=> StkRskCredit_QryCreditReAmtDtl.page_total;
            page_index <=> StkRskCredit_QryCreditReAmtDtl.page_index;
            page_size <=> StkRskCredit_QryCreditReAmtDtl.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryCreditReAmtDtl.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryCreditReAmtDtl.page_size_changed(size);
            }
        }
    }
}
