use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        self,
        ctptype::CTPType,
        global::{CFG, INS, TRADE_OPT_API},
        instrument::Instrument,
        ticonvert::TIConvert,
        titype::TIType,
    },
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{
    CtpReqInputExecCombineOrderField, CtpReqInputExecOrderField, CtpRspInputExecCombineOrderField, CtpRspInputExecOrderField,
};

use super::tradeopt::show_input_msg_box;

/// 行权录入数据
struct InputExecDatas {
    /// 在行权过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputexec: AtomicBool,

    /// 行权响应数据
    rsp_inputexec: Arc<Mutex<CtpRspInputExecOrderField>>,

    /// 输入的合约是否有改变
    input_call_instid_changed: AtomicBool,

    /// 输入的合约是否有改变
    input_put_instid_changed: AtomicBool,
}
impl InputExecDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputexec: AtomicBool::new(false),
            rsp_inputexec: Arc::new(Mutex::new(CtpRspInputExecOrderField::default())),
            input_call_instid_changed: AtomicBool::new(false),
            input_put_instid_changed: AtomicBool::new(false),
        }
    }
}
static INPUT_EXEC_DATAS: OnceLock<InputExecDatas> = OnceLock::new();

/// 行权
pub(super) struct InputExercise {}

// 构建与初始化
impl InputExercise {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化报单录入数据
        let _ = INPUT_EXEC_DATAS.set(InputExecDatas::new());

        let app_inputexec = app.global::<OptTrd_InputExercise>();

        // 添加合约
        let insid_arr: Rc<VecModel<LineItem>> = Rc::new(VecModel::default());
        let call_insid_arr: Rc<VecModel<LineItem>> = Rc::new(VecModel::default());
        let put_insid_arr: Rc<VecModel<LineItem>> = Rc::new(VecModel::default());
        let inids_map = { INS.get().unwrap().read().unwrap().get_opt_id_all_with_name() };
        inids_map.iter().for_each(|it| {
            let key = it.key();
            let var = it.value();

            insid_arr.push(crate::slintui::LineItem {
                text: key.clone().into(),
                remark: var.1.clone().into(),
                ..Default::default()
            });

            match var.0 {
                TIType::OPTIONS_CALL => call_insid_arr.push(crate::slintui::LineItem {
                    text: key.clone().into(),
                    remark: var.1.clone().into(),
                    ..Default::default()
                }),
                TIType::OPTIONS_PUT => put_insid_arr.push(crate::slintui::LineItem {
                    text: key.clone().into(),
                    remark: var.1.clone().into(),
                    ..Default::default()
                }),
                _ => {}
            }
        });
        let sort_insid_model = Rc::new(insid_arr.sort_by(move |a, b| a.text.cmp(&b.text)));
        app_inputexec.set_call_insid_model(sort_insid_model.clone().into()); // 初始化时为普通行权
        app_inputexec.set_all_insid_model(sort_insid_model.into());

        let sort_call_insid_model = Rc::new(call_insid_arr.sort_by(move |a, b| a.text.cmp(&b.text)));
        app_inputexec.set_all_call_insid_model(sort_call_insid_model.into());

        let sort_put_insid_model = Rc::new(put_insid_arr.sort_by(move |a, b| a.text.cmp(&b.text)));
        app_inputexec.set_put_insid_model(sort_put_insid_model.clone().into());
        app_inputexec.set_all_put_insid_model(sort_put_insid_model.into());

        // 注册看涨合约改变事件
        let app_weak = app.as_weak();
        app_inputexec.on_call_insid_txt_changed(move || InputExercise::on_call_insid_txt_changed(&app_weak));

        // 注册看跌合约改变事件
        let app_weak = app.as_weak();
        app_inputexec.on_put_insid_txt_changed(move || InputExercise::on_put_insid_txt_changed(&app_weak));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputexec.on_volume_text_changed(move || InputExercise::on_volume_text_changed(&app_weak));

        // 注册用户双击了持仓事件
        let app_weak = app.as_weak();
        app_inputexec.on_sel_position(move |accid, exchid, insid, vol| {
            InputExercise::on_sel_position(&app_weak, accid, exchid, insid, vol)
        });

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputexec.on_volume_updown_changed(move |upflag| InputExercise::on_volume_updown_changed(&app_weak, upflag));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputexec.on_req_tip_toggled(move |checked| InputExercise::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputexec.on_ok_clicked(move |need_confirm| InputExercise::on_ok_clicked(&app_weak, need_confirm));

        log::trace!("Input exercise init completed");
    }
}

// 与API的交互
impl InputExercise {
    /// 行权响应
    pub fn on_rsp_inputexec(&self, rsp: CtpRspInputExecOrderField) {
        let inputexec_datas = INPUT_EXEC_DATAS.get().unwrap();

        *inputexec_datas.rsp_inputexec.lock().unwrap() = rsp;
        inputexec_datas.is_err_inputexec.store(true, Ordering::Relaxed);
    }

    /// 组合行权响应
    pub fn on_rsp_inputexeccomb(&self, rsp: CtpRspInputExecCombineOrderField) {
        let inputexec_datas = INPUT_EXEC_DATAS.get().unwrap();

        *inputexec_datas.rsp_inputexec.lock().unwrap() = CtpRspInputExecOrderField { ec: rsp.ec, em: rsp.em };
        inputexec_datas.is_err_inputexec.store(true, Ordering::Relaxed);
    }
}

// 事件
impl InputExercise {
    // 双击了持仓
    fn on_sel_position(app_weak: &Weak<App>, accid: SharedString, exchid: SharedString, insid: SharedString, vol: SharedString) {
        let app = app_weak.unwrap();
        let app_inputexec = app.global::<OptTrd_InputExercise>();

        let vol = vol.replace(",", "");
        let vol = vol.parse::<i32>().unwrap_or_default();
        if vol <= 0 {
            return;
        }

        // 行权类型. 普通行权: 直接替换; 组合行权: 按规则替换
        let extype = app_inputexec.get_ex_type();
        if TIType::EXECT == extype {
            app_inputexec.set_volume(vol.to_string().into());
            app_inputexec.set_volume_has_error(false);
            app_inputexec.set_call_insid(insid);
            app_inputexec.invoke_call_insid_txt_changed();
        } else {
            // 获取合约信息
            let ins = {
                let ins = common::global::INS
                    .get()
                    .unwrap()
                    .read()
                    .unwrap()
                    .get_ins(TIConvert::exch_id(&exchid), &insid)
                    .unwrap_or_default();
                if ins.insid.is_empty() {
                    return;
                }
                ins
            };

            let mut is_auto_fill = false; // 是否自动填充
            let call_insid = app_inputexec.get_call_insid(); // 当前的看涨合约
            let put_insid = app_inputexec.get_put_insid(); // 当前的看跌合约

            if TIType::OPTIONS_CALL == ins.optionstype {
                if put_insid.is_empty() {
                    // 看跌期权为空， 直接填充看涨期权
                    app_inputexec.set_call_insid(insid);
                    app_inputexec.invoke_call_insid_txt_changed();
                    is_auto_fill = true;
                } else {
                    // 看跌期权不为空， 需要在符合规则的前提下填充看涨期权
                    let put_ins = {
                        let (put_insid, _) = Instrument::parse_id(&put_insid);
                        common::global::INS
                            .get()
                            .unwrap()
                            .read()
                            .unwrap()
                            .get_ins(TIConvert::exch_id(&exchid), &put_insid)
                            .unwrap_or_default()
                    };
                    if put_ins.insid.is_empty() {
                        // 当前手动输入的看跌合约是错误的或者是其他交易所的合约, 清空看跌期权并填充看涨期权
                        app_inputexec.set_put_insid("".into());
                        app_inputexec.set_put_insname("".into());
                        app_inputexec.set_put_insid_has_error(true);

                        app_inputexec.set_call_insid(insid);
                        app_inputexec.invoke_call_insid_txt_changed();
                        is_auto_fill = true;
                    } else {
                        // 标的相同， 合约乘数相同，看跌期权的执行价大于看涨期权执行价
                        if ins.underlyingid == put_ins.underlyingid
                            && ins.multiple == put_ins.multiple
                            && ins.strikeprice < put_ins.strikeprice
                        {
                            // 符合规则，填充看涨期权
                            app_inputexec.set_call_insid(insid);
                            app_inputexec.invoke_call_insid_txt_changed();
                            is_auto_fill = true;
                        }
                    }
                }
            } else {
                if call_insid.is_empty() {
                    app_inputexec.set_put_insid(insid);
                    app_inputexec.invoke_put_insid_txt_changed();
                    is_auto_fill = true;
                } else {
                    // 看涨期权不为空， 需要在符合规则的前提下填充看涨期权
                    let call_ins = {
                        let (call_insid, _) = Instrument::parse_id(&call_insid);
                        common::global::INS
                            .get()
                            .unwrap()
                            .read()
                            .unwrap()
                            .get_ins(TIConvert::exch_id(&exchid), &call_insid)
                            .unwrap_or_default()
                    };

                    if call_ins.insid.is_empty() {
                        // 当前手动输入的看涨合约是错误的或者是其他交易所的合约, 清空看涨期权并填充看跌期权
                        app_inputexec.set_call_insid("".into());
                        app_inputexec.set_call_insname("".into());
                        app_inputexec.set_call_insid_has_error(true);

                        app_inputexec.set_put_insid(insid);
                        app_inputexec.invoke_put_insid_txt_changed();
                        is_auto_fill = true;
                    } else {
                        // 标的相同， 合约乘数相同，看跌期权的执行价大于看涨期权执行价
                        if ins.underlyingid == call_ins.underlyingid
                            && ins.multiple == call_ins.multiple
                            && ins.strikeprice > call_ins.strikeprice
                        {
                            // 符合规则，填充看跌期权
                            app_inputexec.set_put_insid(insid);
                            app_inputexec.invoke_put_insid_txt_changed();
                            is_auto_fill = true;
                        }
                    }
                }
            }

            // 填充行权数量
            if is_auto_fill {
                let volume = {
                    let volume = app_inputexec.get_volume();
                    volume.parse::<i32>().unwrap_or_default()
                };
                if vol < volume || 0 == volume {
                    app_inputexec.set_volume(vol.to_string().into());
                    app_inputexec.set_volume_has_error(false);
                }
            }
        }
    }

    /// 期权看涨合约编码有变动
    fn on_call_insid_txt_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputexec_datas = INPUT_EXEC_DATAS.get().unwrap();
        inputexec_datas.input_call_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputexec = app.global::<OptTrd_InputExercise>();

        let insid = app_inputexec.get_call_insid();
        let ins = { INS.get().unwrap().read().unwrap().get_ins_by_insid(&insid) };

        app_inputexec.set_exchid("".into());
        if let Some(ins) = ins {
            app_inputexec.set_call_insname(ins.symbol.clone().into());

            if TIType::PRODUCT_OPT != ins.producttype {
                app_inputexec.set_call_insid_has_error(true);
                return false;
            }

            let extype = app_inputexec.get_ex_type();
            if TIType::EXECT != extype {
                if TIType::OPTIONS_CALL != ins.optionstype {
                    app_inputexec.set_call_insid_has_error(true);
                    return false;
                }
            }

            app_inputexec.set_call_insid_has_error(false);

            // 设置交易所编码
            let eid = app_inputexec.get_exchid();
            if eid.as_str() != ins.exchname.as_str() {
                app_inputexec.set_exchid(ins.exchname.clone().into());
            }
        } else {
            app_inputexec.set_call_insname(match insid.is_empty() {
                true => "".into(),
                false => "无效合约".into(),
            });
            app_inputexec.set_call_insid_has_error(true);
        }

        true
    }

    /// 期权看跌合约编码有变动
    fn on_put_insid_txt_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputexec_datas = INPUT_EXEC_DATAS.get().unwrap();
        inputexec_datas.input_put_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputexec = app.global::<OptTrd_InputExercise>();

        let insid = app_inputexec.get_put_insid();
        let ins = { INS.get().unwrap().read().unwrap().get_ins_by_insid(&insid) };

        app_inputexec.set_exchid("".into());
        if let Some(ins) = ins {
            app_inputexec.set_put_insname(ins.symbol.clone().into());

            if TIType::PRODUCT_OPT != ins.producttype {
                app_inputexec.set_put_insid_has_error(true);
                return false;
            }

            if TIType::OPTIONS_PUT != ins.optionstype {
                app_inputexec.set_put_insid_has_error(true);
                return false;
            }

            app_inputexec.set_put_insid_has_error(false);

            // 设置交易所编码
            let eid = app_inputexec.get_exchid();
            if eid.as_str() != ins.exchname.as_str() {
                app_inputexec.set_exchid(ins.exchname.clone().into());
            }
        } else {
            app_inputexec.set_put_insname(match insid.is_empty() {
                true => "".into(),
                false => "无效合约".into(),
            });
            app_inputexec.set_put_insid_has_error(true);
        }

        true
    }

    /// 报单数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputexec = app.global::<OptTrd_InputExercise>();

        let volume = app_inputexec.get_volume().replace(",", "");
        if volume.is_empty() {
            app_inputexec.set_volume_has_error(true);
            return false;
        }

        if volume.trim().parse::<i32>().unwrap_or_default() <= 0 {
            app_inputexec.set_volume_has_error(true);
            return false;
        }

        app_inputexec.set_volume_has_error(false);

        true
    }

    // 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputexec = app.global::<OptTrd_InputExercise>();

        if app_inputexec.get_volume_has_error() {
            app_inputexec.set_volume("1".into());
            app_inputexec.set_volume_has_error(false);
            return;
        }

        let volume_str = app_inputexec.get_volume().replace(",", "");
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            return;
        }

        if upflag {
            volume += 1;
        } else {
            volume -= 1;
            if volume <= 0 {
                return;
            }
        }

        app_inputexec.set_volume(slint::format!("{}", volume));
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();
        app_set.set_trdreqtip_inputexec(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.ExecOrderInsert = checked;
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputexec: OptTrd_InputExercise = app.global::<OptTrd_InputExercise>();

        // 资金账户
        let accid = {
            let aid = app_inputexec.get_accountid();
            if aid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "资金账户不能为空".into());
                return;
            }
            aid.to_string()
        };

        // 投保标志
        let hedge = match app_inputexec.get_hedge() {
            0 => CTPType::HEDGE_SPECULATION,
            1 => CTPType::HEDGE_HEDGE,
            _ => CTPType::HEDGE_ARBITRAGE,
        };

        // 行权数量
        let volume = {
            let volume = app_inputexec.get_volume().replace(",", "");
            if volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "行权数量不能为空".into());
                return;
            }

            let volume = volume.trim().parse::<i32>().unwrap_or_default();
            if volume <= 0 {
                show_input_msg_box(&app, 1, Default::default(), "请输入有效的行权数量".into());
                return;
            }
            volume
        };

        // 行权类型
        let extype = app_inputexec.get_ex_type();

        // 看涨合约
        let call_ins = {
            let call_ins = {
                let id = app_inputexec.get_call_insid();
                INS.get().unwrap().read().unwrap().get_ins_by_insid(&id)
            };

            if call_ins.is_none() {
                show_input_msg_box(&app, 1, Default::default(), {
                    if TIType::EXECT == extype {
                        "获取行权合约失败".into()
                    } else {
                        "获取看涨行权合约失败".into()
                    }
                });
                return;
            }

            let call_ins = call_ins.unwrap();
            if TIType::PRODUCT_OPT != call_ins.producttype {
                show_input_msg_box(&app, 1, Default::default(), "请输入期权合约".into());
                return;
            }

            call_ins
        };

        // 检查是否是行权日
        if call_ins.expiredate != { CFG.get().unwrap().read().unwrap().run.TradingDay.to_string() } {
            show_input_msg_box(&app, 1, Default::default(), "合约在当前交易日不能参与行权".into());
            return;
        }

        // 普通行权
        if TIType::EXECT == extype {
            if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.ExecOrderInsert } {
                show_input_msg_box(
                    &app,
                    100,
                    "行权录入确认".into(),
                    slint::format!(
                        "资金账户:{}\n\n交易所:{}\n行权合约:{}\n行权数量:{}",
                        accid,
                        call_ins.exchname,
                        call_ins.insid,
                        volume
                    ),
                );

                return;
            }

            let mut req = CtpReqInputExecOrderField::default();
            req.InvestorID = accid;
            req.InstrumentID = call_ins.insid.clone();
            req.Volume = volume;
            req.HedgeFlag = hedge;

            let api = TRADE_OPT_API.get().unwrap().read().unwrap();
            if let Err(err) = api.req_exec_orderinput(0, &req) {
                show_input_msg_box(
                    &app,
                    1,
                    Default::default(),
                    slint::format!("请求行权录入失败\n\n错误信息: {}", err),
                );
            }
        }
        // 组合行权
        else {
            // 检查输入的看涨合约是否是看涨
            if TIType::OPTIONS_CALL != call_ins.optionstype {
                show_input_msg_box(&app, 1, Default::default(), "输入的行权看涨合约错误".into());
                return;
            }

            // 看跌合约
            let put_ins = {
                let put_ins = {
                    let id = app_inputexec.get_put_insid();
                    INS.get().unwrap().read().unwrap().get_ins_by_insid(&id)
                };
                if put_ins.is_none() {
                    show_input_msg_box(&app, 1, Default::default(), "获取看跌行权合约失败".into());
                    return;
                }
                let put_ins = put_ins.unwrap();
                if TIType::OPTIONS_PUT != put_ins.optionstype {
                    show_input_msg_box(&app, 1, Default::default(), "输入的行权看跌合约错误".into());
                    return;
                }

                put_ins
            };

            // 检查两个期权合约是否是同一个交易所的
            if call_ins.exchid != put_ins.exchid {
                show_input_msg_box(
                    &app,
                    1,
                    Default::default(),
                    "输入的行权看涨合约与看跌期权合约不属于同一个交易所".into(),
                );
                return;
            }

            // 检查两个期权合约到期日是否同一天
            if call_ins.expiredate != put_ins.expiredate {
                show_input_msg_box(&app, 1, Default::default(), "看涨合约与看跌期权合约到期日期不是同一天".into());
                return;
            }

            if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.ExecOrderInsert } {
                show_input_msg_box(
                    &app,
                    100,
                    "组合行权录入确认".into(),
                    slint::format!(
                        "资金账户:{}\n\n交易所:{}\n看涨合约:{}\n看跌合约:{}\n\n行权数量:{}",
                        accid,
                        call_ins.exchname,
                        call_ins.insid,
                        put_ins.insid,
                        volume
                    ),
                );

                return;
            }

            let mut req = CtpReqInputExecCombineOrderField::default();
            req.ActionType = 49;
            req.InvestorID = accid;
            req.CallInstrumentID = call_ins.insid.clone();
            req.PutInstrumentID = put_ins.insid.clone();
            req.Volume = volume;

            let api = TRADE_OPT_API.get().unwrap().read().unwrap();
            if let Err(err) = api.req_exec_comb_orderinput(0, &req) {
                show_input_msg_box(
                    &app,
                    1,
                    Default::default(),
                    slint::format!("请求合并行权录入失败\n\n错误信息: {}", err),
                );
            }
        }
    }
}

/// 更新UI
impl InputExercise {
    // 更新错误信息
    async fn update_errmsg(&self, app_weak: &Weak<App>) {
        let inputexec_datas = INPUT_EXEC_DATAS.get().unwrap();

        let is_err_inputexec = inputexec_datas
            .is_err_inputexec
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputexec {
            return;
        }

        let rsp = { inputexec_datas.rsp_inputexec.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("行权录入响应失败\n\n错误码:{}\n错误信息:{}", rsp.ec, rsp.em),
            );
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新行权响应错误信息
        self.update_errmsg(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let inputexec_datas = INPUT_EXEC_DATAS.get().unwrap();

        // (看涨期权输入是否有变化, 看跌期权输入是否有变化)
        let is_changed = (
            inputexec_datas
                .input_call_instid_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok(),
            inputexec_datas
                .input_put_instid_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok(),
        );

        if is_changed.0 || is_changed.1 {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_inputexec = app.global::<OptTrd_InputExercise>();

                // 看涨期权输入是否有变化
                if is_changed.0 {
                    let insid = app_inputexec.get_call_insid();

                    if insid.len() > 8 + 5 {
                        // 目前个股期权单合约的编码长度还没有超过8位的
                        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                        app_inputexec.set_call_insid_model(items.into());
                        return;
                    }

                    let extype = app_inputexec.get_ex_type();
                    let all_insid_data = if TIType::EXECT == extype {
                        app_inputexec.get_all_insid_model()
                    } else {
                        app_inputexec.get_all_call_insid_model()
                    };

                    if insid.is_empty() {
                        app_inputexec.set_call_insid_model(all_insid_data);
                        return;
                    }

                    let mut find_flag = false;
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    for i in 0..all_insid_data.row_count() {
                        let li = all_insid_data.row_data(i).unwrap();
                        if li.text.starts_with(insid.as_str()) {
                            items.push(li);
                            find_flag = true;
                        } else {
                            if find_flag {
                                break;
                            }
                        }
                    }

                    app_inputexec.set_call_insid_model(items.into());
                }

                // 看跌期权输入有变化
                if is_changed.1 {
                    let insid = app_inputexec.get_put_insid();

                    if insid.len() > 8 + 5 {
                        // 目前个股期权单合约的编码长度还没有超过8位的
                        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                        app_inputexec.set_put_insid_model(items.into());
                        return;
                    }

                    let all_insid_data = app_inputexec.get_all_put_insid_model();

                    if insid.is_empty() {
                        app_inputexec.set_put_insid_model(all_insid_data);
                        return;
                    }

                    let mut find_flag = false;
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    for i in 0..all_insid_data.row_count() {
                        let li = all_insid_data.row_data(i).unwrap();
                        if li.text.starts_with(insid.as_str()) {
                            items.push(li);
                            find_flag = true;
                        } else {
                            if find_flag {
                                break;
                            }
                        }
                    }

                    app_inputexec.set_put_insid_model(items.into());
                }
            });
        }
    }
}
