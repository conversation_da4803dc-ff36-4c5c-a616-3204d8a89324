import { <PERSON>, <PERSON>bView, <PERSON>B<PERSON>, <PERSON>Buttons, MBIcon, MBResult } from "@zstk/lib.slint";

import {
    FutTrd_Com, FutTrd_TabSelIdx, FutTrd_Setting,
    FutTrd_Account,
    FutTrd_CancelTip, FutTrd_InOrder, FutTrd_InExercise,
    FutTrd_InputTip, FutTrd_InputOrder, FutTrd_InputExercise
} from "struct.slint";

import { AccountPage } from "account.slint";
import { MarketDataPage, FutTrd_MarketData } from "marketdata.slint";
import { PositionPage, FutTrd_Position } from "position.slint";

import { InPage } from "in.slint";

import { FutTrd_QryOrder } from "qry_order.slint";
import { FutTrd_QryTrade } from "qry_trade.slint";
import { FutTrd_QryExercise } from "qry_exercise.slint";
import { QueryPage } from "qry.slint";

import { FutTrd_MonOrderInsertCancel } from "mon_orderinsertcancel.slint";
import { FutTrd_MonLimitPosition } from "mon_limitposition.slint";
import { FutTrd_MonLimitOpen } from "mon_limitopen.slint";
import { FutTrd_MonSelfTrade } from "mon_selftrade.slint";
import { FutTrd_MonSelfTradeDtl } from "mon_selftradedtl.slint";
import { MonitorPage } from "mon.slint";

import { InputPage } from "input.slint";

import { MarketDataDtl } from "../md/mdstruct.slint";
import { MarketDataDtlPage } from "../md/marketdatadtl.slint";

export {
    FutTrd_Com, FutTrd_TabSelIdx, FutTrd_Setting,
    FutTrd_MarketData,
    FutTrd_Account, FutTrd_Position,
    FutTrd_CancelTip, FutTrd_InOrder, FutTrd_InExercise,
    FutTrd_QryOrder, FutTrd_QryTrade, FutTrd_QryExercise,
    FutTrd_MonOrderInsertCancel, FutTrd_MonLimitPosition, FutTrd_MonLimitOpen, FutTrd_MonSelfTrade, FutTrd_MonSelfTradeDtl,
    FutTrd_InputTip, FutTrd_InputOrder, FutTrd_InputExercise
}

export component TradeFutPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 1px;

        HorizontalLayout {
            padding_left: 1px;
            padding_right: 1px;

            AccountPage {
                height: 130px;
            }
        }

        HorizontalLayout {
            padding_left: 1px;
            padding_right: 1px;
            spacing: 1px;
            height: 380px;

            Rectangle {
                border-radius: 2px;
                border-width: 1px;
                border-color: Theme.border;

                MarketDataPage {
                    init => {
                        MarketDataDtl.mdtype = 1;
                    }
                }
            }

            MarketDataDtlPage {
                width: 360px;
            }
        }

        HorizontalLayout {
            padding_left: 1px;
            padding-right: 1px;
            spacing: 1px;

            VerticalLayout {
                TabView {
                    height: 30px;

                    items: [
                        { text: "持仓" },
                        { text: "在途订单" },
                        { text: "查询" },
                        { text: "异常指标监控" },
                    ];

                    current_index <=> FutTrd_TabSelIdx.selidx;
                }

                Rectangle {
                    if 0 == FutTrd_TabSelIdx.selidx: PositionPage { }
                    if 1 == FutTrd_TabSelIdx.selidx: InPage { }
                    if 2 == FutTrd_TabSelIdx.selidx: QueryPage { }
                    if 3 == FutTrd_TabSelIdx.selidx: MonitorPage { }
                }
            }

            Rectangle {
                border-radius: 2px;
                border-width: 1px;
                border-color: Theme.border;
                width: 360px;

                InputPage {}
            }
        }

    }

    // 报单提醒
    if FutTrd_InputTip.app_ret.state > 0: Rectangle {
        init => {
            if 100 == FutTrd_InputTip.app_ret.state {
                i_msgbox_input.id = FutTrd_InputTip.app_ret.state;
                i_msgbox_input.show_check = true;
                i_msgbox_input.checked = true;
                i_msgbox_input.open_title(FutTrd_InputTip.app_ret.title, FutTrd_InputTip.app_ret.msg, MBButtons.YesNo, MBIcon.Question);
            } else {
                i_msgbox_input.show_check = false;
                i_msgbox_input.open_title(FutTrd_InputTip.app_ret.title, FutTrd_InputTip.app_ret.msg, MBButtons.Ok, MBIcon.Error);
            }
            FutTrd_InputTip.app_ret = { state: 0, title: "", msg: "" };
        }
    }
    i_msgbox_input := MessageBox {
        width: root.width;
        height: root.height;
        check_txt: 0 == FutTrd_TabSelIdx.input_selidx ? "提醒报单请求" : "提醒行权请求";

        clicked(id, ret) => {
            if MBResult.Yes == ret {
                if 0 == FutTrd_TabSelIdx.input_selidx {
                    if 100 == id {
                        FutTrd_InputOrder.ok_clicked(false);
                    }
                }
                else if 1 == FutTrd_TabSelIdx.input_selidx {
                    if 100 == id {
                        FutTrd_InputExercise.ok_clicked(false);
                    }
                }
            }
            self.close();
        }

        toggled(id, checked) => {
            if 0 == FutTrd_TabSelIdx.input_selidx {
                if 100 == id {
                    FutTrd_InputOrder.req_tip_toggled(checked);
                }
            }
            else if 1 == FutTrd_TabSelIdx.input_selidx {
                if 100 == id {
                    FutTrd_InputExercise.req_tip_toggled(checked);
                }
            }
        }
    }

    // 撤单提醒
    if FutTrd_CancelTip.app_ret.state > 0: Rectangle {
        init => {
            if 100 == FutTrd_CancelTip.app_ret.state ||
               101 == FutTrd_CancelTip.app_ret.state ||
               102 == FutTrd_CancelTip.app_ret.state ||
               103 == FutTrd_CancelTip.app_ret.state {
                i_msgbox_cancel.id = FutTrd_CancelTip.app_ret.state;
                i_msgbox_cancel.show_check = true;
                i_msgbox_cancel.checked = true;
                i_msgbox_cancel.open_title(FutTrd_CancelTip.app_ret.title, FutTrd_CancelTip.app_ret.msg, MBButtons.YesNo, MBIcon.Question);
            } else {
                i_msgbox_cancel.show_check = false;
                i_msgbox_cancel.open_title(FutTrd_CancelTip.app_ret.title, FutTrd_CancelTip.app_ret.msg, MBButtons.Ok, MBIcon.Error);
            }
            FutTrd_CancelTip.app_ret = { state: 0, title: "", msg: ""};
        }
    }
    i_msgbox_cancel := MessageBox {
        width: root.width;
        height: root.height;
        check_txt: 0 == FutTrd_TabSelIdx.in_subselidx ? "提醒撤单请求" : "提醒撤销行权请求";

        clicked(id, ret) => {
            if MBResult.Yes == ret {
                if 0 == FutTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        FutTrd_InOrder.cancel_order_clieked(FutTrd_InOrder.sel_cancel_order, false);
                    }
                    if 101 == id {
                        FutTrd_InOrder.cancel_order_all_clieked(FutTrd_InOrder.row_data.length, false);
                    }
                    if 102 == id {
                        FutTrd_InOrder.cancel_order_bs_clieked(0, FutTrd_InOrder.row_data.length, false);
                    }
                    if 103 == id {
                        FutTrd_InOrder.cancel_order_bs_clieked(1, FutTrd_InOrder.row_data.length, false);
                    }
                } else if 1 == FutTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        FutTrd_InExercise.cancel_exec_clieked(FutTrd_InExercise.sel_cancel_exec, false);
                    }
                    if 101 == id {
                        FutTrd_InExercise.cancel_exec_all_clieked(FutTrd_InExercise.row_data.length, false);
                    }
                }
            }

            self.close();
        }

        toggled(id, checked) => {
            if 0 == FutTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id || 102 == id || 103 == id {
                    FutTrd_InOrder.req_tip_toggled(checked);
                }

            } else if 1 == FutTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id {
                    FutTrd_InExercise.req_tip_toggled(checked);
                }
            }

        }
    }
}
