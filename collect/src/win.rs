use ::std::os::windows::process::CommandExt;
use std::process::Command;

use crate::com::{self, CollectInfo, IpMac};

// 实现执行命令
struct WinCommand {
    encoding: &'static encoding_rs::Encoding, // 根据系统活动代码页推断的编码
}
impl WinCommand {
    pub fn new() -> Result<Self, String> {
        let codepage = WinCommand::get_active_codepage()?;
        Ok(Self {
            encoding: WinCommand::map_codepage_to_encoding(codepage),
        })
    }

    /// 执行命令
    pub fn run<S, I>(&self, program: S, args: I) -> Result<String, String>
    where
        I: IntoIterator<Item = S>,
        S: AsRef<std::ffi::OsStr>,
    {
        let output = Command::new(program).creation_flags(0x08000000).args(args).output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(std::format!("exec order failed"));
        }

        Ok(self.decode_to_utf8(&output.stdout)?)
    }

    /// 解码为 UTF-8 String
    fn decode_to_utf8(&self, bytes: &[u8]) -> Result<String, String> {
        // 1. 检测 BOM 确定编码
        if let Some((encoding, bom_length)) = WinCommand::detect_bom(bytes) {
            let (text, _, _) = encoding.decode(&bytes[bom_length..]);
            return Ok(text.into_owned());
        }

        // 2. 解码字节流
        let (text, _, _) = self.encoding.decode(bytes);
        Ok(text.into_owned())
    }
}
impl WinCommand {
    /// 获取系统活动代码页
    pub fn get_active_codepage() -> Result<u32, String> {
        let program = "chcp";
        let output = Command::new("cmd").creation_flags(0x08000000).args(&["/C", program]).output();

        let output = output.map_err(|err| std::format!("{}. {}", program, err.to_string()))?;
        if !output.status.success() {
            return Err(std::format!("exec program [{}] failed", program));
        }

        let output_str = encoding_rs::mem::decode_latin1(&output.stdout); // 用 Latin-1 避免递归依赖
        output_str
            .split_ascii_whitespace()
            .last()
            .ok_or("can not get chcp page")?
            .parse::<u32>()
            .map_err(|err| err.to_string())
    }

    /// 执行命令
    pub fn open<S, I>(program: S, args: I) -> Result<String, String>
    where
        I: IntoIterator<Item = S>,
        S: AsRef<std::ffi::OsStr>,
    {
        let output = Command::new(program).creation_flags(0x08000000).args(args).output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(std::format!("exec order failed"));
        }

        Ok(WinCommand::decode_auto(&output.stdout)?)
    }

    /// 检测 BOM 返回 (编码, BOM长度)
    fn detect_bom(bytes: &[u8]) -> Option<(&'static encoding_rs::Encoding, usize)> {
        if bytes.len() >= 3 && bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF {
            Some((encoding_rs::UTF_8, 3)) // UTF-8 BOM
        } else if bytes.len() >= 2 {
            match (bytes[0], bytes[1]) {
                (0xFF, 0xFE) => Some((encoding_rs::UTF_16LE, 2)), // UTF-16 LE
                (0xFE, 0xFF) => Some((encoding_rs::UTF_16BE, 2)), // UTF-16 BE
                _ => None,
            }
        } else {
            None
        }
    }

    /// 代码页到编码的映射
    fn map_codepage_to_encoding(codepage: u32) -> &'static encoding_rs::Encoding {
        match codepage {
            65001 => encoding_rs::UTF_8,       // UTF-8
            936 => encoding_rs::GBK,           // 简体中文 (GBK)
            950 => encoding_rs::BIG5,          // 繁体中文 (Big5)
            932 => encoding_rs::SHIFT_JIS,     // 日文 (Shift-JIS)
            949 => encoding_rs::EUC_KR,        // 韩文 (EUC-KR)
            1252 => encoding_rs::WINDOWS_1252, // 拉丁语系
            _ => encoding_rs::WINDOWS_1252,    // 默认回退
        }
    }

    /// 自动检测编码并解码为 UTF-8 String
    fn decode_auto(bytes: &[u8]) -> Result<String, String> {
        // 1. 检测 BOM 确定编码
        if let Some((encoding, bom_length)) = WinCommand::detect_bom(bytes) {
            let (text, _, _) = encoding.decode(&bytes[bom_length..]);
            return Ok(text.into_owned());
        }

        // 2. 获取系统活动代码页并推断编码
        let codepage = WinCommand::get_active_codepage()?;
        let encoding = WinCommand::map_codepage_to_encoding(codepage);

        // 3. 解码字节流
        let (text, _, _) = encoding.decode(bytes);
        Ok(text.into_owned())
    }
}

/// 采集信息
struct WinCollect {
    cmd: WinCommand,
}
impl WinCollect {
    pub fn new() -> Result<Self, String> {
        let cmd = WinCommand::new();
        match cmd {
            Ok(cmd) => Ok(Self { cmd }),
            Err(err) => Err(err),
        }
    }
}
impl WinCollect {
    /// 获取IP/MAC地址
    /// 第一个地址为默认路由的地址
    pub fn get_ip_mac(&self) -> Result<Vec<IpMac>, String> {
        let output_str = self.cmd.run(
            "wmic",
            [
                "nicconfig",
                "where",
                "IPEnabled=TRUE",
                "get",
                "DefaultIPGateway,Description,IPAddress,MACAddress",
                "/value",
            ],
        )?;
        let lines: Vec<&str> = output_str.lines().collect();

        let mut ipmac_vec = vec![];

        let defaultipgateway_tag = "DefaultIPGateway=";
        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            // 命令成功后一定是连续的4行
            if let Some(posidx) = line.find(defaultipgateway_tag) {
                let isdefault = if posidx + defaultipgateway_tag.len() != line.len() {
                    true
                } else {
                    false
                };

                let mut ipmac = IpMac::default();

                // Description
                let line = lines[inx].trim();
                inx += 1;
                if let Some(posidx) = line.find("=") {
                    ipmac.interface = line[posidx + 1..].to_owned();
                }

                // IPAddress
                let line = lines[inx].trim();
                inx += 1;
                if let Some(posidx) = line.find("=") {
                    // 去除首尾的花扩号,去除首尾的双引号
                    let ips = line[posidx + 1..]
                        .to_string()
                        .replace('{', "")
                        .replace('}', "")
                        .replace('"', "");
                    if !ips.is_empty() {
                        // 获取所有的IP地址. 每块网卡可能有多个地址,这里只取第一个ipv4,ipv6
                        let ip_vec: Vec<&str> = ips.split(',').collect();
                        for ip in ip_vec {
                            // 含有:的一定是IPV6地址(IPV6地址可能包含. 如: ::ffff:127.0.0.1)
                            if ip.contains(':') {
                                if ipmac.ipv6.is_empty() {
                                    ipmac.ipv6 = ip.to_string();
                                }
                            } else {
                                if ipmac.ipv4.is_empty() {
                                    ipmac.ipv4 = ip.to_string();
                                }
                            }

                            if !ipmac.ipv4.is_empty() && !ipmac.ipv6.is_empty() {
                                break;
                            }
                        }
                    }
                }

                // MACAddress
                let line = lines[inx].trim();
                inx += 1;
                if let Some(posidx) = line.find("=") {
                    ipmac.mac = line[posidx + 1..].to_owned();
                }

                if isdefault {
                    ipmac_vec.insert(0, ipmac)
                } else {
                    ipmac_vec.push(ipmac);
                }
            }
        }

        if !ipmac_vec.is_empty() {
            return Ok(ipmac_vec);
        }

        Err("empty".to_owned())
    }

    /// 获取操作系统信息(0:设备名称; 1:操作系统版本; 2:系统盘符)
    pub fn get_os_info(&self) -> Result<(String, String, String), String> {
        let output_str = self.cmd.run("wmic", ["os", "get", "CSName,SystemDrive,Version", "/value"])?;
        let lines: Vec<&str> = output_str.lines().collect();

        let csname_tag = "CSName=";
        let system_drive_tag = "SystemDrive=";
        let version_tag = "Version=";

        let mut csname = String::new();
        let mut system_drive = String::new();
        let mut version = String::new();

        let mut inx = 0;
        while inx < lines.len() {
            if !csname.is_empty() && !system_drive.is_empty() && !version.is_empty() {
                return Ok((csname, version, system_drive));
            }

            let line = lines[inx].trim();
            inx += 1;

            if csname.is_empty() {
                if let Some(posidx) = line.find(csname_tag) {
                    csname = line[posidx + csname_tag.len()..].to_owned();
                    continue;
                }
            }

            if system_drive.is_empty() {
                if let Some(posidx) = line.find(system_drive_tag) {
                    system_drive = line[posidx + system_drive_tag.len()..].to_owned();
                    continue;
                }
            }

            if version.is_empty() {
                if let Some(posidx) = line.find(version_tag) {
                    version = line[posidx + version_tag.len()..].to_owned();
                    continue;
                }
            }
        }

        if !csname.is_empty() && !system_drive.is_empty() && !version.is_empty() {
            return Ok((csname, version, system_drive));
        }

        if csname.is_empty() {
            return Err("get CSName failed".to_owned());
        }

        if system_drive.is_empty() {
            return Err("get SystemDrive failed".to_owned());
        }

        return Err("get Version failed".to_owned());
    }

    /// 获取CPU序列号
    pub fn get_cpu_serial_number(&self) -> Result<Vec<String>, String> {
        let output_str = self.cmd.run("wmic", ["cpu", "get", "ProcessorId", "/value"])?;
        let lines: Vec<&str> = output_str.lines().collect();

        let mut ret_vec = Vec::new();

        let processorid_tag = "ProcessorId=";

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if let Some(posidx) = line.find(processorid_tag) {
                ret_vec.push(line[posidx + processorid_tag.len()..].to_owned());
            }
        }

        Ok(ret_vec)
    }

    /// 获取硬盘序列号
    pub fn get_diskdrive_serial_number(&self) -> Result<Vec<String>, String> {
        let output_str = self.cmd.run("wmic", ["diskdrive", "get", "SerialNumber", "/value"])?;
        let lines: Vec<&str> = output_str.lines().collect();

        let mut ret_vec = Vec::new();

        let serialnumber_tag = "SerialNumber=";

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if let Some(posidx) = line.find(serialnumber_tag) {
                ret_vec.push(line[posidx + serialnumber_tag.len()..].to_owned());
            }
        }

        Ok(ret_vec)
    }

    /// 获取BIOS序列号
    pub fn get_bios_serial_number(&self) -> Result<String, String> {
        let output_str = self.cmd.run("wmic", ["bios", "get", "SerialNumber", "/value"])?;
        let lines: Vec<&str> = output_str.lines().collect();

        let serialnumber_tag = "SerialNumber=";

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if let Some(posidx) = line.find(serialnumber_tag) {
                return Ok(line[posidx + serialnumber_tag.len()..].to_owned());
            }
        }

        Ok("".to_owned())
    }

    /// 获取系统盘分区信息(系统盘符,分区系列号,分区格式,分区容量(G))
    pub fn get_os_logical_disk_info(&self, system_drive: &str) -> Result<String, String> {
        let output_str = self.cmd.run(
            "wmic",
            ["logicaldisk", "get", "DeviceID,FileSystem,Size,VolumeSerialNumber", "/value"],
        )?;
        let lines: Vec<&str> = output_str.lines().collect();

        let deviceid_tag = "DeviceID=";

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            // 命令成功后一定是连续的4行
            if let Some(posidx) = line.find(deviceid_tag) {
                // DeviceID
                let device_id = line[posidx + deviceid_tag.len()..].to_owned();
                if device_id != system_drive {
                    continue;
                }

                // FileSystem
                let mut file_system = String::new();
                let line = lines[inx].trim();
                inx += 1;
                if let Some(posidx) = line.find("=") {
                    file_system = line[posidx + 1..].to_owned();
                }

                // Size
                let mut size_g = 0;
                let mut size = String::new();
                let line = lines[inx].trim();
                inx += 1;
                if let Some(posidx) = line.find("=") {
                    size = line[posidx + 1..].to_owned();
                }
                let ret = size.parse::<i64>();
                if let Ok(size) = ret {
                    size_g = size / 1024 / 1024 / 1024;
                }

                // VolumeSerialNumber
                let mut volume_serial_number = String::new();
                let line = lines[inx].trim();
                if let Some(posidx) = line.find("=") {
                    volume_serial_number = line[posidx + 1..].to_owned();
                }

                return Ok(std::format!(
                    "{},{},{},{}",
                    &device_id[0..1],
                    volume_serial_number,
                    file_system,
                    size_g
                ));
            }
        }

        Err(std::format!("empty. system_drive:[{}]", system_drive))
    }
}

pub(crate) struct Win {}
impl Win {
    /// 获取采集信息
    pub fn get_collect_info() -> Result<CollectInfo, String> {
        let wc = WinCollect::new()?;

        // 终端类型(1)
        let terminal_type = com::get_terminal_type().unwrap();

        // 信息采集时间(19)
        let collect_time = com::get_collect_time();

        // IP(39)/MAC(12)
        let ret = wc.get_ip_mac();
        if let Err(err) = ret {
            return Err(std::format!("get_ip_mac failed. {err}"));
        }
        let ipmac_vec = ret.unwrap();
        let mut ip1 = String::new();
        let mut mac1 = String::new();
        let mut ip2 = String::new();
        let mut mac2 = String::new();
        for ipmac in &ipmac_vec {
            if ip1.is_empty() {
                ip1 = {
                    if !ipmac.ipv4.is_empty() {
                        ipmac.ipv4.clone()
                    } else {
                        com::sub_str(&ipmac.ipv6, 39).to_owned()
                    }
                };
                mac1 = ipmac.mac.clone().replace(':', "");
                continue;
            }

            if ip2.is_empty() {
                ip2 = {
                    if !ipmac.ipv4.is_empty() {
                        ipmac.ipv4.clone()
                    } else {
                        com::sub_str(&ipmac.ipv6, 39).to_owned()
                    }
                };
                mac2 = ipmac.mac.clone().replace(':', "");
                break;
            }
        }

        // 获取操作系统信息(0:设备名称(9); 1:操作系统版本(5); 2:系统盘符)
        let os_info = wc.get_os_info();
        if let Err(err) = os_info {
            return Err(std::format!("get_ip_mac failed. {err}"));
        }

        let os_info = os_info.unwrap();

        // 设备名称(9)
        let device_name = com::replace_at_2_space(&os_info.0);
        let device_name = com::sub_str(&device_name, 9);

        // 操作系统版本(5)
        let os_ver = com::replace_at_2_space(&os_info.1);
        let os_ver = com::sub_str(&os_ver, 5);

        // 硬盘序列号(16)
        let ret = wc.get_diskdrive_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_diskdrive_serial_number failed. {err}"));
        }
        let ret = ret.unwrap();
        let disk_drive_sn = {
            if !ret.is_empty() {
                let sn = ret[0].clone();
                let sn = com::replace_at_2_space(&sn);
                com::sub_str(&sn, 16).to_string()
            } else {
                "".to_owned()
            }
        };

        // CPU序列号(16)
        let ret = wc.get_cpu_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_diskdrive_serial_number failed. {err}"));
        }
        let ret = ret.unwrap();
        let cpu_sn = {
            if !ret.is_empty() {
                let sn = ret[0].clone();
                let sn = com::replace_at_2_space(&sn);
                com::sub_str(&sn, 16).to_string()
            } else {
                "".to_owned()
            }
        };

        // BIOS序列号(10)
        let ret = wc.get_bios_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_bios_serial_number failed. {err}"));
        }
        let bios_sn = {
            let sn = ret.unwrap();
            let sn = com::replace_at_2_space(&sn);
            com::sub_str(&sn, 10).to_string()
        };

        // 系统盘分区信息(24)
        let ret = wc.get_os_logical_disk_info(&os_info.2);
        if let Err(err) = ret {
            return Err(std::format!("get_os_logical_disk_info failed. {err}"));
        }
        let os_logical_disk_info = {
            let ret = ret.unwrap();
            let ret = com::replace_at_2_space(&ret);
            com::sub_str(&ret, 24).to_string()
        };

        // 终端类型@信息采集时间@私网IP1@私网IP2@网卡Mac地址1@网卡Mac地址2@设备名称@操作系统版本@硬盘序列号@CPU序列号@BIOS序列号@系统盘分区信息
        let collect_info = std::format!("{terminal_type}@{collect_time}@{ip1}@{ip2}@{mac1}@{mac2}@{device_name}@{os_ver}@{disk_drive_sn}@{cpu_sn}@{bios_sn}@{os_logical_disk_info}");

        #[cfg(debug_assertions)]
        {
            println!("collect_info(win): {}", collect_info);
        }

        // 对采集信息进行加密
        let ret = com::encrypt(&collect_info);
        if let Err(err) = ret {
            return Err(std::format!("encrypt failed. {err}"));
        }

        Ok(ret.unwrap())
    }

    /// 获取IP/MAC
    pub fn get_ip_mac() -> Result<Vec<IpMac>, String> {
        WinCollect::new()?.get_ip_mac()
    }
}

#[cfg(test)]
mod tests {
    use crate::win::Win;

    #[test]
    fn it_works() {
        let ret = Win::get_collect_info();
        assert_eq!(true, ret.is_ok());

        if let Ok(col) = ret {
            println!("exinfo:{}\ntiinfo:{}", col.exinfo, col.tiinfo);
        }
    }
}
