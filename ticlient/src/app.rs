use crate::{
    common::{
        self, global,
        tistruct::{EClientType, FilePathField},
    },
    ui::{self, IApp},
    version,
};

use crate::slintui::*;
use slint::*;

/// 关于对话框
fn set_about(app: &crate::slintui::App) {
    let app_about = app.global::<AppAbout>();

    let target_os = {
        if cfg!(target_os = "windows") {
            "windows"
        } else if cfg!(target_os = "macos") {
            "macos"
        } else if cfg!(target_os = "linux") {
            "linux"
        } else {
            "unset target_os name"
        }
    };

    let clitype = {
        let clitype = global::CFG.get().unwrap().read().unwrap().com.ClientType;
        match clitype {
            EClientType::StkOpt => "StkOpt",
            EClientType::StkOptTrd => "StkOptTrd",
            EClientType::Stk => "Stk",
            EClientType::StkTrd => "StkTrd",
            EClientType::StkCredit => "StkCredit",
            EClientType::StkCreditTrd => "StkCreditTrd",
            EClientType::Fut => "Fut",
            EClientType::FutTrd => "FutTrd",
            _ => "",
        }
    };

    let app_type = app.get_app_type();
    if EAppType::StkOptTrd == app_type
        || EAppType::StkTrd == app_type
        || EAppType::StkCreditTrd == app_type
        || EAppType::FutTrd == app_type
    {
        app_about.set_name(slint::format!("EaseTrader({}, {})", clitype, target_os));
    } else {
        app_about.set_name(slint::format!("EaseRisk({}, {})", clitype, target_os));
    }

    app_about.set_version(version::CRATE_VERSION.to_owned().into());
    app_about.set_copyright(version::CRATE_COPYRIGHT.to_owned().into());
    app_about.set_show_slint_about(false);

    // 根据不同的公司选择不同的icon
    // let cfg = common::global::CFG.get().unwrap().read().unwrap();
}

/// 运行主界面
pub fn run(app: &crate::slintui::App) {
    let clitype = { common::global::CFG.get().unwrap().read().unwrap().com.ClientType };

    let mut iapp: Option<Box<dyn IApp>> = None;

    #[cfg(feature = "risk")]
    {
        let app_com = app.global::<AppCom>();
        app_com.set_is_risk_app(true);

        if common::tistruct::EClientType::StkOpt == clitype {
            app.set_app_type(EAppType::StkOpt);
        }

        if common::tistruct::EClientType::Stk == clitype {
            app.set_app_type(EAppType::Stk);

            let risk_com = app.global::<StkRsk_Com>();
            risk_com.set_iscredit(false);
        }

        if common::tistruct::EClientType::StkCredit == clitype {
            app.set_app_type(EAppType::StkCredit);

            let risk_com = app.global::<StkRsk_Com>();
            risk_com.set_iscredit(true);

            iapp = Some(Box::new(ui::riskstk::credit::riskstk::RiskStkApp::new()));
        }

        if common::tistruct::EClientType::Fut == clitype {
            app.set_app_type(EAppType::Fut);
        }
    }

    #[cfg(feature = "trade")]
    {
        if common::tistruct::EClientType::StkOptTrd == clitype {
            app.set_app_type(EAppType::StkOptTrd);

            let app_com = app.global::<OptTrd_Com>();
            app_com.set_ismarket(1 == common::global::CFG.get().unwrap().read().unwrap().com.SvrTypeDtl);

            iapp = Some(Box::new(ui::tradeopt::tradeopt::TradeOptApp::new()));
        }

        if common::tistruct::EClientType::StkTrd == clitype {
            app.set_app_type(EAppType::StkTrd);
            iapp = Some(Box::new(ui::tradestk::tradestk::TradeStkApp::new()));
        }

        if common::tistruct::EClientType::StkCreditTrd == clitype {
            app.set_app_type(EAppType::StkCreditTrd);

            let app_com = app.global::<StkTrd_Com>();
            app_com.set_iscredit(true); // 1 == common::global::CFG.get().unwrap().read().unwrap().com.SvrTypeDtl);

            iapp = Some(Box::new(ui::tradestk::tradestk::TradeStkApp::new()));
        }

        if common::tistruct::EClientType::FutTrd == clitype {
            app.set_app_type(EAppType::FutTrd);
            iapp = Some(Box::new(ui::tradefut::tradefut::TradeFutApp::new()));
        }
    }

    // 处理锁定与退出
    {
        let app_com = app.global::<AppCom>();

        let wapp = app.as_weak().unwrap();
        app_com.on_unlocked(move |passwd| {
            let ret = global::unlock(&passwd);

            #[cfg(feature = "risk")]
            if ret {
                wapp.global::<AppCom>().invoke_notify_rskpage(ERskNotifyType::UnLock);
            }

            ret
        });

        let wapp = app.as_weak().unwrap();
        app_com.on_close(move |state| {
            let _ = wapp.hide();
        });

        let wapp = app.as_weak().unwrap();
        app.window().on_close_requested(move || {
            let app_com = wapp.global::<AppCom>();

            #[cfg(feature = "risk")]
            app_com.invoke_notify_rskpage(ERskNotifyType::Close);

            if 100 != app_com.get_app_close().state {
                app_com.set_app_close(AppResult {
                    state: 101,
                    msg: "是否退出?".into(),
                    ..Default::default()
                });
            }

            slint::CloseRequestResponse::KeepWindowShown
        });
    }

    // 处理 control/⌘ + c
    {
        let app_com = app.global::<AppCom>();

        app_com.on_copy_str_to_clipboard(|data| {
            let mut ctx: clipboard::ClipboardContext = clipboard::ClipboardProvider::new().unwrap();
            let _ = clipboard::ClipboardProvider::set_contents(&mut ctx, data.as_str().to_owned());
        });
        app_com.on_copy_arr_to_clipboard(|arr| {
            let data = arr
                .iter()
                .map(|d| {
                    if d.contains(',') {
                        std::format!("\"{}\"", d)
                    } else {
                        d.to_string()
                    }
                })
                .collect::<Vec<_>>()
                .join(",");

            let mut ctx: clipboard::ClipboardContext = clipboard::ClipboardProvider::new().unwrap();
            let _ = clipboard::ClipboardProvider::set_contents(&mut ctx, data.as_str().to_owned());
        });
    }

    // 处理主题
    {
        let app_com = app.global::<AppCom>();
        app_com.invoke_set_theme_style(common::global::CFG.get().unwrap().read().unwrap().com.Theme.Style);
        app_com.on_theme_style_changed(|style| {
            let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
            cfg.com.Theme.Style = style;

            let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::THEME_PATH);
            common::global::serialize_write_user_data(&path, &cfg.com.Theme, false);
        });
    }

    // 处理关于对话框
    set_about(&app);

    if let Some(mut iapp) = iapp {
        iapp.setup(&app);
        let _ = common::global::IAPP.set(std::sync::Arc::new(std::sync::RwLock::new(iapp)));
    }
}

// 运行现货(信用)风控
#[cfg(feature = "risk")]
pub fn run_rsk_stk_credit(app: &crate::slintui::App, rsk_stk_app: &crate::slintui::RiskStkCreditDetailPage) {
    // 关闭时保存窗口大小
    {
        let wapp = rsk_stk_app.as_weak().unwrap();
        rsk_stk_app.window().on_close_requested(move || {
            wapp.window().set_size(wapp.window().size());
            slint::CloseRequestResponse::HideWindow
        });
    }

    // 注册主界面选择的资金账户有改变的事件
    {
        // release 模式下, 第一次显示时窗口最大化
        let is_first_show_with_max =
            std::sync::Arc::new(std::sync::Mutex::new(if cfg!(not(debug_assertions)) { true } else { false }));
        let is_first_show_with_max_clone = is_first_show_with_max.clone();

        let rsk_stk_wapp = rsk_stk_app.as_weak();
        let risk_com = app.global::<StkRskCredit_Com>();
        risk_com.on_trd_accountid_changed(move |accid| {
            let is_first_show_with_max_clone = is_first_show_with_max_clone.clone();
            let _ = rsk_stk_wapp.upgrade_in_event_loop(move |app| {
                if app.window().is_visible() {
                    let _ = app.hide();
                    app.window().set_size(app.window().size());
                }
                let _ = app.show();

                #[cfg(not(debug_assertions))]
                {
                    if *is_first_show_with_max_clone.lock().unwrap() {
                        *is_first_show_with_max_clone.lock().unwrap() = false;

                        let app_weak_inner = app.as_weak();
                        global::TOKIO_RT.get().unwrap().spawn(async move {
                            tokio::time::sleep(std::time::Duration::from_millis(1)).await;
                            let _ = app_weak_inner.upgrade_in_event_loop(|app| {
                                if !app.window().is_maximized() {
                                    app.window().set_maximized(true);
                                }
                            });
                        });
                    }
                }

                app.invoke_trd_accountid_changed(accid);
            });
        });
    }

    // 注册通知事件
    {
        let rsk_stk_wapp = rsk_stk_app.as_weak();
        let app_com = app.global::<AppCom>();
        app_com.on_notify_rskpage(move |ntype| {
            let _ = rsk_stk_wapp.upgrade_in_event_loop(move |app| match ntype {
                ERskNotifyType::Close | ERskNotifyType::Lock => {
                    let is_visible = app.window().is_visible();
                    {
                        let mut cfg = global::CFG.get().unwrap().write().unwrap();
                        cfg.rsk_notify.rskpage_is_visible = is_visible;
                    }

                    if is_visible {
                        let _ = app.hide();
                    }
                }
                ERskNotifyType::CloseCancle | ERskNotifyType::UnLock => {
                    let pre_is_visible = { global::CFG.get().unwrap().read().unwrap().rsk_notify.rskpage_is_visible };
                    if pre_is_visible {
                        let _ = app.show();
                    }
                }
                _ => {}
            });
        });
    }

    // 注册主界面的资金账户有改变的事件
    {
        let rsk_stk_wapp = rsk_stk_app.as_weak();
        let risk_com = app.global::<StkRskCredit_Com>();
        risk_com.on_accid_model_changed(move |accid_model| {
            use slint::Model;
            let accid_vec: Vec<String> = accid_model.iter().map(|f: ListViewItem| f.text.as_str().to_owned()).collect();
            let _ = rsk_stk_wapp.upgrade_in_event_loop(move |app| {
                let svritems: std::rc::Rc<slint::VecModel<ListViewItem>> = std::rc::Rc::new(slint::VecModel::default());

                accid_vec.iter().for_each(|sif| {
                    svritems.push(ListViewItem {
                        text: sif.clone().into(),
                        ..Default::default()
                    });
                });

                let risk_com = app.global::<StkRskCredit_Com>();
                risk_com.set_accid_model(slint::ModelRc::from(svritems));
            });
        });
    }

    let mut iapp = Box::new(ui::riskstk::credit::riskstkdetail::RiskStkDetailPage::new());
    iapp.setup_rsk_stk_credit(&rsk_stk_app);
    let _ = common::global::IRSKPAGE.set(std::sync::Arc::new(std::sync::RwLock::new(iapp)));
}
