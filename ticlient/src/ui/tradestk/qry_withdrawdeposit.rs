use std::rc::Rc;

use crate::{
    common::{config::Config, global::TRADE_STK_BUF, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::account_trade_ctp::CtpReqQryWithdrawDepositField;

/// 查询 - 出入金
pub(super) struct QueryWithdrawDeposit {}

impl QueryWithdrawDeposit {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let qry = app.global::<StkTrd_QryWithdrawDeposit>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryWithdrawDeposit::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryWithdrawDeposit::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryWithdrawDeposit::on_page_size_changed(&app_weak, size));

        log::trace!("Query wd init completed");
    }
}

impl QueryWithdrawDeposit {
    /// 查询事件
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qry_wd = app.global::<StkTrd_QryWithdrawDeposit>();

        let req = CtpReqQryWithdrawDepositField {
            ExchID: TIConvert::exch_id(&app_qry_wd.get_exchid()),
            AccountID: "".into(),
            TransTime0: app_qry_wd.get_starttime_int(),
            TransTime1: app_qry_wd.get_endtime_int(),
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qry_wd.get_page_index()
            }
        };
        let page_size = app_qry_wd.get_page_size();

        let ret = {
            TRADE_STK_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_wd(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qry_wd.set_page_index_model(page_index_data.into());
            app_qry_wd.set_item_total(ret.0);
            app_qry_wd.set_page_index(0);
            app_qry_wd.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|wd| {
            let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

            // 处理时间
            items.push(TIConvert::transtime(wd.DealTime).into());

            // 入金金额
            items.push(slint::format!("{:.*}", Config::AMT_DIGITS, wd.Deposit));

            // 出金金额
            items.push(slint::format!("{:.*}", Config::AMT_DIGITS, wd.Withdraw));

            // 交易所
            items.push(slint::format!("{}", TIConvert::exch_name(wd.ExchID)));

            // 资金账号
            items.push(wd.AccountID.clone().into());

            row_data.push(items.into());
        });

        app_qry_wd.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryWithdrawDeposit::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryWithdrawDeposit::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryWithdrawDeposit::on_query(app_weak, true);
    }
}
