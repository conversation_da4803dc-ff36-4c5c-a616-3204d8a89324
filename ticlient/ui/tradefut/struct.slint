import { ListViewItem } from "@zstk/lib.slint";
import { LineItem } from "../widgets/lineeditex.slint";
import { AppResult, TradeColor, MonTradeself } from "../struct.slint";

// 公共
export global FutTrd_Com {
    in_out property <string> accountid; // 资金账户
}

// 选择的Tab的索引
export global FutTrd_TabSelIdx {
    in_out property <int> selidx;
    in_out property <int> in_subselidx;
    in_out property <int> rtn_subselidx;
    in_out property <int> mon_subselidx;

    in_out property <int> input_selidx;
}

// 资金信息
export global FutTrd_Account {
    in_out property <[[string]]> row_data;

    in_out property <int> sort_asc_column_index: 0;
    in_out property <int> sort_dec_column_index: -1;

    in_out property <string> sel_accid;
    in_out property <bool> show_fund_trans;

    callback sort_ascending(int);
    callback sort_descending(int);
    pure callback get_row_data_color(int, int, string) -> brush;
}

// 录入提示
export global FutTrd_InputTip {
    in property <AppResult> app_ret;
}

// 撤单提示
export global FutTrd_CancelTip {
    in property <AppResult> app_ret;
}

// 报单录入
export global FutTrd_InputOrder {
    // 资金账号
    in_out property <string> accountid;

    // 是否买入
    in_out property <bool> is_buy: true;

    // 开平标志. 0:开仓; 1:平仓; 2:平今
    in_out property <int> offset: 0;

    // 投保标志. 0:投机; 1:套保; 2:套利
    in_out property <int> hedge: 0;

    // 交易所编码
    in_out property <string> exchid;

    // 合约编码
    in_out property <string> insid;

    // 合约编码是否错误
    in_out property <bool> insid_has_error: true;

    // 报单价格条件
    in_out property <int> price_type: 0;
    in_out property <string> price_type_str: "普通限价";

    // 报单价格条件下拉列表. 0:默认; 1:中金期货; 2:中金期权; ... 期货交易所做时再实现
    in_out property <int> price_type_model: 0;

    // 报单价格
    in_out property <string> price;

    // 报单价格是否错误
    in_out property <bool> price_has_error: true;

    // 报单数量
    in_out property <string> volume: "1";

    // 报单数量是否错误
    in_out property <bool> volume_has_error: false;

    // 最小成交数量
    in_out property <string> min_volume;

    // 最小成交数量是否错误
    in_out property <bool> min_volume_has_error: false;

    // 合约列表
    in_out property <[LineItem]> insid_model;
    in_out property <[LineItem]> all_insid_model;

    // 初始的报单价格条件
    out property <[ListViewItem]> init_price_type_model: [
        { text: "普通限价"}
    ];

    // 中金所期货报单价格条件
    out property <[ListViewItem]> cffex_fut_price_type_model: [
        { text: "普通限价"},
        { text: "限价 即时全部成交或撤销"},
        { text: "限价 即时成交剩余撤销"},
        { text: "限价 即时成交(按最小成交量)剩余撤销"},
        { text: "最优一挡 即时成交剩余撤销"},
        { text: "最优一挡 即时成交剩余转限价"},
        { text: "最优五挡 即时成交剩余撤销"},
        { text: "最优五挡 即时成交剩余转限价"},
    ];

    // 中金所期权报单价格条件
    out property <[ListViewItem]> cffex_opt_price_type_model: [
        { text: "普通限价"},
        { text: "限价 即时全部成交或撤销"},
        { text: "限价 即时成交剩余撤销"},
        { text: "限价 即时成交(按最小成交量)剩余撤销"},
    ];

    // 市场有变化时的回调
    callback exchid_changed();

    // 合约编码有变动时的回调
    callback insid_text_changed() -> bool;

    // 报单价格有变动时的回调
    callback price_text_changed() -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 最小成交量有变动时的回调
    callback min_volume_text_changed() -> bool;

    // 买卖方向有变动时的回调
    callback dir_changed(/* is_buy */ bool);

    // 价格上下调整时的回调
    callback price_updown_changed(/** upflag */ bool);

    // 数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // 价格的小数位数
    in property <int> price_dig: 4;

    // 价格加减的步长
    in property <float> price_step: 0.0001;

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 设置投机套保标志
    public function set_hedge_by_str(hedge_str: string) {
        if "投机" == hedge_str {
            hedge = 0;
        }
        else if "套保" == hedge_str {
            hedge = 1;
        }
        else if "套利" == hedge_str {
            hedge = 2;
        }
    }

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        is_buy ? TradeColor.dir_buy : TradeColor.dir_sell
    }
}

// 行权录入
export global FutTrd_InputExercise {
    // 资金账号
    in_out property <string> accountid;

    // 投保标志. 0:投机; 1:套保; 2:套利
    in_out property <int> hedge: 0;

    // 行权类型. 0:普通行权; 1:组合行权
    in_out property <int> ex_type: 0;

    // 交易所编码
    in_out property <string> exchid;

    // 看涨期权合约(普通行权时使用本字段)
    in_out property <string> call_insid;

    // 看涨期权合约编码是否错误
    in_out property <bool> call_insid_has_error: true;

    // 看跌期权合约
    in_out property <string> put_insid;

    // 看跌期权合约编码是否错误
    in_out property <bool> put_insid_has_error: true;

    // 行权数量
    in_out property <string> volume: "1";

    // 行权数量是否错误
    in_out property <bool> volume_has_error: false;

    // 期权合约列表
    in_out property <[LineItem]> all_insid_model;

    // 看涨期权合约列表
    in_out property <[LineItem]> call_insid_model;
    in_out property <[LineItem]> all_call_insid_model;

    // 看跌期权合约列表
    in_out property <[LineItem]> put_insid_model;
    in_out property <[LineItem]> all_put_insid_model;

    // 行权类型
    out property <[ListViewItem]> ex_type_model: [
        { text: "普通行权"},
        { text: "合并行权"},
    ];

    // 看涨期权合约有变动时的回调
    callback call_insid_txt_changed() -> bool;

    // 看跌期权合约有变动时的回调
    callback put_insid_txt_changed() -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 设置投机套保标志
    public function set_hedge_by_str(hedge_str: string) {
        if "投机" == hedge_str {
            hedge = 0;
        }
        else if "套保" == hedge_str {
            hedge = 1;
        }
        else if "套利" == hedge_str {
            hedge = 2;
        }
    }

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        TradeColor.dir_buy
    }
}

// 普通撤单结构
struct CancelOrderField {
    exchid: string,
    accountid: string,
    ordersysid: string,
    bs: string,
}

// 行权撤单结构
struct CancelExerciseField {
    exchid: string,
    accountid: string,
    ordersysid: string,
}

// 撤单
export global FutTrd_InOrder {
    // 在途订单
    in property <[[string]]> row_data;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_order_clieked(/* ord */ CancelOrderField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_order_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤买/卖单
    // buy_sell: 0:buy; other:sell
    callback cancel_order_bs_clieked(/* buy_sell */ int,  /* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelOrderField> cancel_order;
    public function get_cancel_order(row_index: int) -> CancelOrderField {
        if row_index >= row_data.length {
            cancel_order.exchid = "";
            cancel_order.accountid = "";
            cancel_order.ordersysid = "";
            return cancel_order;
        }

        cancel_order.exchid = row_data[row_index][1];
        cancel_order.accountid = row_data[row_index][0];
        cancel_order.ordersysid = row_data[row_index][10];
        cancel_order.bs = row_data[row_index][5];
        cancel_order
    }

    // 撤选中单的撤单信息
    in_out property <CancelOrderField> sel_cancel_order;
}

// 撤行权
export global FutTrd_InExercise {
    // 在途行权
    in property <[[string]]> row_data;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_exec_clieked(/* exec */ CancelExerciseField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_exec_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelExerciseField> cancel_exec;
    public function get_cancel_exec(row_index: int) -> CancelExerciseField {
        if row_index >= row_data.length {
            cancel_exec.exchid = "";
            cancel_exec.accountid = "";
            cancel_exec.ordersysid = "";
            return cancel_exec;
        }

        cancel_exec.exchid = row_data[row_index][1];
        cancel_exec.accountid = row_data[row_index][0];
        cancel_exec.ordersysid = row_data[row_index][7];
        cancel_exec
    }

    // 撤选中单的撤单信息
    in_out property <CancelExerciseField> sel_cancel_exec;
}

// 设置
export global FutTrd_Setting {
    // 当前选中的项
    in_out property <{ parent: int, item: int }> sel_item: { parent: 0, item: 0 };

    // 标题
    in_out property <string> set_txt;

    // 交易请求确认 - 报单
    in_out property <bool> trdreqtip_inputorder: true;
    callback trdreqtip_inputorder_changed();

    // 交易请求确认 - 撤单
    in_out property <bool> trdreqtip_ordercancel: true;
    callback trdreqtip_ordercancel_changed();

    // 交易请求确认 - 行权
    in_out property <bool> trdreqtip_inputexec: true;
    callback trdreqtip_inputexec_changed();

    // 交易请求确认 - 撤行权
    in_out property <bool> trdreqtip_execcancel: true;
    callback trdreqtip_execcancel_changed();

    // 异常监控 - 自成交
    in_out property <MonTradeself> mon_tradeself;
    in_out property <string> mon_tradeself_err_tips;
    in_out property <int> mon_tradeself_err_status;
    callback mon_tradeself_changed();
    callback reset_mon_tradeself();
}
