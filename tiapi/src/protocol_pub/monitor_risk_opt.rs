/// 报撤单消息
#[derive(Default)]
pub struct OrderInsertCancelField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 报单笔数
    pub InsertNum: i32,

    /// 撤单笔数
    pub CancelNum: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 限仓消息
#[derive(Default)]
pub struct LimitPositionField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 权利仓持仓最大数量
    pub MaxLongVolume: i32,

    /// 总持仓最大数量
    pub MaxTotalVolume: i32,

    /// 当日买入开仓最大数量
    pub MaxTodayBuyVolume: i32,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 当前权利仓持仓数量
    pub CurLongVolume: i32,

    /// 当前持仓数量
    pub CurTotalVolume: i32,

    /// 当日买入开仓数量
    pub CurTodayBuyVolume: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 成交持仓比
#[derive(Default)]
pub struct TradePositionField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 成交量
    pub TradeVolume: i32,

    /// 持仓量
    pub Position: i32,

    /// 成交量/持仓量
    pub Ratio: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,

    /// 是否异常
    pub IsAbnormal: bool,
}

/// 自成交
#[derive(Default)]
pub struct TradeSelfField {
    /// 程序编码
    pub AID: i32,

    /// 统计类型. 1:按合约2:按标的(此时InstrumentID填标的品种编码)
    pub TSType: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 自成交笔数
    pub TradeNum: i32,

    /// 自成交数量
    pub TradeVol: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
