use serde::{Deserialize, Serialize};

/// 请求查询交易编码
#[derive(Default, Serialize)]
pub struct CtpReqQryTradingCodeField {
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 交易编码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ClientID: String,

    /// 交易编码类型
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub ClientIDType: i32,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestUnitID: String,
}

/// 交易编码消息
#[derive(Default, Debug, Deserialize, Clone, PartialEq)]
#[serde[default]]
pub struct CtpTradingCodeField {
    pub InvestorID: String,   // 投资者代码
    pub BrokerID: String,     // 经纪公司代码
    pub ExchangeID: String,   // 交易所代码
    pub ClientID: String,     // 交易编码
    pub IsActive: i32,        //是否活跃
    pub ClientIDType: i32,    // 交易编码类型
    pub BranchID: String,     // 营业部代码
    pub BizType: i32,         // 业务类型
    pub InvestUnitID: String, // 投资者单元代码
}

/// 请求查询资金账户
#[derive(Default, Serialize)]
pub struct CtpReqQryTradingAccountField {
    // CThostFtdcQryTradingAccountField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 币种代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CurrencyID: String,

    /// 业务类型
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BizType: i32,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,
}

/// 请求查询资金账户(分交易所)
/// 字段与 CtpReqQryTradingAccountField 相同
#[derive(Default, Serialize)]
pub struct CtpReqQryTradingAccountExField {
    // CThostFtdcQryTradingAccountField
    /// 经纪公司代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub BrokerID: String,

    /// 投资者代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InvestorID: String,

    /// 币种代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub CurrencyID: String,

    /// 业务类型
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BizType: i32,

    /// 投资单元代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,
}

/// 资金账户消息
#[derive(Default, Debug, Deserialize, Clone, PartialEq)]
#[serde[default]]
pub struct CtpAccountField {
    // CThostFtdcTradingAccountField
    pub BrokerID: String,                    // 经纪公司代码
    pub AccountID: String,                   // 投资者帐号
    pub PreMortgage: f64,                    // 上次质押金额
    pub PreCredit: f64,                      // 上次信用额度
    pub PreDeposit: f64,                     // 上次存款额
    pub PreBalance: f64,                     // 上次结算准备金
    pub PreMargin: f64,                      // 上次占用的保证金
    pub InterestBase: f64,                   // 利息基数
    pub Interest: f64,                       // 利息收入
    pub Deposit: f64,                        // 入金金额
    pub Withdraw: f64,                       // 出金金额
    pub FrozenMargin: f64,                   // 冻结的保证金
    pub FrozenCash: f64,                     // 冻结的资金
    pub FrozenCommission: f64,               // 冻结的手续费
    pub CurrMargin: f64,                     // 当前保证金总额
    pub CashIn: f64,                         // 资金差额
    pub Commission: f64,                     // 手续费
    pub CloseProfit: f64,                    // 平仓盈亏
    pub PositionProfit: f64,                 // 持仓盈亏
    pub Balance: f64,                        // 期货结算准备金
    pub Available: f64,                      // 可用资金
    pub WithdrawQuota: f64,                  // 可取资金
    pub Reserve: f64,                        // 基本准备金
    pub TradingDay: String,                  // 交易日
    pub SettlementID: i32,                   // 结算编号
    pub Credit: f64,                         // 信用额度
    pub Mortgage: f64,                       // 质押金额
    pub ExchangeMargin: f64,                 // 交易所保证金
    pub DeliveryMargin: f64,                 // 投资者交割保证金
    pub ExchangeDeliveryMargin: f64,         // 交易所交割保证金
    pub ReserveBalance: f64,                 // 保底期货结算准备金
    pub CurrencyID: String,                  // 币种代码
    pub PreFundMortgageIn: f64,              // 上次货币质入金额
    pub PreFundMortgageOut: f64,             // 上次货币质出金额
    pub FundMortgageIn: f64,                 // 货币质入金额
    pub FundMortgageOut: f64,                // 货币质出金额
    pub FundMortgageAvailable: f64,          // 货币质押余额
    pub MortgageableFund: f64,               // 可质押货币金额
    pub SpecProductMargin: f64,              // 特殊产品占用保证金
    pub SpecProductFrozenMargin: f64,        // 特殊产品冻结保证金
    pub SpecProductCommission: f64,          // 特殊产品手续费
    pub SpecProductFrozenCommission: f64,    // 特殊产品冻结手续费
    pub SpecProductPositionProfit: f64,      // 特殊产品持仓盈亏
    pub SpecProductCloseProfit: f64,         // 特殊产品平仓盈亏
    pub SpecProductPositionProfitByAlg: f64, // 根据持仓盈亏算法计算的特殊产品持仓盈亏
    pub SpecProductExchangeMargin: f64,      // 特殊产品交易所保证金
    pub BizType: i32,                        // 业务类型
    pub FrozenSwap: f64,                     // 延时换汇冻结金额
    pub RemainSwap: f64,                     // 剩余换汇额度

    // 现货信息交易相关字段
    pub XYAllDebt: f64,     // 总负债
    pub XYRzDebt: f64,      // 融资负债
    pub XYRqDebt: f64,      // 融券负债
    pub XYBuyCdMargin: f64, // 担保品买入时的金额可折算进入保证金的金额
    pub XYBuyValue: f64,    // Sum(可充抵保证金的证券市值 * 折算率)
    pub XYBuy: f64,         // Sum((融资买入证券市值 - 融资买入金额) * 折算率)
    pub XYSell: f64,        // Sum((融券卖出金额 - 融券卖出证券市值) * 折算率)
    pub XYSellAmt: f64,     // Sum(融券卖出金额)
    pub XYBuyMargin: f64,   // Sum(融资买入证券金额 * 融资保证金比例)
    pub XYSellMargin: f64,  // Sum(融券卖出证券市值 * 融券保证金比例)
    pub XYInterest: f64,    // Sum(利息)
    pub XYCommi: f64,       // Sum(费用-已还合约费用)
    pub XYAllValue: f64,    // 信用证券账户内证券市值总和 --- 用于计算维持担保比例
    pub XYValue: f64,       // 融资买入金额 + 融券卖出证券数量 * 最新价 + 利息及费用  --- 用于计算维持担保比例
    pub XYAvlMargin: f64,   // 保证金可用余额
    pub XYMRatio: f64,      // 维持担保比例
    pub XYPremium: f64,     // 资金收支
    pub XYFroPrem: f64,     // 冻结的保证金
    pub XYFroValPrem: f64,  // 冻结的报单资金
    pub XYFroCommi: f64,    // 冻结的手续费
    pub XYReDirAmt: f64,    // 直接还款金额

    /**********下面字段为协议扩展字段**********/
    pub ValueBalance: f64, // 市值权益

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 查询时是否是最后一条
    pub lt: i32,

    /// 标识消息唯一性
    pub Key: String,
}

/// 出入金消息
#[derive(Default, Deserialize, Clone, Debug)]
#[serde[default]]
pub struct CtpWithdrawDepositField {
    pub FrontNo: i32,      // 交易端前置
    pub SessionNo: i32,    // 交易端SessionID
    pub BizSeqNum: i32,    // 引起本条信息变化的业务序号
    pub MsgSeqNum: i32,    // 消息序号
    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub ClientID: String,  // 交易编码
    pub Deposit: f64,      // 入金
    pub Withdraw: f64,     // 出金
    pub DealTime: i32,     // 处理时间
}

/// 出入金请求查询消息
#[derive(Default)]
pub struct CtpReqQryWithdrawDepositField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 请求资金划转
#[derive(Default, Serialize)]
pub struct CtpReqFundTransferField {
    /// 投资者帐号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,

    /// 密码(主席密码)
    #[serde(skip_serializing_if = "String::is_empty")]
    pub Passwd: String,

    /// 转出系统 <br>**0:主席系统; 1:TITD(SSE)系统; 2:TITD(SZSE)系统; 9:TITD(BSE)系统**
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub OutSys: i32,

    /// 转入系统 <br>**0:主席系统; 1:TITD(SSE)系统; 2:TITD(SZSE)系统; 9:TITD(BSE)**
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub InSys: i32,

    /// 金额 <br>*值必须大于等于0.005*
    #[serde(skip_serializing_if = "comfun::is_zero_f64")]
    pub Amount: f64,
}

/// titdadmin返回的划转结果
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct pbtitdwebaccountupdrsp {
    pub msgtype: i32,
    pub svrid: i32,
    pub islast: i32,
    pub requestno: i32,
    pub responsecode: i32,
    pub responsestr: String,
    pub rspseqno: i32,
    pub rsptotnum: i32,
    pub sourceclusterid: String,
    pub sourceexchangeid: i32,
    pub destclusterid: String,
    pub destexchangeid: i32,
    pub accountid: String,
    pub amount: f64,
    pub status: i32, // 0未操作 1正确 -1错误
    pub transferno: String,
}

/// 请求资金划转结果
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct CtpRspFundTransferField {
    pub AccountID: String,
    pub OutSys: i32,
    pub InSys: i32,
    pub Amount: f64,
    pub Rst: String,
    pub Arr: Vec<pbtitdwebaccountupdrsp>,
}

// 请求查询资金划转
#[derive(Default, Serialize)]
pub struct CtpReqQryFundTransferField {
    /// 资金账号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,

    /// 密码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub Passwd: String,

    /// 划转序号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub TransNo: String,

    /// 交易日
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub TradeDate: i32,

    /// 划转状态. 100:全部; -1:失败; 0:未操作; 1:成功;
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub TransStatus: i32,

    /// 起始项序号(从1开始)
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub BeginNo: i32,

    /// 结束项序号
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub EndNo: i32,
}

/// titdadmin返回的划转结果
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct CtpRspQryFundTransferField {
    // pbtitdwebaccountupdqryrsp
    pub msgtype: i32,
    pub svrid: i32,
    pub islast: i32,
    pub requestno: i32,
    pub responsecode: i32,
    pub responsestr: String,
    pub rspseqno: i32,
    pub rsptotnum: i32,
    pub destsvrid: String,  // 目标svrid
    pub transferno: String, // 划转序号
    pub tradeday: i32,      // 交易日
    pub status: i32,        // 0未操作 1正确 -1错误
    pub accountid: String,  // 资金账号
    pub amount: f64,        // 金额
    pub logtime: String,    // 日志时间
    pub loguserid: String,  // 资金划转操作人员
    pub logmessage: String, // 日志
    pub reversal: i32,      // 1冲正
}

/// 请求查询资金划转响应
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct CtpRspQryFundTransfer {
    pub Rst: String,
}

/// 请求资金继续划转
#[derive(Default, Debug, Serialize)]
pub struct CtpReqReFundTransferField {
    /// 投资者帐号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub AccountID: String,

    /// 密码(主席密码)
    #[serde(skip_serializing_if = "String::is_empty")]
    pub Passwd: String,

    /// 划转序号
    #[serde(skip_serializing_if = "String::is_empty")]
    pub TransNo: String,

    /// 交易日
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub TradeDate: i32,
}

/// 请求资金继续划转响应
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct CtpRspReFundTransferField {
    pub Rst: String,
}
