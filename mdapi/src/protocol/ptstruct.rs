use num_enum::{IntoPrimitive, TryFromPrimitive};
use serde::Deserialize;

/// 行情消息类型
#[derive(IntoPrimitive, TryFromPrimitive)]
#[repr(i32)]
pub enum EMessageType {
    TIMT_WS_Com_Min = 60000,

    /// 系统级消息(如心跳)
    TIMT_WS_Com_Rtn_App,

    /// 请求登录
    TIMT_WS_Com_Req_Login,

    /// 请求登录响应
    TIMT_WS_Com_Rsp_Login,

    /// 请求登出
    TIMT_WS_Com_Req_Logout,

    /// 请求登出响应
    TIMT_WS_Com_Rsp_Logout,

    TIMT_WS_Com_Max,

    TIMT_WS_Md_Req_Min = 65000,

    /// 请求订阅行情
    TIMT_WS_Md_Req_Subscribe,

    /// 请求订阅行情响应
    TIMT_WS_Md_Rsp_Subscribe,

    /// 请求退订行情
    TIMT_WS_Md_Req_UnSubscribe,

    /// 请求退订行情响应
    TIMT_WS_Md_Rsp_UnSubscribe,

    TIMT_WS_Md_Req_Max,

    TIMT_WS_Md_Rtn_Min = 65100,

    /// L1行情推送
    TIMT_WS_Md_Rtn_L1,

    TIMT_WS_Md_Rtn_Max,
}

// 将响应推送所有可能的字段头放入一个头中
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub(crate) struct PTMsgBase {
    /// 消息类型(message type)
    pub mt: i32,

    // PTRspBase
    /// Is Last
    pub lt: i32,

    /// Total count
    pub tc: i32,

    /// Request ID
    pub reqid: i32,

    /// Error Code
    pub ec: i32,

    /// Error Message
    pub em: String,
}