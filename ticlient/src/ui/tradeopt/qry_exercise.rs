use std::rc::Rc;

use crate::{
    common::{global::TRADE_OPT_BUF, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::exercise_trade_ctp::{CtpExecOrderField, CtpReqQryExecField};

/// 查询 - 行权
pub(super) struct QueryExercise {}

impl QueryExercise {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_qryexec = app.global::<OptTrd_QryExercise>();

        let app_weak = app.as_weak();
        app_qryexec.on_qry_clieked(move || QueryExercise::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        app_qryexec.on_page_index_changed(move |index| QueryExercise::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        app_qryexec.on_page_size_changed(move |size| QueryExercise::on_page_size_changed(&app_weak, size));

        let app_weak = app.as_weak();
        app_qryexec.on_export_clicked(move |etype| QueryExercise::on_export_clicked(&app_weak, etype));

        log::trace!("Query exercise init completed");
    }
}

impl QueryExercise {
    /// 获取一行数据
    fn get_row_items(eo: &CtpExecOrderField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 资金账号
        items.push(eo.InvestorID.clone().into());

        // 交易所
        items.push(eo.ExchangeID.clone().into());

        // 交易编码
        items.push(eo.ClientID.clone().into());

        // 发生时间
        if !eo.InsertTime.is_empty() {
            items.push(eo.InsertTime.clone().into());
        } else if !eo.CancelTime.is_empty() {
            items.push(eo.CancelTime.clone().into());
        } else {
            items.push("".into());
        }

        // 合约编码
        items.push(eo.InstrumentID.clone().into());

        // 数量
        items.push(slint::format!("{}", eo.Volume));

        // 执行结果
        items.push(eo.StatusMsg.clone().into());

        // 执行编码
        items.push(eo.ExecOrderSysID.clone().into());

        // 本地执行编码
        items.push(eo.ExecOrderLocalID.clone().into());

        // 用户代码
        items.push(eo.UserID.clone().into());

        items
    }

    // 查询
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qryexec = app.global::<OptTrd_QryExercise>();

        // 清空上一次结果
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qryexec.set_row_data(row_data.into());

        let st = app_qryexec.get_starttime();
        let et = app_qryexec.get_endtime();
        let ordstatus = app_qryexec.get_ordstatus();
        let req = CtpReqQryExecField {
            ExchID: app_qryexec.get_exchid().into(),
            AccountID: "".into(),
            InstrumentID: app_qryexec.get_insid().into(),
            OrdStatus: TIConvert::order_status_id(&ordstatus),
            OrderSysID: app_qryexec.get_ordsysid().into(),
            TransTime0: std::format!("{:02}:{:02}:{:02}", st.hour, st.minute, st.second),
            TransTime0_Int: st.hour * 10000 + st.minute * 100 + st.second,
            TransTime1: std::format!("{:02}:{:02}:{:02}", et.hour, et.minute, et.second),
            TransTime1_Int: et.hour * 10000 + et.minute * 100 + et.second,
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qryexec.get_page_index()
            }
        };
        let page_size = app_qryexec.get_page_size();

        let ret = {
            TRADE_OPT_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_exercise(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qryexec.set_page_index_model(page_index_data.into());
            app_qryexec.set_item_total(ret.0);
            app_qryexec.set_page_index(0);
            app_qryexec.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|ord| {
            let items = QueryExercise::get_row_items(&ord);
            row_data.push(items.into());
        });

        app_qryexec.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryExercise::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryExercise::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryExercise::on_query(app_weak, true);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("exercise_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出行权失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "交易编码",
            "发生时间",
            "合约编码",
            "数量",
            "执行结果",
            "执行宣告编号",
            "本地执行宣告编号",
            "用户代码",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出行权失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<OptTrd_QryExercise>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let ret = { TRADE_OPT_BUF.get().unwrap().read().unwrap().qry_exercise_all() };
            ret.iter().for_each(|ord| {
                let items = QueryExercise::get_row_items(&ord);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出行权失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出行权成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}
