import { <PERSON><PERSON><PERSON>, TableView, ListViewItem, Label, ComboBox } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";
import { FutTrd_TabSelIdx, FutTrd_InputOrder } from "struct.slint";

export global FutTrd_Position {

    in property <[[string]]> row_data;
    in property <[ListViewItem]> insid_model;

    in_out property <string> exchid;
    in_out property <string> insid;
    in_out property <string> posdir;
    in_out property <int> posdir_idx;

    callback insid_text_changed();
    callback filter_changed();
    callback export_clicked(/* type */ int);
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <int> sort_asc_column_index: -1;
    in_out property <int> sort_dec_column_index: -1;
    callback sort_ascending(int);
    callback sort_descending(int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

export component PositionPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> FutTrd_Position.exchid;
                model: CommonModel.exch_fut_model;

                selected => {
                    FutTrd_Position.filter_changed();
                }
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }

            i_cbx_insid := ComboBox {
                width: 150px;
                max_popup_height: root.height - 50px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> FutTrd_Position.insid;
                model <=> FutTrd_Position.insid_model;

                edited(by_selected) => {
                    FutTrd_Position.insid_text_changed();

                    if !by_selected {
                        FutTrd_Position.filter_changed();
                    }
                }

                selected => {
                    FutTrd_Position.filter_changed();
                }
            }

            Label {
                text: "持仓方向";
                vertical_alignment: center;
            }

            i_cbx_poddir := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> FutTrd_Position.posdir;
                model: [
                    { text: "" },
                    { text: "多头" },
                    { text: "空头" },
                ];

                selected => {
                    FutTrd_Position.posdir_idx = self.current_index;
                    FutTrd_Position.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    FutTrd_Position.export_clicked(type);
                }
            }
        }

        TableView {
            sortable: true;
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 3,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.fut_insid },
                    { title: @tr("持仓方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("投保标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.hedge },
                    { title: @tr("持仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("实收保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("计算保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("在途冻结 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "报单平仓未成交的冻结 + 行权冻结" },
                    { title: @tr("可平仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "持仓量 - 在途冻结" },
                    { title: @tr("今日持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "开仓量 - 平仓量" },
                    { title: @tr("上日持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日结算持仓量, 盘中该值不会变动" },
                    { title: @tr("开仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("平仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("开仓金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("平仓金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("平仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("持仓均价"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    { title: @tr("持仓日期 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_90, tips: "除 SHFE/INE 外的交易所, 上日结算后的持仓均作为当日持仓" },
                ]
            };
            row_count: FutTrd_Position.row_data.length;

            sort_canceled => {
                FutTrd_Position.sort_asc_column_index = -1;
                FutTrd_Position.sort_dec_column_index = -1;
            }

            sort_ascending(index) => {
                FutTrd_Position.sort_ascending(index);
                FutTrd_Position.sort_asc_column_index = index;
                FutTrd_Position.sort_dec_column_index = -1;
                true
            }

            sort_descending(index) => {
                FutTrd_Position.sort_descending(index);
                FutTrd_Position.sort_asc_column_index = -1;
                FutTrd_Position.sort_dec_column_index = index;
                true
            }

            get_cell_data(row_index, column_index) => {
                FutTrd_Position.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                FutTrd_Position.get_row_data_color(row_index, column_index, data)
            }

            cell_double_clicked(row_index, column_index) => {
                FutTrd_TabSelIdx.input_selidx = 0;

                if FutTrd_Position.row_data.length > row_index {
                    FutTrd_InputOrder.is_buy = false;
                    FutTrd_InputOrder.offset = 1;
                    FutTrd-InputOrder.set_hedge_by_str(FutTrd_Position.row_data[row_index][4]);
                    FutTrd_InputOrder.price = "";
                    FutTrd_InputOrder.price_has_error = true;
                    FutTrd_InputOrder.volume = FutTrd_Position.row_data[row_index][9];

                    if FutTrd_Position.row_data[row_index][0] != FutTrd_InputOrder.exchid {
                        FutTrd_InputOrder.exchid = FutTrd_Position.row_data[row_index][0];
                        FutTrd_InputOrder.exchid_changed();
                    }
                    if FutTrd_Position.row_data[row_index][2] != FutTrd_InputOrder.insid {
                        FutTrd_InputOrder.insid = FutTrd_Position.row_data[row_index][2];
                        FutTrd_InputOrder.insid_text_changed();
                    }
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(FutTrd_Position.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(FutTrd_Position.row_data[row_index]);
                }
            }
        }
    }
}
