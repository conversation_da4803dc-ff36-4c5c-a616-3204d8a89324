#![allow(dead_code)]
#![allow(unused_variables)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]

pub mod error;
pub mod mdapi;
pub mod protocol;
pub mod serverinfo;
pub use self::error::Result;

mod mdapiimpl;
mod ws;
use mdapi::{IMdSpi, MdApi};

/// 创建行情API
pub fn create_mdapi<T: IMdSpi + 'static>(spi: T) -> Result<Box<dyn MdApi>> {
    Ok(Box::new(mdapiimpl::MdApiImpl::new(spi)))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn mdstruct() {
        let mut si = serverinfo::MDServerInfo::default();
        assert_eq!("ws://:0", si.get_url());

        si.host = "127.0.0.1".to_owned();
        si.port = 1234;
        assert_eq!("ws://127.0.0.1:1234", si.get_url());

        si.uri = "/md".to_owned();
        assert_eq!("ws://127.0.0.1:1234/md", si.get_url());

        si.encon = 1;
        assert_eq!("wss://127.0.0.1:1234/md", si.get_url());
    }
}
