import { TradeColor } from "../struct.slint";

// 行情档位信息
export struct MdLevelInfo {
    price: string,
    volume: string,
    falg: int
}

// 行情详情
export global MarketDataDtl {

    in_out property <int> mdtype;       // 行情类型 0:现货; 1:期货期权,个股期权
    in-out property <string> pre_key;   // 上一次合约的key
    in-out property <string> cur_key;   // 当前标识合约的key

    in_out property <string> exchid: 0 == mdtype ? "SSE" : "";      // 交易所编码
    in_out property <string> insid: "----";                         // 合约编码
    in_out property <string> insname: 0 == mdtype ? "----" : "";    // 合约名称

    out property <string> pre_stl_price: "--";      // 昨结(收)
    in_out property <string> upper_price: "--";     // 涨停价
    in_out property <string> lower_price: "--";     // 跌停价
    in_out property <string> open_price: "--";      // 今开
    in_out property <int> open_price_flag;          // 今开相对于昨结(收)的涨跌标志

    in_out property <string> last_price: "--";      // 最新价
    in_out property <string> last_price_ud: "--";   // 最新价涨跌
    in_out property <string> last_price_rate: "--"; // 最新价涨跌幅
    in_out property <string> update_time: "";       // 更新时间
    in_out property <int> last_price_falg;          // 最新价相对于昨结(收)的涨跌标志

    in_out property <string> highest_price: "--";   // 最高价
    in_out property <int> highest_price_flag;       // 最高价相对于昨结(收)的涨跌标志

    in_out property <string> lowest_price: "--";    // 最低价
    in_out property <int> lowest_price_flag;        // 最低价相对于昨结(收)的涨跌标志

    in_out property <[MdLevelInfo]> bid: [{price:"--", volume: "0"}, {price:"--", volume: "0"}, {price:"--", volume: "0"}, {price:"--", volume: "0"}, {price:"--", volume: "0"}];   // 买5档
    in_out property <[MdLevelInfo]> ask: [{price:"--", volume: "0"}, {price:"--", volume: "0"}, {price:"--", volume: "0"}, {price:"--", volume: "0"}, {price:"--", volume: "0"}];   // 卖5档

    // 点击价格时的回调
    // 参数 type 仅参考. 1:买(点击最新价,买5档价格,最低价,跌停价时传递); 2:卖(点击卖5档价格,最高价,涨停价时传递)
    callback price_clicked(/* exchid */ string, /* insid */ string, /* price */ string, /* type */ int);

    // 获取价格颜色
    // up_down_flag: 涨跌标志(0: 等于; 1:上涨; 其他值:下跌)
    pure public function get_color(up_down_flag: int) -> color {
        1 == up_down_flag ? TradeColor.price_up : (0 != up_down_flag ? TradeColor.price_down : TradeColor.price_eq)
    }

    // 设置昨结(收)
    public function set_prestlprice(y_price: string) {
        pre_stl_price = y_price;
    }

    // 选中合约是否有改变
    public function is_sel_ins_changed() -> bool {
        pre_key != cur_key
    }

    // 置选中合约前后相同
    public function set_prekey_with_curkey() {
        pre_key = cur_key;
    }

    // 重置行情信息
    public function reset() {
        pre_key = "";
        cur_key = "";

        exchid = 0 == mdtype ? "SSE" : "";
        insid = "";
        insname = 0 == mdtype ? "----" : "";

        pre_stl_price = "--";
        upper_price = "--";
        lower_price = "--";
        open_price = "--";
        open_price_flag = 0;
        last_price = "--";
        last_price_ud = "0.00";
        last_price_rate = "0.00";
        last_price_falg = 0;
        highest_price = "--";
        highest_price_flag = 0;
        lowest_price = "--";
        lowest_price_flag = 0;
        update_time = "";
        bid = [{price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, ];
        ask = [{price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, ];
    }

    // 重置行情信息
    public function reset_by_sel_ins(eid: string, iid: string, iname: string, up: string, lp: string, psp: string) {
        pre_key = "";
        cur_key = @tr("{}{}", eid, iid);

        exchid = eid;
        insid = iid;
        insname = iname;

        pre_stl_price = psp;
        upper_price = up;
        lower_price = lp;
        open_price = "--";
        open_price_flag = 0;
        last_price = "--";
        last_price_ud = "0.00";
        last_price_rate = "0.00";
        last_price_falg = 0;
        highest_price = "--";
        highest_price_flag = 0;
        lowest_price = "--";
        lowest_price_flag = 0;
        update_time = "";
        bid = [{price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, ];
        ask = [{price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, {price: "--", volume: "0" }, ];
    }
}