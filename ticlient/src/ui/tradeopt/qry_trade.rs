use std::rc::Rc;

use crate::{
    common::{config::Config, global::DB, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::trade_trade_ctp::{CtpReqQryTradeField, CtpTradeField};

/// 查询 - 成交
pub(super) struct QueryTrade {}

impl QueryTrade {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let qry = app.global::<OptTrd_QryTrade>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryTrade::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryTrade::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryTrade::on_page_size_changed(&app_weak, size));

        let app_weak = app.as_weak();
        qry.on_export_clicked(move |etype| QueryTrade::on_export_clicked(&app_weak, etype));

        log::trace!("Query trade init completed");
    }
}

impl QueryTrade {
    /// 获取一行数据
    fn get_row_items(trd: &CtpTradeField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        // 资金账号
        items.push(trd.InvestorID.clone().into());

        // 交易所
        items.push(trd.ExchangeID.clone().into());

        // 交易编码
        items.push(trd.ClientID.clone().into());

        // 发生时间
        items.push(trd.TradeTime.clone().into());

        // 合约编码
        items.push(trd.InstrumentID.clone().into());

        // 买卖方向
        items.push(TIConvert::ctp_side(trd.Direction).into());

        // 开平标志
        items.push(TIConvert::ctp_offsetflag(trd.OffsetFlag).into());

        // 成交数量
        items.push(slint::format!("{}", trd.Volume));

        // 成交价格
        items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, trd.Price));

        // 成交编号
        items.push(trd.TradeID.clone().into());

        // 交易所报单编号
        items.push(trd.OrderSysID.clone().into());

        // 本地报单编号
        items.push(trd.OrderLocalID.clone().into());

        // 备兑标志
        items.push(TIConvert::ctp_hedgeflag2(trd.HedgeFlag).into());

        items
    }

    /// 查询事件
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qrytrd = app.global::<OptTrd_QryTrade>();

        // 清空上一次结果
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qrytrd.set_row_data(row_data.into());

        let st = app_qrytrd.get_starttime();
        let et = app_qrytrd.get_endtime();
        let req = CtpReqQryTradeField {
            ExchID: app_qrytrd.get_exchid().into(),
            AccountID: "".into(),
            InstrumentID: std::format!("%{}%", app_qrytrd.get_insid()),
            TradeID: std::format!("%{}%", app_qrytrd.get_tradeid()),
            OrderSysID: std::format!("%{}%", app_qrytrd.get_ordersysid()),
            TransTime0: std::format!("{:02}:{:02}:{:02}", st.hour, st.minute, st.second),
            TransTime0_Int: st.hour * 10000 + st.minute * 100 + st.second,
            TransTime1: std::format!("{:02}:{:02}:{:02}", et.hour, et.minute, et.second),
            TransTime1_Int: et.hour * 10000 + et.minute * 100 + et.second,
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qrytrd.get_page_index()
            }
        };
        let page_size = app_qrytrd.get_page_size();

        let ret = { DB.get().unwrap().lock().unwrap().qry_trd_trade(&req, page_index, page_size) };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qrytrd.set_page_index_model(page_index_data.into());
            app_qrytrd.set_item_total(ret.0);
            app_qrytrd.set_page_index(0);
            app_qrytrd.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|trd| {
            let items = QueryTrade::get_row_items(&trd);
            row_data.push(items.into());
        });

        app_qrytrd.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryTrade::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryTrade::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryTrade::on_query(app_weak, true);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("trade_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出成交失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "交易编码",
            "发生时间",
            "合约编码",
            "买卖方向",
            "开平标志",
            "成交数量",
            "成交价格",
            "成交编码",
            "交易所报单编号",
            "本地报单编号",
            "备兑标志",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出成交失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<OptTrd_QryTrade>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let mut page_index = 0;
            let page_size = 2000;
            let req = CtpReqQryTradeField {
                TransTime1: "23:59:59".into(),
                TransTime1_Int: 235959,
                ..Default::default()
            };

            loop {
                let ret = { DB.get().unwrap().lock().unwrap().qry_trd_trade(&req, page_index, page_size) };

                ret.1.iter().for_each(|trd| {
                    let items = QueryTrade::get_row_items(&trd);
                    let _ = writer.write_record(items.iter());
                });

                if (ret.1.len() as i32) < page_size {
                    break;
                }

                page_index += 1;
            }
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出成交失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出成交成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}
