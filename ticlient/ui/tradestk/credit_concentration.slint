import { F<PERSON>s, TableView, Label, ComboBox, LineEdit, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";

export global StkTrd_CreditConcentration {
    in property <[[string]]> row_data;
    pure callback get_row_data_color(int, int, string) -> brush;

    in property <[ListViewItem]> group_name_model;
    in_out property <string> sel_group_name;
    in_out property <int> sel_sub_index;
    in_out property <string> sel_sub_str;
    in_out property <string> sel_exchid;
    in_out property <string> sel_insid;

    callback filter_changed();
    callback export_clicked(/* type */ int);

    public function group_name_model_len() -> int {
        group_name_model.length
    }

    public function get_row_data() -> [[string]] {
        row_data
    }
}

export component CreditConcentrationPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "组名称";
                vertical_alignment: center;
            }

            ComboBox {
                width: 120px;
                placeholder_text: "组名称";
                drop_down_list: false;
                current_text <=> StkTrd_CreditConcentration.sel_group_name;
                model <=> StkTrd_CreditConcentration.group_name_model;

                edited => {
                    StkTrd_CreditConcentration.filter_changed();
                }
            }

            Label {
                text: "类型";
                vertical_alignment: center;
            }

            i_cb_sub := ComboBox {
                width: 100px;
                placeholder_text: "集中度类型";
                current_text <=> StkTrd_CreditConcentration.sel_sub_str;
                model: [
                    { text: "" },
                    { text: "单一证券", tag: 1 },
                    { text: "全组", tag: 2 },
                ];

                selected => {
                    StkTrd_CreditConcentration.sel_sub_index = self.current_value.tag;
                    if StkTrd_CreditConcentration.sel_sub_index == 2 {
                        StkTrd_CreditConcentration.sel_insid = "";
                    }
                    StkTrd_CreditConcentration.filter_changed();
                }
            }

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            ComboBox {
                width: 100px;
                placeholder_text: "交易所编码";
                current_text <=> StkTrd_CreditConcentration.sel_exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkTrd_CreditConcentration.filter_changed();
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }

            LineEdit {
                width: 100px;
                enabled: 2 != StkTrd_CreditConcentration.sel_sub_index;
                text <=> StkTrd_CreditConcentration.sel_insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }

                changed text => {
                    if 2 != StkTrd_CreditConcentration.sel_sub_index {
                        StkTrd_CreditConcentration.filter_changed();
                    }
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    StkTrd_CreditConcentration.export_clicked(type);
                }
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 7,
                columns: [
                    { title: @tr("组编码"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_60 },
                    { title: @tr("组名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("资金账号"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid, tips: "全组集中度时为空" },
                    { title: @tr("股东代码 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid, tips: "全组集中度时为空" },
                    { title: @tr("证券代码 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100, tips: "全组集中度时为空" },
                    { title: @tr("市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "持仓市值 + 在途市值" },
                    { title: @tr("总资产 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "可用资金 + 总持仓市值 + 总在途市值" },
                    { title: @tr("维持担保比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_140 },
                    { title: @tr("集中度上限"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("集中度比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("是否异常"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("证券市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "单一证券或组内证券的持仓市值" },
                    { title: @tr("总证券市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "所有证券的持仓市值" },
                    { title: @tr("未成交市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "单一证券或组内证券的未成交市值" },
                    { title: @tr("总未成交市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "所有证券的未成交市值" },
                ]
            };
            row_count: StkTrd_CreditConcentration.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_CreditConcentration.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkTrd_CreditConcentration.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_CreditConcentration.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_CreditConcentration.row_data[row_index]);
                }
            }
        }
    }
}
