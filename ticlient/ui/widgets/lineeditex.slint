import { Theme, Label, Size, LineEdit, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ont<PERSON> } from "@zstk/lib.slint";

export struct LineItem
{
    text: string,
    remark: string,
}

component LineItemRec inherits Rectangle {
    in property <LineItem> item;
    in property <int> index;
    in property <bool> show_remark: true;

    in property <bool> enabled <=> touch_area.enabled;

    out property <bool> pressed <=> touch_area.pressed;
    out property <bool> has_hover <=> touch_area.has_hover;

    callback clicked <=> touch_area.clicked;
    callback pointer_event <=> touch_area.pointer_event;

    // property <length> item_min_height: show_remark ? 40px : 21px;
    property <length> item_min_height: 21px;
    property <bool> is_show_remark: show_remark && "" != item.remark;

    min_width: i_layout.min_width;
    min_height: max(item_min_height, i_layout.min_height);
    vertical_stretch: 0;
    horizontal_stretch: 1;

    background: transparent;
    border_radius: 4px;
    animate background { duration: 300ms; }

    i_layout := HorizontalLayout {
        padding_left: 6px;
        padding_right: 6px;
        spacing: 4px;
        min_height: item_min_height;

        HorizontalLayout {
            spacing: 10px;

            i_text := Label {
                visible: root.visible;
                text: root.item.text;
                min_height: 21px;
                color: Theme.control_foreground;
                vertical_alignment: center;
                horizontal_alignment: left;

                animate color { duration: 200ms; }
            }

            if is_show_remark : Label {
                visible: root.visible;
                text: root.item.remark;
                min_height: 18px;
                font_size: Fonts.small.size;
                color: Theme.control_foreground;
                vertical_alignment: center;
                horizontal_alignment: left;
                overflow: elide;
            }

            if is_show_remark : Rectangle {
            }
        }
    }

    touch_area := TouchArea {
        mouse_cursor: MouseCursor.pointer;
    }

    states [
        disabled when !root.enabled: {
            root.opacity: 0.6;
        }
        pressed when root.pressed: {
            root.background: Theme.accent_background.with_alpha(0.8);
        }
        hover when root.has_hover: {
            root.background: Theme.accent_background.with_alpha(0.5);
        }
    ]
}

export component LineEditEx {
    in property <[LineItem]> model;             // 数据源

    in property <string> placeholder_text;          // 占位符
    in property <bool> enabled: true;               // 是否可用
    in property <bool> has_border: true;            // 是否有边框
    in property <bool> has_error: false;            // 值是否有错误(仅当has_border为true时有效)
    in property <bool> read_only: false;            // 是否只读
    in property <bool> top_popup: false;            // 是否将下拉菜单显示在上方
    in property <length> max_popup_height: 300px;   // 下拉菜单的最大高度
    in property <bool> show_remark: true;           // 是否显示备注
    in property <bool> show_popup_arrow: true;      // 是还显示下拉菜单的箭头

    in_out property <int> current_index: -1;   // 使用函数 select 可以选择项
    out property <LineItem> current_value: current_index >= 0 && current_index < model.length ? model[current_index] : { };
    out property <bool> has_focus <=> i_line_edit.has_focus;

    in_out property <string> current_text <=> i_line_edit.text;   // 可编辑模式下,值可能不是预选项

    private property <bool> is_popup_open;  // 目前PopupWindow只知道什么时候打开的,但关闭时不知道,某些操作尽量置PopupWindow未打开

    callback accepted;   // only valid on drop_down_list is false
    callback edited(/* by_selected */ bool); // only valid on drop_down_list is false

    public function select(index: int) {
        if index >= 0 && index < root.model.length {
            if i_line_edit.text != root.model[index].text {
                i_line_edit.text = root.model[index].text;
                root.edited(true);
            }
            if root.current_index != index {
                root.current_index = index;
            }
        }
    }

    property <length> scroll_delta: 2px;
    property <length> item_height: 24px;

    forward_focus: i_line_edit;
    min_width: max(Size.large, i_layout.min_width);
    min_height: max(Size.small, i_layout.min_height);
    horizontal_stretch: 1;
    vertical_stretch: 0;

    i_container := Rectangle {
        border_radius: Radius.extra_small;
        background: Theme.control_background;

        i_layout := HorizontalLayout {
            padding_right: 4px;

            i_line_edit := LineEdit {
                enabled: root.enabled;
                read_only: root.read_only;
                has_border: false;
                placeholder_text: root.placeholder_text;

                changed has_focus => {
                    if !self.has_focus {
                        on_popup_close();
                    }
                }

                accepted => {
                    root.accepted();
                    on_popup_close();
                }

                edited => {
                    root.edited(false);

                    if !is_popup_open {
                        on_popup_show();
                    }
                }
            }

            if root.show_popup_arrow : Image {
                y: (parent.height - self.height) / 2;
                image_fit: contain;
                colorize: root.enabled ? (i_ta.has_hover ? Theme.border_focus : Theme.foreground) : Theme.foreground_disabled;
                width: 18px;
                source: Icons.arrow_down;

                i_ta := TouchArea {
                    enabled: root.enabled;
                    mouse_cursor: MouseCursor.pointer;

                    clicked => {
                        on_popup_show();
                        is_popup_open = false;
                    }
                }
            }
        }
    }

    if has_border: Rectangle {
        width: root.width + 2px;
        height: root.height + 2px;
        x: (parent.width - self.width) / 2;
        y: (parent.height - self.height) / 2;
        border_radius: i_container.border_radius + 1px;
        border_width: 1px;
        border_color: !has_error ? (!i_line_edit.has_focus ? Theme.border : Theme.border_focus) : Theme.border_error;
    }

    i_popup := PopupWindow {
        x: 0;
        y: !top_popup ? root.height + 1.5px : 0 - 1.5px - min(root.max_popup_height, 5px + root.model.length * root.item_height);
        width: root.width;
        height: root.max_popup_height ;
        close_policy: PopupClosePolicy.close_on_click_outside;

        init => {
            is_popup_open = true;
        }

        Rectangle {
            background: i_container.background;
            border_radius: i_container.border_radius + 1px;
            border_width: has_border ? 1px : 0.2px;
            border_color: Theme.border;
        }

        sv := ListView {
            for item[index] in root.model: LineItemRec {
                item: item;
                index: index;
                show_remark: root.show_remark;
                height: self.visible ? root.item_height : 0px;

                clicked => {
                    root.select(index);
                    on_popup_close();
                }
            }
        }
    }

    function on_popup_show() {
        if root.model.length > 0 {
            i_popup.show();
            i_line_edit.focus_input();
        }
    }

    function on_popup_close() {
        i_popup.close();
        is_popup_open = false;
    }

    states [
        disabled when !root.enabled: {
            i_container.opacity: 0.6;
        }
    ]
}
