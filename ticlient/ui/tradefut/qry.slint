import { Theme, TabView } from "@zstk/lib.slint";

import { QryOrderPage } from "qry_order.slint";
import { QryTradePage } from "qry_trade.slint";
import { QryExercisePage } from "qry_exercise.slint";

import { FutTrd_TabSelIdx } from "struct.slint";

export component QueryPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: [
                { text: "委托" },
                { text: "成交" },
                { text: "行权" },
            ];

            current_index <=> FutTrd_TabSelIdx.rtn_subselidx;
        }

        if 0 == FutTrd_TabSelIdx.rtn_subselidx: QryOrderPage { }

        if 1 == FutTrd_TabSelIdx.rtn_subselidx: QryTradePage { }

        if 2 == FutTrd_TabSelIdx.rtn_subselidx: QryExercisePage { }
    }
}
