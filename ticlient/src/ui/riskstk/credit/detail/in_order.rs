use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::OrderMap,
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert, titype::TIType},
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::trade_req_ctp::CtpRspInputOrderActionField;

type AccOrdChgMap = dashmap::DashMap<String, AtomicBool>;
type AccOrderMap = dashmap::DashMap<String, OrderMap>;

///合约数据
struct InOrderDatas {
    filter_accid: Arc<Mutex<String>>,

    filter_changed: AtomicBool,

    is_err_in_cancel: AtomicBool,
}
impl InOrderDatas {
    pub fn new() -> Self {
        Self {
            filter_accid: Arc::new(Mutex::new(String::new())),
            filter_changed: AtomicBool::new(false),
            is_err_in_cancel: AtomicBool::new(false),
        }
    }
}
static IN_ORDER_DATAS: OnceLock<InOrderDatas> = OnceLock::new();

/// 在途 - 委托
pub(in crate::ui::riskstk::credit) struct InOrder {
    /// 当前显示的资金账号
    accountid: Arc<Mutex<String>>,

    /// 数据是否有变动
    data_changed_map: Arc<AccOrdChgMap>,

    /// 数据
    data_map: Arc<AccOrderMap>,

    /// 撤单响应数据
    rsp_cancel: Arc<Mutex<CtpRspInputOrderActionField>>,

    /// 在撤单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_in_cancel: AtomicBool,
}

impl InOrder {
    pub fn new() -> Self {
        Self {
            accountid: Arc::new(Mutex::new(String::new())),
            data_changed_map: Arc::new(AccOrdChgMap::new()),
            data_map: Arc::new(AccOrderMap::new()),
            rsp_cancel: Arc::new(Mutex::new(CtpRspInputOrderActionField::default())),
            is_err_in_cancel: AtomicBool::new(false),
        }
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let _ = IN_ORDER_DATAS.set(InOrderDatas::new());

        let app_inord = app.global::<StkRskCredit_InOrder>();

        // 过滤
        let app_weak = app.as_weak();
        app_inord.on_filter_changed(move || InOrder::on_filter_changed(&app_weak));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inord.on_req_tip_toggled(move |checked| InOrder::on_req_tip_toggled(&app_weak, checked));

        // 注册撤单笔事件
        let app_weak = app.as_weak();
        app_inord
            .on_cancel_order_clieked(move |ord, need_confirm| InOrder::on_cancel_order_clieked(&app_weak, ord, need_confirm));

        // 注册撤全部事件
        let app_weak = app.as_weak();
        app_inord.on_cancel_order_all_clieked(move |row_cnt, need_confirm| {
            InOrder::on_cancel_order_all_clieked(&app_weak, row_cnt, need_confirm)
        });

        // 注册撤买/卖事件
        let app_weak = app.as_weak();
        app_inord.on_cancel_order_bs_clieked(move |bs, row_cnt, need_confirm| {
            InOrder::on_cancel_order_bs_clieked(&app_weak, bs, row_cnt, need_confirm)
        });

        log::trace!("InOrder init completed");
    }
}

impl InOrder {
    /// 请求查询委托信息
    pub fn qry_in_order(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_in_order() };
        if ret.is_empty() {
            return;
        }

        ret.iter().for_each(|it| {
            let key = it.key();
            let ord = it.value();

            let mut data_changed = false;
            let aid = ord.AccountID.clone();
            let is_in_order = TIType::ORDER_STATUS_SUCCESS == ord.OrdStatus || TIType::ORDER_STATUS_TRADE == ord.OrdStatus;

            // 方法1
            if is_in_order {
                self.data_map
                    .entry(ord.AccountID.clone())
                    .or_insert_with(OrderMap::new)
                    .insert(key.to_owned(), ord.clone());
                data_changed = true;
            } else {
                if let Some(order_map) = self.data_map.get_mut(&aid) {
                    if order_map.contains_key(key) {
                        order_map.remove(key);
                        data_changed = true;
                    }
                }
            }

            // 方法2
            // if self.data_map.contains_key(&ord.AccountID) {
            //     let mut order_map = self.data_map.get_mut(&ord.AccountID).unwrap();
            //     if order_map.contains_key(key) {
            //         if !is_in_order {
            //             order_map.remove(key);
            //             data_changed = true;
            //         } else {
            //             let mut pre_order = order_map.get_mut(key).unwrap();
            //             pre_order.OrdStatus = ord.OrdStatus;
            //             pre_order.LeavesVolume = ord.LeavesVolume;
            //             pre_order.TransTime = ord.TransTime;
            //             pre_order.LocalOrderNo = ord.LocalOrderNo;
            //             pre_order.UserID = ord.UserID.clone();

            //             data_changed = true;
            //         }
            //     } else {
            //         if is_in_order {
            //             order_map.insert(key.clone(), ord.clone());
            //             data_changed = true;
            //         }
            //     }
            // } else {
            //     if is_in_order {
            //         self.data_map
            //             .entry(ord.AccountID.clone())
            //             .or_insert_with(OrderMap::new)
            //             .insert(key, ord.clone());
            //         data_changed = true;
            //     }
            // }

            if data_changed {
                self.data_changed_map.insert(aid, AtomicBool::new(true));
            }
        });
    }

    /// 撤单操作响应
    pub fn on_rsp_inputorderaction(&self, rsp: CtpRspInputOrderActionField) {
        // *self.rsp_cancel.lock().unwrap() = rsp;
        // IS_ERR_IN_CANCEL.get().unwrap().store(true, Ordering::Relaxed);
        // self.is_err_in_cancel.store(true, Ordering::Relaxed);
    }
}

impl InOrder {
    /// 过滤
    fn on_filter_changed(app_weak: &Weak<RiskStkCreditDetailPage>) {
        let app = app_weak.unwrap();
        let accid = app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned();

        let app_datas = IN_ORDER_DATAS.get().unwrap();
        *app_datas.filter_accid.lock().unwrap() = accid;
        app_datas.filter_changed.store(true, Ordering::Relaxed);
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<RiskStkCreditDetailPage>, checked: bool) {
        // let app = app_weak.unwrap();
        // let app_set = app.global::<StkTrd_Setting>();
        // app_set.set_trdreqtip_ordercancel(checked);
        // CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputOrderAction = checked;
    }

    /// 撤单笔
    fn on_cancel_order_clieked(app_weak: &Weak<RiskStkCreditDetailPage>, ord: CancelOrderField, need_confirm: bool) {}

    /// 撤全部
    fn on_cancel_order_all_clieked(app_weak: &Weak<RiskStkCreditDetailPage>, row_cnt: i32, need_confirm: bool) {}

    /// 撤买/卖单
    fn on_cancel_order_bs_clieked(app_weak: &Weak<RiskStkCreditDetailPage>, buy_sell: i32, row_cnt: i32, need_confirm: bool) {}
}

impl InOrder {
    /// 更新
    pub async fn update(&self, app_weak: Weak<RiskStkCreditDetailPage>) {
        let accid = {
            let app_datas = IN_ORDER_DATAS.get().unwrap();
            if app_datas
                .filter_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                let filter_accid = { app_datas.filter_accid.lock().unwrap().clone() };
                self.data_changed_map.insert(filter_accid.clone(), AtomicBool::new(true));
                *self.accountid.lock().unwrap() = filter_accid;
            }

            self.accountid.lock().unwrap().clone()
        };
        if accid.is_empty() {
            return;
        }

        let data_map_clone = {
            // 1. data_changed_map中没有该账户，直接返回
            let Some(data_changed) = self.data_changed_map.get(&accid) else {
                return;
            };

            // 2. data_changed为false，直接返回
            if !data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                return;
            }

            // 3. data_map中没有该账户，直接返回
            let Some(order_map) = self.data_map.get(&accid) else {
                return;
            };

            // 4. 主流程
            order_map.clone()
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            data_map_clone.iter().for_each(|ord| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 发生时间
                items.push(TIConvert::transtime(ord.TransTime).into());

                // 资金账号
                items.push(ord.AccountID.clone().into());

                // 合约编码
                items.push(ord.InstrumentID.clone().into());

                // 数量
                items.push(slint::format!("{}", ord.Volume));

                // 价格
                items.push(slint::format!("{:.*}", Config::STK_PRICE_DIGITS, ord.Price));

                // 买买方向
                items.push(TIConvert::side(ord.Side).into());

                // 报单状态
                items.push(TIConvert::order_status(ord.OrdStatus).into());

                // 交易所报单编号
                items.push(ord.OrderSysID.clone().into());

                // 本地报单编号
                items.push(slint::format!("{}", ord.LocalOrderNo));

                // 报单价格条件
                items.push(TIConvert::pricetype(ord.PriceType).into());

                // 有效期类型
                items.push(TIConvert::timeinforce(ord.TimeInForce).into());

                // 剩余数量
                items.push(slint::format!("{}", ord.LeavesVolume));

                // 用户代码
                items.push(ord.UserID.clone().into());

                // 交易所
                items.push(TIConvert::exch_name(ord.ExchID).into());

                // 序号(用于排序)
                items.push(slint::format!("{:>13}", ord.MsgSeqNum));

                row_data.push(items.into());
            });

            // 按序号列降序显示
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(14 as usize).unwrap();
                let c_b = r_b.row_data(14 as usize).unwrap();
                c_b.cmp(&c_a)
            }));
            app.global::<StkRskCredit_InOrder>().set_row_data(sort_model.into());
        });
    }
}
