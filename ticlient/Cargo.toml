[package]
name = "ticlient"
version = "0.25.623"
edition = "2021"
description = "TachyonInfo Client"
default-run = "ticlient"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[package.metadata.winres]
FileDescription = "ticlient"
OriginalFilename = "ticlient.exe"
LegalCopyright = "Copyright © TachyonInfo 2025. All rights reserved."

[package.metadata.bundle]
name = "ticlient"
identifier = "TachyonInfo Client"
icon = ["ui/resource/logo.ico"]
copyright = "Copyright © TachyonInfo 2025. All rights reserved."
category = "Utility"
short_description = "TachyonInfo Client"
long_description = "TachyonInfo Client"

[build-dependencies]
zstk = { path = "../../zstk/zstk"}
winres = "=0.1.12"
toml_edit = "=0.22.24"
slint-build = "=1.11.0"

[target.'cfg(target_os = "macos")'.dependencies]
objc = "=0.2.7"
objc-foundation = "=0.1.1"
slint = { version = "=1.11.0", default-features = false, features = [
    "compat-1-2", "std",
    "backend-winit",
    "renderer-skia", "renderer-femtovg", "renderer-software"
]}

[target.'cfg(windows)'.dependencies]
slint = { version = "=1.11.0", default-features = false, features = [
    "compat-1-2", "std",
    "backend-winit",
    "renderer-skia", "renderer-femtovg", "renderer-software"
]}

[target.'cfg(linux)'.dependencies]
slint = "=1.11.0"

[dependencies]
i-slint-backend-winit = "=1.11.0"
log = "=0.4.27"
fern = "=0.7.1"
tokio = { version = "=1.44.2", features = ["rt", "macros"] }
serde = { version = "=1.0.219", features = ["derive"] }
serde_json = "=1.0.140"
num_enum = "=0.7.3"
num-format = "=0.4.4"
image = "=0.25.6"
dashmap = { version = "=6.1.0", features = ["inline"] }
native-dialog = "=0.9.0"
csv = "=1.3.1"
chrono = "=0.4.40"
rusqlite = { version = "=0.34.0", features = ["bundled"] }
clipboard = "=0.5.0"
clap = { version = "=4.5.38", features = ["derive"] }

comfun = { path = "../comfun" }
lan = { path = "../lan" }
collect = { path = "../collect" }
mdapi = { path = "../mdapi" }
tiapi = { path = "../tiapi" }

[features]
trade = []
risk = []
