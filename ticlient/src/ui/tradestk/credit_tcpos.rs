use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{self, global::TRADE_STK_BUF, openselfile, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::position_trade_ctp::CtpCreditTcPosField;

type CtpCreditTcPosMap = dashmap::DashMap<String, CtpCreditTcPosField>;
type StringSet = dashmap::DashSet<String>;

/// 股份头寸数据
struct CreditTcPosDatas {
    /// 数据
    data_map: Arc<CtpCreditTcPosMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 所有的持仓合约列表
    insid_set: Arc<StringSet>,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl CreditTcPosDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpCreditTcPosMap::new()),
            data_changed: AtomicBool::new(false),
            insid_set: Arc::new(StringSet::new()),

            filter_clicked: AtomicBool::new(false),
        }
    }
}
static CREDIT_TC_POS_DATAS: OnceLock<CreditTcPosDatas> = OnceLock::new();

/// 股份头寸
pub(super) struct CreditTcPos {}

/// 构建与初始化
impl CreditTcPos {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = CREDIT_TC_POS_DATAS.set(CreditTcPosDatas::new());

        let app_tc = app.global::<StkTrd_CreditTcPos>();

        let app_weak = app.as_weak();
        app_tc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditTcPos::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_tc.on_filter_changed(move || CreditTcPos::on_filter_changed(&app_weak));

        let app_weak = app.as_weak();
        app_tc.on_export_clicked(move |etype| CreditTcPos::on_export_clicked(&app_weak, etype));

        log::trace!("Credit tc position init completed");
    }
}

/// 查询
impl CreditTcPos {
    /// 查询股份头寸
    pub fn qry_credit_tcpos(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_credit_tcpos() };
        if ret.is_empty() {
            return;
        }

        let tc_datas = CREDIT_TC_POS_DATAS.get().unwrap();

        for (key, pos) in ret {
            if !tc_datas.insid_set.contains(&pos.InsID) {
                tc_datas.insid_set.insert(pos.InsID.clone());
            }

            tc_datas.data_map.insert(key, pos);
        }
        tc_datas.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditTcPos {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 可用数量
        if 6 == column_index {
            let data = data.replace(",", "").parse::<i32>().unwrap_or_default();
            if data < 0 {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        CREDIT_TC_POS_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("credit_tc_pos_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出股份头寸失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "头寸类型",
            "资金账户",
            "股东代码",
            "交易所",
            "证券代码",
            "授权数量",
            "可用数量",
            "占用数量",
            "冻结数量",
            "转入数量",
            "转出数量",
            "证券名称",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出股份头寸失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<StkTrd_CreditTcPos>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let tc_datas = CREDIT_TC_POS_DATAS.get().unwrap();
            let data_map_clone = tc_datas.data_map.clone();
            data_map_clone.iter().for_each(|cl| {
                let items = CreditTcPos::get_row_items(&cl);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出股份头寸失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出股份头寸成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}

/// 更新UI
impl CreditTcPos {
    /// 获取一行数据
    fn get_row_items(tc: &CtpCreditTcPosField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        let mut insname = "".to_owned();
        {
            if let Some(ins) = common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins(tc.ExchID, &tc.InsID)
            {
                insname = ins.name.clone()
            }
        };

        // 头寸类型
        items.push(
            match tc.TcType {
                1 => "普通头寸",
                2 => "专项头寸",
                _ => "未知头寸",
            }
            .into(),
        );

        // 资金账户
        items.push(slint::format!("{}", tc.AccID));

        // 股东代码
        items.push(slint::format!("{}", tc.CliID));

        // 交易所
        items.push(TIConvert::exch_name(tc.ExchID).into());

        // 证券代码
        items.push(slint::format!("{}", tc.InsID));

        // 授权数量
        items.push(TIConvert::format_i(tc.Distrib).into());

        // 可用数量
        items.push(TIConvert::format_i(tc.Distrib - tc.Used - tc.Frozen + tc.In - tc.Out).into());

        // 占用数量
        items.push(TIConvert::format_i(tc.Used).into());

        // 冻结数量
        items.push(TIConvert::format_i(tc.Frozen).into());

        // 转入数量
        items.push(TIConvert::format_i(tc.In).into());

        // 转出数量
        items.push(TIConvert::format_i(tc.Out).into());

        // 证券名称
        items.push(slint::format!("{}", insname));

        items
    }

    // 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<App>) {
        let tc_datas = CREDIT_TC_POS_DATAS.get().unwrap();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_tc = app.global::<StkTrd_CreditTcPos>();

            if tc_datas.insid_set.len() != app_tc.invoke_insid_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                tc_datas.insid_set.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_tc.set_insid_model(id_arr.into());
            }
        });
    }

    // 更新股份头寸
    async fn update_credit_contract(&self, app_weak: &Weak<App>) {
        let tc_datas = CREDIT_TC_POS_DATAS.get().unwrap();

        let data_map_clone = {
            if tc_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                tc_datas.filter_clicked.store(false, Ordering::Relaxed);
                tc_datas.data_map.clone()
            } else {
                if tc_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    tc_datas.data_map.clone()
                } else {
                    Arc::new(CtpCreditTcPosMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_tc = app.global::<StkTrd_CreditTcPos>();

            let filter_tctype = app_tc.get_tctype();
            let filter_exchid = TIConvert::exch_id(app_tc.get_exchid().as_str());
            let filter_insid = app_tc.get_insid().to_string();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|tc| {
                if filter_tctype > 0 {
                    if tc.TcType != filter_tctype {
                        return;
                    }
                }

                if 0 != filter_exchid {
                    if tc.ExchID != filter_exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !tc.InsID.contains(&filter_insid) {
                        return;
                    }
                }

                let items = CreditTcPos::get_row_items(&tc);
                row_data.push(items.into());
            });

            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                // 头寸类型+股东代码+证券代码
                let c_a = slint::format!(
                    "{}{}{}",
                    r_a.row_data(0).unwrap(),
                    r_a.row_data(2).unwrap(),
                    r_a.row_data(4).unwrap()
                );
                let c_b = slint::format!(
                    "{}{}{}",
                    r_b.row_data(0).unwrap(),
                    r_b.row_data(2).unwrap(),
                    r_a.row_data(4).unwrap()
                );
                c_b.cmp(&c_a)
            }));

            app_tc.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新股份头寸
        self.update_credit_contract(&app_weak).await;
    }
}
