#![cfg(target_os = "macos")]
#![allow(unexpected_cfgs)]

use objc::{
    declare::ClassDecl,
    msg_send,
    runtime::{sel_getName, Class, Object, Sel},
    sel, sel_impl,
};
use objc_foundation::{INSString, NSString};

/// 注册处理程序坞菜单点击的自定义类
fn register_dock_custom_class() -> *const Class {
    static mut CLASS: *const Class = std::ptr::null();
    unsafe {
        if CLASS.is_null() {
            let superclass = Class::get("NSObject").unwrap();
            let mut decl = ClassDecl::new("RustDockMenuHandler", superclass).unwrap();

            #[allow(improper_ctypes_definitions)]
            extern "C" fn handle_menu_item(_this: &Object, _sel: Sel) {
                let sel_name = unsafe {
                    let cstr: *const std::os::raw::c_char = sel_getName(_sel);
                    std::ffi::CStr::from_ptr(cstr).to_string_lossy().into_owned()
                };

                match sel_name.as_str() {
                    "new_window_item" => {
                        if let Ok(exe) = std::env::current_exe() {
                            #[cfg(debug_assertions)]
                            {
                                let _ = std::process::Command::new("open")
                                    .args(["-n", exe.to_string_lossy().to_string().as_str()])
                                    .spawn();
                            }

                            #[cfg(not(debug_assertions))]
                            {
                                if let Some(app_bundle) = exe.parent().and_then(|p| {
                                    p.parent().and_then(|p| {
                                        let path = p.parent();
                                        if let Some(path) = path {
                                            if let Some(ext) = path.extension() {
                                                if ext == "app" {
                                                    return Some(path);
                                                }
                                            }
                                        }

                                        None
                                    })
                                }) {
                                    let _ = std::process::Command::new("open")
                                        .args(["-n", app_bundle.to_string_lossy().to_string().as_str()])
                                        .spawn();
                                } else {
                                    log::warn!("handle_menu_item: Failed to get app bundle path [{:?}]", exe);
                                }
                            }
                        } else {
                            log::warn!("handle_menu_item: Failed to get current exe path");
                        }
                    }
                    _ => {
                        log::warn!("handle_menu_item: Unknown menu item: {}", sel_name);
                    }
                }
            }

            decl.add_method(sel!(new_window_item), handle_menu_item as extern "C" fn(&Object, Sel));

            CLASS = decl.register();
        }
        CLASS
    }
}

/// 创建程序坞菜单
fn create_dock_menu() -> *mut Object {
    let menu_class = Class::get("NSMenu").unwrap();
    let menu: *mut Object = unsafe { msg_send![menu_class, new] };

    let handler_class = register_dock_custom_class();
    let new_windows_title = NSString::from_str("新建窗口");
    let new_windows_selector = sel!(new_window_item);

    unsafe {
        let handler_instance: *mut Object = msg_send![handler_class, new];
        let new_window_item: *mut Object = msg_send![Class::get("NSMenuItem").unwrap(), new];
        let _: () = msg_send![new_window_item, setTitle: new_windows_title];
        let _: () = msg_send![new_window_item, setAction: new_windows_selector];
        let _: () = msg_send![new_window_item, setTarget: handler_instance];
        let _: () = msg_send![menu, addItem: new_window_item];
    }

    menu
}

/// 设置程序坞菜单
pub fn set_dock_menu() {
    if let Some(app) = Class::get("NSApplication") {
        let shared_app: *mut Object = unsafe { msg_send![app, sharedApplication] };
        let dock_menu = create_dock_menu();
        unsafe {
            let _: () = msg_send![shared_app, setDockMenu: dock_menu];
        }
    } else {
        log::warn!("set_dock_menu: Get NSApplication class failed");
    }
}
