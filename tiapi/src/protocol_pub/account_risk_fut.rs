/// 账户资金
#[derive(Default)]
pub struct AccountsFutField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 上次结算准备金
    /// 系统上场时读取到的值,在整个交易日中不会改变
    pub PreBalance: f64,

    /// 本系统分配资金
    /// 初始为上次结算准备金,如果系统设置了本系统冻结且冻结资金小于 PreBalance 则为冻结资金
    pub DistribFund: f64,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 权益
    pub Balance: f64,

    /// 市值权益
    pub ValueBalance: f64,

    /// 可用资金
    pub Available: f64,

    /// 可取资金
    pub WithdrawQuota: f64,

    /// 入金金额
    pub Deposit: f64,

    /// 出金金额
    pub Withdraw: f64,

    /// 申报手续费
    pub OrderCommi: f64,

    /// 手续费
    pub Commision: f64,

    /// 冻结的手续费
    pub FrozenCommission: f64,

    /// 平仓盈亏
    pub CloseProfit: f64,

    /// 持仓盈亏(浮动盈亏)
    pub PositionProfit: f64,

    /// 冻结的资金
    pub FrozenPremium: f64,

    /// 资金收支
    pub Premium: f64,

    /// 行权冻结资金
    pub ExFrozen: f64,

    /// 行权冻结费用(买方行权冻结手续费)
    pub ExFrozenCommi: f64,

    /// 冻结保证金
    pub FrozenMargin: f64,

    /// 占用保证金
    pub UseMargin: f64,

    /// 实时风险度
    pub RiskDegree: f64,

    /// 实时市值风险度
    pub ValueRiskDegree: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 出入金
#[derive(Default)]
pub struct WithdrawDepositFutField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 处理时间
    pub DealTime: i32,

    /// 入金金额
    pub Deposit: f64,

    /// 出金金额
    pub Withdraw: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
