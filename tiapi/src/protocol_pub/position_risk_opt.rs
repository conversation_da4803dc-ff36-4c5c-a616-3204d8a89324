use super::order_risk_opt::OmlItem;

/// 持仓量信息
#[derive(Default)]
pub struct PositionInfoField {
    /// 昨持仓量中未用于组合的持仓量
    pub PrePosition: i32,

    /// 昨持仓量中用于组合的持仓量
    pub PreCombPosition: i32,

    /// 实际持仓量 = 昨持仓量(PrePosition+PreCombPosition) + 开仓量 - 平仓量 - 用于组合的持仓量
    pub Position: i32,

    /// 开仓量
    pub OpenVolume: i32,

    /// 平仓量
    pub CloseVolume: i32,

    /// 用于组合的持仓量
    pub CombPosition: i32,

    /// 冻结持仓量(在途平仓报单冻结)
    pub FrozenPosition: i32,

    /// 用于计算保证金的期权价格
    pub OptMPrice: f64,

    /// 用于计算保证金的标的价格
    pub ObjMPrice: f64,

    /// 交易所保证金
    pub ExchMargin: f64,

    /// 占用保证金
    pub UseMargin: f64,

    /// 开仓金额
    pub OpenAmount: f64,

    /// 平仓金额
    pub CloseAmount: f64,

    /// 持仓均价
    pub AvgPrice: f64,

    /// 持仓市值
    pub PosValue: f64,

    /// 持仓市值计算价格
    pub PosValPrice: f64,

    /**********下面字段为非协议内容用于展示***************/
    /// Delta
    pub Delta: f64,

    /// Delta金额
    pub DeltaAmount: f64,

    /// Vega
    pub Vega: f64,

    /// Vega金额
    pub VegaAmount: f64,

    /// Gamma
    pub Gamma: f64,

    /// Gamma金额
    pub GammaAmount: f64,

    /// Theta
    pub Theta: f64,

    /// Theta金额
    pub ThetaAmount: f64,
}
impl PositionInfoField {
    /// 是否有持仓量
    fn has_position(&self) -> bool {
        0 != self.PreCombPosition
            || 0 != self.PrePosition
            || 0 != self.Position
            || 0 != self.OpenVolume
            || 0 != self.CloseVolume
            || 0 != self.CombPosition
            || 0 != self.FrozenPosition
    }
}

/// 持仓信息
#[derive(Default)]
pub struct PositionField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 投机套保标志
    pub HedgeFlag: i32,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 期权最新价
    pub OptLPrice: f64,

    /// 标的最新价
    pub ObjLPrice: f64,

    /// 实虚值
    pub VirValue: f64,

    /// 多头持仓信息
    pub Long: PositionInfoField,

    /// 空头持仓信息
    pub Short: PositionInfoField,

    /// 备兑持仓信息
    pub Covered: PositionInfoField,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,

    /// 备兑标识
    pub CoveredFlag: i32,
}

/// 组合持仓信息
#[derive(Default)]
pub struct PositionCombField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 组合策略编码
    pub CombID: String,

    /// 组合编码
    pub CombInstID: String,

    /// 数量
    pub Volume: i32,

    /// 冻结数量
    pub FrozenVolume: i32,

    /// 成分合约数
    pub NoLeges: i32,

    /// 成份合约
    pub Items: Vec<OmlItem>,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 组合状态(组合|已解除)
    pub ComdStatus: i32,

    /// 交易所保证金
    pub ExchMargin: f64,

    /// 占用保证金
    pub UseMargin: f64,

    /// 解组后的交易所保证金
    pub UnCombExchMargin: f64,

    /// 解组后的占用保证金
    pub UnCombUseMargin: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
