use std::sync::atomic::Ordering;
use std::sync::{atomic::AtomicBool, Arc, Mutex, OnceLock};

use tiapi::protocol_pub::app::{AppTDServerErrorField, AppTDSessionTimeoutField};

use crate::apiproc::abnormalstate::IOnRtnAbnormalState;

use crate::{
    common::{
        self,
        global::{TRADE_FUT_BUF, TRADE_OPT_BUF, TRADE_STK_BUF},
        tistruct::EClientType,
    },
    slintui::*,
};
use slint::*;

/// 异常状态数据
struct AbnormalStateDatas {
    /// 与服务端连接状态是否正常
    is_connected: AtomicBool,

    /// 最新的交易服务端异常消息
    tdservererror: Arc<Mutex<AppTDServerErrorField>>,

    /// 最新的登录到交易服务端的Session超时消息
    tdsessiontimeout: Arc<Mutex<AppTDSessionTimeoutField>>,
}
impl AbnormalStateDatas {
    pub fn new() -> Self {
        Self {
            is_connected: AtomicBool::new(true),
            tdservererror: Arc::new(Mutex::new(AppTDServerErrorField::default())),
            tdsessiontimeout: Arc::new(Mutex::new(AppTDSessionTimeoutField::default())),
        }
    }
}
static ABNORMALSTATE_DATAS: OnceLock<AbnormalStateDatas> = OnceLock::new();

/// 异常状态回调类
struct AbnormalStateImpl {}
impl AbnormalStateImpl {
    pub fn new() -> Self {
        Self {}
    }
}
impl IOnRtnAbnormalState for AbnormalStateImpl {
    /// 断开连接
    fn on_rtn_disconnect(&self) {
        let as_datas = ABNORMALSTATE_DATAS.get().unwrap();
        as_datas.is_connected.store(false, Ordering::Relaxed);
    }

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {
        let as_datas = ABNORMALSTATE_DATAS.get().unwrap();
        *as_datas.tdservererror.lock().unwrap() = tderr;
    }

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {
        let as_datas = ABNORMALSTATE_DATAS.get().unwrap();
        *as_datas.tdsessiontimeout.lock().unwrap() = tdst;
    }
}

/// 异常状态
pub(in crate::ui) struct AbnormalState {}

/// 构建与初始化
impl AbnormalState {
    /// 构建
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = ABNORMALSTATE_DATAS.set(AbnormalStateDatas::new());

        // 注册接收异常
        {
            let clitype = { common::global::CFG.get().unwrap().read().unwrap().com.ClientType };

            match clitype {
                EClientType::StkOpt => todo!(),
                EClientType::StkOptTrd => {
                    let mut trdopt_buf = TRADE_OPT_BUF.get().unwrap().write().unwrap();
                    trdopt_buf.register_rtn_abnormalstate_callback(Box::new(AbnormalStateImpl::new()));
                }
                EClientType::Stk | EClientType::StkCredit => todo!(),
                EClientType::StkTrd | EClientType::StkCreditTrd => {
                    let mut trdstk_buf = TRADE_STK_BUF.get().unwrap().write().unwrap();
                    trdstk_buf.register_rtn_abnormalstate_callback(Box::new(AbnormalStateImpl::new()));
                }
                EClientType::Fut => todo!(),
                EClientType::FutTrd => {
                    let mut trdfut_buf = TRADE_FUT_BUF.get().unwrap().write().unwrap();
                    trdfut_buf.register_rtn_abnormalstate_callback(Box::new(AbnormalStateImpl::new()));
                }
                _ => {}
            }
        }
    }

    /// 与服务端连接状态是否正常
    pub fn is_connected(&self) -> bool {
        let as_datas = ABNORMALSTATE_DATAS.get().unwrap();
        as_datas.is_connected.load(Ordering::Acquire)
    }
}

/// 更新UI
impl AbnormalState {
    /// 更新 - 登录到交易服务端的Session超时
    async fn update_tdsessiontimeout(&self, app_weak: &Weak<App>) {
        let as_datas = ABNORMALSTATE_DATAS.get().unwrap();

        let tdsessiontimeout = {
            let mut tdst = AppTDSessionTimeoutField::default();

            let mut tmp = as_datas.tdsessiontimeout.lock().unwrap();
            if tmp.Time > 0 {
                tdst = tmp.clone();
                tmp.Time = 0;
            }
            tdst
        };
        if tdsessiontimeout.Time <= 0 {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_com = app.global::<AppCom>();
            app_com.set_td_session_timeout(true);

            let app_ss = app.global::<AppStatusStrip>();
            app_ss.set_msg((2, tdsessiontimeout.to_string(false).into()));
        });
    }

    /// 更新 - 交易服务端异常消息
    async fn update_tdservererror(&self, app_weak: &Weak<App>) {
        let as_datas = ABNORMALSTATE_DATAS.get().unwrap();

        let tdservererror = {
            let mut tderr = AppTDServerErrorField::default();

            let mut tmp = as_datas.tdservererror.lock().unwrap();
            if tmp.Time > 0 {
                tderr = tmp.clone();
                tmp.Time = 0;
            }
            tderr
        };
        if tdservererror.Time <= 0 {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_ss = app.global::<AppStatusStrip>();

            let errmsg = std::format!("{}", tdservererror.to_string(false));
            app_ss.set_msg((1, errmsg.into()));
        });
    }

    /// 更新 - 断开连接
    async fn update_disconnect(&self, app_weak: &Weak<App>) {
        let as_datas = ABNORMALSTATE_DATAS.get().unwrap();
        if as_datas.is_connected.load(Ordering::Acquire) {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            AbnormalState::deal_disconnect(&app);
        });
    }

    /// 处理断开连接
    pub fn deal_disconnect(app: &crate::slintui::App) {
        let app_ss = app.global::<AppStatusStrip>();
        let app_com = app.global::<AppCom>();

        let errmsg = std::format!("{} 与服务端断开连接, 请重新登录", chrono::Local::now().format("%H:%M:%S"));

        app_com.set_app_close(AppResult {
            state: 100,
            msg: errmsg.clone().into(),
            ..Default::default()
        });
        app_ss.set_msg((3, errmsg.into()));
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 登录到交易服务端的Session超时
        self.update_tdsessiontimeout(&app_weak).await;

        // 交易服务端异常消息
        //self.update_tdservererror(&app_weak).await;

        // 断开连接
        self.update_disconnect(&app_weak).await;
    }
}
