import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";
import { StkTrd_TabSelIdx, StkTrd_Com } from "struct.slint";

import { QryOrderPage } from "qry_order.slint";
import { QryTradePage } from "qry_trade.slint";
import { QryFjyOrderPage } from "qry_fjy_order.slint";
import { QryFjyTradePage } from "qry_fjy_trade.slint";
import { QryWithdrawDepositPage } from "qry_wd.slint";
import { QryPositionTransPage } from "qry_postrans.slint";
import { QryFundTransDtlPage } from "qry_fundtransdtl.slint";
import { QryPositionTransDtlPage } from "qry_postransdtl.slint";
import { QryInstrumentPage } from "qry_instrument.slint";
import { QryCreditReAmtDtlPage } from "qry_credit_reamt_detail.slint";

export component QueryPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "委托" },
        { text: "成交" },
        { text: "非交易委托" },
        { text: "非交易成交" },

        { text: "出入金" },
        { text: "股份划转" },

        { text: "资金划转记录" },
        { text: "股份划转记录" },

        { text: "证券信息" },
    ];

    private property <[TabBarItem]> tabview_credit_items: [
        { text: "委托" },
        { text: "成交" },

        { text: "融资还款明细" },

        { text: "出入金" },
        { text: "股份划转" },

        { text: "资金划转记录" },
        { text: "股份划转记录" },

        { text: "证券信息" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: StkTrd_Com.iscredit ? tabview_credit_items : tabview_items;

            current_index <=> StkTrd_TabSelIdx.rtn_subselidx;
        }

        if 0 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryOrderPage { }
        if 1 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryTradePage { }
        if 2 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryFjyOrderPage { }
        if 3 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryFjyTradePage { }
        if 4 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryWithdrawDepositPage { }
        if 5 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryPositionTransPage { }
        if 6 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryFundTransDtlPage {}
        if 7 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryPositionTransDtlPage {}
        if 8 == StkTrd_TabSelIdx.rtn_subselidx && !StkTrd_Com.iscredit: QryInstrumentPage {}

        if 0 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryOrderPage { }
        if 1 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryTradePage { }
        if 2 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryCreditReAmtDtlPage {}
        if 3 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryWithdrawDepositPage { }
        if 4 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryPositionTransPage { }
        if 5 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryFundTransDtlPage {}
        if 6 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryPositionTransDtlPage {}
        if 7 == StkTrd_TabSelIdx.rtn_subselidx && StkTrd_Com.iscredit: QryInstrumentPage {}
    }
}