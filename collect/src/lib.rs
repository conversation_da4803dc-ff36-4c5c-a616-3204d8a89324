//! # collect
//!
//! 看穿式信息采集

#![allow(dead_code)]

mod com;

use com::{CollectInfo, IpMac};

#[cfg(target_os = "windows")]
mod win;
#[cfg(target_os = "windows")]
type OS = win::Win;

#[cfg(target_os = "macos")]
mod mac;
#[cfg(target_os = "macos")]
type OS = mac::Mac;

#[cfg(target_os = "linux")]
mod linux;
#[cfg(target_os = "linux")]
type OS = linux::Linux;

/// 获取看穿式信息
pub fn get_info() -> Result<CollectInfo, String> {
    let ret: Result<CollectInfo, String>;

    if cfg!(target_os = "windows") || cfg!(target_os = "macos") || cfg!(target_os = "linux") {
        ret = OS::get_collect_info();
    } else {
        ret = Err("Get Collect info failed. Unsupported operating system".to_owned());
    }

    ret
}

/// 获取默认路由的IpMac
pub fn get_default_ipmac() -> Result<IpMac, String> {
    let ret: Result<IpMac, String>;

    if cfg!(target_os = "windows") || cfg!(target_os = "macos") || cfg!(target_os = "linux") {
        let ipmac_vec = OS::get_ip_mac();
        if ipmac_vec.is_err() {
            return Err(std::format!("get default ip mac failed. {}", ipmac_vec.err().unwrap()));
        }

        let ipmac_vec = ipmac_vec.unwrap();
        ret = Ok(ipmac_vec[0].clone());
        #[cfg(debug_assertions)]
        {
            println!("default_ipmac: {:?}", ret);
        }
    } else {
        ret = Err("Get default ip mac failed. Unsupported operating system".to_owned())
    }

    ret
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let result = get_info();
        assert_eq!(true, result.is_ok());
    }
}
