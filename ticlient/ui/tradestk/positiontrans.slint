import { <PERSON><PERSON>, Label, LineEdit, ComboBox, I<PERSON>s, Theme } from "@zstk/lib.slint";
import { WndRec } from "../widgets/wndrec.slint";
import { PositionTransField, StkTrd_Com, StkTrd_Position, StkTrd_TabSelIdx } from "struct.slint";
import { WidgetColor } from "../struct.slint";

export component PositionTransPage {

    private property <PositionTransField> pos_trans;
    private property <length> label_width: 60px;

    i_wnd := WndRec {
        title: @tr("股份划转");
        can_close: StkTrd_Position.ctrls_enabled;

        btn_close_clicked => {
            StkTrd_Position.show_pos_trans = false;
        }

        VerticalLayout {
            spacing: 10px;
            padding: 10px;

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: label_width;
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "资金账号";
                }

                i_le_aid := LineEdit {
                    width: 150px;
                    read_only: true;
                    text: StkTrd_Position.sel_accid;
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    height: 26px;
                    width: label_width;
                    vertical_alignment: center;
                    horizontal_alignment: right;
                    text: "交易所";
                }

                i_cbx_exchid := ComboBox {
                    enabled: StkTrd_Position.ctrls_enabled;
                    placeholder_text: "请选择";
                    current_text <=> StkTrd_Position.sel_exchid;
                    model: [{ text: "SSE" }, { text: "SZSE" }, { text: "BSE" }];

                    edited => {
                        StkTrd_Position.sel_insid = "";
                        StkTrd_Position.sel_volume = "";
                        i_le_volume.text = "";
                    }
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    height: 26px;
                    width: label_width;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "证券代码";
                }

                i_le_insid := LineEdit {
                    enabled: StkTrd_Position.ctrls_enabled;
                    placeholder_text: "请输入";
                    action_icon: Icons.close;
                    text <=> StkTrd_Position.sel_insid;

                    action => {
                        self.text = "";
                    }

                    edited => {
                        StkTrd_Position.sel_volume = "";
                        i_le_volume.text = "";
                    }
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: label_width;
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "划转方向";
                }

                i_cbx_dir := ComboBox {
                    enabled: StkTrd_Position.ctrls_enabled;
                    placeholder_text: "请选择";
                    current_text: "TITD -> 主席";
                    model: [{ text: "TITD -> 主席" }, { text: "主席 -> TITD" }];

                    edited => {
                        i_le_volume.text = "";
                    }
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: label_width;
                    height: 26px;
                    vertical_alignment: center;
                    horizontal-alignment: right;
                    text: "划转数量";
                }

                i_le_volume := LineEdit {
                    enabled: StkTrd_Position.ctrls_enabled;
                    placeholder_text: "数量必须大于0";
                    action_icon: Icons.close;

                    action => {
                        self.text = "";
                    }
                }
            }

            if i_cbx_dir.current_index <= 0 && "" != StkTrd-Position.sel_volume : HorizontalLayout {
                spacing: 10px;

                Rectangle {
                    width: 60px;
                    height: 18px;
                }

                HorizontalLayout {
                    Rectangle {
                        horizontal_stretch: 1;
                    }

                    Label {
                        text: @tr("TITD可用: {}", StkTrd_Position.sel_volume);
                        font-size: 0.8rem;
                        color: Theme.accent_background;

                        TouchArea {
                            enabled: StkTrd_Position.ctrls_enabled;
                            mouse_cursor: pointer;
                            clicked => {
                                i_le_volume.text = StkTrd_Position.sel_volume;
                            }
                        }
                    }
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    width: label_width;
                    height: 26px;
                    vertical_alignment: center;
                    horizontal_alignment: right;
                    text: "密码";
                }

                i_le_passwd := LineEdit {
                    enabled: StkTrd_Position.ctrls_enabled;
                    placeholder_text: "划转密码";
                    input_type: password;
                    action_icon: Icons.close;
                    text <=> StkTrd_Com.transpwd;

                    action => {
                        self.text = "";
                    }
                }
            }

            HorizontalLayout {
                Rectangle {
                    height: 2px;
                }
            }

            HorizontalLayout {
                spacing: 10px;

                Label {
                    height: 26px;
                    vertical_alignment: center;
                    text: "划转记录";
                    color: Theme.accent_background;

                    TouchArea {
                        mouse_cursor: pointer;
                        clicked => {
                            StkTrd_TabSelIdx.selidx = StkTrd_Com.iscredit ? 4 : 2;
                            StkTrd_TabSelIdx.rtn_subselidx = StkTrd_Com.iscredit ? 6 : 8;
                        }
                    }
                }

                HorizontalLayout {
                    Rectangle {
                        horizontal_stretch: 1;
                    }

                    i_btn_trans := Button {
                        width: 50px;
                        height: 25px;
                        background: WidgetColor.btn_background;
                        enabled: StkTrd_Position.ctrls_enabled;
                        text: "划转";
                        clicked => {
                            StkTrd_Position.ctrls_enabled = false;

                            if 1 == i_cbx_dir.current_index {
                                pos_trans.transflag = 1;
                            } else {
                                pos_trans.transflag = 2;
                            }

                            pos_trans.accountid = i_le_aid.text;
                            pos_trans.password = i_le_passwd.text;
                            pos_trans.exchid = i_cbx_exchid.current_text;
                            pos_trans.insid = i_le_insid.text;
                            pos_trans.volume = i_le_volume.text;

                            StkTrd_Position.req_pos_trans(pos_trans);
                        }
                    }
                }
            }
        }
    }
}
