use serde::Deserialize;

/// 执行宣告消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpExecOrderField {
    // CThostFtdcExecOrderField
    pub BrokerID: String,         // 经纪公司代码
    pub InvestorID: String,       // 投资者代码
    pub InstrumentID: String,     // 合约代码
    pub ExecOrderRef: String,     // 执行宣告引用
    pub UserID: String,           // 用户代码
    pub Volume: i32,              // 数量
    pub RequestID: i32,           // 请求编号
    pub BusinessUnit: String,     // 业务单元
    pub OffsetFlag: i32,          // 开平标志
    pub HedgeFlag: i32,           // 投机套保标志
    pub ActionType: i32,          // 执行类型
    pub PosiDirection: i32,       // 保留头寸申请的持仓方向
    pub ReservePositionFlag: i32, // 期权行权后是否保留期货头寸的标记,该字段已废弃
    pub CloseFlag: i32,           // 期权行权后生成的头寸是否自动平仓
    pub ExecOrderLocalID: String, // 本地执行宣告编号
    pub ExchangeID: String,       // 交易所代码
    pub ParticipantID: String,    // 会员代码
    pub ClientID: String,         // 客户代码
    pub ExchangeInstID: String,   // 合约在交易所的代码
    pub TraderID: String,         // 交易所交易员代码
    pub InstallID: i32,           // 安装编号
    pub OrderSubmitStatus: i32,   // 执行宣告提交状态
    pub NotifySequence: i32,      // 报单提示序号
    pub TradingDay: String,       // 交易日
    pub SettlementID: i32,        // 结算编号
    pub ExecOrderSysID: String,   // 执行宣告编号
    pub InsertDate: String,       // 报单日期
    pub InsertTime: String,       // 插入时间
    pub CancelTime: String,       // 撤销时间
    pub ExecResult: i32,          // 执行结果
    pub ClearingPartID: String,   // 结算会员编号
    pub SequenceNo: i32,          // 序号
    pub FrontID: i32,             // 前置编号
    pub SessionID: i32,           // 会话编号
    pub UserProductInfo: String,  // 用户端产品信息
    pub StatusMsg: String,        // 状态信息
    pub ActiveUserID: String,     // 操作用户代码
    pub BrokerExecOrderSeq: i32,  // 经纪公司报单编号
    pub BranchID: String,         // 营业部编号
    pub InvestUnitID: String,     // 投资单元代码
    pub AccountID: String,        // 资金账号
    pub CurrencyID: String,       // 币种代码
    pub reserve1: String,         // 保留的无效字段
    pub MacAddress: String,       // Mac地址
    pub OwnerType: String,        // 所有者类型
    pub LoginPBU: String,         // 登陆PBU
    pub IPAddress: String,        // IP地址

    // 以下非协议内容
    //#define TI_OrdStatus_Success 1    // 未执行
    //#define TI_OrdStatus_Cancel 4     // 已取消
    //#define TI_OrdStatus_Reject 8     // 已拒绝
    pub LastStatus: i32, // 最后状态
}

/// 行权请求查询消息
#[derive(Default)]
pub struct CtpReqQryExecField {
    /// 交易所代码
    pub ExchID: String,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: String,
    pub TransTime0_Int: i32,

    /// 结束时间
    pub TransTime1: String,
    pub TransTime1_Int: i32,
}

/// 行权响应查询消息
#[derive(Default)]
pub struct CtpRspQryExecField {
    pub array: Vec<CtpExecOrderField>,
}

/// 行权指令合并消息
#[derive(Default, Clone, Deserialize)]
#[serde[default]]
pub struct CtpExecCombineOrderField {
    // CThostFtdcExecCombineOrderField
    pub BrokerID: String,                // 经纪公司代码
    pub InvestorID: String,              // 投资者代码
    pub CallInstrumentID: String,        // 看涨合约代码
    pub PutInstrumentID: String,         // 看跌合约代码
    pub ExecCombineOrderRef: String,     // 执行宣告合并引用
    pub UserID: String,                  // 用户代码
    pub Volume: i32,                     // 数量
    pub RequestID: i32,                  // 请求编号
    pub BusinessUnit: String,            // 业务单元
    pub ActionType: i32,                 // 执行类型
    pub ExecCombineOrderLocalID: String, // 本地执行宣告合并编号
    pub ExchangeID: String,              // 交易所代码
    pub ParticipantID: String,           // 会员代码
    pub ClientID: String,                // 客户代码
    pub UnderlyingInstrID: String,       // 标的合约在交易所的代码
    pub ExchangeCallInstID: String,      // 看涨合约在交易所的代码
    pub ExchangePutInstID: String,       // 看跌合约在交易所的代码
    pub TraderID: String,                // 交易所交易员代码
    pub InstallID: i32,                  // 安装编号
    pub OrderSubmitStatus: i32,          // 执行宣告合并提交状态
    pub NotifySequence: i32,             // 报单提示序号
    pub TradingDay: String,              // 交易日
    pub SettlementID: i32,               // 结算编号
    pub ExecCombineOrderSysID: String,   // 执行宣告合并编号
    pub InsertDate: String,              // 报单日期
    pub InsertTime: String,              // 插入时间
    pub CancelTime: String,              // 撤销时间
    pub ExecResult: i32,                 // 执行结果
    pub ClearingPartID: String,          // 结算会员编号
    pub SequenceNo: i32,                 // 序号
    pub FrontID: i32,                    // 前置编号
    pub SessionID: i32,                  // 会话编号
    pub UserProductInfo: String,         // 用户端产品信息
    pub StatusMsg: String,               // 状态信息
    pub ActiveUserID: String,            // 操作用户代码
    pub BrokerExecCombineOrderSeq: i32,  // 经纪公司报单编号
    pub BranchID: String,                // 营业部编号
    pub InvestUnitID: String,            // 投资单元代码
    pub AccountID: String,               // 资金账号
    pub CurrencyID: String,              // 币种代码
    pub reserve1: String,                // 保留的无效字段
    pub MacAddress: String,              // Mac地址
    pub OwnerType: String,               // 所有者类型
    pub LoginPBU: String,                // 登陆PBU
    pub IPAddress: String,               // IP地址

    // 以下非协议内容
    //#define TI_OrdStatus_Success 1             // 未执行
    //#define TI_OrdStatus_Cancel 4              // 已取消
    //#define TI_OrdStatus_Reject 8              // 已拒绝
    pub LastStatus: i32, // 最后状态
}

/// 行权指令合并请求查询消息
#[derive(Default)]
pub struct CtpReqQryExecCombField {
    /// 交易所代码
    pub ExchID: String,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: String,
    pub TransTime0_Int: i32,

    /// 结束时间
    pub TransTime1: String,
    pub TransTime1_Int: i32,
}