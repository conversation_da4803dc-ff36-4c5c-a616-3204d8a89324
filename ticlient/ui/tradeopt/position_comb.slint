import { <PERSON><PERSON><PERSON>, TableView, Label, ComboBox } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";
import { OptTrd_TabSelIdx, OptTrd_PositionComb, OptTrd_InputOml, OptTrd_Com } from "struct.slint";

export component PositionCombPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <string> sel_strategy;     // 双击选中的组合策略

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            ComboBox {
                width: 80px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_PositionComb.exchid;
                model: CommonModel.exch_opt_model;

                selected => {
                    OptTrd_PositionComb.filter_changed();
                }
            }

            Label {
                text: "组合策略";
                vertical_alignment: center;
            }
            ComboBox {
                width: 80px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_PositionComb.strategyid;
                model: [
                    { text: "" },
                    { text: "CNSJC" },
                    { text: "CXSJC" },
                    { text: "PNSJC" },
                    { text: "PXSJC" },
                    { text: "KKS" },
                    { text: "KS" },
                ];

                selected => {
                    OptTrd_PositionComb.filter_changed();
                }
            }

            Label {
                text: "组合状态";
                vertical_alignment: center;
            }
            ComboBox {
                width: 80px;
                placeholder_text: "请选择";
                current_index <=> OptTrd_PositionComb.state;
                model: [
                    { text: "" },
                    { text: "组合" },
                    { text: "已解除" },
                ];

                init => {
                    self.select(OptTrd_PositionComb.state);
                }

                selected => {
                    OptTrd_PositionComb.filter_changed();
                }
            }

            Label {
                text: "组合编码";
                vertical_alignment: center;
            }
            ComboBox {
                width: 160px;
                max_popup_height: root.height - 50px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> OptTrd_PositionComb.combtradeid;
                model <=> OptTrd_PositionComb.combtradeid_model;

                edited(by_selected) => {
                    OptTrd_PositionComb.combid_text_changed();

                    if !by_selected {
                        OptTrd_PositionComb.filter_changed();
                    }
                }

                selected => {
                    OptTrd_PositionComb.filter_changed();
                }
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }
            ComboBox {
                width: 110px;
                max_popup_height: root.height - 50px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> OptTrd_PositionComb.insid;
                model <=> OptTrd_PositionComb.insid_model;

                edited(by_selected) => {
                    OptTrd_PositionComb.insid_text_changed();

                    if !by_selected {
                        OptTrd_PositionComb.filter_changed();
                    }
                }

                selected => {
                    OptTrd_PositionComb.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    OptTrd_PositionComb.export_clicked(type);
                }
            }
        }

        TableView {
            sortable: true;
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 4,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("组合策略"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("组合编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("组合状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("持仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },

                    { title: @tr("第1腿合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("第1腿合约名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insname },
                    { title: @tr("第1腿方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("第1腿数量"), alignment: TextHorizontalAlignment.center, width: ColWidth.volume },

                    { title: @tr("第2腿合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("第2腿合约名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insname },
                    { title: @tr("第2腿方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    { title: @tr("第2腿数量"), alignment: TextHorizontalAlignment.center, width: ColWidth.volume },

                    { title: @tr("实收保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("交易所保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                ]
            };
            row_count: OptTrd_PositionComb.row_data.length;

            sort_canceled => {
                OptTrd_PositionComb.sort_asc_column_index = -1;
                OptTrd_PositionComb.sort_dec_column_index = -1;
            }

            sort_ascending(index) => {
                OptTrd_PositionComb.sort_ascending(index);
                OptTrd_PositionComb.sort_asc_column_index = index;
                OptTrd_PositionComb.sort_dec_column_index = -1;
                true
            }

            sort_descending(index) => {
                OptTrd_PositionComb.sort_descending(index);
                OptTrd_PositionComb.sort_asc_column_index = -1;
                OptTrd_PositionComb.sort_dec_column_index = index;
                true
            }

            get_cell_data(row_index, column_index) => {
                OptTrd_PositionComb.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                OptTrd_PositionComb.get_row_data_color(row_index, column_index, data)
            }

            cell_double_clicked(row_index, column_index) => {
                OptTrd_TabSelIdx.input_selidx = 1;

                if OptTrd_PositionComb.row_data.length > row_index {
                    OptTrd_Com.accountid = OptTrd_PositionComb.row_data[row_index][0];          // 资金账号
                    OptTrd_InputOml.accountid = OptTrd_Com.accountid;

                    OptTrd_InputOml.is_comb = false;
                    OptTrd_InputOml.volume = OptTrd_PositionComb.row_data[row_index][5];        // 组合数量
                    OptTrd_InputOml.volume_has_error = false;
                    OptTrd_InputOml.combtradeid = OptTrd_PositionComb.row_data[row_index][3];   // 组合编码

                    OptTrd_InputOml.leg1_insid = OptTrd_PositionComb.row_data[row_index][6];    // 第1腿合约编码
                    OptTrd_InputOml.leg1_insname = OptTrd_PositionComb.row_data[row_index][7];  // 第1腿合约名称
                    OptTrd_InputOml.leg1_has_error = false;

                    OptTrd_InputOml.leg2_insid = OptTrd_PositionComb.row_data[row_index][10];   // 第2腿合约编码
                    OptTrd_InputOml.leg2_insname = OptTrd_PositionComb.row_data[row_index][11]; // 第2腿合约名称
                    OptTrd_InputOml.leg2_has_error = false;

                    sel_strategy = OptTrd_PositionComb.row_data[row_index][2];                  // 组合策略

                    if "CNSJC" == sel_strategy {
                        OptTrd_InputOml.strategyid = "CNSJC - 认购牛市价差策略";
                        OptTrd_InputOml.strategyid_idx = 0;
                        OptTrd_InputOml.leg1_dir = "权利仓";
                        OptTrd_InputOml.leg2_dir = "义务仓";
                        OptTrd_InputOml.leg1_dir_i32 = 50;
                        OptTrd_InputOml.leg2_dir_i32 = 51;
                    } else if "CXSJC" == sel_strategy {
                        OptTrd_InputOml.strategyid = "CXSJC - 认购熊市价差策略";
                        OptTrd_InputOml.strategyid_idx = 1;
                        OptTrd_InputOml.leg1_dir = "权利仓";
                        OptTrd_InputOml.leg2_dir = "义务仓";
                        OptTrd_InputOml.leg1_dir_i32 = 50;
                        OptTrd_InputOml.leg2_dir_i32 = 51;
                    } else if "PNSJC" == sel_strategy {
                        OptTrd_InputOml.strategyid = "PNSJC - 认沽牛市价差策略";
                        OptTrd_InputOml.strategyid_idx = 2;
                        OptTrd_InputOml.leg1_dir = "权利仓";
                        OptTrd_InputOml.leg2_dir = "义务仓";
                        OptTrd_InputOml.leg1_dir_i32 = 50;
                        OptTrd_InputOml.leg2_dir_i32 = 51;
                    } else if "PXSJC" == sel_strategy {
                        OptTrd_InputOml.strategyid = "PXSJC - 认沽熊市价差策略";
                        OptTrd_InputOml.strategyid_idx = 3;
                        OptTrd_InputOml.leg1_dir = "权利仓";
                        OptTrd_InputOml.leg2_dir = "义务仓";
                        OptTrd_InputOml.leg1_dir_i32 = 50;
                        OptTrd_InputOml.leg2_dir_i32 = 51;
                    } else if "KS" == sel_strategy {
                        OptTrd_InputOml.strategyid = "KS - 跨式策略";
                        OptTrd_InputOml.strategyid_idx = 4;
                        OptTrd_InputOml.leg1_dir = "义务仓";
                        OptTrd_InputOml.leg2_dir = "义务仓";
                        OptTrd_InputOml.leg1_dir_i32 = 51;
                        OptTrd_InputOml.leg2_dir_i32 = 51;
                    } else if "KKS" == sel_strategy {
                        OptTrd_InputOml.strategyid = "KKS - 宽跨式策略";
                        OptTrd_InputOml.strategyid_idx = 5;
                        OptTrd_InputOml.leg1_dir = "义务仓";
                        OptTrd_InputOml.leg2_dir = "义务仓";
                        OptTrd_InputOml.leg1_dir_i32 = 51;
                        OptTrd_InputOml.leg2_dir_i32 = 51;
                    }

                    OptTrd_InputOml.sel_comb_position();
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_PositionComb.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_PositionComb.row_data[row_index]);
                }
            }
        }
    }
}
