use std::sync::{
    atomic::{AtomicU64, Ordering},
    Arc, Mutex,
};

use tiapi::protocol_pub::{
    account_trade_ctp::{CtpAccountField, CtpReqQryWithdrawDepositField, CtpTrading<PERSON>odeField, CtpWithdrawDepositField},
    app::{AppHeartbeatField, AppTDServerErrorField, AppTDSessionTimeoutField},
    instrument_trade_ctp::{CtpRspQryFJYInstrumentField, CtpRspQryInstrumentField},
    monitor_trade_ctp::{
        CtpConvertBondsMonField, CtpOrderInsertCancelField, CtpReqQrySelfTradeDtlField, CtpTradeSelfDtlField, CtpTradeSelfField,
    },
    order_trade_ctp::{CtpBusinessOrderField, CtpOrderField, CtpReqQryBusinessOrderField},
    position_trade_ctp::{
        CtpCreditConcentrationField, CtpCreditContractField, CtpCreditLimitField, CtpCreditReturnDtlField, CtpCreditTcAmtField,
        CtpCreditTcPosField, CtpPositionField, CtpPositionTransField, CtpReqQryCreditReDtlField, CtpReqQryPositionTransField,
    },
    trade_req_ctp::{
        CtpRspBusinessOrderActionField, CtpRspBusinessOrderField, CtpRspInputOrderActionField, CtpRspInputOrderField,
    },
    trade_trade_ctp::{CtpBusinessTradeField, CtpReqQryBusinessTradeField, CtpTradeField},
};

use crate::common::{self, ticonvert::TIConvert, titype::TIType};

use super::abnormalstate::IOnRtnAbnormalState;

pub type CtpOrderStatusMap = dashmap::DashMap<String, i32>;
pub type CtpOrderMap = dashmap::DashMap<String, CtpOrderField>;
pub type CtpFjyOrderMap = dashmap::DashMap<String, CtpBusinessOrderField>;
pub type CtpFjyTradeMap = dashmap::DashMap<String, CtpBusinessTradeField>;
pub type CtpWDMap = dashmap::DashMap<String, CtpWithdrawDepositField>;
pub type CtpPosTransMap = dashmap::DashMap<String, CtpPositionTransField>;
pub type CtpTradingCodeMap = dashmap::DashMap<String, CtpTradingCodeField>;

pub type CtpOrderInsertCancelMap = dashmap::DashMap<String, CtpOrderInsertCancelField>;
pub type CtpConvertBondsMonMap = dashmap::DashMap<String, CtpConvertBondsMonField>;
pub type CtpTradeSelfMap = dashmap::DashMap<String, CtpTradeSelfField>;
pub type CtpTradeSelfDtlMap = dashmap::DashMap<String, CtpTradeSelfDtlField>;

pub type CtpOrderVec = Vec<CtpOrderField>;
pub type CtpTradeVec = Vec<CtpTradeField>;
pub type CtpFjyOrderVec = Vec<CtpBusinessOrderField>;
pub type CtpFjyTradeVec = Vec<CtpBusinessTradeField>;
pub type CtpWDVec = Vec<CtpWithdrawDepositField>;
pub type CtpPosTransVec = Vec<CtpPositionTransField>;
pub type CtpTradeSelfDtlVec = Vec<CtpTradeSelfDtlField>;
pub type CtpTradingCodeVec = Vec<CtpTradingCodeField>;

pub type CtpCreditLimitMap = dashmap::DashMap<String, CtpCreditLimitField>;
pub type CtpCreditTcAmtMap = dashmap::DashMap<String, CtpCreditTcAmtField>;
pub type CtpCreditTcPosMap = dashmap::DashMap<String, CtpCreditTcPosField>;
pub type CtpCreditContractMap = dashmap::DashMap<String, CtpCreditContractField>;
pub type CtpCreditConcentrationMap = dashmap::DashMap<String, CtpCreditConcentrationField>;
pub type CtpCreditReturnDtlMap = dashmap::DashMap<i32, CtpCreditReturnDtlField>;
pub type CtpCreditReturnDtlVec = Vec<CtpCreditReturnDtlField>;

pub struct TradeStkBuf {
    key_index: AtomicU64,

    ////////////////////////////////////////////////////////////////////////////
    /// 异常状态回调
    rtn_abnormalstate_callback: Option<Box<dyn IOnRtnAbnormalState>>,

    /// 查询资金响应回调
    qry_acc_callback: Option<Arc<dyn Fn(CtpAccountField) + Send + Sync>>,

    /// 查询持仓响应回调
    qry_pos_callback: Option<Arc<dyn Fn(CtpPositionField) + Send + Sync>>,

    /// 报单响应回调
    rsp_inputorder_callback: Option<Arc<dyn Fn(CtpRspInputOrderField) + Send + Sync>>,

    /// 非交易业务报单响应回调
    rsp_inputfjyorder_callback: Option<Arc<dyn Fn(CtpRspBusinessOrderField) + Send + Sync>>,

    /// 撤单响应回调
    rsp_inputorderaction_callback: Option<Arc<dyn Fn(CtpRspInputOrderActionField) + Send + Sync>>,

    /// 非交易业务撤单响应回调
    rsp_inputfjyorderaction_callback: Option<Arc<dyn Fn(CtpRspBusinessOrderActionField) + Send + Sync>>,

    ////////////////////////////////////////////////////////////////////////////
    /// 已经结束的交易所报单
    order_status_map: Arc<CtpOrderStatusMap>,

    /// 报单回报
    order_map: Arc<Mutex<CtpOrderMap>>,

    /// 在途报单回报
    in_order_map: Arc<Mutex<CtpOrderMap>>,

    /// 成交回报
    trade_vec: Arc<Mutex<CtpTradeVec>>,

    /// 非交易业务报单回报
    fjy_order_map: Arc<CtpFjyOrderMap>,

    /// 在途非交易业务报单回报
    in_fjy_order_map: Arc<Mutex<CtpFjyOrderMap>>,

    /// 非交易业务成交回报
    fjy_trade_map: Arc<CtpFjyTradeMap>,

    /// 出入金回报
    wd_map: Arc<CtpWDMap>,

    /// 股份划转回报
    postrans_map: Arc<CtpPosTransMap>,

    /// 交易编码
    tradingcode_map: Arc<CtpTradingCodeMap>,

    ////////////////////////////////////////////////////////////////////////////
    /// 报撤单回报
    orderinsertcancel_map: Arc<Mutex<CtpOrderInsertCancelMap>>,

    /// 可转债监控回报
    convertbonds_map: Arc<Mutex<CtpConvertBondsMonMap>>,

    /// 自成交回报
    selftrade_map: Arc<Mutex<CtpTradeSelfMap>>,

    /// 自成交详情回报
    selftradedtl_map: Arc<CtpTradeSelfDtlMap>,

    ////////////////////////////////////////////////////////////////////////////
    /// 融资授信额度
    credit_limitamt_map: Arc<Mutex<CtpCreditLimitMap>>,

    /// 融券授信额度
    credit_limitpos_map: Arc<Mutex<CtpCreditLimitMap>>,

    /// 资金头寸
    credit_tcamt_map: Arc<Mutex<CtpCreditTcAmtMap>>,

    /// 股份头寸
    credit_tcpos_map: Arc<Mutex<CtpCreditTcPosMap>>,

    /// 信用合约
    credit_contract_map: Arc<Mutex<CtpCreditContractMap>>,

    /// 信用集中度
    credit_concentration_map: Arc<Mutex<CtpCreditConcentrationMap>>,

    /// 信用合约归还融资明细
    credit_retamtdtl_map: Arc<CtpCreditReturnDtlMap>,
}

impl TradeStkBuf {
    pub fn new() -> Self {
        Self {
            key_index: AtomicU64::new(1),

            rtn_abnormalstate_callback: None,
            qry_acc_callback: None,
            qry_pos_callback: None,
            rsp_inputorder_callback: None,
            rsp_inputfjyorder_callback: None,
            rsp_inputorderaction_callback: None,
            rsp_inputfjyorderaction_callback: None,

            order_status_map: Arc::new(CtpOrderStatusMap::new()),
            order_map: Arc::new(Mutex::new(CtpOrderMap::new())),
            in_order_map: Arc::new(Mutex::new(CtpOrderMap::new())),
            trade_vec: Arc::new(Mutex::new(CtpTradeVec::new())),

            fjy_order_map: Arc::new(CtpFjyOrderMap::new()),
            in_fjy_order_map: Arc::new(Mutex::new(CtpFjyOrderMap::new())),
            fjy_trade_map: Arc::new(CtpFjyTradeMap::new()),

            wd_map: Arc::new(CtpWDMap::new()),
            postrans_map: Arc::new(CtpPosTransMap::new()),

            tradingcode_map: Arc::new(CtpTradingCodeMap::new()),

            orderinsertcancel_map: Arc::new(Mutex::new(CtpOrderInsertCancelMap::new())),
            convertbonds_map: Arc::new(Mutex::new(CtpConvertBondsMonMap::new())),
            selftrade_map: Arc::new(Mutex::new(CtpTradeSelfMap::new())),
            selftradedtl_map: Arc::new(CtpTradeSelfDtlMap::new()),

            credit_limitamt_map: Arc::new(Mutex::new(CtpCreditLimitMap::new())),
            credit_limitpos_map: Arc::new(Mutex::new(CtpCreditLimitMap::new())),
            credit_tcamt_map: Arc::new(Mutex::new(CtpCreditTcAmtMap::new())),
            credit_tcpos_map: Arc::new(Mutex::new(CtpCreditTcPosMap::new())),
            credit_contract_map: Arc::new(Mutex::new(CtpCreditContractMap::new())),
            credit_concentration_map: Arc::new(Mutex::new(CtpCreditConcentrationMap::new())),
            credit_retamtdtl_map: Arc::new(CtpCreditReturnDtlMap::new()),
        }
    }
}

impl TradeStkBuf {
    // 注册异常状态回调
    pub fn register_rtn_abnormalstate_callback(&mut self, callback: Box<dyn IOnRtnAbnormalState>) {
        self.rtn_abnormalstate_callback = Some(callback);
    }

    /// 注册查询资金响应回调
    pub fn register_qry_acc_callback<F>(&mut self, callback: F)
    where
        F: Fn(CtpAccountField) + Send + Sync + 'static,
    {
        self.qry_acc_callback = Some(Arc::new(callback));
    }

    /// 注册查询持仓响应回调
    pub fn register_qry_pos_callback<F>(&mut self, callback: F)
    where
        F: Fn(CtpPositionField) + Send + Sync + 'static,
    {
        self.qry_pos_callback = Some(Arc::new(callback));
    }

    /// 注册报单响应回调
    pub fn register_rsp_inputorder_callback<F>(&mut self, callback: F)
    where
        F: Fn(CtpRspInputOrderField) + Send + Sync + 'static,
    {
        self.rsp_inputorder_callback = Some(Arc::new(callback));
    }

    /// 注册非交易业务报单响应回调
    pub fn register_rsp_inpufjytorder_callback<F>(&mut self, callback: F)
    where
        F: Fn(CtpRspBusinessOrderField) + Send + Sync + 'static,
    {
        self.rsp_inputfjyorder_callback = Some(Arc::new(callback));
    }

    /// 注册撤单响应回调
    pub fn register_rsp_inputorderaction_callback<F>(&mut self, callback: F)
    where
        F: Fn(CtpRspInputOrderActionField) + Send + Sync + 'static,
    {
        self.rsp_inputorderaction_callback = Some(Arc::new(callback));
    }

    /// 注册非交易业务撤单响应回调
    pub fn register_rsp_inputfjyorderaction_callback<F>(&mut self, callback: F)
    where
        F: Fn(CtpRspBusinessOrderActionField) + Send + Sync + 'static,
    {
        self.rsp_inputfjyorderaction_callback = Some(Arc::new(callback));
    }
}

impl TradeStkBuf {
    /// 获取所有的交易编码
    pub fn get_tradingcode_all(&self) -> CtpTradingCodeVec {
        self.tradingcode_map.iter().map(|it| it.value().clone()).collect()
    }

    /// 获取交易编码
    pub fn get_tradingcode(&self, exchid: &str, investorid: &str) -> Option<CtpTradingCodeField> {
        let mut ret: Option<CtpTradingCodeField> = None;
        self.tradingcode_map.iter().for_each(|tc| {
            if tc.ExchangeID == exchid && tc.InvestorID == investorid {
                ret = Some(tc.clone());
                return;
            }
        });

        ret
    }

    /// 获取报单
    pub fn pop_order(&self) -> CtpOrderMap {
        let order_map = self.order_map.lock().unwrap();
        let ret = order_map.clone();
        order_map.clear();
        ret
    }

    /// 获取在途报单
    pub fn pop_in_order(&self) -> CtpOrderMap {
        let in_order_map = self.in_order_map.lock().unwrap();
        let ret = in_order_map.clone();
        in_order_map.clear();
        ret
    }

    /// 获取成交
    pub fn pop_trade(&self) -> CtpTradeVec {
        let mut trade_vec = self.trade_vec.lock().unwrap();
        let ret = trade_vec.clone();
        trade_vec.clear();
        ret
    }

    /// 获取在途非交易业务报单
    pub fn pop_in_fjy_order(&self) -> CtpFjyOrderMap {
        let in_fjy_order_map = self.in_fjy_order_map.lock().unwrap();
        let ret = in_fjy_order_map.clone();
        in_fjy_order_map.clear();
        ret
    }

    /// 获取报撤单
    pub fn pop_orderinsertcancel(&self) -> CtpOrderInsertCancelMap {
        let oicmap = self.orderinsertcancel_map.lock().unwrap();
        let ret = oicmap.clone();
        oicmap.clear();
        ret
    }

    /// 获取可转债
    pub fn pop_convertbonds(&self) -> CtpConvertBondsMonMap {
        let cbmap = self.convertbonds_map.lock().unwrap();
        let ret = cbmap.clone();
        cbmap.clear();
        ret
    }

    /// 获取自成交
    pub fn pop_selftrade(&self) -> CtpTradeSelfMap {
        let stmap = self.selftrade_map.lock().unwrap();
        let ret = stmap.clone();
        stmap.clear();
        ret
    }

    /// 获取融资授信额度
    pub fn pop_credit_limit_amt(&self) -> CtpCreditLimitMap {
        let clmap = self.credit_limitamt_map.lock().unwrap();
        let ret = clmap.clone();
        clmap.clear();
        ret
    }

    /// 获取融券授信额度
    pub fn pop_credit_limit_pos(&self) -> CtpCreditLimitMap {
        let clmap = self.credit_limitpos_map.lock().unwrap();
        let ret = clmap.clone();
        clmap.clear();
        ret
    }

    /// 获取资金头寸
    pub fn pop_credit_tcamt(&self) -> CtpCreditTcAmtMap {
        let tcamtmap = self.credit_tcamt_map.lock().unwrap();
        let ret = tcamtmap.clone();
        tcamtmap.clear();
        ret
    }

    /// 获取股份头寸
    pub fn pop_credit_tcpos(&self) -> CtpCreditTcPosMap {
        let tcposmap = self.credit_tcpos_map.lock().unwrap();
        let ret = tcposmap.clone();
        tcposmap.clear();
        ret
    }

    /// 获取信用合约
    pub fn pop_credit_contract(&self) -> CtpCreditContractMap {
        let ccmap = self.credit_contract_map.lock().unwrap();
        let ret = ccmap.clone();
        ccmap.clear();
        ret
    }

    /// 获取信用集中度
    pub fn pop_credit_concentration(&self) -> CtpCreditConcentrationMap {
        let ccmap = self.credit_concentration_map.lock().unwrap();
        let ret = ccmap.clone();
        ccmap.clear();
        ret
    }

    /// 查询非交易业务委托(page_index从0开始)
    pub fn qry_fjy_order(&self, req: &CtpReqQryBusinessOrderField, page_index: i32, page_size: i32) -> (i32, CtpFjyOrderVec) {
        log::debug!(
            "qry_fjy_order: exch:{}, insid:{}, ordstatus:{}, ordsysid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.OrdStatus,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut ord_vec = CtpFjyOrderVec::new();
        if page_size <= 0 {
            return (cnt, ord_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_order_vec: CtpFjyOrderVec = self.fjy_order_map.iter().map(|it| it.value().clone()).collect();
        sort_order_vec.sort_by(|a, b| b.TransTime.cmp(&a.TransTime));
        sort_order_vec.into_iter().for_each(|ord| {
            if 0 != req.ExchID {
                if req.ExchID != ord.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != ord.AccountID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !ord.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.OrdStatus {
                if req.OrdStatus != ord.OrdStatus {
                    return;
                }
            }

            if !req.OrderSysID.is_empty() {
                if !ord.OrderSysID.contains(&req.OrderSysID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if ord.TransTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if ord.TransTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                ord_vec.push(ord);
            }

            cnt += 1;
        });

        (cnt, ord_vec)
    }

    /// 查询非交易业务成交(page_index从0开始)
    pub fn qry_fjy_trade(&self, req: &CtpReqQryBusinessTradeField, page_index: i32, page_size: i32) -> (i32, CtpFjyTradeVec) {
        log::debug!(
            "qry_fjy_trade: exch:{}, insid:{}, tradeid:{}, ordsysid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.TradeID,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut trade_vec = CtpFjyTradeVec::new();
        if page_size <= 0 {
            return (cnt, trade_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_trade_vec: CtpFjyTradeVec = self.fjy_trade_map.iter().map(|it| it.value().clone()).collect();
        sort_trade_vec.sort_by(|a, b| b.TransTime.cmp(&a.TransTime));
        sort_trade_vec.into_iter().for_each(|trd| {
            if 0 != req.ExchID {
                if req.ExchID != trd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != trd.AccountID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !trd.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if !req.TradeID.is_empty() {
                if !trd.TradeID.contains(&req.TradeID) {
                    return;
                }
            }

            if !req.OrderSysID.is_empty() {
                if !trd.OrderSysID.contains(&req.OrderSysID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if trd.TransTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if trd.TransTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                trade_vec.push(trd);
            }

            cnt += 1;
        });

        (cnt, trade_vec)
    }

    /// 查询出入金(page_index从0开始)
    pub fn qry_wd(&self, req: &CtpReqQryWithdrawDepositField, page_index: i32, page_size: i32) -> (i32, CtpWDVec) {
        log::debug!(
            "qry_wd: exch:{}, accountid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut wd_vec = CtpWDVec::new();
        if page_size <= 0 {
            return (cnt, wd_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_wd_vec: CtpWDVec = self.wd_map.iter().map(|it| it.value().clone()).collect();
        sort_wd_vec.sort_by(|a, b| b.DealTime.cmp(&a.DealTime));
        sort_wd_vec.into_iter().for_each(|wd| {
            if 0 != req.ExchID {
                if req.ExchID != wd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != wd.AccountID {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if wd.DealTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if wd.DealTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                wd_vec.push(wd);
            }

            cnt += 1;
        });

        (cnt, wd_vec)
    }

    /// 查询股份划转(page_index从0开始)
    pub fn qry_positrans(&self, req: &CtpReqQryPositionTransField, page_index: i32, page_size: i32) -> (i32, CtpPosTransVec) {
        log::debug!(
            "qry_positrans: exch:{}, accountid:{}, insid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.InstrumentID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut pt_vec = CtpPosTransVec::new();
        if page_size <= 0 {
            return (cnt, pt_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_pt_vec: CtpPosTransVec = self.postrans_map.iter().map(|it| it.value().clone()).collect();
        sort_pt_vec.sort_by(|a, b| b.DealTime.cmp(&a.DealTime));
        sort_pt_vec.into_iter().for_each(|pt| {
            if 0 != req.ExchID {
                if req.ExchID != pt.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != pt.AccountID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !pt.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if pt.DealTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if pt.DealTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                pt_vec.push(pt);
            }

            cnt += 1;
        });

        (cnt, pt_vec)
    }

    /// 查询自成交详情
    pub fn qry_selftradedtl(
        &self,
        req: &CtpReqQrySelfTradeDtlField,
        page_index: i32,
        page_size: i32,
    ) -> (i32, CtpTradeSelfDtlVec) {
        log::debug!(
            "qry_selftradedtl: exch:{}, insid:{}, tradeid:{}, ordsysid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.TradeID,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut selftrd_vec = CtpTradeSelfDtlVec::new();
        if page_size <= 0 {
            return (cnt, selftrd_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_selftrd_vec: CtpTradeSelfDtlVec = self.selftradedtl_map.iter().map(|it| it.value().clone()).collect();
        sort_selftrd_vec.sort_by(|a, b| b.TransTime.cmp(&a.TransTime));
        sort_selftrd_vec.into_iter().for_each(|trd| {
            if 0 != req.ExchID {
                if req.ExchID != trd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != trd.AccountID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !trd.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if !req.TradeID.is_empty() {
                if !trd.TradeID.contains(&req.TradeID) {
                    return;
                }
            }

            if !req.OrderSysID.is_empty() {
                if !trd.OrderSysID.contains(&req.OrderSysID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if trd.TransTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if trd.TransTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                selftrd_vec.push(trd);
            }

            cnt += 1;
        });

        (cnt, selftrd_vec)
    }

    /// 查询信用偿还明细
    pub fn qry_credit_redtl(
        &self,
        req: &CtpReqQryCreditReDtlField,
        page_index: i32,
        page_size: i32,
    ) -> (i32, CtpCreditReturnDtlVec) {
        log::debug!(
            "qry_credit_redtl: exch:{}, insid:{}, reid:{}, ordsysid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.ReID,
            req.OrdSysID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut redtl_vec = CtpCreditReturnDtlVec::new();
        if page_size <= 0 {
            return (cnt, redtl_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_redtl_vec: CtpCreditReturnDtlVec = self.credit_retamtdtl_map.iter().map(|it| it.value().clone()).collect();
        sort_redtl_vec.sort_by(|a, b| a.No.cmp(&b.No));
        sort_redtl_vec.into_iter().for_each(|rd| {
            if 0 != req.ExchID {
                if req.ExchID != rd.ExchID {
                    return;
                }
            }

            if 0 != req.Retype {
                if req.Retype != rd.ReType {
                    return;
                }
            }

            if !req.ReID.is_empty() {
                if !rd.ReID.contains(&req.ReID) {
                    return;
                }
            }

            if !req.OrdSysID.is_empty() {
                if !rd.OrdSydID.contains(&req.OrdSysID) {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !rd.InsID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if rd.Time < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if rd.Time > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                redtl_vec.push(rd);
            }

            cnt += 1;
        });

        (cnt, redtl_vec)
    }
}

impl TradeStkBuf {
    /// 断开连接
    pub fn on_rtn_disconnect(&self) {
        if let Some(rtn) = self.rtn_abnormalstate_callback.as_ref() {
            rtn.on_rtn_disconnect();
        }
    }

    /// 心跳消息
    pub fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    pub fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {
        if let Some(rtn) = self.rtn_abnormalstate_callback.as_ref() {
            rtn.on_rtn_tdservererror(tderr);
        }
    }

    /// 登录到交易服务端的Session超时
    pub fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {
        if let Some(rtn) = self.rtn_abnormalstate_callback.as_ref() {
            rtn.on_rtn_tdsessiontimeout(tdst);
        }
    }

    /// 委托消息
    pub fn on_rtn_order(&self, ord: CtpOrderField) {
        let key = {
            if !ord.OrderSysID.is_empty() {
                format!("{}{}{}", ord.InvestorID, ord.ExchangeID, ord.OrderSysID)
            } else {
                let index = self.key_index.fetch_add(1, Ordering::SeqCst);
                format!("{}{}", ord.InvestorID, index)
            }
        };

        let mut ord = ord;
        ord.LastStatus = TIConvert::ti_ordstatus(ord.OrderStatus);
        ord.Key = key;

        let mut insert_falg = false;
        if self.order_status_map.contains_key(&ord.Key) {
            let mut prev_order_status = self.order_status_map.get_mut(&ord.Key).unwrap();
            if *prev_order_status < ord.LastStatus {
                *prev_order_status = ord.LastStatus;
                insert_falg = true;
            }
        } else {
            insert_falg = true;
        }

        if insert_falg {
            self.order_map.lock().unwrap().insert(ord.Key.clone(), ord.clone());
            self.in_order_map.lock().unwrap().insert(ord.Key.clone(), ord);
        }
    }

    /// 成交消息
    pub fn on_rtn_trade(&self, trd: CtpTradeField) {
        self.trade_vec.lock().unwrap().push(trd);
    }

    /// 非交易业务委托消息
    pub fn on_rtn_businessorder(&self, ord: CtpBusinessOrderField) {
        let key = {
            if !ord.OrderSysID.is_empty() {
                format!("{}{}{}", ord.AccountID, ord.ExchID, ord.OrderSysID)
            } else {
                format!("{}{}", ord.AccountID, ord.MsgSeqNum)
            }
        };

        let mut insert_falg = false;
        if self.fjy_order_map.contains_key(&key) {
            let mut prev_order = self.fjy_order_map.get_mut(&key).unwrap();
            if prev_order.OrdStatus < ord.OrdStatus {
                prev_order.OrdStatus = ord.OrdStatus;
                prev_order.TransTime = ord.TransTime;
                prev_order.LeavesVolume = ord.LeavesVolume;
                insert_falg = true;
            }
        } else {
            insert_falg = true;
            self.fjy_order_map.insert(key.clone(), ord.clone());
        }

        if insert_falg {
            self.in_fjy_order_map.lock().unwrap().insert(key, ord);
        }
    }

    /// 非交易业务成交消息
    pub fn on_rtn_businesstrade(&self, trd: CtpBusinessTradeField) {
        self.fjy_trade_map.insert(
            format!("{}{}{}{}{}", trd.AccountID, trd.ExchID, trd.TradeID, trd.OrderSysID, trd.Side),
            trd,
        );
    }

    /// 出入金消息
    pub fn on_rtn_wd(&self, wd: CtpWithdrawDepositField) {
        if wd.FrontNo >= 0 {
            self.wd_map.insert(format!("{}{}", wd.AccountID, wd.BizSeqNum), wd);
        } else {
            // 目前服务端发送的消息当FrontNo为负时为信用交易的授信额度与头寸的划入划出(目前结构未最终确定下来暂不处理)
            log::warn!("on_rtn_wd: {:?}", wd);
        }
    }

    /// 股份划转消息
    pub fn on_rtn_positiontrans(&self, pt: CtpPositionTransField) {
        self.postrans_map.insert(format!("{}{}", pt.AccountID, pt.BizSeqNum), pt);
    }

    /// 查询交易编码响应
    pub fn on_rsp_qry_tradingcode(&self, tc: CtpTradingCodeField, islast: bool) {
        self.tradingcode_map
            .insert(format!("{}{}{}", tc.ExchangeID, tc.ClientID, tc.ClientIDType), tc);
    }

    /// 查询合约响应
    pub fn on_rsp_qry_instrument(&self, ins: CtpRspQryInstrumentField, islast: bool) {
        let mut gins = common::global::INS.get().unwrap().write().unwrap();

        if !ins.InstrumentID.is_empty() {
            gins.push_ins(ins.to_ins());
        }

        if islast {
            log::info!("Received the last instrument");
            gins.is_last = true;
        }
    }

    /// 查询非交易合约响应
    pub fn on_rsp_qry_fjyinstrument(&self, ins: CtpRspQryFJYInstrumentField, islast: bool) {
        let mut gins = common::global::INS.get().unwrap().write().unwrap();

        if !ins.insid.is_empty() {
            gins.push_fjy_ins(ins.to_fjyins());
        }

        if islast {
            log::info!("Received the last fjy instrument");
            gins.is_fjy_last = true;
        }
    }

    /// 查询资金响应消息
    pub fn on_rsp_qry_tradingaccount(&self, acc: CtpAccountField) {
        if let Some(cb) = self.qry_acc_callback.as_ref() {
            cb(acc);
        }
    }

    /// 查询持仓响应消息
    pub fn on_rsp_qry_investorposition(&self, pos: CtpPositionField) {
        if let Some(cb) = self.qry_pos_callback.as_ref() {
            cb(pos);
        }
    }

    /// 报单录入响应消息
    pub fn on_rsp_inputorder(&self, rsp: CtpRspInputOrderField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputorder_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 报单操作响应
    pub fn on_rsp_inputorderaction(&self, rsp: CtpRspInputOrderActionField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputorderaction_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 非交易业务报单录入响应消息
    pub fn on_rsp_businessorder(&self, rsp: CtpRspBusinessOrderField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputfjyorder_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 非交易业务报单操作响应
    pub fn on_rsp_businessorderaction(&self, rsp: CtpRspBusinessOrderActionField, reqid: i32, islast: bool) {
        if let Some(cb) = self.rsp_inputfjyorderaction_callback.as_ref() {
            cb(rsp);
        }
    }

    /// 自成交
    pub fn on_rtn_tradeself(&self, trdslf: CtpTradeSelfField) {
        let stmap = self.selftrade_map.lock().unwrap();
        stmap.insert(
            std::format!(
                "{}{}{}{}",
                trdslf.ExchID,
                trdslf.AccountID,
                trdslf.ClientID,
                trdslf.InstrumentID
            ),
            trdslf,
        );
    }

    /// 自成交详情
    pub fn on_rtn_tradeselfdtl(&self, trd: CtpTradeSelfDtlField) {
        self.selftradedtl_map
            .insert(std::format!("{}{}{}", trd.ExchID, trd.ClientID, trd.MsgSeqNum), trd);
    }

    /// 报撤单
    pub fn on_rtn_orderinsertcancel(&self, oic: CtpOrderInsertCancelField) {
        let oicmap = self.orderinsertcancel_map.lock().unwrap();
        oicmap.insert(std::format!("{}{}{}", oic.ExchID, oic.AccountID, oic.ClientID), oic);
    }

    /// 可转债监控
    pub fn on_rtn_convertbondsmon(&self, cbm: CtpConvertBondsMonField) {
        let cbmap = self.convertbonds_map.lock().unwrap();
        cbmap.insert(
            std::format!("{}{}{}{}", cbm.ExchID, cbm.AccountID, cbm.ClientID, cbm.InstrumentID),
            cbm,
        );
    }

    /// 信用授信额度
    pub fn on_rtn_creditlimit(&self, cl: CtpCreditLimitField) {
        if 1 == cl.LType {
            let clmap = self.credit_limitamt_map.lock().unwrap();
            clmap.insert(std::format!("{}{}", cl.ExchID, cl.AccID), cl);
        } else if 2 == cl.LType {
            let clmap = self.credit_limitpos_map.lock().unwrap();
            clmap.insert(std::format!("{}{}", cl.ExchID, cl.AccID), cl);
        } else {
            log::warn!("on_rtn_creditlimit: unknown credit limit type. {:?}", cl);
        }
    }

    /// 信用资金头寸
    pub fn on_rtn_credittcamt(&self, amt: CtpCreditTcAmtField) {
        let tcamtmap = self.credit_tcamt_map.lock().unwrap();
        tcamtmap.insert(std::format!("{}{}{}", amt.TcType, amt.ExchID, amt.AccID), amt);
    }

    /// 信用股份头寸
    pub fn on_rtn_credittcpos(&self, pos: CtpCreditTcPosField) {
        let tcposmap = self.credit_tcpos_map.lock().unwrap();
        tcposmap.insert(std::format!("{}{}{}{}", pos.TcType, pos.CliID, pos.ExchID, pos.InsID), pos);
    }

    /// 信用合约
    pub fn on_rtn_creditcontract(&self, cc: CtpCreditContractField) {
        let ccmap = self.credit_contract_map.lock().unwrap();
        ccmap.insert(std::format!("{}{}{}", cc.ExchID, cc.AccID, cc.OrderSysID), cc);
    }

    /// 信用集中度
    pub fn on_rtn_creditconcentration(&self, cc: CtpCreditConcentrationField) {
        let ccmap = self.credit_concentration_map.lock().unwrap();
        ccmap.insert(
            std::format!("{}{}{}{}{}", cc.GID, cc.CType, cc.CliID, cc.ExchID, cc.InsID),
            cc,
        );
    }

    /// 信用合约归还明细
    pub fn on_rtn_creditretdtl(&self, rtd: CtpCreditReturnDtlField) {
        if TIType::CREDIT_RETURN_TYPE_AMT == rtd.Type {
            self.credit_retamtdtl_map.insert(rtd.No, rtd);
        }
    }
}
