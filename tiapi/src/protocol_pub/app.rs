use serde::Deserialize;

/// 心跳
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct AppHeartbeatField {
    /// 风控服务端APPID
    pub AID: i32,

    /// 风控服务端时间(s)
    pub Time: i64,
}

// 交易端异常
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct AppTDServerErrorField {
    /// 风控服务端APPID
    pub AID: i32,

    /// 交易端ServerID
    pub SID: i32,

    /// 类型: 1:重启; 2:超时; 3:丢数据
    pub Type: i32,

    /// 发生时间(风控端检测到的时间)
    pub Time: i32,

    /// 异常信息
    pub Msg: String,
}
impl AppTDServerErrorField {
    pub fn to_string(&self, auto_wrap: bool) -> String {
        let h = self.Time / 10000;
        let m = (self.Time - h * 10000) / 100;
        let s = self.Time % 100;

        if auto_wrap {
            format!(
                "\n消息:{}\n\n服务端APPID:{}\n\n交易服务端ServerID:{}\n\n时间:{:0>2}:{:0>2}:{:0>2}",
                self.Msg, self.AID, self.SID, h, m, s
            )
        } else {
            format!(
                "{} | 服务端APPID:{} | 交易服务端ServerID:{} | 时间:{:0>2}:{:0>2}:{:0>2}",
                self.Msg, self.AID, self.SID, h, m, s
            )
        }
    }
}

// 登录到交易服务端的Session超时
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct AppTDSessionTimeoutField {
    /// 风控服务端APPID
    pub AID: i32,

    /// 交易端ServerID
    pub SID: i32,

    /// 发生时间(风控端检测到的时间)
    pub Time: i32,

    /// 信息
    pub Msg: String,
}
impl AppTDSessionTimeoutField {
    pub fn to_string(&self, auto_wrap: bool) -> String {
        let h = self.Time / 10000;
        let m = (self.Time - h * 10000) / 100;
        let s = self.Time % 100;

        if auto_wrap {
            format!(
                "\n消息:{}\n\n服务端APPID:{}\n\n交易服务端ServerID:{}\n\n时间:{:0>2}:{:0>2}:{:0>2}",
                self.Msg, self.AID, self.SID, h, m, s
            )
        } else {
            format!(
                "{} | 服务端APPID:{} | 交易服务端ServerID:{} | 时间:{:0>2}:{:0>2}:{:0>2}",
                self.Msg, self.AID, self.SID, h, m, s
            )
        }
    }
}
