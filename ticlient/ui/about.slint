import { Theme, ThemeStyle, Label } from "@zstk/lib.slint";
import { AppCom, AppAbout } from "./struct.slint";
import { WndRec } from "./widgets/wndrec.slint";
import { AboutSlint } from "std-widgets.slint";

export component AboutPage {

    preferred_width: 100%;
    preferred_height: 100%;

    min_height: 200px;
    min_width: 300px;

    WndRec {
        title: @tr("关于...");
        btn_close_clicked => {
            AppCom.show_about = false;
        }

        i_layout := VerticalLayout {
            padding: 10px;
            spacing: 10px;

            Image {
                image_fit: fill;
                source: 0 == AppAbout.icon ?
                    (Theme.theme_style == ThemeStyle.Light ? @image-url("./resource/ti_en_black.svg") : @image-url("./resource/ti_en_white.svg")) :
                    @image_url("");
            }

            Rectangle {
                border-color: Theme.border_spliter;
                border-width: 1px;
                height: 1px;
            }

            Label {
                text: AppAbout.name;
                horizontal_alignment: center;
            }

            Label {
                text: AppAbout.version;
                horizontal_alignment: center;
            }

            Label {
                text: AppAbout.copyright;
                horizontal_alignment: center;
            }

            if AppAbout.show_slint_about: Label {
                text: "slint";
                color: Theme.accent_background;
                horizontal_alignment: center;

                TouchArea {
                    mouse_cursor: pointer;

                    clicked => {
                        i_popwnd_slint.show();
                    }
                }
            }
        }
    }

    i_popwnd_slint := PopupWindow {
        x: i_layout.width / 2 - 140px;
        y: i-layout.y + 10px;
        height: 175px;

        Rectangle {
            background: Theme.control_background;
            AboutSlint { }
        }
    }
}
