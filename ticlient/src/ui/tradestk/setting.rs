use crate::{
    common::{
        self,
        tistruct::{ConvertBondsMonitorField, FilePathField, OrderInsertCancelMonitorField, TradeSelfMonitorField},
    },
    slintui::*,
};
use slint::*;

/// 设置
pub(super) struct Setting {}

/// 构建与初始化
impl Setting {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_set = app.global::<StkTrd_Setting>();

        // 初始化设置的值
        {
            let cfg = common::global::CFG.get().unwrap().read().unwrap();

            app_set.set_trdreqtip_inputorder(cfg.com.ReqTradeTip.InputOrder);
            app_set.set_trdreqtip_ordercancel(cfg.com.ReqTradeTip.InputOrderAction);

            app_set.set_trdreqtip_fjy_inputorder(cfg.com.ReqTradeTip.FjyInputOrder);
            app_set.set_trdreqtip_fjy_ordercancel(cfg.com.ReqTradeTip.FjyInputOrderAction);

            app_set.set_mon_convertbonds(MonConvertbonds {
                trade_amount: cfg.stk_mon.CovrtBondMonitor.TradeAmount.to_string().into(),
                ratio: cfg.stk_mon.CovrtBondMonitor.Ratio.to_string().into(),
            });

            app_set.set_mon_orderinsertcancel(MonOrderinsertcancel {
                greater_than_num: cfg.stk_mon.OrdInsertCancelMonitor.GreaterThanNum.to_string().into(),
                cancel_ratio: cfg.stk_mon.OrdInsertCancelMonitor.CancelRatio.to_string().into(),
            });

            app_set.set_mon_tradeself(MonTradeself {
                trade_vol: cfg.stk_mon.TrdSelfMonitor.TradeVol.to_string().into(),
            });
        }

        // 交易请求确认 - 报单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_inputorder_changed(move || Setting::on_trdreqtip_inputorder_changed(&app_weak));

        // 交易请求确认 - 撤单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_ordercancel_changed(move || Setting::on_trdreqtip_ordercancel_changed(&app_weak));

        // 交易请求确认 - 非交易业务报单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_fjy_inputorder_changed(move || Setting::on_trdreqtip_fjy_inputorder_changed(&app_weak));

        // 交易请求确认 - 非交易业务撤单
        let app_weak = app.as_weak();
        app_set.on_trdreqtip_fjy_ordercancel_changed(move || Setting::on_trdreqtip_fjy_ordercancel_changed(&app_weak));

        // 异常监控 - 可转债 - 重置
        let app_weak = app.as_weak();
        app_set.on_reset_mon_convertbonds(move || Setting::on_reset_mon_convertbonds(&app_weak));

        // 异常监控 - 可转债
        let app_weak = app.as_weak();
        app_set.on_mon_convertbonds_changed(move || Setting::on_mon_convertbonds_changed(&app_weak));

        // 异常监控 - 报撤单 - 重置
        let app_weak = app.as_weak();
        app_set.on_reset_mon_orderinsertcancel(move || Setting::on_reset_mon_orderinsertcancel(&app_weak));

        // 异常监控 - 报撤单
        let app_weak = app.as_weak();
        app_set.on_mon_orderinsertcancel_changed(move || Setting::on_mon_orderinsertcancel_changed(&app_weak));

        // 异常监控 - 自成交 - 重置
        let app_weak = app.as_weak();
        app_set.on_reset_mon_tradeself(move || Setting::on_reset_mon_tradeself(&app_weak));

        // 异常监控 - 自成交
        let app_weak = app.as_weak();
        app_set.on_mon_tradeself_changed(move || Setting::on_mon_tradeself_changed(&app_weak));

        log::trace!("Setting init completed");
    }
}

// 事件
impl Setting {
    /// 交易请求确认 - 报单
    fn on_trdreqtip_inputorder_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputOrder = app_set.get_trdreqtip_inputorder();
    }

    /// 交易请求确认 - 撤单
    fn on_trdreqtip_ordercancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.InputOrderAction = app_set.get_trdreqtip_ordercancel();
    }

    /// 交易请求确认 - 非交易业务报单
    fn on_trdreqtip_fjy_inputorder_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.FjyInputOrder = app_set.get_trdreqtip_fjy_inputorder();
    }

    /// 交易请求确认 - 非交易业务撤单
    fn on_trdreqtip_fjy_ordercancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.com.ReqTradeTip.FjyInputOrderAction = app_set.get_trdreqtip_fjy_ordercancel();
    }

    /// 异常监控 - 可转债 - 重置
    fn on_reset_mon_convertbonds(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        app_set.set_mon_convertbonds_err_status(0);
        app_set.set_mon_convertbonds_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.stk_mon.CovrtBondMonitor = ConvertBondsMonitorField::default();
        app_set.set_mon_convertbonds(MonConvertbonds {
            trade_amount: cfg.stk_mon.CovrtBondMonitor.TradeAmount.to_string().into(),
            ratio: cfg.stk_mon.CovrtBondMonitor.Ratio.to_string().into(),
        });

        let path = std::format!(
            "{}/{}",
            cfg.com.FPath.LoginUserCfgDir,
            FilePathField::COVRT_BOND_MONITOR_PATH
        );
        common::global::serialize_write_user_data(&path, &cfg.stk_mon.CovrtBondMonitor, false);
    }

    /// 异常监控 - 可转债
    fn on_mon_convertbonds_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        let cbm = app_set.get_mon_convertbonds();

        let ret = cbm.trade_amount.parse::<f64>();
        if let Err(err) = ret {
            app_set.set_mon_convertbonds_err_status(1);
            app_set.set_mon_convertbonds_err_tips(slint::format!("请输入正确的金额. {}", err.to_string()));
            return;
        }
        let trade_amount = ret.unwrap();
        if trade_amount <= 0.0 {
            app_set.set_mon_convertbonds_err_status(1);
            app_set.set_mon_convertbonds_err_tips(slint::format!("输入的金额必需大于0"));
            return;
        }

        let ret = cbm.ratio.parse::<f64>();
        if let Err(err) = ret {
            app_set.set_mon_convertbonds_err_status(2);
            app_set.set_mon_convertbonds_err_tips(slint::format!("请输入正确的比率. {}", err.to_string()));
            return;
        }
        let ratio = ret.unwrap();
        if ratio < 0.01 {
            app_set.set_mon_convertbonds_err_status(2);
            app_set.set_mon_convertbonds_err_tips(slint::format!("输入的比率不能小于0.01"));
            return;
        }

        app_set.set_mon_convertbonds_err_status(0);
        app_set.set_mon_convertbonds_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.stk_mon.CovrtBondMonitor.TradeAmount = trade_amount;
        cfg.stk_mon.CovrtBondMonitor.Ratio = ratio;
        let path = std::format!(
            "{}/{}",
            cfg.com.FPath.LoginUserCfgDir,
            FilePathField::COVRT_BOND_MONITOR_PATH
        );
        common::global::serialize_write_user_data(&path, &cfg.stk_mon.CovrtBondMonitor, false);
    }

    /// 异常监控 - 报撤单 - 重置
    fn on_reset_mon_orderinsertcancel(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        app_set.set_mon_orderinsertcancel_err_status(0);
        app_set.set_mon_orderinsertcancel_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.stk_mon.OrdInsertCancelMonitor = OrderInsertCancelMonitorField::default();
        app_set.set_mon_orderinsertcancel(MonOrderinsertcancel {
            greater_than_num: cfg.stk_mon.OrdInsertCancelMonitor.GreaterThanNum.to_string().into(),
            cancel_ratio: cfg.stk_mon.OrdInsertCancelMonitor.CancelRatio.to_string().into(),
        });

        let path = std::format!(
            "{}/{}",
            cfg.com.FPath.LoginUserCfgDir,
            FilePathField::ORD_INSERT_CANCEL_MONITOR_PATH
        );
        common::global::serialize_write_user_data(&path, &cfg.stk_mon.OrdInsertCancelMonitor, false);
    }

    /// 异常监控 - 报撤单
    fn on_mon_orderinsertcancel_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        let oicm = app_set.get_mon_orderinsertcancel();

        let ret = oicm.greater_than_num.parse::<i32>();
        if let Err(err) = ret {
            app_set.set_mon_orderinsertcancel_err_status(1);
            app_set.set_mon_orderinsertcancel_err_tips(slint::format!("请输入正确的总笔数. {}", err.to_string()));
            return;
        }
        let greater_than_num = ret.unwrap();
        if greater_than_num <= 0 {
            app_set.set_mon_orderinsertcancel_err_status(1);
            app_set.set_mon_orderinsertcancel_err_tips(slint::format!("总笔数必需大于0"));
            return;
        }

        app_set.set_mon_orderinsertcancel_err_status(0);
        app_set.set_mon_orderinsertcancel_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.stk_mon.OrdInsertCancelMonitor.GreaterThanNum = greater_than_num;
        let path = std::format!(
            "{}/{}",
            cfg.com.FPath.LoginUserCfgDir,
            FilePathField::ORD_INSERT_CANCEL_MONITOR_PATH
        );
        common::global::serialize_write_user_data(&path, &cfg.stk_mon.OrdInsertCancelMonitor, false);
    }

    /// 异常监控 - 自成交 - 重置
    fn on_reset_mon_tradeself(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        app_set.set_mon_tradeself_err_status(0);
        app_set.set_mon_tradeself_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.stk_mon.TrdSelfMonitor = TradeSelfMonitorField::default();
        app_set.set_mon_tradeself(MonTradeself {
            trade_vol: cfg.stk_mon.TrdSelfMonitor.TradeVol.to_string().into(),
        });

        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.stk_mon.TrdSelfMonitor, false);
    }

    /// 异常监控 - 自成交
    fn on_mon_tradeself_changed(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_set = app.global::<StkTrd_Setting>();

        let tsm = app_set.get_mon_tradeself();

        let ret = tsm.trade_vol.parse::<i32>();
        if let Err(err) = ret {
            app_set.set_mon_tradeself_err_status(1);
            app_set.set_mon_tradeself_err_tips(slint::format!("请输入正确的数量. {}", err.to_string()));
            return;
        }
        let trade_vol = ret.unwrap();
        if trade_vol <= 0 {
            app_set.set_mon_tradeself_err_status(1);
            app_set.set_mon_tradeself_err_tips(slint::format!("数量必需大于0"));
            return;
        }

        app_set.set_mon_tradeself_err_status(0);
        app_set.set_mon_tradeself_err_tips("".into());

        let mut cfg = common::global::CFG.get().unwrap().write().unwrap();
        cfg.stk_mon.TrdSelfMonitor.TradeVol = trade_vol;
        let path = std::format!("{}/{}", cfg.com.FPath.LoginUserCfgDir, FilePathField::TRD_SELF_MONITOR_PATH);
        common::global::serialize_write_user_data(&path, &cfg.stk_mon.TrdSelfMonitor, false);
    }
}
