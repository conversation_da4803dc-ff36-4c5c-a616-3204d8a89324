import { <PERSON>, <PERSON>b<PERSON>iew, <PERSON><PERSON><PERSON>, MBButtons, MBIcon, MBResult } from "@zstk/lib.slint";

import {
    OptTrd_Com, OptTrd_TabSelIdx, OptTrd_Setting,
    OptTrd_Account, OptTrd_Position, OptTrd_PositionComb,
    OptTrd_CancelTip, OptTrd_InOrder, OptTrd_InExercise, OptTrd_InExerciseComb, OptTrd_InQuote,
    OptTrd_InputTip, OptTrd_InputOrder, OptTrd_InputOml, OptTrd_InputExercise, OptTrd_InputStockLock, OptTrd_InputQuote
} from "struct.slint";

import { AccountPage } from "account.slint";
import { FundTransPage } from "fundtrans.slint";
import { MarketDataPage, OptTrd_MarketData } from "marketdata.slint";
import { PositionPage } from "position.slint";
import { CoveredConvertPage } from "covered_convert.slint";
import { CombPreviewPage } from "comb_preview.slint";
import { PositionCombPage } from "position_comb.slint";

import { InPage } from "in.slint";

import { OptTrd_QryOrder } from "qry_order.slint";
import { OptTrd_QryTrade } from "qry_trade.slint";
import { OptTrd_QryOml } from "qry_oml.slint";
import { OptTrd_QryExercise } from "qry_exercise.slint";
import { OptTrd_QryExerciseComb } from "qry_exercise_comb.slint";
import { OptTrd_QryQuote } from "qry_quote.slint";
import { OptTrd_QryWithdrawDeposit } from "qry_wd.slint";
import { OptTrd_QryInstrument } from "qry_instrument.slint";
import { OptTrd_QryFundTransDtl } from "qry_fundtransdtl.slint";
import { QueryPage } from "qry.slint";

import { OptTrd_MonTrdPosOrdInsertCancel } from "mon_trdpos_ordinsertcancel.slint";
import { OptTrd_MonLimitAmount } from "mon_limitamount.slint";
import { OptTrd_MonLimitPosition } from "mon_limitposition.slint";
import { OptTrd_MonSelfTrade } from "mon_selftrade.slint";
import { OptTrd_MonSelfTradeDtl } from "mon_selftradedtl.slint";
import { MonitorPage } from "mon.slint";

import { InputPage } from "input.slint";

import { MarketDataDtl } from "../md/mdstruct.slint";
import { MarketDataDtlPage } from "../md/marketdatadtl.slint";

export {
    OptTrd_Com, OptTrd_TabSelIdx, OptTrd_Setting,
    OptTrd_MarketData,
    OptTrd_Account, OptTrd_Position, OptTrd_PositionComb,
    OptTrd_CancelTip, OptTrd_InOrder, OptTrd_InExercise, OptTrd_InExerciseComb, OptTrd_InQuote,
    OptTrd_QryOrder, OptTrd_QryTrade, OptTrd_QryOml, OptTrd_QryExercise, OptTrd_QryExerciseComb, OptTrd_QryQuote, OptTrd_QryWithdrawDeposit, OptTrd_QryInstrument, OptTrd_QryFundTransDtl,
    OptTrd_MonTrdPosOrdInsertCancel, OptTrd_MonLimitAmount, OptTrd_MonLimitPosition, OptTrd_MonSelfTrade, OptTrd_MonSelfTradeDtl,
    OptTrd_InputTip, OptTrd_InputOrder, OptTrd_InputOml, OptTrd_InputExercise, OptTrd_InputStockLock, OptTrd_InputQuote
}

export component TradeOptPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 1px;

        HorizontalLayout {
            padding_left: 1px;
            padding_right: 1px;

            AccountPage {
                height: 130px;
            }
        }

        HorizontalLayout {
            padding_left: 1px;
            padding_right: 1px;
            spacing: 1px;
            height: 380px;

            Rectangle {
                border-radius: 2px;
                border-width: 1px;
                border-color: Theme.border;

                MarketDataPage {
                    init => {
                        MarketDataDtl.mdtype = 1;
                    }
                }
            }

            MarketDataDtlPage {
                width: 360px;
            }
        }

        HorizontalLayout {
            padding_left: 1px;
            padding-right: 1px;
            spacing: 1px;

            VerticalLayout {
                TabView {
                    height: 30px;

                    items: [
                        { text: "持仓" },
                        { text: "组合持仓" },
                        { text: "在途订单" },
                        { text: "查询" },
                        { text: "异常指标监控" },
                    ];

                    current_index <=> OptTrd_TabSelIdx.selidx;
                }

                Rectangle {
                    if 0 == OptTrd_TabSelIdx.selidx: PositionPage { }
                    if 1 == OptTrd_TabSelIdx.selidx: PositionCombPage {}
                    if 2 == OptTrd_TabSelIdx.selidx: InPage { }
                    if 3 == OptTrd_TabSelIdx.selidx: QueryPage { }
                    if 4 == OptTrd_TabSelIdx.selidx: MonitorPage { }
                }
            }

            Rectangle {
                border-radius: 2px;
                border-width: 1px;
                border-color: Theme.border;
                width: 360px;

                InputPage {}
            }
        }
    }

    // 资金划转
    if OptTrd_Account.show_fund_trans : FundTransPage {
        y: 135px;
    }

    // 备兑转换
    if OptTrd_Position.show_covered_convert: CoveredConvertPage {
        y: 415px;
    }

    // 组合预览
    if OptTrd-InputOml.show_oml_preview: CombPreviewPage {
        y: 415px;
    }

    // 报单提醒
    if OptTrd_InputTip.app_ret.state > 0: Rectangle {
        init => {
            if 100 == OptTrd_InputTip.app_ret.state {
                i_msgbox_input.id = OptTrd_InputTip.app_ret.state;
                i_msgbox_input.show_check = true;
                i_msgbox_input.checked = true;
                i_msgbox_input.open_title(OptTrd_InputTip.app_ret.title, OptTrd_InputTip.app_ret.msg, MBButtons.YesNo, MBIcon.Question);
            } else {
                i_msgbox_input.show_check = false;
                i_msgbox_input.open_title(OptTrd_InputTip.app_ret.title, OptTrd_InputTip.app_ret.msg, MBButtons.Ok, MBIcon.Error);
            }
            OptTrd_InputTip.app_ret = { state: 0, title: "", msg: "" };
        }
    }
    i_msgbox_input := MessageBox {
        width: root.width;
        height: root.height;
        check_txt: 0 == OptTrd_TabSelIdx.input_selidx ? "提醒报单请求" :
            (1 == OptTrd_TabSelIdx.input_selidx ? (OptTrd_InputOml.is_comb ? "提醒组合请求" : "提醒解组合请求") :
            (2 == OptTrd_TabSelIdx.input_selidx ? "提醒行权请求" :
            (3 == OptTrd_TabSelIdx.input_selidx ? "提醒备兑解锁仓请求" : "提醒报价请求")));

        clicked(id, ret) => {
            if MBResult.Yes == ret {
                if 0 == OptTrd_TabSelIdx.input_selidx {
                    if 100 == id {
                        OptTrd_InputOrder.ok_clicked(false);
                    }
                } else if 1 == OptTrd_TabSelIdx.input_selidx {
                    if 100 == id {
                        OptTrd_InputOml.ok_clicked(false);
                    }
                } else if 2 == OptTrd_TabSelIdx.input_selidx {
                    if 100 == id {
                        OptTrd_InputExercise.ok_clicked(false);
                    }
                } else if 3 == OptTrd_TabSelIdx.input_selidx {
                    if 100 == id {
                        OptTrd_InputStockLock.ok_clicked(false);
                    }
                } else if 4 == OptTrd_TabSelIdx.input_selidx {
                    if 100 == id {
                        OptTrd_InputQuote.ok_clicked(false);
                    }
                }
            }
            self.close();
        }

        toggled(id, checked) => {
            if 0 == OptTrd_TabSelIdx.input_selidx {
                OptTrd_InputOrder.req_tip_toggled(checked);
            } else if 1 == OptTrd_TabSelIdx.input_selidx {
                OptTrd_InputOml.req_tip_toggled(checked);
            } else if 2 == OptTrd_TabSelIdx.input_selidx {
                if 100 == id {
                    OptTrd_InputExercise.req_tip_toggled(checked);
                }
            } else if 3 == OptTrd_TabSelIdx.input_selidx {
                OptTrd_InputStockLock.req_tip_toggled(checked);
            } else if 4 == OptTrd_TabSelIdx.input_selidx {
                OptTrd_InputQuote.req_tip_toggled(checked);
            }
        }
    }

    // 撤单提醒
    if OptTrd_CancelTip.app_ret.state > 0: Rectangle {
        init => {
            if 100 == OptTrd_CancelTip.app_ret.state ||
               101 == OptTrd_CancelTip.app_ret.state ||
               102 == OptTrd_CancelTip.app_ret.state ||
               103 == OptTrd_CancelTip.app_ret.state {
                i_msgbox_cancel.id = OptTrd_CancelTip.app_ret.state;
                i_msgbox_cancel.show_check = true;
                i_msgbox_cancel.checked = true;
                i_msgbox_cancel.open_title(OptTrd_CancelTip.app_ret.title, OptTrd_CancelTip.app_ret.msg, MBButtons.YesNo, MBIcon.Question);
            } else {
                i_msgbox_cancel.show_check = false;
                i_msgbox_cancel.open_title(OptTrd_CancelTip.app_ret.title, OptTrd_CancelTip.app_ret.msg, MBButtons.Ok, MBIcon.Error);
            }
            OptTrd_CancelTip.app_ret = {state: 0, title: "", msg: ""};
        }
    }
    i_msgbox_cancel := MessageBox {
        width: root.width;
        height: root.height;
        check_txt: 0 == OptTrd_TabSelIdx.in_subselidx ? "提醒撤单请求" :
                  (1 == OptTrd_TabSelIdx.in_subselidx ? "提醒撤销行权请求" :
                  (2 == OptTrd_TabSelIdx.in_subselidx ? "提醒撤销行权合并请求" : "提醒撤报价请求"));

        clicked(id, ret) => {
            if MBResult.Yes == ret {
                if 0 == OptTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        OptTrd_InOrder.cancel_order_clieked(OptTrd_InOrder.sel_cancel_order, false);
                    }
                    if 101 == id {
                        OptTrd_InOrder.cancel_order_all_clieked(OptTrd_InOrder.row_data.length, false);
                    }
                    if 102 == id {
                        OptTrd_InOrder.cancel_order_bs_clieked(0, OptTrd_InOrder.row_data.length, false);
                    }
                    if 103 == id {
                        OptTrd_InOrder.cancel_order_bs_clieked(1, OptTrd_InOrder.row_data.length, false);
                    }
                } else if 1 == OptTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        OptTrd_InExercise.cancel_exec_clieked(OptTrd_InExercise.sel_cancel_exec, false);
                    }
                    if 101 == id {
                        OptTrd_InExercise.cancel_exec_all_clieked(OptTrd_InExercise.row_data.length, false);
                    }
                } else if 2 == OptTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        OptTrd_InExerciseComb.cancel_exec_clieked(OptTrd_InExerciseComb.sel_cancel_exec, false);
                    }
                    if 101 == id {
                        OptTrd_InExerciseComb.cancel_exec_all_clieked(OptTrd_InExerciseComb.row_data.length, false);
                    }
                } else if 3 == OptTrd_TabSelIdx.in_subselidx {
                    if 100 == id {
                        OptTrd_InQuote.cancel_order_clieked(OptTrd_InQuote.sel_cancel_order, false);
                    }
                    if 101 == id {
                        OptTrd_InQuote.cancel_order_all_clieked(OptTrd_InQuote.row_data.length, false);
                    }
                }
            }

            self.close();
        }

        toggled(id, checked) => {
            if 0 == OptTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id || 102 == id || 103 == id {
                    OptTrd_InOrder.req_tip_toggled(checked);
                }
            } else if 1 == OptTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id {
                    OptTrd_InExercise.req_tip_toggled(checked);
                }
            } else if 2 == OptTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id {
                    OptTrd_InExerciseComb.req_tip_toggled(checked);
                }
            } else if 3 == OptTrd_TabSelIdx.in_subselidx {
                if 100 == id || 101 == id {
                    OptTrd_InQuote.req_tip_toggled(checked);
                }
            }
        }
    }
}
