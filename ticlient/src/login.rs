#![allow(unused_variables)]

use crate::common::{
    global,
    ticode::{self, HisText},
    tistruct::{EClientType, FilePathField, ServerInfoField},
};
use crate::error::Result;
use slint::{ModelRc, VecModel};
use std::{io::Read, rc::Rc, sync::Arc};

use crate::slintui::*;
use slint::*;

/// 获取客户端类型描述
fn get_clitype_name(clitype: i32) -> &'static str {
    match clitype {
        100 => "个股期权风控",
        101 => "个股期权交易",
        200 => "现货风控",
        201 => "现货交易",
        202 => "现货风控(两融)",
        203 => "现货交易(两融)",
        300 => "期货风控",
        301 => "期货交易",
        _ => "未知",
    }
}

/// 切换服务端
/// ### Arguments
/// * `svr_name` - 服务器名称
/// ### Returns
/// * `Result<()>` - 成功返回Ok, 失败返回Err
///
/// This function changes the server configuration based on the provided server name.
/// It updates the global configuration and sets the default server name.
/// If the operation is successful, it returns Ok(()), otherwise it returns an error.
///
/// ### Example
///
/// ```
/// let result = on_change_server("NewServerName".to_string()).await;
/// match result {
///     Ok(_) => println!("Server changed successfully!"),
///     Err(e) => println!("Failed to change server: {}", e),
/// }
///
async fn on_change_server(svr_name: String) -> Result<()> {
    match global::change_server(&svr_name).await {
        Ok(_) => {
            global::set_default_svr_name(&svr_name);
            Ok(())
        }
        Err(err) => Err(err),
    }
}

/// 登录
/// ### Arguments
/// * `uid` - 用户名
/// * `passwd` - 密码
/// * `vc_seqnum` - 验证码序号
/// * `vc_code` - 验证码
/// * `rem_passwd` - 是否记住密码
/// * `progress` - 进度回调函数
/// * `hu_map` - 历史用户信息映射表
/// ### Returns
/// * `Result<()>` - 成功返回Ok, 失败返回Err
async fn on_login<F>(
    uid: String,
    passwd: String,
    vc_seqnum: i32,
    vc_code: String,
    rem_passwd: bool,
    hu_map: Arc<dashmap::DashMap<String, String>>,
    mut progress: F,
) -> Result<()>
where
    F: FnMut(String) + Send + 'static,
{
    // 请求登录
    progress("正在请求登录...".into());
    let ret = global::req_login(vc_seqnum, &vc_code, &uid, &passwd);
    if let Err(err) = ret.await {
        return Err(err.into());
    }
    progress("登录成功...".into());

    // 请求查询合约
    progress("请求查询交易编码...".into());
    let ret = global::req_qry_tradingcode();
    if let Err(err) = ret.await {
        log::warn!("query trading code failed. {}", err);
    }

    // 请求查询合约
    progress("请求查询合约信息...".into());
    let ret = global::req_qry_instrument();
    if let Err(err) = ret.await {
        log::warn!("query instrument failed. {}", err);
    }

    // 初始化并登录行情
    progress("初始化行情...".into());
    if let Err(err) = global::init_md().await {
        log::warn!("init_md failed. {}", err);
    }

    // 保存用户信息
    progress("保存用户信息...".into());
    {
        if !rem_passwd {
            hu_map.insert(uid.clone(), "".to_owned());
        } else {
            hu_map.insert(uid.clone(), passwd.clone());
        }

        let mut hu_vec: Vec<HisText> = hu_map
            .iter()
            .map(|hu| HisText {
                text1: hu.key().to_string(),
                text2: hu.value().to_string(),
            })
            .collect();

        hu_vec.sort_by(|a, b| a.text1.cmp(&b.text1));
        let path = {
            let cfg = global::CFG.get().unwrap().read().unwrap();
            std::format!("{}/{}", cfg.com.FPath.WorkDir, FilePathField::HIS_USER_INFO_PATH)
        };
        ticode::TICode::w(&hu_vec, &path);
    }

    // 更新登录信息
    progress("更新登录信息...".into());
    global::update_login_user_info(&uid, &passwd);

    progress("登录完成...".into());

    Ok(())
}

/// 运行登录界面
/// ### Arguments
/// * `app` - 登录界面应用实例
/// ### Returns
/// * `bool` - 返回true表示运行成功, false表示失败
pub fn run(app: &crate::slintui::LoginForm) -> bool {
    #[cfg(feature = "risk")]
    app.set_set_is_trade(false);

    let hu_map = Arc::new(dashmap::DashMap::new());

    let wapp = app.as_weak().unwrap();
    app.on_msgbox_closed(move |id, ret| {
        if 0x0fff1233 == id {
            let _ = wapp.hide();
        }
    });

    let wapp = app.as_weak().unwrap();
    wapp.on_copy_str_to_clipboard(|data| {
        let mut ctx: clipboard::ClipboardContext = clipboard::ClipboardProvider::new().unwrap();
        let _ = clipboard::ClipboardProvider::set_contents(&mut ctx, data.as_str().to_owned());
    });

    let wapp = app.as_weak().unwrap();
    let hu_map_clone = hu_map.clone();
    app.on_sys_init(move || {
        // 初始化系统
        {
            let ret = global::init();
            if ret.is_err() {
                wapp.invoke_set_msgbox_id(0x0fff1233);
                wapp.set_msgtip((ret.err().unwrap().into(), 1, "系统初始化失败".into()));
                return false;
            }
        }

        // 添加服务器
        {
            let SIFMap = { global::CFG.get().unwrap().read().unwrap().com.SIFMap.clone() };
            if !SIFMap.is_empty() {
                let mut svr_name = String::new();
                if let Some(sn) = global::get_default_svr_name() {
                    svr_name = sn;
                }
                let svr_arr: Rc<VecModel<ModelRc<slint::SharedString>>> = Rc::new(VecModel::default());
                let svritems: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());
                {
                    let mut sif_vec = Vec::new();
                    {
                        SIFMap.iter().for_each(|sif| {
                            let svr_items: Rc<VecModel<slint::SharedString>> = Rc::new(VecModel::default());
                            svr_items.push(sif.1.Name.clone().into());
                            svr_items.push(slint::format!("{}", get_clitype_name(sif.1.CliType)));
                            svr_items.push(sif.1.Host.clone().into());
                            svr_items.push(slint::format!("{}", sif.1.Port));

                            #[cfg(feature = "trade")]
                            if EClientType::StkOptTrd as i32 == sif.1.CliType
                                || EClientType::StkTrd as i32 == sif.1.CliType
                                || EClientType::StkCreditTrd as i32 == sif.1.CliType
                                || EClientType::FutTrd as i32 == sif.1.CliType
                            {
                                sif_vec.push(sif.1.Name.clone());
                                svr_arr.push(svr_items.into());
                            }

                            #[cfg(feature = "risk")]
                            if EClientType::StkOpt as i32 == sif.1.CliType
                                || EClientType::Stk as i32 == sif.1.CliType
                                || EClientType::StkCredit as i32 == sif.1.CliType
                                || EClientType::Fut as i32 == sif.1.CliType
                            {
                                sif_vec.push(sif.1.Name.clone());
                                svr_arr.push(svr_items.into());
                            }
                        });
                    }
                    sif_vec.sort_by(|a, b| a.cmp(&b));
                    sif_vec.iter().for_each(|sif| {
                        svritems.push(ListViewItem {
                            text: sif.clone().into(),
                            ..Default::default()
                        });
                    });

                    if !sif_vec.contains(&svr_name) {
                        svr_name = "".to_owned();
                        if sif_vec.len() > 0 {
                            svr_name = sif_vec[0].clone();
                        }
                    }
                }

                wapp.set_svr_arr(svr_arr.into());
                wapp.set_svr_model(ModelRc::from(svritems));
                if !svr_name.is_empty() {
                    wapp.set_select_server(svr_name.into());
                    wapp.invoke_cbx_server_sel_changed();
                }
            } else {
                wapp.invoke_change_win_func(1);
            }
        }

        // 获取历史用户
        {
            let path = {
                let cfg = global::CFG.get().unwrap().read().unwrap();
                std::format!("{}/{}", cfg.com.FPath.WorkDir, FilePathField::HIS_USER_INFO_PATH)
            };
            if let Some(hu_vec) = ticode::TICode::r(&path) {
                let userid_items: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());
                hu_vec.iter().for_each(|hu| {
                    userid_items.push(ListViewItem {
                        text: hu.text1.clone().into(),
                        ..Default::default()
                    });

                    hu_map_clone.insert(hu.text1.clone(), hu.text2.clone());
                });
                wapp.set_user_model(ModelRc::from(userid_items));
            }
        }

        true
    });

    let wapp = app.as_weak().unwrap();
    app.on_cbx_server_sel_changed(move || {
        wapp.set_process_status(2);
        wapp.set_enabled(false);
        wapp.set_process_tips("正在切换服务端...".into());

        wapp.set_vercode("".into());
        wapp.set_image_vercode(slint::Image::default());

        let svr_name = wapp.get_select_server().to_string();
        let app_weak = wapp.as_weak();
        global::TOKIO_RT.get().unwrap().spawn(async move {
            let ret = global::TOKIO_RT
                .get()
                .unwrap()
                .spawn_blocking(move || {
                    let rt = tokio::runtime::Handle::current();
                    rt.block_on(on_change_server(svr_name))
                })
                .await;

            let _ = app_weak.upgrade_in_event_loop(move |app| {
                app.set_process_status(0);
                app.set_enabled(true);

                match ret {
                    Ok(ret) => match ret {
                        Ok(_) => app.invoke_vercode_clicked(),
                        Err(err) => app.set_msgtip((err.into(), 1, "切换服务端失败".into())),
                    },
                    Err(err) => {
                        app.set_msgtip((
                            slint::format!("启动切换服务端任务失败. {:?}", err),
                            1,
                            "切换服务端失败".into(),
                        ));
                    }
                }
            });
        });
    });

    let hu_map_clone = hu_map.clone();
    let wapp = app.as_weak().unwrap();
    app.on_cbx_user_text_changed(move || {
        let uid = wapp.get_userid().to_string();
        if hu_map_clone.contains_key(&uid) {
            let passwd = hu_map_clone.get(&uid).unwrap().value().clone();
            wapp.set_rem_passwd(!passwd.is_empty());
            wapp.set_passwd(passwd.into());
        } else {
            wapp.set_rem_passwd(false);
            wapp.set_passwd("".into());
        }
    });

    let wapp = app.as_weak().unwrap();
    app.on_vercode_clicked(move || {
        wapp.set_enabled(false);
        wapp.set_process_tips("正在刷新验证码...".into());
        wapp.set_process_status(1);
        wapp.set_vercode("".into());
        wapp.set_image_vercode(slint::Image::default());

        let app_weak = wapp.as_weak();
        global::TOKIO_RT.get().unwrap().spawn(async move {
            let ret = global::TOKIO_RT
                .get()
                .unwrap()
                .spawn_blocking(move || {
                    let rt = tokio::runtime::Handle::current();
                    rt.block_on(global::get_vercode())
                })
                .await;

            let _ = app_weak.upgrade_in_event_loop(move |app| {
                app.set_process_status(0);
                app.set_enabled(true);

                match ret {
                    Ok(ret) => match ret {
                        Ok(vc) => {
                            app.set_vc_seqnum(vc.0);

                            let vc_rgb_img = vc.1.to_rgb8();
                            let mut index = 0;
                            let mut pixel_buffer =
                                slint::SharedPixelBuffer::<slint::Rgb8Pixel>::new(vc_rgb_img.width(), vc_rgb_img.height());
                            let pixel_buffer_bytes = pixel_buffer.make_mut_bytes();
                            for byte in vc_rgb_img.bytes() {
                                pixel_buffer_bytes[index] = byte.unwrap();
                                index = index + 1;
                            }
                            app.set_image_vercode(slint::Image::from_rgb8(pixel_buffer));
                        }
                        Err(err) => {
                            app.set_msgtip((err.into(), 1, "刷新验证码失败".into()));
                        }
                    },
                    Err(err) => {
                        app.set_msgtip((
                            slint::format!("启动刷新验证码任务失败. {:?}", err),
                            1,
                            "刷新验证码失败".into(),
                        ));
                    }
                }
            });
        });
    });

    let wapp = app.as_weak().unwrap();
    let hu_map_clone = hu_map.clone();
    app.on_btn_login_clicked(move || {
        let uid = wapp.get_userid().to_string();
        let passwd = wapp.get_passwd().to_string();
        let vc_seqnum = wapp.get_vc_seqnum();
        let vc_code = wapp.get_vercode().to_string();
        let rem_passwd = wapp.get_rem_passwd();

        // 参数检查
        {
            if wapp.get_select_server().is_empty() {
                wapp.set_msgtip(("请选择要登录的服务器".into(), 1, "".into()));
                return;
            }
            if uid.is_empty() {
                wapp.set_msgtip(("用户名不能为空".into(), 1, "".into()));
                return;
            }
            if passwd.is_empty() {
                wapp.set_msgtip(("密码不能为空".into(), 1, "".into()));
                return;
            }
            if vc_code.is_empty() {
                wapp.set_msgtip(("验证码不能为空".into(), 1, "".into()));
                return;
            }
        }

        wapp.set_process_tips("".into());
        wapp.set_process_status(1);
        wapp.set_enabled(false);

        let app_weak = wapp.as_weak();
        let hu_map_clone = hu_map_clone.clone();
        global::TOKIO_RT.get().unwrap().spawn(async move {
            let app_weak_proc = app_weak.clone();
            let hu_map_clone = hu_map_clone.clone();
            let ret = global::TOKIO_RT
                .get()
                .unwrap()
                .spawn_blocking(move || {
                    let rt = tokio::runtime::Handle::current();
                    rt.block_on(on_login(
                        uid,
                        passwd,
                        vc_seqnum,
                        vc_code,
                        rem_passwd,
                        hu_map_clone,
                        move |msg| {
                            let _ = app_weak_proc.upgrade_in_event_loop(move |app| {
                                app.set_process_tips(msg.into());
                            });
                        },
                    ))
                })
                .await;

            let _ = app_weak.upgrade_in_event_loop(move |app| match ret {
                Ok(ret) => match ret {
                    Ok(_) => {
                        app.invoke_login_success();
                        app.set_logret(true);
                    }
                    Err(err) => {
                        app.set_process_status(0);
                        app.set_enabled(true);
                        app.set_msgtip((err.into(), 1, "登录失败".into()));
                        app.invoke_vercode_clicked();
                    }
                },
                Err(err) => {
                    app.set_process_status(0);
                    app.set_enabled(true);
                    app.set_msgtip((slint::format!("启动登录任务失败. {:?}", err), 1, "登录失败".into()));
                    app.invoke_vercode_clicked();
                }
            });
        });
    });

    let wapp = app.as_weak().unwrap();
    app.on_btn_add_new_server(move || {
        let mut sif = ServerInfoField::default();
        {
            sif.Name = {
                let ret = wapp.get_set_svrname().to_string();
                let ret = ret.trim();
                if ret.is_empty() {
                    wapp.set_msgtip(("配置的名称不能为空".into(), 1, "".into()));
                    return false;
                }
                ret.to_owned()
            };
            sif.CliType = {
                let ret = wapp.get_set_clitype().to_string();
                let clitype = match ret.as_str() {
                    "个股期权风控" => 100,
                    "个股期权交易" => 101,
                    "现货风控" => 200,
                    "现货交易" => 201,
                    "现货风控(两融)" => 202,
                    "现货交易(两融)" => 203,
                    "期货风控" => 300,
                    "期货交易" => 301,
                    _ => 0,
                };
                if 0 == clitype {
                    wapp.set_msgtip(("类型未选择".into(), 1, "".into()));
                    return false;
                }
                clitype
            };
            sif.Host = {
                let ret = wapp.get_set_host().to_string();
                let ret = ret.trim();
                if ret.is_empty() {
                    wapp.set_msgtip(("地址不能为空".into(), 1, "".into()));
                    return false;
                }
                ret.to_owned()
            };
            sif.Port = {
                let ret = wapp.get_set_port().to_string();
                let ret = ret.trim().parse::<i32>();
                if ret.is_err() {
                    wapp.set_msgtip(("请输入正确的端口号".into(), 1, "".into()));
                    return false;
                }
                let port = ret.unwrap();
                if port <= 0 || port >= 65536 {
                    wapp.set_msgtip(("请输入正确的端口号".into(), 1, "".into()));
                    return false;
                }

                port
            };
        }

        let mut cfg = global::CFG.get().unwrap().write().unwrap();

        if cfg.com.SIFMap.contains_key(&sif.Name) {
            wapp.set_msgtip(("配置的名称重复".into(), 1, "".into()));
            return false;
        }
        cfg.com.SIFMap.insert(sif.Name.clone(), sif);

        // 保存到文件
        let sif_vec = cfg.com.SIFMap.iter().map(|sif| sif.1.clone()).collect::<Vec<_>>();
        let path = std::format!("{}/{}", cfg.com.FPath.ResDir, FilePathField::SERVER_INFO_PATH);
        global::serialize_write_user_data(&path, &sif_vec, true);

        let svr_arr: Rc<VecModel<ModelRc<slint::SharedString>>> = Rc::new(VecModel::default());
        cfg.com.SIFMap.iter().for_each(|sif| {
            let svr_items: Rc<VecModel<slint::SharedString>> = Rc::new(VecModel::default());
            svr_items.push(sif.1.Name.clone().into());
            svr_items.push(slint::format!("{}", get_clitype_name(sif.1.CliType)));
            svr_items.push(sif.1.Host.clone().into());
            svr_items.push(slint::format!("{}", sif.1.Port));

            #[cfg(feature = "trade")]
            if EClientType::StkOptTrd as i32 == sif.1.CliType
                || EClientType::StkTrd as i32 == sif.1.CliType
                || EClientType::StkCreditTrd as i32 == sif.1.CliType
                || EClientType::FutTrd as i32 == sif.1.CliType
            {
                svr_arr.push(svr_items.into());
            }

            #[cfg(feature = "risk")]
            if EClientType::StkOpt as i32 == sif.1.CliType
                || EClientType::Stk as i32 == sif.1.CliType
                || EClientType::StkCredit as i32 == sif.1.CliType
                || EClientType::Fut as i32 == sif.1.CliType
            {
                svr_arr.push(svr_items.into());
            }
        });
        wapp.set_svr_arr(svr_arr.into());

        true
    });

    let wapp = app.as_weak().unwrap();
    app.on_btn_del_server(move |svrname| {
        if svrname.is_empty() {
            wapp.set_msgtip(("未选择要删除的配置".into(), 1, "".into()));
            return false;
        }

        let mut cfg = global::CFG.get().unwrap().write().unwrap();
        if !cfg.com.SIF.Name.is_empty() && cfg.com.SIF.Name == svrname.to_string() {
            wapp.set_msgtip(("当前选择删除的配置为登录界面已选择配置, 不能删除".into(), 1, "".into()));
            return false;
        }

        cfg.com.SIFMap.remove(svrname.as_str());

        // 保存到文件
        let sif_vec = cfg.com.SIFMap.iter().map(|sif| sif.1.clone()).collect::<Vec<_>>();
        let path = std::format!("{}/{}", cfg.com.FPath.ResDir, FilePathField::SERVER_INFO_PATH);
        global::serialize_write_user_data(&path, &sif_vec, true);

        let svr_arr: Rc<VecModel<ModelRc<slint::SharedString>>> = Rc::new(VecModel::default());
        cfg.com.SIFMap.iter().for_each(|sif| {
            let svr_items: Rc<VecModel<slint::SharedString>> = Rc::new(VecModel::default());
            svr_items.push(sif.1.Name.clone().into());
            svr_items.push(slint::format!("{}", get_clitype_name(sif.1.CliType)));
            svr_items.push(sif.1.Host.clone().into());
            svr_items.push(slint::format!("{}", sif.1.Port));

            #[cfg(feature = "trade")]
            if EClientType::StkOptTrd as i32 == sif.1.CliType
                || EClientType::StkTrd as i32 == sif.1.CliType
                || EClientType::StkCreditTrd as i32 == sif.1.CliType
                || EClientType::FutTrd as i32 == sif.1.CliType
            {
                svr_arr.push(svr_items.into());
            }

            #[cfg(feature = "risk")]
            if EClientType::StkOpt as i32 == sif.1.CliType
                || EClientType::Stk as i32 == sif.1.CliType
                || EClientType::StkCredit as i32 == sif.1.CliType
                || EClientType::Fut as i32 == sif.1.CliType
            {
                svr_arr.push(svr_items.into());
            }
        });
        wapp.set_svr_arr(svr_arr.into());

        true
    });

    let wapp = app.as_weak().unwrap();
    app.on_btn_back_login(move || {
        let SIFMap = { global::CFG.get().unwrap().read().unwrap().com.SIFMap.clone() };

        let svritems: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());
        let mut sif_vec = Vec::new();
        {
            SIFMap.iter().for_each(|sif| {
                #[cfg(feature = "trade")]
                if EClientType::StkOptTrd as i32 == sif.1.CliType
                    || EClientType::StkTrd as i32 == sif.1.CliType
                    || EClientType::StkCreditTrd as i32 == sif.1.CliType
                    || EClientType::FutTrd as i32 == sif.1.CliType
                {
                    sif_vec.push(sif.1.Name.clone());
                }

                #[cfg(feature = "risk")]
                if EClientType::StkOpt as i32 == sif.1.CliType
                    || EClientType::Stk as i32 == sif.1.CliType
                    || EClientType::StkCredit as i32 == sif.1.CliType
                    || EClientType::Fut as i32 == sif.1.CliType
                {
                    sif_vec.push(sif.1.Name.clone());
                }
            });
        }
        sif_vec.sort_by(|a, b| a.cmp(&b));
        sif_vec.iter().for_each(|sif| {
            svritems.push(ListViewItem {
                text: sif.clone().into(),
                ..Default::default()
            });
        });
        wapp.set_svr_model(ModelRc::from(svritems));
    });

    let wapp = app.as_weak().unwrap();
    app.window().on_close_requested(move || {
        if wapp.invoke_close_set() {
            slint::CloseRequestResponse::KeepWindowShown
        } else {
            slint::CloseRequestResponse::HideWindow
        }
    });

    true
}
