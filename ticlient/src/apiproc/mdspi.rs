#![allow(unused_variables)]

use std::sync::{Arc, RwLock};

use mdapi::{
    self,
    mdapi::IMdSpi,
    protocol::{
        ptapp::AppHeartbeat,
        ptmd::{MdSubscribeField, PTRtnMarketDataL1},
    },
};

use super::mdbuf::MdBuf;

pub struct MdSpi {
    buf: Option<Arc<RwLock<MdBuf>>>,
}

impl MdSpi {
    pub fn new() -> Self {
        Self { buf: None }
    }

    pub fn register_buf(&mut self, buf: Arc<RwLock<MdBuf>>) {
        self.buf = Some(buf);
    }
}

impl IMdSpi for MdSpi {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    /// <br> `heartbeat` 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeat) {}

    /// 请求行情登录响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_login(&self, ec: i32, em: &String, reqid: i32, islast: i32) {
        if let Some(buf) = self.buf.as_ref() {
            buf.write().unwrap().on_rsp_login(ec, em);
        }
    }

    /// 请求行情登出响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_logout(&self, ec: i32, em: &String, reqid: i32, islast: i32) {
        if let Some(buf) = self.buf.as_ref() {
            buf.write().unwrap().on_rsp_logout(ec, em);
        }
    }

    /// 请求行情订阅响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`sub`: 行情订阅字段
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_md_subscribe(&self, ec: i32, em: &String, sub: &MdSubscribeField, reqid: i32, islast: i32) {
        if let Some(buf) = self.buf.as_ref() {
            buf.write().unwrap().on_rsp_md_subscribe(ec, em, sub);
        }
    }

    /// 请求行情退订阅响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`sub`: 行情退订阅字段
    /// <br>`reqid`: 请求编号
    /// <br>`islast`: 是否最后一条
    fn on_rsp_md_un_subscribe(&self, ec: i32, em: &String, sub: &MdSubscribeField, reqid: i32, islast: i32) {
        if let Some(buf) = self.buf.as_ref() {
            buf.write().unwrap().on_rsp_md_un_subscribe(ec, em, sub);
        }
    }

    /// L1行情推送
    /// <br>`md`: L1行情
    fn on_rtn_market_data_l1(&self, md: PTRtnMarketDataL1) {
        if let Some(buf) = self.buf.as_ref() {
            buf.write().unwrap().on_rtn_market_data_l1(md);
        }
    }
}
