use std::{
    rc::Rc,
    sync::{Arc, OnceLock},
};

use crate::{
    common::{
        self,
        global::{CFG, TOKIO_RT, TRADE_STK_API, TRADE_STK_BUF},
    },
    show_msg_box,
    slintui::*,
    ui::tradecom::transresault::{TransResault, TransResaultMap},
};
use slint::*;
use tiapi::protocol_pub::position_trade_ctp::{CtpReqQryPositionTransferField, CtpReqRePositionTransferField};

/// 划转详情数据
struct TransDtlDatas {
    rst_map: Arc<TransResaultMap>,
}
impl TransDtlDatas {
    pub fn new() -> Self {
        Self {
            rst_map: Arc::new(TransResaultMap::new()),
        }
    }
}
static TRANS_DTL_DATAS: OnceLock<TransDtlDatas> = OnceLock::new();

/// 查询 - 股份划转详情
pub(super) struct QueryPositionTransDtl {
    clientid_set: Arc<dashmap::DashSet<String>>,
}

impl QueryPositionTransDtl {
    pub fn new() -> Self {
        Self {
            clientid_set: Arc::new(dashmap::DashSet::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = TRANS_DTL_DATAS.set(TransDtlDatas::new());

        let app_qry = app.global::<StkTrd_PositionTransDtl>();

        let cliid_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());
        if { common::global::CFG.get().unwrap().read().unwrap().com.SvrBuildDate } < 20250328 {
            // 服务端版本在20250328及之后的版本支持分页查询, 此时不能一次查询所有交易编码
            cliid_arr.push(ListViewItem {
                text: "".into(),
                ..Default::default()
            });
        }

        let tclientid_vec = { TRADE_STK_BUF.get().unwrap().read().unwrap().get_tradingcode_all() };
        tclientid_vec.iter().for_each(|tc| {
            cliid_arr.push(ListViewItem {
                text: tc.ClientID.clone().into(),
                ..Default::default()
            });
            self.clientid_set.insert(tc.ClientID.clone());
        });

        app_qry.set_clientid_model(cliid_arr.into());

        app_qry.set_tradedate(std::format!("{}", CFG.get().unwrap().read().unwrap().run.TradingDay).into());

        let app_weak = app.as_weak();
        let clientid_set: Arc<dashmap::DashSet<String>> = self.clientid_set.clone();
        app_qry.on_qry_clieked(move || QueryPositionTransDtl::on_qry_clieked(&app_weak, &clientid_set));

        let app_weak = app.as_weak();
        app_qry.on_page_index_changed(move |index| QueryPositionTransDtl::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        app_qry.on_page_size_changed(move |size| QueryPositionTransDtl::on_page_size_changed(&app_weak, size));

        let app_weak = app.as_weak();
        app_qry.on_re_position_trans(move || QueryPositionTransDtl::on_re_position_trans(&app_weak));

        let app_weak = app.as_weak();
        app_qry.on_get_row_data_color(move |row_index, column_index, data| {
            QueryPositionTransDtl::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Query position trans detail init completed");
    }
}

impl QueryPositionTransDtl {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        let var = data.as_str();

        // 划转方向
        if 1 == column_index {
            if "划入" == var {
                return nc.get_normal();
            } else if "划出" == var {
                return nc.get_caution();
            }
        }

        // 划转数量
        if 5 == column_index {
            if let Ok(var) = data.replace(",", "").parse::<f64>() {
                if var >= 0. {
                    return nc.get_normal();
                }
                return nc.get_caution();
            }
        }

        // 状态
        if 6 == column_index {
            if "成功" == var {
                return nc.get_normal();
            } else if "失败" == var {
                return nc.get_error();
            } else if "未操作" == var {
                return nc.get_prompt();
            } else {
                return nc.get_error();
            }
        }

        // 是否冲正
        if 7 == column_index {
            if "是" == var {
                return nc.get_prompt();
            }
        }

        nc.get_default()
    }

    // 查询
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qry = app.global::<StkTrd_PositionTransDtl>();
        let trans_datas = TRANS_DTL_DATAS.get().unwrap();

        // 清空上一次结果
        trans_datas.rst_map.clear();
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qry.set_row_data(row_data.clone().into());

        let page_size = app_qry.get_page_size();

        let mut req = CtpReqQryPositionTransferField::default();
        req.ClientID = app_qry.get_cliid().trim().to_owned();
        req.Passwd = app_qry.get_passwd().as_str().into();
        req.BeginNo = if is_qry_btn_clicked {
            1
        } else {
            1 + app_qry.get_page_index() * page_size
        };
        req.EndNo = req.BeginNo + page_size - 1;
        req.TradeDate = app_qry.get_tradedate().trim().parse::<i32>().unwrap_or_default();
        if req.TradeDate <= 0 {
            show_msg_box(&app, 3, Default::default(), "请输入操作日期.\n\n格式: yyyyMMdd".into());
            return;
        }
        let tradestatus = app_qry.get_transstatus();
        if !tradestatus.is_empty() {
            if "失败" == tradestatus.as_str() {
                req.TransStatus = -1;
            } else if "成功" == tradestatus.as_str() {
                req.TransStatus = 1;
            }
        } else {
            req.TransStatus = 100;
        }

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let mut rsp = Vec::new();
            let resault = {
                TRADE_STK_API
                    .get()
                    .unwrap()
                    .read()
                    .await
                    .req_qry_positiontrans(&req, &mut rsp)
                    .await
            };

            if let Err(err) = resault {
                let _ = wapp.upgrade_in_event_loop(move |app| {
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("请求查询股份划转记录失败.\n\n{}", err),
                    );
                });

                return;
            }

            if rsp.is_empty() {
                let _ = wapp.upgrade_in_event_loop(|app| {
                    show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
                });
                return;
            }

            if is_qry_btn_clicked {
                let rsptotnum = rsp[0].rsptotnum;
                let page_total = rsptotnum / page_size + {
                    if 0 == rsptotnum % page_size {
                        0
                    } else {
                        1
                    }
                };

                let _ = wapp.upgrade_in_event_loop(move |app| {
                    let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());
                    for idx in 1..=page_total {
                        page_index_data.push(ListViewItem {
                            text: slint::format!("{}/{}", idx, page_total),
                            ..Default::default()
                        });
                    }

                    let app_qry = app.global::<StkTrd_PositionTransDtl>();
                    app_qry.set_page_index_model(page_index_data.into());
                    app_qry.set_item_total(rsptotnum);
                    app_qry.set_page_index(0);
                    app_qry.set_page_total(page_total);
                });
            }

            let trans_datas = TRANS_DTL_DATAS.get().unwrap();

            // 仅当前交易日可以继续划转
            if req.TradeDate == { CFG.get().unwrap().read().unwrap().run.TradingDay } {
                rsp.iter().for_each(|pt| {
                    let (out_cnt, in_cnt) = match (pt.volume, pt.reversal) {
                        (volume, 0) if volume < 0 => (1, 0),  // 本次是划出
                        (volume, 0) if volume >= 0 => (0, 1), // 本次是划入
                        _ => (0, 0),
                    };

                    let (out_success_cnt, in_success_cnt) = match (pt.status, pt.volume, pt.reversal) {
                        (1, volume, 0) if volume < 0 => (1, 0),   // 划出成功 - 正常
                        (1, volume, _) if volume < 0 => (-1, 0),  // 划出成功 - 冲正
                        (1, volume, 0) if volume >= 0 => (0, 1),  // 划入成功 - 正常
                        (1, volume, _) if volume >= 0 => (0, -1), // 划入成功 - 冲正
                        _ => (0, 0),
                    };

                    if !trans_datas.rst_map.contains_key(&pt.transferno) {
                        trans_datas.rst_map.insert(pt.transferno.clone(), TransResault::new());
                    }
                    let mut tr = trans_datas.rst_map.get_mut(&pt.transferno).unwrap();
                    tr.in_cnt += in_cnt;
                    tr.out_cnt += out_cnt;
                    tr.in_success_cnt += in_success_cnt;
                    tr.out_success_cnt += out_success_cnt;
                });
            }

            let _ = wapp.upgrade_in_event_loop(move |app| {
                let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
                rsp.iter().for_each(|pt| {
                    let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                    // 序号
                    items.push(slint::format!("{}", pt.rspseqno));

                    // 划转方向
                    if pt.volume >= 0 {
                        items.push("划入".into());
                    } else {
                        items.push("划出".into());
                    }

                    // 划转对象
                    items.push(pt.serverid.clone().into());

                    // 股东代码
                    items.push(pt.clientid.clone().into());

                    // 合约编码
                    items.push(pt.instrumentid.clone().into());

                    // 划转数量
                    items.push(slint::format!("{}", pt.volume));

                    // 状态
                    if 1 == pt.status {
                        items.push("成功".into());
                    } else if -1 == pt.status {
                        items.push("失败".into());
                    } else if 0 == pt.status {
                        items.push("未操作".into());
                    } else {
                        items.push(slint::format!("未知:{}", pt.status));
                    }

                    // 是否冲正账
                    if 0 == pt.reversal {
                        items.push("".into());
                    } else if 1 == pt.reversal {
                        items.push("冲正".into());
                    } else {
                        items.push(slint::format!("未知:{}", pt.status));
                    }

                    // 操作日期
                    items.push(pt.tradeday.to_string().into());

                    // 操作时间
                    items.push(pt.logtime.clone().into());

                    // 消息
                    items.push(pt.message.clone().into());

                    // 继续划转
                    let (in_can_trans, out_can_trans) = trans_datas
                        .rst_map
                        .get(&pt.transferno)
                        .map_or((false, false), |tr| (tr.in_can_trans(), tr.out_can_trans()));
                    items.push(
                        match pt.volume {
                            volume if volume >= 0 && in_can_trans => "继续划转",
                            volume if volume < 0 && out_can_trans => "继续划转",
                            _ => "",
                        }
                        .into(),
                    );

                    // 划转序号(未显示列,用于继续划转时提取划转序号使用)
                    items.push(pt.transferno.clone().into());

                    row_data.push(items.into());
                });

                app.global::<StkTrd_PositionTransDtl>().set_row_data(row_data.into());
            });
        });
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>, cliid_set: &Arc<dashmap::DashSet<String>>) {
        // 服务端版本在20250328及之后的版本支持分页查询, 之前的版本忽略相关字段. 此时 admin 对应的版本如果需要分页字段则会返回查询错误否则返回全部数据
        if { common::global::CFG.get().unwrap().read().unwrap().com.SvrBuildDate } >= 20250328 {
            QueryPositionTransDtl::on_query(app_weak, true);
            return;
        }

        // 按以前的未分页的方式查询 - 防止 admin 未更新

        let app = app_weak.unwrap();
        let app_qry = app.global::<StkTrd_PositionTransDtl>();
        let trans_datas = TRANS_DTL_DATAS.get().unwrap();

        // 清空上一次结果
        trans_datas.rst_map.clear();
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qry.set_row_data(row_data.clone().into());

        let mut cliid_vec = Vec::new();
        let clientid = app_qry.get_cliid().to_string();
        if clientid.trim().is_empty() {
            if cliid_set.is_empty() {
                show_msg_box(
                    &app,
                    3,
                    Default::default(),
                    slint::format!("请求查询股份划转记录失败.\n\n系统自动获取股东代码失败,请手动输入"),
                );
                return;
            }

            for cliid in cliid_set.iter() {
                cliid_vec.push(cliid.to_owned());
            }
        } else {
            cliid_vec.push(clientid.trim().to_owned());
        }

        let mut req = CtpReqQryPositionTransferField::default();
        req.Passwd = app_qry.get_passwd().as_str().into();
        let tradedate = app_qry.get_tradedate();
        if !tradedate.is_empty() {
            if let Ok(td) = tradedate.parse::<i32>() {
                req.TradeDate = td;
            }
        }
        let tradestatus = app_qry.get_transstatus();
        if !tradestatus.is_empty() {
            if "失败" == tradestatus.as_str() {
                req.TransStatus = -1;
            } else if "成功" == tradestatus.as_str() {
                req.TransStatus = 1;
            }
        } else {
            req.TransStatus = 100;
        }

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let mut rsp = Vec::new();
            let api = TRADE_STK_API.get().unwrap().read().await;
            for cliid in cliid_vec.iter() {
                req.ClientID = cliid.to_owned();

                if let Err(err) = api.req_qry_positiontrans(&req, &mut rsp).await {
                    let _ = wapp.upgrade_in_event_loop(move |app| {
                        show_msg_box(
                            &app,
                            3,
                            Default::default(),
                            slint::format!("请求查询股份划转记录失败.\n\n{}", err),
                        );
                    });
                    return;
                }
            }
            if rsp.is_empty() {
                let _ = wapp.upgrade_in_event_loop(|app| {
                    show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
                });
                return;
            }

            rsp.sort_by(|a, b| {
                let b_no = std::format!("{:>13}", b.transferno);
                let a_no = std::format!("{:>13}", a.transferno);
                b_no.cmp(&a_no)
            });

            let _ = wapp.upgrade_in_event_loop(move |app| {
                let app_qry = app.global::<StkTrd_PositionTransDtl>();
                let trans_datas = TRANS_DTL_DATAS.get().unwrap();

                // 仅当前交易日可以继续划转
                if app_qry.get_tradedate().parse::<i32>().unwrap_or_default() == {
                    CFG.get().unwrap().read().unwrap().run.TradingDay
                } {
                    rsp.iter().for_each(|pt| {
                        let (out_cnt, in_cnt) = match (pt.volume, pt.reversal) {
                            (volume, 0) if volume < 0 => (1, 0),  // 本次是划出
                            (volume, 0) if volume >= 0 => (0, 1), // 本次是划入
                            _ => (0, 0),
                        };

                        let (out_success_cnt, in_success_cnt) = match (pt.status, pt.volume, pt.reversal) {
                            (1, volume, 0) if volume < 0 => (1, 0),   // 划出成功 - 正常
                            (1, volume, _) if volume < 0 => (-1, 0),  // 划出成功 - 冲正
                            (1, volume, 0) if volume >= 0 => (0, 1),  // 划入成功 - 正常
                            (1, volume, _) if volume >= 0 => (0, -1), // 划入成功 - 冲正
                            _ => (0, 0),
                        };

                        if !trans_datas.rst_map.contains_key(&pt.transferno) {
                            trans_datas.rst_map.insert(pt.transferno.clone(), TransResault::new());
                        }
                        let mut tr = trans_datas.rst_map.get_mut(&pt.transferno).unwrap();
                        tr.in_cnt += in_cnt;
                        tr.out_cnt += out_cnt;
                        tr.in_success_cnt += in_success_cnt;
                        tr.out_success_cnt += out_success_cnt;
                    });
                }

                let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
                rsp.iter().for_each(|pt| {
                    let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                    // 序号
                    items.push(slint::format!("{}", pt.rspseqno));

                    // 划转方向
                    if pt.volume >= 0 {
                        items.push("划入".into());
                    } else {
                        items.push("划出".into());
                    }

                    // 划转对象
                    items.push(pt.serverid.clone().into());

                    // 股东代码
                    items.push(pt.clientid.clone().into());

                    // 合约编码
                    items.push(pt.instrumentid.clone().into());

                    // 划转数量
                    items.push(slint::format!("{}", pt.volume));

                    // 状态
                    if 1 == pt.status {
                        items.push("成功".into());
                    } else if -1 == pt.status {
                        items.push("失败".into());
                    } else if 0 == pt.status {
                        items.push("未操作".into());
                    } else {
                        items.push(slint::format!("未知:{}", pt.status));
                    }

                    // 是否冲正账
                    if 0 == pt.reversal {
                        items.push("".into());
                    } else if 1 == pt.reversal {
                        items.push("冲正".into());
                    } else {
                        items.push(slint::format!("未知:{}", pt.status));
                    }

                    // 操作日期
                    items.push(pt.tradeday.to_string().into());

                    // 操作时间
                    items.push(pt.logtime.clone().into());

                    // 消息
                    items.push(pt.message.clone().into());

                    // 继续划转
                    let (in_can_trans, out_can_trans) = trans_datas
                        .rst_map
                        .get(&pt.transferno)
                        .map_or((false, false), |tr| (tr.in_can_trans(), tr.out_can_trans()));
                    items.push(
                        match pt.volume {
                            volume if volume >= 0 && in_can_trans => "继续划转",
                            volume if volume < 0 && out_can_trans => "继续划转",
                            _ => "",
                        }
                        .into(),
                    );

                    // 划转序号(未显示列,用于继续划转时提取划转序号使用)
                    items.push(pt.transferno.clone().into());

                    row_data.push(items.into());
                });

                app_qry.set_row_data(row_data.into());
            });
        });
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryPositionTransDtl::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryPositionTransDtl::on_query(app_weak, true);
    }

    /// 股份继续划转事件
    fn on_re_position_trans(app_weak: &Weak<App>) {
        let app = app_weak.unwrap();
        let app_qry = app.global::<StkTrd_PositionTransDtl>();

        let mut req = CtpReqRePositionTransferField::default();
        {
            req.AccountID = "".into();
            req.ClientID = app_qry.get_sel_cliid().as_str().into();
            req.Passwd = app_qry.get_passwd().as_str().into();
            req.TransNo = app_qry.get_sel_transno().as_str().into();
            req.TradeDate = {
                let tradedate = app_qry.get_sel_tradedate().to_string();
                if let Ok(td) = tradedate.parse::<i32>() {
                    td
                } else {
                    -1
                }
            }
        };

        let wapp = app.as_weak();
        TOKIO_RT.get().unwrap().spawn(async move {
            let mut rsp = Vec::new();
            let resault = {
                TRADE_STK_API
                    .get()
                    .unwrap()
                    .read()
                    .await
                    .req_re_positiontrans(&req, &mut rsp)
                    .await
            };

            if let Err(err) = resault {
                let _ = wapp.upgrade_in_event_loop(move |app| {
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("请求股份继续划转失败.\n\n{}", err),
                    );
                });

                return;
            }

            let mut success_cnt = 0;
            let mut all_cnt = 0;
            let mut errmsg = String::new();
            rsp.iter().for_each(|pt| {
                all_cnt = pt.rsptotnum;

                if 0 == pt.status {
                    success_cnt += 1;
                } else {
                    errmsg.push_str(&std::format!("\n节点:{}, 错误信息:{}", pt.serverid, pt.responsestr));
                }
            });

            let _ = wapp.upgrade_in_event_loop(move |app| {
                if success_cnt == all_cnt {
                    show_msg_box(
                        &app,
                        1,
                        Default::default(),
                        slint::format!("请求股份继续划转成功. 已按当前查询条件自动刷新查询结果"),
                    );
                } else {
                    show_msg_box(
                        &app,
                        if success_cnt > 0 { 2 } else { 3 },
                        Default::default(),
                        if success_cnt > 0 {
                            slint::format!(
                                "请求股份继续划转部分成功({}/{})\n\n错误消息:{}\n\n已按当前查询条件自动刷新查询结果",
                                success_cnt,
                                all_cnt,
                                errmsg
                            )
                        } else {
                            slint::format!("请求股份继续划转失败.\n\n错误消息:{}", errmsg)
                        },
                    );
                }

                if success_cnt > 0 {
                    app.global::<StkTrd_PositionTransDtl>().invoke_qry_clieked();
                }
            });
        });
    }
}
