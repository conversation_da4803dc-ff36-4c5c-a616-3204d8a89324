use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    common::{
        self,
        config::Config,
        global::{TRADE_STK_API, TRADE_STK_BUF},
        openselfile,
        ticonvert::TIConvert,
    },
    show_msg_box,
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::position_trade_ctp::{
    CtpPositionField, CtpReqPositionTransferField, CtpReqQryPositionField, CtpRspPositionTransferField,
};

type CtpPositionMap = dashmap::DashMap<String, CtpPositionField>;
type PosInsIDSet = dashmap::DashSet<String>;

/// 持仓数据
struct PositionDatas {
    /// 数据
    data_map: Arc<CtpPositionMap>,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 所有的持仓合约列表
    insid_set: Arc<PosInsIDSet>,

    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,

    /// 合约编码是否有编辑过 - 如果编辑过,需要重新更新下拉列表
    insid_edited: AtomicBool,

    /// 是否有新的持仓合约 - 如果有,需要重新更新下拉列表
    new_pos_insid: AtomicBool,
}
impl PositionDatas {
    pub fn new() -> Self {
        Self {
            data_map: Arc::new(CtpPositionMap::new()),
            data_changed: AtomicBool::new(false),
            insid_set: Arc::new(PosInsIDSet::new()),

            filter_clicked: AtomicBool::new(false),
            insid_edited: AtomicBool::new(false),
            new_pos_insid: AtomicBool::new(false),
        }
    }
}
static POSITION_DATAS: OnceLock<PositionDatas> = OnceLock::new();

// 持仓
pub(super) struct Position {}

/// 构建与初始化
impl Position {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = POSITION_DATAS.set(PositionDatas::new());

        let app_pos = app.global::<StkTrd_Position>();

        let app_weak = app.as_weak();
        app_pos.on_sort_ascending(move |column_index| Position::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_sort_descending(move |column_index| Position::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_pos.on_insid_text_changed(move || Position::on_insid_text_changed(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_filter_changed(move || Position::on_filter_changed(&app_weak));

        let app_weak = app.as_weak();
        app_pos.on_export_clicked(move |etype| Position::on_export_clicked(&app_weak, etype));

        let app_weak = app.as_weak();
        app_pos.on_req_pos_trans(move |pt| Position::on_req_pos_trans(&app_weak, pt));

        log::trace!("Position init completed");
    }
}

/// 与API的交互
impl Position {
    /// 请求查询持仓信息
    pub fn qry_position(&self) {
        let req = CtpReqQryPositionField::default();

        crate::common::global::TOKIO_RT.get().unwrap().spawn(async move {
            let api = TRADE_STK_API.get().unwrap().read().await;
            if let Err(err) = api.req_qry_investorposition(0, &req).await {
                log::warn!("req_qry_investorposition failed. {}", err);
            }
        });
    }

    /// 响应查询持仓信息
    pub fn on_rsp_qry_acc(&self, pos: CtpPositionField) {
        let pos_datas = POSITION_DATAS.get().unwrap();

        if !pos_datas.insid_set.contains(&pos.InstrumentID) {
            pos_datas.insid_set.insert(pos.InstrumentID.clone());
            POSITION_DATAS.get().unwrap().new_pos_insid.store(true, Ordering::Relaxed);
        }

        let mut insert_flag = true;
        let key = std::format!("{}{}{}", pos.InvestorID, pos.ExchangeID, pos.InstrumentID);

        if pos_datas.data_map.contains_key(&key) {
            let prepos = pos_datas.data_map.get(&key).unwrap();
            if pos == *prepos {
                insert_flag = false;
            }
        }

        if insert_flag {
            pos_datas.data_map.insert(key, pos);
            pos_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

/// 事件
impl Position {
    /// 升序
    fn on_sort_ascending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_pos = app.global::<StkTrd_Position>();
        let row_data = app_pos.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_pos.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_pos = app.global::<StkTrd_Position>();
        let row_data = app_pos.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_pos.set_row_data(sort_model.into());
    }

    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) {
        POSITION_DATAS.get().unwrap().insid_edited.store(true, Ordering::Relaxed);
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        POSITION_DATAS.get().unwrap().filter_clicked.store(true, Ordering::Relaxed);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();
        let is_credit = app.global::<StkTrd_Com>().get_iscredit();

        let default_name = std::format!("position_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出持仓失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if !is_credit {
            if let Err(err) = writer.write_record(&[
                "证券代码",
                "持仓量",
                "当前可用",
                "上日持仓量",
                "当日买入",
                "当日卖出",
                "划转量",
                "冻结持仓",
                "持仓均价",
                "持仓成本",
                "持仓盈亏",
                "交易所",
                "证券名称",
                "资金账户",
            ]) {
                show_msg_box(
                    &app,
                    3,
                    Default::default(),
                    slint::format!("导出持仓失败\n\n导出表头失败 \n{:?}", err),
                );
                return;
            }
        } else {
            if let Err(err) = writer.write_record(&[
                "证券代码",
                "持仓量",
                "当前可用",
                "上日持仓量",
                "当日买入",
                "当日卖出",
                "划转量",
                "冻结持仓",
                "持仓均价",
                "持仓成本",
                "最新价",
                "持仓市值",
                "持仓盈亏",
                "现券还券量",
                "融资已还折算量",
                "融资未还折算量",
                "折算率",
                "充抵保证金",
                "交易所",
                "证券名称",
                "资金账户",
            ]) {
                show_msg_box(
                    &app,
                    3,
                    Default::default(),
                    slint::format!("导出持仓失败\n\n导出表头失败 \n{:?}", err),
                );
                return;
            }
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<StkTrd_Position>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let pos_datas = POSITION_DATAS.get().unwrap();
            let data_map_clone = pos_datas.data_map.clone();

            data_map_clone.iter().for_each(|pos| {
                let items = Position::get_row_items(&pos, is_credit);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出持仓失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出持仓成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }

    // 股份划转
    fn on_req_pos_trans(app_weak: &Weak<App>, pt: PositionTransField) {
        let app = app_weak.unwrap();

        // 请求赋值与参数检查
        let mut req = CtpReqPositionTransferField::default();
        {
            req.AccountID = {
                let aid = pt.accountid.to_string();
                if aid.is_empty() {
                    show_msg_box(&app, 3, Default::default(), "资金账号不能为空".into());
                    app.global::<StkTrd_Position>().set_ctrls_enabled(true);
                    return;
                }
                aid
            };
            req.Passwd = pt.password.as_str().into();
            req.ExchID = TIConvert::exch_id(&pt.exchid);
            req.InstrumentID = {
                let insid = pt.insid.to_string();
                if insid.trim().is_empty() {
                    show_msg_box(&app, 3, Default::default(), "证券代码不能为空".into());
                    app.global::<StkTrd_Position>().set_ctrls_enabled(true);
                    return;
                }

                insid.trim().to_owned()
            };
            req.Dir = {
                if 1 != pt.transflag && 2 != pt.transflag {
                    show_msg_box(&app, 3, Default::default(), slint::format!("划转方向({})错误", pt.transflag));
                    app.global::<StkTrd_Position>().set_ctrls_enabled(true);
                    return;
                }

                pt.transflag
            };
            req.Volume = {
                let ret = pt.volume.replace(",", "").trim().parse::<i32>();
                if let Ok(vol) = ret {
                    if vol > 0 {
                        vol
                    } else {
                        show_msg_box(&app, 3, Default::default(), slint::format!("划转数量({})必须大于0", vol));
                        app.global::<StkTrd_Position>().set_ctrls_enabled(true);
                        return;
                    }
                } else {
                    let errmsg = ret.err().unwrap().to_string();
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("划转数量错误\n输入值:{}\n错误信息:{}", pt.volume, errmsg),
                    );
                    app.global::<StkTrd_Position>().set_ctrls_enabled(true);
                    return;
                }
            };
            req.ClientID = {
                let ret = TRADE_STK_BUF
                    .get()
                    .unwrap()
                    .read()
                    .unwrap()
                    .get_tradingcode(&pt.exchid, &req.AccountID);
                if let Some(tc) = ret {
                    tc.ClientID
                } else {
                    show_msg_box(
                        &app,
                        3,
                        Default::default(),
                        slint::format!("获取股东代码失败. 交易所:{}, 资金账户:{}", pt.exchid, pt.accountid),
                    );
                    app.global::<StkTrd_Position>().set_ctrls_enabled(true);
                    return;
                }
            };
        }

        // 请求股份划转
        let app_weak = app.as_weak();
        crate::common::global::TOKIO_RT.get().unwrap().spawn(async move {
            let mut rsp = CtpRspPositionTransferField::default();
            let api = TRADE_STK_API.get().unwrap().read().await;
            if let Err(err) = api.req_positiontrans(&req, &mut rsp).await {
                let _ = app_weak.upgrade_in_event_loop(move |app| {
                    show_msg_box(&app, 3, Default::default(), slint::format!("请求股份划转失败.\n\n{}", err));
                    app.global::<StkTrd_Position>().set_ctrls_enabled(true);
                });

                return;
            }

            // 检查划转结果
            let all_cnt = rsp.Arr.len() as i32;
            let mut src_success_cnt = 0;
            let mut dst_success_cnt = 0;
            let mut errmsg = String::new();
            rsp.Arr.iter().for_each(|ret| {
                if 1 == ret.status {
                    if ret.volume > 0 {
                        src_success_cnt += 1;
                    }
                    if ret.volume < 0 {
                        dst_success_cnt += 1;
                    }
                } else {
                    errmsg.push_str(&std::format!("\n节点:{}, 错误信息:{}", ret.serverid, ret.message));
                }
            });

            let _ = app_weak.upgrade_in_event_loop(move |app| {
                if src_success_cnt + dst_success_cnt == all_cnt && src_success_cnt >= 1 && dst_success_cnt >= 1 {
                    show_msg_box(&app, 1, Default::default(), "股份划转成功".into());
                } else {
                    if 0 == src_success_cnt && 0 == dst_success_cnt {
                        show_msg_box(&app, 3, Default::default(), slint::format!("请求股份划转失败\n\n{}", errmsg));
                    } else {
                        show_msg_box(
                            &app,
                            2,
                            Default::default(),
                            slint::format!(
                                "请求股份划转部分成功({}/{}).\n\n{}",
                                src_success_cnt + dst_success_cnt,
                                all_cnt,
                                errmsg
                            ),
                        );
                    }
                }
                app.global::<StkTrd_Position>().set_ctrls_enabled(true);
            });
        });
    }
}

/// 更新UI
impl Position {
    /// 获取一行数据
    fn get_row_items(pos: &CtpPositionField, is_credit: bool) -> Rc<VecModel<SharedString>> {
        let mut tp1 = 1;
        let mut multiple = 1;
        let mut insname = "".to_owned();
        let mut digits = Config::STK_PRICE_DIGITS;
        {
            if let Some(ins) = common::global::INS
                .get()
                .unwrap()
                .read()
                .unwrap()
                .get_ins(TIConvert::exch_id(&pos.ExchangeID), &pos.InstrumentID)
            {
                tp1 = ins.tp1;
                multiple = ins.multiple;
                insname = ins.name.clone();
                digits = ins.ptw as usize;
            }
        };

        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        if !is_credit {
            // 合约编码
            items.push(pos.InstrumentID.clone().into());

            // 持仓量
            items.push(TIConvert::format_i(pos.Position).into());

            // 当前可用
            let mut apos = pos.YdPosition - pos.CloseVolume - pos.LongFrozen + pos.SettlementID - pos.XYRqRePos;
            if 0 == tp1 {
                apos += pos.OpenVolume;
            }
            items.push(TIConvert::format_i(apos).into());

            // 上日持仓量
            items.push(TIConvert::format_i(pos.YdPosition).into());

            // 当日买入
            items.push(TIConvert::format_i(pos.OpenVolume).into());

            // 当日卖出
            items.push(TIConvert::format_i(pos.CloseVolume).into());

            // 划转量
            items.push(TIConvert::format_i(pos.SettlementID).into());

            // 冻结持仓量
            items.push(TIConvert::format_i(pos.LongFrozen).into());

            // 持仓均价
            let mut avgprice: f64 = 0.;
            if 0 != pos.Position {
                avgprice = pos.PositionCost / pos.Position as f64 / multiple as f64;
            }
            items.push(TIConvert::format_f(avgprice, digits).into());

            // 持仓成本
            items.push(TIConvert::format_f(pos.PositionCost, Config::AMT_DIGITS).into());

            // 持仓盈亏
            items.push(TIConvert::format_f(pos.PositionProfit, Config::AMT_DIGITS).into());

            // 交易所
            items.push(pos.ExchangeID.clone().into());

            // 合约名称
            items.push(insname.into());

            // 资金账号
            items.push(pos.InvestorID.clone().into());
        } else {
            // 合约编码
            items.push(pos.InstrumentID.clone().into());

            // 持仓量
            items.push(TIConvert::format_i(pos.Position).into());

            // 当前可用
            let mut apos = pos.YdPosition - pos.CloseVolume - pos.LongFrozen + pos.SettlementID - pos.XYRqRePos;
            if 0 == tp1 {
                apos += pos.OpenVolume;
            }
            items.push(TIConvert::format_i(apos).into());

            // 上日持仓量
            items.push(TIConvert::format_i(pos.YdPosition).into());

            // 当日买入
            items.push(TIConvert::format_i(pos.OpenVolume).into());

            // 当日卖出
            items.push(TIConvert::format_i(pos.CloseVolume).into());

            // 划转量
            items.push(TIConvert::format_i(pos.SettlementID).into());

            // 冻结持仓量
            items.push(TIConvert::format_i(pos.LongFrozen).into());

            // 持仓均价
            let mut avgprice: f64 = 0.;
            if 0 != pos.Position {
                avgprice = pos.PositionCost / pos.Position as f64 / multiple as f64;
            }
            items.push(TIConvert::format_f(avgprice, digits).into());

            // 持仓成本
            items.push(TIConvert::format_f(pos.PositionCost, Config::AMT_DIGITS).into());

            // 最新价
            items.push(TIConvert::format_f(pos.LPrice, Config::AMT_DIGITS).into());

            // 持仓市值
            items.push(TIConvert::format_f(pos.PosValues, Config::AMT_DIGITS).into());

            // 持仓盈亏
            items.push(TIConvert::format_f(pos.PositionProfit, Config::AMT_DIGITS).into());

            // 现券还券量
            items.push(TIConvert::format_i(pos.XYRqRePos).into());

            // 融资已还折算量
            items.push(TIConvert::format_i(pos.XYRzRePos as i32).into());

            // 融资未还折算量
            items.push(TIConvert::format_i(pos.XYRzNdPos as i32).into());

            // 折算率
            items.push(TIConvert::format_f(pos.XYDiscRatio, Config::AMT_DIGITS).into());

            // 充抵保证金
            items.push(TIConvert::format_f(pos.XYCdMarg, Config::AMT_DIGITS).into());

            // 交易所
            items.push(pos.ExchangeID.clone().into());

            // 合约名称
            items.push(insname.into());

            // 资金账号
            items.push(pos.InvestorID.clone().into());
        }

        items
    }

    // 更新合约下拉列表
    async fn update_insid(&self, app_weak: &Weak<App>) {
        let pos_datas = POSITION_DATAS.get().unwrap();

        let insid_edited = pos_datas
            .insid_edited
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        let new_pos_insid = pos_datas
            .new_pos_insid
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        if !insid_edited && !new_pos_insid {
            return;
        }

        let insid_set_clone = pos_datas.insid_set.clone();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let pos = app.global::<StkTrd_Position>();
            let filter = pos.get_insid().trim().to_owned();
            if insid_edited || new_pos_insid {
                let insid_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                insid_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                insid_set_clone.iter().for_each(|insid| {
                    if insid.contains(&filter) {
                        insid_arr.push(ListViewItem {
                            text: insid.clone().into(),
                            ..Default::default()
                        });
                    }
                });

                pos.set_insid_model(insid_arr.into());
            }
        });
    }

    // 更新持仓
    async fn update_position(&self, app_weak: &Weak<App>) {
        let pos_datas = POSITION_DATAS.get().unwrap();

        let data_map_clone = {
            if pos_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                pos_datas.filter_clicked.store(false, Ordering::Relaxed);
                pos_datas.data_map.clone()
            } else {
                if pos_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    pos_datas.data_map.clone()
                } else {
                    Arc::new(CtpPositionMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let is_credit = app.global::<StkTrd_Com>().get_iscredit();
            let app_pos = app.global::<StkTrd_Position>();
            let show_zero_pos = app.global::<AppInerArgs>().get_trd_zero_pos();

            let show_pos_trans = app_pos.get_show_pos_trans();
            let sel_accid = app_pos.get_sel_accid();
            let sel_exchid = app_pos.get_sel_exchid();
            let sel_insid = app_pos.get_sel_insid();

            let filter_exchid = app_pos.get_exchid().as_str().to_owned();
            let filter_insid = app_pos.get_insid().as_str().to_owned();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|pos| {
                // 更新划转时的可用持仓
                if show_pos_trans {
                    if sel_accid.as_str() == &pos.InvestorID
                        && sel_exchid.as_str() == &pos.ExchangeID
                        && sel_insid.as_str() == &pos.InstrumentID
                    {
                        let mut tp1 = 1;
                        {
                            if let Some(ins) = common::global::INS
                                .get()
                                .unwrap()
                                .read()
                                .unwrap()
                                .get_ins(TIConvert::exch_id(&pos.ExchangeID), &pos.InstrumentID)
                            {
                                tp1 = ins.tp1;
                            }
                        };
                        let mut apos = pos.YdPosition - pos.CloseVolume - pos.LongFrozen + pos.SettlementID;
                        if 0 == tp1 {
                            apos += pos.OpenVolume;
                        }
                        app_pos.set_sel_volume(apos.to_string().into());
                    }
                }

                if !show_zero_pos {
                    if 0 == pos.Position {
                        return;
                    }
                }

                if !filter_exchid.is_empty() {
                    if pos.ExchangeID != filter_exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !pos.InstrumentID.contains(&filter_insid) {
                        return;
                    }
                }

                let items = Position::get_row_items(&pos, is_credit);
                row_data.push(items.into());
            });

            let asc_column_index = app_pos.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_pos.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_pos.set_row_data(sort_model.into());
                return;
            }

            app_pos.set_row_data(row_data.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新合约下拉列表
        self.update_insid(&app_weak).await;

        // 更新持仓
        self.update_position(&app_weak).await;
    }
}
