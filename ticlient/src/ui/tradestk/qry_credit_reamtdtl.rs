use std::rc::Rc;

use crate::{
    common::{config::Config, global::TRADE_STK_BUF, ticonvert::TIConvert, titype::TIType},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::position_trade_ctp::CtpReqQryCreditReDtlField;

/// 查询 - 信用交易 - 还款明细
pub(super) struct QueryCreditReAmtDtl {}

impl QueryCreditReAmtDtl {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let qry = app.global::<StkTrd_QryCreditReAmtDtl>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryCreditReAmtDtl::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryCreditReAmtDtl::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryCreditReAmtDtl::on_page_size_changed(&app_weak, size));

        log::trace!("Query credit return amount detail init completed");
    }
}

impl QueryCreditReAmtDtl {
    /// 查询事件
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qry = app.global::<StkTrd_QryCreditReAmtDtl>();

        let req = CtpReqQryCreditReDtlField {
            ExchID: TIConvert::exch_id(&app_qry.get_exchid()),
            Retype: app_qry.get_retype(),
            ReID: app_qry.get_reid().into(),
            OrdSysID: app_qry.get_ordsysid().into(),
            InstrumentID: app_qry.get_insid().into(),
            TransTime0: app_qry.get_starttime_int(),
            TransTime1: app_qry.get_endtime_int(),
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qry.get_page_index()
            }
        };
        let page_size = app_qry.get_page_size();

        let ret = {
            TRADE_STK_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_credit_redtl(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qry.set_page_index_model(page_index_data.into());
            app_qry.set_item_total(ret.0);
            app_qry.set_page_index(0);
            app_qry.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|ord| {
            let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

            // 资金账户
            items.push(ord.AccID.clone().into());

            // 股东代码
            items.push(ord.CliID.clone().into());

            // 交易所
            items.push(slint::format!("{}", TIConvert::exch_name(ord.ExchID)));

            // 偿还类型
            items.push(
                match ord.ReType {
                    TIType::CREDIT_RETURN_AMT_TYPE_DIRECT => "直接还款",
                    TIType::CREDIT_RETURN_AMT_TYPE_SELL => "卖券还款",
                    TIType::CREDIT_RETURN_AMT_TYPE_CLOSE => "平仓还款",
                    _ => "未知",
                }
                .into(),
            );

            // 偿还编号
            items.push(ord.ReID.clone().into());

            // 偿还序号
            items.push(slint::format!("{}", ord.ReIndex));

            // 委托编号
            items.push(ord.OrdSydID.clone().into());

            // 证券代码
            items.push(ord.InsID.clone().into());

            // 还前金额
            items.push(TIConvert::format_f(ord.Amount, Config::AMT_DIGITS).into());

            // 明细类型
            items.push(
                match ord.ReDtlType {
                    TIType::CREDIT_RETURN_AMT_DTL_TYPE_INTEREST => "利息",
                    TIType::CREDIT_RETURN_AMT_DTL_TYPE_AMT => "本金",
                    TIType::CREDIT_RETURN_AMT_DTL_TYPE_COMMI => "费用",
                    TIType::CREDIT_RETURN_AMT_DTL_TYPE_FREEINT => "罚息",
                    TIType::CREDIT_RETURN_AMT_DTL_TYPE_AMTCOMMI => "本金及费用",
                    _ => "未知",
                }
                .into(),
            );

            // 偿还金额
            items.push(TIConvert::format_f(ord.ReAmt, Config::AMT_DIGITS).into());

            // 剩余金额
            items.push(TIConvert::format_f(ord.LeftAmt, Config::AMT_DIGITS).into());

            // 发生时间
            items.push(TIConvert::transtime(ord.Time).into());

            row_data.push(items.into());
        });

        app_qry.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryCreditReAmtDtl::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryCreditReAmtDtl::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryCreditReAmtDtl::on_query(app_weak, true);
    }
}
