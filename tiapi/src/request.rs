use crate::protocol::ptstruct::EMessageType;
use bytes::Bytes;
use reqwest::{Client, ClientBuilder};

/// Http客户端
pub(crate) struct HttpClient {
    /// 是否加密连接
    encon: bool,
}

impl HttpClient {
    pub fn new() -> Self {
        Self { encon: true }
    }

    /// 设置是否启动加密连接
    pub fn set_encon(&mut self, encon: bool) {
        self.encon = encon;
    }

    /// 获取HTTP响应
    pub async fn get_response(&self, url: &str, mt: EMessageType, reqbody: &str) -> crate::Result<reqwest::Response> {
        let msgtype: i32 = mt.into();

        if self.encon {
            let client = ClientBuilder::new()
                .danger_accept_invalid_certs(true)
                .build()
                .map_err(|e| e.without_url().to_string())?;

            client
                .post(url)
                .header("msg-type", msgtype)
                .body(reqbody.to_owned())
                .send()
                .await
                .map_err(|e| e.without_url().to_string())
        } else {
            Client::new()
                .post(url)
                .header("msg-type", msgtype)
                .body(reqbody.to_owned())
                .send()
                .await
                .map_err(|e| e.without_url().to_string())
        }
    }

    /// 获取HTTP响应的消息体
    pub async fn get_response_body_text(&self, url: &str, mt: EMessageType, reqbody: &str) -> crate::Result<String> {
        self.get_response_sync(url, mt, reqbody)
            .await?
            .text()
            .await
            .map_err(|e| e.without_url().to_string())
    }

    /// 获取服务端信息
    pub async fn get_svrinfo(&self, url: &str) -> crate::Result<String> {
        self.get_response_body_text(url, EMessageType::TIMT_Com_Req_Qry_ServerType, "{}")
            .await
    }

    /// 获取验证码
    pub async fn get_vercode(&self, url: &str) -> crate::Result<(i32, Bytes)> {
        let rsp = self.get_response(url, EMessageType::TIMT_Com_Req_Qry_VerCode, "{}").await?;

        let sn = rsp
            .headers()
            .get("seqnum")
            .and_then(|v| v.to_str().ok())
            .and_then(|v| v.parse::<i32>().ok())
            .and_then(|v| if v > 0 { Some(v) } else { None })
            .ok_or_else(|| "parse seqnum error".to_owned())?;

        let bytes = rsp.bytes().await.map_err(|e| e.without_url().to_string())?;

        Ok((sn, bytes))
    }
}

impl HttpClient {
    /// 获取HTTP响应
    async fn get_response_sync(&self, url: &str, mt: EMessageType, reqbody: &str) -> crate::Result<reqwest::Response> {
        self.get_response(url, mt, reqbody).await
    }
}
