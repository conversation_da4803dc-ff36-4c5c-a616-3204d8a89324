import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, TableView } from "@zstk/lib.slint";
import { AppC<PERSON>, ColWidth, WidgetColor, AppCom } from "../struct.slint";
import { StkTrd_InOrder } from "struct.slint";

export component InOrderPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <int> select_row_index: -1;
    function req_cancel_order(need_confirm: bool) {
        StkTrd_InOrder.sel_cancel_order.exchid = "";
        if select_row_index >= 0 && StkTrd_InOrder.row_data.length > select_row_index {
            StkTrd_InOrder.sel_cancel_order.exchid = StkTrd_InOrder.row_data[select_row_index][13];
            StkTrd_InOrder.sel_cancel_order.accountid = StkTrd_InOrder.row_data[select_row_index][14];
            StkTrd_InOrder.sel_cancel_order.ordersysid = StkTrd_InOrder.row_data[select_row_index][6];
        }
        StkTrd_InOrder.cancel_order_clieked(StkTrd_InOrder.sel_cancel_order, need_confirm);
    }

    VerticalLayout {
        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 2,
                columns: [
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    { title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    { title: @tr("报单状态"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_status },
                    { title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("报单价格条件"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_pricetype },
                    { title: @tr("有效期类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_timecondition },
                    { title: @tr("成交量类型"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_volumecondition },
                    { title: @tr("剩余数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("用户代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.userid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                ]
            };
            row_count: StkTrd_InOrder.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_InOrder.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_clicked(row_index, column_index) => {
                self.select_full_row = true;
            }

            cell_double_clicked(row_index, column_index) => {
                if StkTrd_InOrder.ctrls_enabled {
                    req_cancel_order(true);
                }
            }

            current_cell_changed(row_index, column_index) => {
                select_row_index = row_index;
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_InOrder.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_InOrder.row_data[row_index]);
                }
            }
        }

        HorizontalLayout {
            padding: 6px;
            spacing: 30px;
            height: 36px;

            Button {
                text: "撤单";
                background: WidgetColor.btn_background;
                enabled: StkTrd_InOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkTrd_InOrder.ctrls_enabled = false;
                    req_cancel_order(true);
                }
            }

            Button {
                text: "全撤";
                background: WidgetColor.btn_background;
                enabled: StkTrd_InOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkTrd_InOrder.ctrls_enabled = false;
                    StkTrd_InOrder.cancel_order_all_clieked(StkTrd_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤买";
                background: WidgetColor.btn_background;
                enabled: StkTrd_InOrder.ctrls_enabled;

                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkTrd_InOrder.ctrls_enabled = false;
                    StkTrd_InOrder.cancel_order_bs_clieked(0, StkTrd_InOrder.row_data.length, true);
                }
            }

            Button {
                text: "撤卖";
                background: WidgetColor.btn_background;
                enabled: StkTrd_InOrder.ctrls_enabled;
                clicked => {
                    if AppCom.check_td_session_timeout() {
                        return ;
                    }

                    StkTrd_InOrder.ctrls_enabled = false;
                    StkTrd_InOrder.cancel_order_bs_clieked(1, StkTrd_InOrder.row_data.length, true);
                }
            }
        }
    }
}
