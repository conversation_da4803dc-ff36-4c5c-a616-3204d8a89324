use crate::{
    common::global::{self, DB, MD_BUF, TRADE_FUT_BUF},
    slintui::*,
    ui::{
        tradecom::{abnormalstate::AbnormalState, statusstrip::StatusStrip},
        IApp,
    },
};
use slint::*;

use std::{
    sync::{Arc, RwLock},
    thread,
};

use super::{
    account::Account, in_exercise::InExercise, in_order::InOrder, input_exercise::InputExercise, input_order::InputOrder,
    marketdata::Marketdata, mon_limitopen::MonLimitOpen, mon_limitposition::MonLimitPosition,
    mon_orderinsertcancel::MonLimitOrderInsertCancel, mon_selftrade::MonSelfTrade, mon_selftradedtl::MonSelfTradeDtl,
    position::Position, qry_exercise::QueryExercise, qry_order::QueryOrder, qry_trade::QueryTrade, setting::Setting,
};

/// 显示报单消息对话框<br>state. 100:报单确认; 其他:错误
pub(super) fn show_input_msg_box(app: &crate::App, state: i32, title: SharedString, msg: SharedString) {
    app.global::<crate::FutTrd_InputTip>()
        .set_app_ret(crate::AppResult { state, title, msg });
}

/// 显示撤单消息对话框<br>state. 100:撤单笔确认; 101:撤全部确认; 102:撤买确认; 103:撤卖确认; 其他:错误
pub(super) fn show_cancel_msg_box(app: &crate::App, state: i32, title: SharedString, msg: SharedString) {
    app.global::<crate::FutTrd_CancelTip>()
        .set_app_ret(crate::AppResult { state, title, msg });
}

pub struct TradeFutApp {
    thread: Option<thread::JoinHandle<()>>,
    ui_thread: Option<thread::JoinHandle<()>>,
    db_thread: Option<thread::JoinHandle<()>>,
    run_falg: Arc<RwLock<bool>>,

    abnormalstate: Arc<AbnormalState>,
    statusstrip: Arc<StatusStrip>,
    setting: Arc<Setting>,

    marketdata: Arc<Marketdata>,
    account: Arc<Account>,
    position: Arc<Position>,

    in_order: Arc<InOrder>,
    in_exercise: Arc<InExercise>,

    qry_order: Arc<QueryOrder>,
    qry_trade: Arc<QueryTrade>,
    qry_exercise: Arc<QueryExercise>,

    mon_limitopen: Arc<MonLimitOpen>,
    mon_limitposition: Arc<MonLimitPosition>,
    mon_orderinsertcancel: Arc<MonLimitOrderInsertCancel>,
    mon_selftrade: Arc<MonSelfTrade>,
    mon_selftradedtl: Arc<MonSelfTradeDtl>,

    input_ord: Arc<InputOrder>,
    input_exec: Arc<InputExercise>,
}

impl TradeFutApp {
    pub fn new() -> Self {
        Self {
            thread: None,
            ui_thread: None,
            db_thread: None,
            run_falg: Arc::new(RwLock::new(true)),

            abnormalstate: Arc::new(AbnormalState::new()),
            statusstrip: Arc::new(StatusStrip::new()),
            setting: Arc::new(Setting::new()),

            marketdata: Arc::new(Marketdata::new()),
            account: Arc::new(Account::new()),
            position: Arc::new(Position::new()),

            in_order: Arc::new(InOrder::new()),
            in_exercise: Arc::new(InExercise::new()),

            qry_order: Arc::new(QueryOrder::new()),
            qry_trade: Arc::new(QueryTrade::new()),
            qry_exercise: Arc::new(QueryExercise::new()),

            mon_limitopen: Arc::new(MonLimitOpen::new()),
            mon_limitposition: Arc::new(MonLimitPosition::new()),
            mon_orderinsertcancel: Arc::new(MonLimitOrderInsertCancel::new()),
            mon_selftrade: Arc::new(MonSelfTrade::new()),
            mon_selftradedtl: Arc::new(MonSelfTradeDtl::new()),

            input_ord: Arc::new(InputOrder::new()),
            input_exec: Arc::new(InputExercise::new()),
        }
    }
}

impl IApp for TradeFutApp {
    fn setup(&mut self, app: &crate::slintui::App) {
        // 初始化
        {
            app.global::<AppCom>()
                .set_svr_build_date(crate::common::global::CFG.get().unwrap().read().unwrap().com.SvrBuildDate);

            self.abnormalstate.init(&app);
            self.statusstrip.init(&app);

            // 等待所有合约查询完成, 后面不再针对合约的查询是否结束做特殊处理
            // 此处可能出现在登录成功后,主界面打开缓慢的情形
            // 在等待查询合约完成的过程中,可能出出现与服务端断开的情况
            {
                let start_time = std::time::Instant::now();
                let timeout = std::time::Duration::from_secs(60);

                loop {
                    let is_ins_qry_completed = { crate::common::global::INS.get().unwrap().read().unwrap().is_last };

                    if is_ins_qry_completed {
                        break;
                    }

                    if !self.abnormalstate.is_connected() {
                        AbnormalState::deal_disconnect(&app);
                        return;
                    }

                    if start_time.elapsed() >= timeout {
                        crate::show_msg_box(&app,
                            2,
                            "".into(),
                            "登录成功后超过60秒都未获取到最后一条合约信息\n\n可能是网络缓慢的原因, 不再继续等待, 直接打开主界面.\n\n如果之后一直接收不到最后一条合约信息, 可能的影响:\n使用合约代码时可能找不到该合约的信息".into()
                        );
                        break;
                    }

                    std::thread::sleep(std::time::Duration::from_millis(100));
                }

                log::info!(
                    "Wait for all instrument query completed took {} milliseconds",
                    start_time.elapsed().as_millis()
                );
            }

            log::info!("App init started");

            self.setting.init(&app);
            self.marketdata.init(&app);
            self.account.init(&app);
            self.position.init(&app);

            self.in_order.init(&app);
            self.in_exercise.init(&app);

            self.qry_order.init(&app);
            self.qry_trade.init(&app);
            self.qry_exercise.init(&app);

            self.mon_limitopen.init(&app);
            self.mon_limitposition.init(&app);
            self.mon_orderinsertcancel.init(&app);
            self.mon_selftrade.init(&app);
            self.mon_selftradedtl.init(&app);

            self.input_ord.init(&app);
            self.input_exec.init(&app);

            log::info!("App init completed");
        }

        // 注册回调
        {
            {
                log::info!("App set md callback started");

                let mut md_buf = MD_BUF.get().unwrap().write().unwrap();

                let md_clone = self.marketdata.clone();
                md_buf.register_on_subscribe_failed_callback(move |em| md_clone.on_sub_failed(em));

                let md_clone = self.marketdata.clone();
                md_buf.register_on_un_subscribe_failed_callback(move |em| md_clone.on_un_sub_failed(em));

                log::info!("App set md callback completed");
            }

            {
                log::info!("App set callback started");

                let mut trd_fut_buf = TRADE_FUT_BUF.get().unwrap().write().unwrap();

                let account_clone = self.account.clone();
                trd_fut_buf.register_qry_acc_callback(move |acc: tiapi::protocol_pub::account_trade_ctp::CtpAccountField| {
                    account_clone.on_rsp_qry_acc(acc)
                });

                let position_clone = self.position.clone();
                trd_fut_buf.register_qry_pos_callback(move |pos| position_clone.on_rsp_qry_pos(pos));

                let in_order_clone = self.in_order.clone();
                trd_fut_buf.register_rsp_inputorderaction_callback(move |rsp| in_order_clone.on_rsp_inputorderaction(rsp));

                let in_exercise_clone = self.in_exercise.clone();
                trd_fut_buf.register_rsp_execorderaction_callback(move |rsp| in_exercise_clone.on_rsp_inputexecaction(rsp));

                let input_ord_clone = self.input_ord.clone();
                trd_fut_buf.register_rsp_inputorder_callback(move |rsp| input_ord_clone.on_rsp_inputorder(rsp));

                let input_exec_clone = self.input_exec.clone();
                trd_fut_buf.register_rsp_execorder_callback(move |rsp| input_exec_clone.on_rsp_inputexec(rsp));

                log::info!("App set callback completed");
            }
        }

        // 逻辑更新线程
        {
            log::info!("App start qyery thread started");

            let abnormalstate_clone = self.abnormalstate.clone();

            let account_clone = self.account.clone();
            let position_clone = self.position.clone();
            let in_order_clone = self.in_order.clone();
            let in_exercise_clone = self.in_exercise.clone();
            let mon_limitopen_clone = self.mon_limitopen.clone();
            let mon_limitposition_clone = self.mon_limitposition.clone();
            let mon_orderinsertcancel_clone = self.mon_orderinsertcancel.clone();
            let mon_selftrade_clone = self.mon_selftrade.clone();

            let run_falg: Arc<RwLock<bool>> = self.run_falg.clone();
            self.thread = Some(thread::spawn(move || {
                loop {
                    if !*run_falg.read().unwrap() {
                        break;
                    }
                    if !abnormalstate_clone.is_connected() {
                        break;
                    }

                    account_clone.qry_account();
                    position_clone.qry_position();

                    in_order_clone.qry_in_order();
                    in_exercise_clone.qry_in_exercise();

                    mon_limitopen_clone.qry_limitopen();
                    mon_limitposition_clone.qry_limitposition();
                    mon_orderinsertcancel_clone.qry_orderinsertcancel();
                    mon_selftrade_clone.qry_selftrade();

                    thread::sleep(std::time::Duration::from_secs(1));
                }

                log::info!(
                    "run_falg:{}, is_connected:{}. qry thread is end....",
                    *run_falg.read().unwrap(),
                    abnormalstate_clone.is_connected()
                );
            }));

            log::info!("App start qyery thread completed");
        }

        // UI更新线程
        {
            log::info!("App start ui thread started");

            let abnormalstate_clone = self.abnormalstate.clone();
            let status_strip_clone = self.statusstrip.clone();

            let md_clone = self.marketdata.clone();
            let md_1_clone = self.marketdata.clone();
            let account_clone = self.account.clone();
            let position_clone = self.position.clone();
            let in_order_clone = self.in_order.clone();
            let in_exercise_clone = self.in_exercise.clone();
            let mon_limitopen_clone = self.mon_limitopen.clone();
            let mon_limitposition_clone = self.mon_limitposition.clone();
            let mon_orderinsertcancel_clone = self.mon_orderinsertcancel.clone();
            let mon_selftrade_clone = self.mon_selftrade.clone();
            let input_ord_clone = self.input_ord.clone();
            let input_ord_1_clone = self.input_ord.clone();
            let input_exec_clone = self.input_exec.clone();
            let input_exec_1_clone = self.input_exec.clone();

            let app_weak: Weak<App> = app.as_weak();
            let run_falg: Arc<RwLock<bool>> = self.run_falg.clone();

            let rt = tokio::runtime::Builder::new_multi_thread().enable_all().build().unwrap();

            self.ui_thread = Some(thread::spawn(move || {
                let mut pre_input_time = std::time::Instant::now();
                let mut pre_other_time = std::time::Instant::now();

                loop {
                    if !*run_falg.read().unwrap() {
                        break;
                    }

                    rt.block_on(abnormalstate_clone.update(app_weak.clone()));
                    if !abnormalstate_clone.is_connected() {
                        break;
                    }

                    let cur_time = std::time::Instant::now();

                    if cur_time.duration_since(pre_input_time).as_millis() >= 1000 {
                        pre_input_time = std::time::Instant::now();

                        rt.block_on(md_1_clone.update_input(app_weak.clone()));
                        rt.block_on(input_ord_1_clone.update_input(app_weak.clone()));
                        rt.block_on(input_exec_1_clone.update_input(app_weak.clone()));
                    }

                    if cur_time.duration_since(pre_other_time).as_millis() >= 500 {
                        pre_other_time = std::time::Instant::now();

                        rt.block_on(status_strip_clone.update(app_weak.clone()));

                        rt.block_on(md_clone.update(app_weak.clone()));

                        rt.block_on(account_clone.update(app_weak.clone()));
                        rt.block_on(position_clone.update(app_weak.clone()));

                        rt.block_on(in_order_clone.update(app_weak.clone()));
                        rt.block_on(in_exercise_clone.update(app_weak.clone()));

                        rt.block_on(mon_limitopen_clone.update(app_weak.clone()));
                        rt.block_on(mon_limitposition_clone.update(app_weak.clone()));
                        rt.block_on(mon_orderinsertcancel_clone.update(app_weak.clone()));
                        rt.block_on(mon_selftrade_clone.update(app_weak.clone()));

                        rt.block_on(input_ord_clone.update(app_weak.clone()));
                        rt.block_on(input_exec_clone.update(app_weak.clone()));
                    }

                    thread::sleep(std::time::Duration::from_millis(100));
                }

                log::info!(
                    "run_falg:{}, is_connected:{}. ui thread is end....",
                    *run_falg.read().unwrap(),
                    abnormalstate_clone.is_connected()
                );
            }));

            log::info!("App start ui update thread completed");
        }

        // DB线程
        {
            log::info!("App start db thread started");

            let run_falg: Arc<RwLock<bool>> = self.run_falg.clone();
            let abnormalstate_clone = self.abnormalstate.clone();

            self.db_thread = Some(thread::spawn(move || {
                loop {
                    if !*run_falg.read().unwrap() {
                        break;
                    }
                    if !abnormalstate_clone.is_connected() {
                        break;
                    }

                    let order_map = {
                        let trd_fut_buf = TRADE_FUT_BUF.get().unwrap().read().unwrap();
                        trd_fut_buf.pop_order()
                    };
                    if !order_map.is_empty() {
                        let mut db = DB.get().unwrap().lock().unwrap();
                        db.write_trd_order(order_map);
                    }

                    let trade_vec = {
                        let trd_fut_buf = TRADE_FUT_BUF.get().unwrap().read().unwrap();
                        trd_fut_buf.pop_trade()
                    };
                    if !trade_vec.is_empty() {
                        let mut db = DB.get().unwrap().lock().unwrap();
                        db.write_trd_trade(trade_vec);
                    }

                    thread::sleep(std::time::Duration::from_millis(500));
                }

                log::info!(
                    "run_falg:{}, is_connected:{}. db thread is end....",
                    *run_falg.read().unwrap(),
                    abnormalstate_clone.is_connected()
                );
            }));

            log::info!("App start db thread completed");
        }

        global::set_app_init_complete();
    }

    fn teardown(&mut self) {
        log::info!("App stop started");

        *self.run_falg.write().unwrap() = false;

        if let Some(t) = self.thread.take() {
            let _ = t.join();
        }
        log::info!("App stop query thread completed");

        if let Some(t) = self.ui_thread.take() {
            let _ = t.join();
        }
        log::info!("App stop ui thread completed");

        if let Some(t) = self.db_thread.take() {
            let _ = t.join();
        }
        log::info!("App stop db thread completed");
    }
}
