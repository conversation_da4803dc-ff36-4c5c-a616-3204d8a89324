use std::sync::{
    atomic::{AtomicU64, Ordering},
    Arc, Mutex,
};

use tiapi::protocol_pub::{
    account_risk_stk::{
        AccountsStkField, CreditGaChaField, CreditLimitField, CreditTcAmtField, CreditTcPosField, ReqQryCreditLimitField,
        ReqQryCreditTcAmtField, ReqQryWithdrawDepositField, WithdrawDepositStkField,
    },
    instrument::InstrumentField,
    order_risk_stk::OrderStkField,
    position_risk_stk::{
        CreditConcentrationField, CreditContractField, CreditReturnDtlField, PositionStkField, PositionTransStkField,
        ReqQryCreditReDtlField, ReqQryPositionTransStkField,
    },
    trade_risk_stk::TradeStkField,
};

use crate::common::titype::TIType;

use super::abnormalstate::IOnRtnAbnormalState;

pub type AccountsStkMap = dashmap::DashMap<String, AccountsStkField>;
pub type PositionStkMap = dashmap::DashMap<String, PositionStkField>;

pub type OrderStatusMap = dashmap::DashMap<String, i32>;
pub type OrderMap = dashmap::DashMap<String, OrderStkField>;
pub type OrderVec = Vec<OrderStkField>;
pub type TradeVec = Vec<TradeStkField>;

pub type PosTransMap = dashmap::DashMap<String, PositionTransStkField>;
pub type PosTransVec = Vec<PositionTransStkField>;

pub type CreditGaChaMap = dashmap::DashMap<String, CreditGaChaField>;
pub type CreditContractMap = dashmap::DashMap<String, CreditContractField>;
pub type CreditLimitMap = dashmap::DashMap<String, CreditLimitField>;
pub type CreditTcAmtMap = dashmap::DashMap<String, CreditTcAmtField>;
pub type CreditTcPosMap = dashmap::DashMap<String, CreditTcPosField>;
pub type CreditConcentrationMap = dashmap::DashMap<String, CreditConcentrationField>;
pub type CreditReturnDtlMap = dashmap::DashMap<i32, CreditReturnDtlField>;
pub type CreditReturnDtlVec = Vec<CreditReturnDtlField>;

pub type WithdrawDepositMap = dashmap::DashMap<String, WithdrawDepositStkField>;
pub type WithdrawDepositVec = Vec<WithdrawDepositStkField>;

pub struct RiskStkBuf {
    key_index: AtomicU64,

    /// 异常状态回调
    rtn_abnormalstate_callback: Option<Box<dyn IOnRtnAbnormalState>>,

    /// 资金回报(主界面)
    account_map: Arc<Mutex<AccountsStkMap>>,

    /// 资金回报
    account_2_map: Arc<Mutex<AccountsStkMap>>,

    /// 持仓消息
    pos_map: Arc<Mutex<PositionStkMap>>,

    /// 查询出入金消息
    qry_withdraw_deposit_map: Arc<WithdrawDepositMap>,

    /// 已经结束的交易所报单
    order_status_map: Arc<OrderStatusMap>,

    /// 报单回报
    order_map: Arc<Mutex<OrderMap>>,

    /// 在途报单回报
    in_order_map: Arc<Mutex<OrderMap>>,

    /// 成交回报
    trade_vec: Arc<Mutex<TradeVec>>,

    /// 股份划转回报
    postrans_map: Arc<PosTransMap>,

    /******************下面的是信用交易字段***********************/
    /// 轧差信息
    credit_gacha_map: Arc<Mutex<CreditGaChaMap>>,

    /// 合约信息
    credit_contract_map: Arc<Mutex<CreditContractMap>>,

    /// 当日到期的合约信息
    credit_expite_contract_map: Arc<Mutex<CreditContractMap>>,

    /// 融资授信额度
    credit_limitamt_map: Arc<Mutex<CreditLimitMap>>,

    /// 融券授信额度
    credit_limitpos_map: Arc<Mutex<CreditLimitMap>>,

    /// 资金头寸
    credit_tcamt_map: Arc<Mutex<CreditTcAmtMap>>,

    /// 股份头寸
    credit_tcpos_map: Arc<Mutex<CreditTcPosMap>>,

    /// 集中度
    credit_concentrat_map: Arc<Mutex<CreditConcentrationMap>>,

    /// 融资归还明细
    qry_credit_redtl_amt_map: Arc<CreditReturnDtlMap>,

    /// 融券归还明细
    qry_credit_redtl_pos_map: Arc<CreditReturnDtlMap>,

    /// 查询信用授信额度明细
    qry_credit_limit_map: Arc<WithdrawDepositMap>,

    /// 查询信用资金头寸明细
    qry_credit_tcamt_map: Arc<WithdrawDepositMap>,

    /// 查询信用股份头寸明细
    qry_credit_tcpos_map: Arc<WithdrawDepositMap>,
}

impl RiskStkBuf {
    pub fn new() -> Self {
        Self {
            key_index: AtomicU64::new(1),

            rtn_abnormalstate_callback: None,

            account_map: Arc::new(Mutex::new(AccountsStkMap::new())),
            account_2_map: Arc::new(Mutex::new(AccountsStkMap::new())),
            pos_map: Arc::new(Mutex::new(PositionStkMap::new())),
            qry_withdraw_deposit_map: Arc::new(WithdrawDepositMap::new()),

            order_status_map: Arc::new(OrderStatusMap::new()),
            order_map: Arc::new(Mutex::new(OrderMap::new())),
            in_order_map: Arc::new(Mutex::new(OrderMap::new())),
            trade_vec: Arc::new(Mutex::new(TradeVec::new())),
            postrans_map: Arc::new(PosTransMap::new()),

            credit_gacha_map: Arc::new(Mutex::new(CreditGaChaMap::new())),
            credit_contract_map: Arc::new(Mutex::new(CreditContractMap::new())),
            credit_expite_contract_map: Arc::new(Mutex::new(CreditContractMap::new())),
            credit_limitamt_map: Arc::new(Mutex::new(CreditLimitMap::new())),
            credit_limitpos_map: Arc::new(Mutex::new(CreditLimitMap::new())),
            credit_tcamt_map: Arc::new(Mutex::new(CreditTcAmtMap::new())),
            credit_tcpos_map: Arc::new(Mutex::new(CreditTcPosMap::new())),
            credit_concentrat_map: Arc::new(Mutex::new(CreditConcentrationMap::new())),
            qry_credit_redtl_amt_map: Arc::new(CreditReturnDtlMap::new()),
            qry_credit_redtl_pos_map: Arc::new(CreditReturnDtlMap::new()),
            qry_credit_limit_map: Arc::new(WithdrawDepositMap::new()),
            qry_credit_tcamt_map: Arc::new(WithdrawDepositMap::new()),
            qry_credit_tcpos_map: Arc::new(WithdrawDepositMap::new()),
        }
    }
}

impl RiskStkBuf {
    // 注册异常状态回调
    pub fn register_rtn_abnormalstate_callback(&mut self, callback: Box<dyn IOnRtnAbnormalState>) {
        self.rtn_abnormalstate_callback = Some(callback);
    }
}

impl RiskStkBuf {
    /// 获取资金
    pub fn pop_account(&self) -> AccountsStkMap {
        let acc_map = self.account_map.lock().unwrap();
        let ret = acc_map.clone();
        acc_map.clear();
        ret
    }

    /// 获取资金
    pub fn pop_account_2(&self) -> AccountsStkMap {
        let acc_map = self.account_2_map.lock().unwrap();
        let ret = acc_map.clone();
        acc_map.clear();
        ret
    }

    /// 获取持仓
    pub fn pop_position(&self) -> PositionStkMap {
        let pos_map = self.pos_map.lock().unwrap();
        let ret = pos_map.clone();
        pos_map.clear();
        ret
    }

    /// 获取报单
    pub fn pop_order(&self) -> OrderMap {
        let order_map = self.order_map.lock().unwrap();
        let ret = order_map.clone();
        order_map.clear();
        ret
    }

    /// 获取在途报单
    pub fn pop_in_order(&self) -> OrderMap {
        let in_order_map = self.in_order_map.lock().unwrap();
        let ret = in_order_map.clone();
        in_order_map.clear();
        ret
    }

    /// 获取成交
    pub fn pop_trade(&self) -> TradeVec {
        let mut trade_vec = self.trade_vec.lock().unwrap();
        let ret = trade_vec.clone();
        trade_vec.clear();
        ret
    }

    /// 获取信用轧差信息
    pub fn pop_credit_gacha(&self) -> CreditGaChaMap {
        let credit_gacha_map = self.credit_gacha_map.lock().unwrap();
        let ret = credit_gacha_map.clone();
        credit_gacha_map.clear();
        ret
    }

    /// 获取到期信用合约
    pub fn pop_credit_expire_contract(&self) -> CreditContractMap {
        let cc_map = self.credit_expite_contract_map.lock().unwrap();
        let ret = cc_map.clone();
        cc_map.clear();
        ret
    }

    /// 获取信用合约
    pub fn pop_credit_contract(&self) -> CreditContractMap {
        let cc_map = self.credit_contract_map.lock().unwrap();
        let ret = cc_map.clone();
        cc_map.clear();
        ret
    }

    /// 获取融资授信额度
    pub fn pop_credit_limit_amt(&self) -> CreditLimitMap {
        let clmap = self.credit_limitamt_map.lock().unwrap();
        let ret = clmap.clone();
        clmap.clear();
        ret
    }

    /// 获取融券授信额度
    pub fn pop_credit_limit_pos(&self) -> CreditLimitMap {
        let clmap = self.credit_limitpos_map.lock().unwrap();
        let ret = clmap.clone();
        clmap.clear();
        ret
    }

    /// 获取资金头寸
    pub fn pop_credit_tcamt(&self) -> CreditTcAmtMap {
        let clmap = self.credit_tcamt_map.lock().unwrap();
        let ret = clmap.clone();
        clmap.clear();
        ret
    }

    /// 获取股份头寸
    pub fn pop_credit_tcpos(&self) -> CreditTcPosMap {
        let clmap = self.credit_tcpos_map.lock().unwrap();
        let ret = clmap.clone();
        clmap.clear();
        ret
    }

    /// 获取信用集中度
    pub fn pop_credit_concentration(&self) -> CreditConcentrationMap {
        let ccmap = self.credit_concentrat_map.lock().unwrap();
        let ret = ccmap.clone();
        ccmap.clear();
        ret
    }
}

impl RiskStkBuf {
    /// 查询信用融资偿还明细
    pub fn qry_credit_redtl_amt(
        &self,
        req: &ReqQryCreditReDtlField,
        page_index: i32,
        page_size: i32,
    ) -> (i32, CreditReturnDtlVec) {
        log::debug!(
            "qry_credit_reamtdtl: accid:{}, exch:{}, insid:{}, reid:{}, ordsysid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.AccID,
            req.ExchID,
            req.InstrumentID,
            req.ReID,
            req.OrdSysID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut redtl_vec = CreditReturnDtlVec::new();
        if page_size <= 0 {
            return (cnt, redtl_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_redtl_vec: CreditReturnDtlVec = self.qry_credit_redtl_amt_map.iter().map(|it| it.value().clone()).collect();
        sort_redtl_vec.sort_by(|a, b| a.No.cmp(&b.No));
        sort_redtl_vec.into_iter().for_each(|rd| {
            if !req.AccID.is_empty() {
                if req.AccID != rd.AccID {
                    return;
                }
            }

            if 0 != req.ExchID {
                if req.ExchID != rd.ExchID {
                    return;
                }
            }

            if 0 != req.ReType {
                if req.ReType != rd.ReType {
                    return;
                }
            }

            if !req.ReID.is_empty() {
                if !rd.ReID.contains(&req.ReID) {
                    return;
                }
            }

            if !req.OrdSysID.is_empty() {
                if !rd.OrdSydID.contains(&req.OrdSysID) {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !rd.InsID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if rd.Time < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if rd.Time > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                redtl_vec.push(rd);
            }

            cnt += 1;
        });

        (cnt, redtl_vec)
    }

    /// 查询出入金(page_index从0开始)
    pub fn qry_wd(&self, req: &ReqQryWithdrawDepositField, page_index: i32, page_size: i32) -> (i32, WithdrawDepositVec) {
        log::debug!(
            "qry_wd: exch:{}, accountid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut wd_vec = WithdrawDepositVec::new();
        if page_size <= 0 {
            return (cnt, wd_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_wd_vec: WithdrawDepositVec = self.qry_withdraw_deposit_map.iter().map(|it| it.value().clone()).collect();
        sort_wd_vec.sort_by(|a, b| b.DealTime.cmp(&a.DealTime));
        sort_wd_vec.into_iter().for_each(|wd| {
            if 0 != req.ExchID {
                if req.ExchID != wd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != wd.AccountID {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if wd.DealTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if wd.DealTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                wd_vec.push(wd);
            }

            cnt += 1;
        });

        (cnt, wd_vec)
    }

    /// 查询信用授权额度明细
    pub fn qry_credit_limit(&self, req: &ReqQryCreditLimitField, page_index: i32, page_size: i32) -> (i32, WithdrawDepositVec) {
        log::debug!(
            "qry_credit_limit: ltype:{} exch:{}, accountid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.LType,
            req.ExchID,
            req.AccountID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut wd_vec = WithdrawDepositVec::new();
        if page_size <= 0 {
            return (cnt, wd_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_wd_vec: WithdrawDepositVec = self.qry_credit_limit_map.iter().map(|it| it.value().clone()).collect();
        sort_wd_vec.sort_by(|a, b| b.DealTime.cmp(&a.DealTime));
        sort_wd_vec.into_iter().for_each(|wd| {
            if 0 != req.LType {
                if req.LType != wd.FrontNo {
                    return;
                }
            }

            if 0 != req.ExchID {
                if req.ExchID != wd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != wd.AccountID {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if wd.DealTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if wd.DealTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                wd_vec.push(wd);
            }

            cnt += 1;
        });

        (cnt, wd_vec)
    }

    /// 查询信用资金头寸明细
    pub fn qry_credit_tcamt(&self, req: &ReqQryCreditTcAmtField, page_index: i32, page_size: i32) -> (i32, WithdrawDepositVec) {
        log::debug!(
            "qry_credit_tcamt: exch:{}, accountid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut wd_vec = WithdrawDepositVec::new();
        if page_size <= 0 {
            return (cnt, wd_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_wd_vec: WithdrawDepositVec = self.qry_credit_tcamt_map.iter().map(|it| it.value().clone()).collect();
        sort_wd_vec.sort_by(|a, b| b.DealTime.cmp(&a.DealTime));
        sort_wd_vec.into_iter().for_each(|wd| {
            if 0 != req.ExchID {
                if req.ExchID != wd.ExchID {
                    return;
                }
            }

            if !req.AccountID.is_empty() {
                if req.AccountID != wd.AccountID {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if wd.DealTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if wd.DealTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                wd_vec.push(wd);
            }

            cnt += 1;
        });

        (cnt, wd_vec)
    }

    /// 查询股份划转(page_index从0开始)
    pub fn qry_positrans(&self, req: &ReqQryPositionTransStkField, page_index: i32, page_size: i32) -> (i32, PosTransVec) {
        log::debug!(
            "qry_positrans: exch:{}, accountid:{}, insid:{}, start:{}, end:{}, pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.InstrumentID,
            req.TransTime0,
            req.TransTime1,
            page_index,
            page_size
        );

        let mut cnt = 0;
        let mut pt_vec = PosTransVec::new();
        if page_size <= 0 {
            return (cnt, pt_vec);
        }

        let start_index = page_index * page_size;
        let end_index = start_index + page_size;

        let mut sort_pt_vec: PosTransVec = self.postrans_map.iter().map(|it| it.value().clone()).collect();
        sort_pt_vec.sort_by(|a, b| b.DealTime.cmp(&a.DealTime));
        sort_pt_vec.into_iter().for_each(|pt| {
            if req.AccountID != pt.AccountID {
                return;
            }

            if 0 != req.ExchID {
                if req.ExchID != pt.ExchID {
                    return;
                }
            }

            if !req.InstrumentID.is_empty() {
                if !pt.InstrumentID.contains(&req.InstrumentID) {
                    return;
                }
            }

            if 0 != req.TransTime0 {
                if pt.DealTime < req.TransTime0 {
                    return;
                }
            }

            if 235959 != req.TransTime1 {
                if pt.DealTime > req.TransTime1 {
                    return;
                }
            }

            if cnt >= start_index && cnt < end_index {
                pt_vec.push(pt);
            }

            cnt += 1;
        });

        (cnt, pt_vec)
    }
}

impl RiskStkBuf {
    /// 断开连接
    pub fn on_rtn_disconnect(&self) {
        if let Some(rtn) = self.rtn_abnormalstate_callback.as_ref() {
            rtn.on_rtn_disconnect();
        }
    }

    /// 查询合约响应
    pub fn on_rsp_qry_instrument(&self, reqid: i32, ins: InstrumentField) {
        if !ins.insid.is_empty() {
            let mut ins = ins;
            ins.exchname = crate::common::ticonvert::TIConvert::exch_name(ins.exchid).to_owned();
            ins.ptw = {
                let mut ret = 0;
                let ptw = ins.pricetick.to_string();
                if let Some(idx) = ptw.find(".") {
                    ret = ptw.len() - idx - 1;
                }
                if ret <= 4 {
                    if 0 == ret {
                        ret = 2;
                    }
                    ret as i32
                } else {
                    4
                }
            };
            if ins.name.is_empty() {
                ins.name = ins.symbol.clone();
            }

            let mut gins = crate::common::global::INS.get().unwrap().write().unwrap();
            gins.push_ins(ins);
        }
    }

    /// 资金消息
    pub fn on_rtn_account(&self, acc: AccountsStkField) {
        let acc_gacha = CreditGaChaField {
            ExchID: acc.ExchID,
            AccountID: acc.AccountID.clone(),
            XYAllDebt: acc.XYAllDebt,
            XYRzDebt: acc.XYRzDebt,
            XYRqDebt: acc.XYRqDebt,
            XYInitDebt: acc.XYInitDebt,
            XYInDebt: acc.XYInDebt,
            XYOutDebt: acc.XYOutDebt,
        };
        {
            let credit_gacha_map = self.credit_gacha_map.lock().unwrap();
            credit_gacha_map.insert(std::format!("{}{}", acc_gacha.ExchID, acc_gacha.AccountID), acc_gacha);
        }

        {
            let acc_map = self.account_map.lock().unwrap();
            acc_map.insert(std::format!("{}{}", acc.ExchID, acc.AccountID), acc.clone());

            let acc_map = self.account_2_map.lock().unwrap();
            acc_map.insert(std::format!("{}{}", acc.ExchID, acc.AccountID), acc);
        }
    }

    /// 信用合约消息
    pub fn on_rtn_credit_contract(&self, cc: CreditContractField) {
        if 1 == cc.IsExpire {
            let cc_map = self.credit_expite_contract_map.lock().unwrap();
            cc_map.insert(std::format!("{}{}{}", cc.ExchID, cc.AccID, cc.OrderSysID), cc.clone());
        }

        let cc_map = self.credit_contract_map.lock().unwrap();
        cc_map.insert(std::format!("{}{}{}", cc.ExchID, cc.AccID, cc.OrderSysID), cc);
    }

    /// 信用授信额度消息
    pub fn on_rtn_credit_limit(&self, limit: CreditLimitField) {
        match limit.LType {
            TIType::CREDIT_LIMIT_AMT => {
                let clmap = self.credit_limitamt_map.lock().unwrap();
                clmap.insert(std::format!("{}{}", limit.ExchID, limit.AccID), limit);
            }
            TIType::CREDIT_LIMIT_POS => {
                let clmap = self.credit_limitpos_map.lock().unwrap();
                clmap.insert(std::format!("{}{}", limit.ExchID, limit.AccID), limit);
            }
            _ => {
                log::warn!("on_rtn_credit_limit: unknown credit limit type. {:?}", limit);
            }
        }
    }

    /// 信用资金头寸消息
    pub fn on_rtn_credit_tcamt(&self, tcamt: CreditTcAmtField) {
        let tcamtmap = self.credit_tcamt_map.lock().unwrap();
        tcamtmap.insert(std::format!("{}{}{}", tcamt.TcType, tcamt.ExchID, tcamt.AccID), tcamt);
    }

    /// 信用股份头寸消息
    pub fn on_rtn_credit_tcpos(&self, tcpos: CreditTcPosField) {
        let tcposmap = self.credit_tcpos_map.lock().unwrap();
        tcposmap.insert(
            std::format!("{}{}{}{}", tcpos.TcType, tcpos.CliID, tcpos.ExchID, tcpos.InsID),
            tcpos,
        );
    }

    /// 信用集中度消息
    pub fn on_rtn_credit_concentration(&self, cc: CreditConcentrationField) {
        let ccmap = self.credit_concentrat_map.lock().unwrap();
        ccmap.insert(
            std::format!("{}{}{}{}{}", cc.GID, cc.CType, cc.CliID, cc.ExchID, cc.InsID),
            cc,
        );
    }

    /// 信用归还明细消息
    pub fn on_rtn_credit_returndtl(&self, revdtl: CreditReturnDtlField) {
        match revdtl.Type {
            TIType::CREDIT_RETURN_TYPE_AMT => {
                self.qry_credit_redtl_amt_map.insert(revdtl.No, revdtl);
            }
            TIType::CREDIT_RETURN_TYPE_POS => {
                self.qry_credit_redtl_pos_map.insert(revdtl.No, revdtl);
            }
            _ => {
                log::warn!("on_rtn_credit_returndtl: unknown credit return type. {:?}", revdtl);
            }
        }
    }

    /// 持仓消息
    pub fn on_rtn_position(&self, position: PositionStkField) {
        let pos_map = self.pos_map.lock().unwrap();
        pos_map.insert(
            std::format!("{}{}{}", position.ExchID, position.AccountID, position.InstrumentID),
            position,
        );
    }

    /// 出入金消息
    pub fn on_rtn_withdrawdeposit(&self, wd: WithdrawDepositStkField) {
        let key = format!("{}{}", wd.AccountID, wd.BizSeqNum);

        match wd.FrontNo {
            n if n >= 0 => {
                // 出入金
                self.qry_withdraw_deposit_map.insert(key, wd);
            }
            -100 | -101 => {
                // 授信额度
                self.qry_credit_limit_map.insert(key, wd);
            }
            -103 => {
                // 资金头寸
                self.qry_credit_tcamt_map.insert(key, wd);
            }
            -104 => {
                // 股份头寸
                self.qry_credit_tcpos_map.insert(key, wd);
            }
            _ => {
                // 目前服务端发送的消息当FrontNo为负时为信用交易的授信额度与头寸的划入划出(目前结构未最终确定下来暂不处理)
                log::warn!("on_rtn_withdrawdeposit: {:?}", wd);
            }
        }
    }

    /// 股份划转
    pub fn on_rtn_position_trans(&self, position_trans: PositionTransStkField) {
        self.postrans_map.insert(
            format!("{}{}", position_trans.AccountID, position_trans.BizSeqNum),
            position_trans,
        );
    }

    /// 委托消息
    pub fn on_rtn_order(&self, ord: OrderStkField) {
        let key = {
            if !ord.OrderSysID.is_empty() {
                format!("{}{}{}", ord.ExchID, ord.AccountID, ord.OrderSysID)
            } else {
                let index = self.key_index.fetch_add(1, Ordering::SeqCst);
                format!("{}{}", ord.AccountID, index)
            }
        };

        let mut ord = ord;
        ord.Key = key;

        let mut insert_falg = false;
        if self.order_status_map.contains_key(&ord.Key) {
            let mut prev_order_status = self.order_status_map.get_mut(&ord.Key).unwrap();
            if *prev_order_status < ord.OrdStatus {
                *prev_order_status = ord.OrdStatus;
                insert_falg = true;
            }
        } else {
            insert_falg = true;
        }

        if insert_falg {
            self.order_map.lock().unwrap().insert(ord.Key.clone(), ord.clone());
            self.in_order_map.lock().unwrap().insert(ord.Key.clone(), ord);
        }
    }

    /// 成交消息
    pub fn on_rtn_trade(&self, trd: TradeStkField) {
        self.trade_vec.lock().unwrap().push(trd);
    }
}
