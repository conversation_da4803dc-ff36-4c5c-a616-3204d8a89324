/// 委托消息
#[derive(Default)]
pub struct OrderField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 原始报单交易用户代码
    pub UserID: String,

    /// 报单状态
    pub OrdStatus: i32,

    /// 本地报单编号
    pub LocalOrderNo: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 数量
    pub Volume: i32,

    /// 价格
    pub Price: f64,

    /// 止损价
    pub StopPrice: f64,

    /// 买卖方向
    pub Side: i32,

    /// 开平标志
    pub OffsetFlag: i32,

    /// 投保标志
    pub HedgeFlag: i32,

    /// 备兑标志
    pub Covered: i32,

    /// 报单价格条件
    pub PriceType: i32,

    /// 触发条件
    pub TrigCondition: i32,

    /// 成交量类型
    pub VolumeCondition: i32,

    /// 有效期类型
    pub TimeInForce: i32,

    /// 订单所有类型
    pub OwnerType: i32,

    /// 剩余数量(交易所返回字段,每个交易所的含义不同)
    pub LeavesVolume: i32,

    /// 撤销数量
    pub CancelVolume: i32,

    /// 最小成交量
    pub MinVolume: i32,

    /// 发生时间
    pub TransTime: i32,
}

/// 组合持仓成份合约数
#[derive(Default)]
pub struct OmlItem {
    /// 合约编码
    pub LegInsID: String,

    /// 合约方向
    pub LegSide: i32,

    /// 备兑标签
    pub Covered: i32,

    /// 申报数量
    pub LegVolume: i32,
}

/// 组合持仓信息
#[derive(Default)]
pub struct OmlPositionField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 组合策略编码
    pub CombID: String,

    /// 组合编码
    pub CombInstID: String,

    /// 数量
    pub Volume: i32,

    /// 冻结数量
    pub FrozenVolume: i32,

    /// 成分合约数
    pub NoLeges: i32,

    /// 成份合约
    pub Items: Vec<OmlItem>,
}

/// 组合消息
#[derive(Default)]
pub struct OmlField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 组合策略编码
    pub CombID: String,

    /// 组合编码
    pub CombInstID: String,

    /// 数量
    pub Volume: i32,

    /// 冻结数量
    pub FrozenVolume: i32,

    /// 成分合约数
    pub NoLeges: i32,

    /// 成份合约
    pub Items: Vec<OmlItem>,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 组合与拆分组合
    pub Side: i32,

    /// 本地报单编号
    pub LocalOrderNo: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 原始报单交易用户代码
    pub UserID: String,

    /// 订单所有类型
    pub OwnerType: i32,

    /// 发生时间
    pub TransTime: i32,
}

/// 委托请求查询消息
#[derive(Default)]
pub struct ReqQryOrderField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 订单状态
    pub OrdStatus: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 委托响应查询消息
#[derive(Default)]
pub struct RspQryOrderField {
    /// 委托消息
    pub array: Vec<OrderField>,
}

/// 组合请求查询消息
#[derive(Default)]
pub struct ReqQryOmlField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 组合策略
    pub CombID: String,

    /// 组合编码
    pub CombInsID: String,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 组合响应查询消息
#[derive(Default)]
pub struct RspQryOmlField {
    /// 组合消息
    pub array: Vec<OmlField>,
}
