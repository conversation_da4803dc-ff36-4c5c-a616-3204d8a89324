use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    apiproc::tradeoptbuf::CtpOrderMap,
    common::{
        config::Config,
        global::{CFG, TRADE_OPT_API, TRADE_OPT_BUF},
        ticonvert::TIConvert,
    },
    slintui::*,
};
use dashmap::DashMap;
use slint::*;
use tiapi::protocol_pub::{
    order_trade_ctp::CtpOrderField,
    trade_req_ctp::{CtpReqInputOrderActionField, CtpRspInputOrderActionField},
};

use super::tradeopt::show_cancel_msg_box;

/// 在途 - 委托 - 数据
struct InOrderDatas {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpOrderMap>,

    /// 撤单响应数据
    rsp_cancel: Arc<Mutex<CtpRspInputOrderActionField>>,

    /// 在撤单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_in_cancel: AtomicBool,

    /// 用于全撤时, 当在撤单过程中发生错误停止继续撤单
    need_stop_cancel: AtomicBool,
}
impl InOrderDatas {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpOrderMap::new()),

            rsp_cancel: Arc::new(Mutex::new(CtpRspInputOrderActionField::default())),
            is_err_in_cancel: AtomicBool::new(false),

            need_stop_cancel: AtomicBool::new(false),
        }
    }
}
static IN_ORDER_DATAS: OnceLock<InOrderDatas> = OnceLock::new();

/// 在途 - 委托
pub(super) struct InOrder {}

// 构建与初始化
impl InOrder {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        let _ = IN_ORDER_DATAS.set(InOrderDatas::new());

        let app_inord = app.global::<OptTrd_InOrder>();

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inord.on_req_tip_toggled(move |checked| InOrder::on_req_tip_toggled(&app_weak, checked));

        // 注册撤单笔事件
        let app_weak: Weak<App> = app.as_weak();
        app_inord
            .on_cancel_order_clieked(move |ord, need_confirm| InOrder::on_cancel_order_clieked(&app_weak, ord, need_confirm));

        // 注册撤全部事件
        let app_weak = app.as_weak();
        app_inord.on_cancel_order_all_clieked(move |row_cnt, need_confirm| {
            InOrder::on_cancel_order_all_clieked(&app_weak, row_cnt, need_confirm)
        });

        // 注册撤买/卖事件
        let app_weak = app.as_weak();
        app_inord.on_cancel_order_bs_clieked(move |bs, row_cnt, need_confirm| {
            InOrder::on_cancel_order_bs_clieked(&app_weak, bs, row_cnt, need_confirm)
        });

        log::trace!("InOrder init completed");
    }
}

// 查询与响应
impl InOrder {
    /// 请求查询委托信息
    pub fn qry_in_order(&self) {
        let ret = { TRADE_OPT_BUF.get().unwrap().read().unwrap().pop_in_order() };
        if ret.is_empty() {
            return;
        }

        let mut data_changed = false;
        let inord_datas = IN_ORDER_DATAS.get().unwrap();

        ret.iter().for_each(|it| {
            let key = it.key();
            let ord = it.value();

            if inord_datas.data_map.contains_key(key) {
                if 1 != ord.LastStatus && 2 != ord.LastStatus {
                    inord_datas.data_map.remove(key);
                    data_changed = true;
                } else {
                    let mut pre_order = inord_datas.data_map.get_mut(key).unwrap();
                    pre_order.LastStatus = ord.LastStatus;
                    pre_order.VolumeTotal = ord.VolumeTotal;
                    pre_order.InsertTime = ord.InsertTime.clone();
                    pre_order.OrderLocalID = ord.OrderLocalID.clone();
                    pre_order.UserID = ord.UserID.clone();
                    data_changed = true;
                }
            } else {
                if 1 == ord.LastStatus || 2 == ord.LastStatus {
                    inord_datas.data_map.insert(key.clone(), ord.clone());
                    data_changed = true;
                }
            }
        });

        if data_changed {
            inord_datas.data_changed.store(true, Ordering::Relaxed);
        }
    }

    /// 撤单操作响应
    pub fn on_rsp_inputorderaction(&self, rsp: CtpRspInputOrderActionField) {
        let inord_datas = IN_ORDER_DATAS.get().unwrap();

        inord_datas.need_stop_cancel.store(true, Ordering::Relaxed);

        *inord_datas.rsp_cancel.lock().unwrap() = rsp;
        inord_datas.is_err_in_cancel.store(true, Ordering::Relaxed);
    }
}

// 事件
impl InOrder {
    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();
        app_set.set_trdreqtip_ordercancel(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputOrderAction = checked;
    }

    /// 撤单笔
    fn on_cancel_order_clieked(app_weak: &Weak<App>, ord: CancelOrderField, need_confirm: bool) {
        let app = app_weak.unwrap();
        let in_ord = app.global::<OptTrd_InOrder>();

        if ord.exchid.is_empty() {
            show_cancel_msg_box(&app, 1, Default::default(), "请先选中要撤的报单".into());
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputOrderAction } {
            show_cancel_msg_box(
                &app,
                100,
                "撤单请求确认".into(),
                slint::format!(
                    "资金账户: {}\n\n交易所: {}\n交易所编码: [{}]",
                    ord.accountid,
                    ord.exchid,
                    ord.ordersysid
                ),
            );

            return;
        }

        let mut req = CtpReqInputOrderActionField::default();
        req.ExchangeID = ord.exchid.as_str().into();
        req.InvestorID = ord.accountid.as_str().into();
        req.OrderSysID = ord.ordersysid.as_str().into();

        let api = TRADE_OPT_API.get().unwrap().read().unwrap();
        if let Err(err) = api.req_orderaction(0, &req) {
            show_cancel_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("请求撤单失败.\n\nn错误信息: {}", err),
            );
        }
    }

    /// 撤全部
    fn on_cancel_order_all_clieked(app_weak: &Weak<App>, row_cnt: i32, need_confirm: bool) {
        let app = app_weak.unwrap();
        let in_ord = app.global::<OptTrd_InOrder>();

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputOrderAction } {
            show_cancel_msg_box(&app, 101, Default::default(), "是否撤销当前所有的有效报单?".into());
            return;
        }

        if 0 == row_cnt {
            show_cancel_msg_box(&app, 1, Default::default(), "没有报单可撤".into());
            return;
        }

        // 获取当前所有的在途报单
        let mut ord_vec = Vec::new();
        for row_index in 0..row_cnt {
            ord_vec.push(in_ord.invoke_get_cancel_order(row_index))
        }

        // 开始撤单
        let inord_datas = IN_ORDER_DATAS.get().unwrap();
        inord_datas.need_stop_cancel.store(false, Ordering::Relaxed);
        let api = TRADE_OPT_API.get().unwrap().read().unwrap();
        for ord in ord_vec {
            if inord_datas.need_stop_cancel.load(Ordering::Relaxed) {
                // 撤单过程中遇到错误, 不再继续撤单
                return;
            }

            let mut req = CtpReqInputOrderActionField::default();
            req.ExchangeID = ord.exchid.as_str().into();
            req.InvestorID = ord.accountid.as_str().into();
            req.OrderSysID = ord.ordersysid.as_str().into();
            if let Err(err) = api.req_orderaction(0, &req) {
                show_cancel_msg_box(
                    &app,
                    1,
                    Default::default(),
                    slint::format!(
                        "请求撤单失败\n\n资金账户: {}\n\n交易所: {}\n交易所编码: [{}]\n\n错误信息: {}",
                        ord.accountid,
                        ord.exchid,
                        ord.ordersysid,
                        err
                    ),
                );
                return;
            }
        }
    }

    /// 撤买/卖单
    fn on_cancel_order_bs_clieked(app_weak: &Weak<App>, buy_sell: i32, row_cnt: i32, need_confirm: bool) {
        let app = app_weak.unwrap();
        let in_ord = app.global::<OptTrd_InOrder>();

        let bs_str = if 0 == buy_sell { "买入" } else { "卖出" };

        if need_confirm {
            let need_confirm = { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputOrderAction };
            if need_confirm {
                show_cancel_msg_box(
                    &app,
                    if 0 == buy_sell { 102 } else { 103 },
                    Default::default(),
                    slint::format!("是否撤销当前所有的有效[{}]报单?", bs_str),
                );
                return;
            }
        }

        // 获取当前所有的在途报单
        let mut ord_vec = Vec::new();
        for row_index in 0..row_cnt {
            let co = in_ord.invoke_get_cancel_order(row_index);
            if 0 == buy_sell {
                if "买" == co.bs.as_str() {
                    ord_vec.push(co);
                }
            } else {
                if "卖" == co.bs.as_str() {
                    ord_vec.push(co);
                }
            }
        }

        if 0 == ord_vec.len() {
            show_cancel_msg_box(&app, 1, Default::default(), slint::format!("没有[{}]报单可撤", bs_str));
            return;
        }

        // 开始撤单
        let inord_datas = IN_ORDER_DATAS.get().unwrap();
        inord_datas.need_stop_cancel.store(false, Ordering::Relaxed);
        let api = TRADE_OPT_API.get().unwrap().read().unwrap();
        for ord in ord_vec {
            if inord_datas.need_stop_cancel.load(Ordering::Relaxed) {
                // 撤单过程中遇到错误, 不再继续撤单
                return;
            }

            let mut req = CtpReqInputOrderActionField::default();
            req.ExchangeID = ord.exchid.as_str().into();
            req.InvestorID = ord.accountid.as_str().into();
            req.OrderSysID = ord.ordersysid.as_str().into();
            if let Err(err) = api.req_orderaction(0, &req) {
                show_cancel_msg_box(
                    &app,
                    1,
                    "请求撤单失败".into(),
                    slint::format!(
                        "资金账户: {}\n\n交易所: {}\n交易所报单编码: [{}]\n\n错误信息: {}",
                        ord.accountid,
                        ord.exchid,
                        ord.ordersysid,
                        err
                    ),
                );
                return;
            }
        }
    }
}

// 更新UI
impl InOrder {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let inord_datas = IN_ORDER_DATAS.get().unwrap();

        let is_err_in_cancel = inord_datas
            .is_err_in_cancel
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();

        let rsp_cancel: CtpRspInputOrderActionField;
        let data_map_clone: Arc<DashMap<String, CtpOrderField>>;

        if !is_err_in_cancel {
            let data_changed = inord_datas
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok();
            if !data_changed {
                return;
            }

            rsp_cancel = CtpRspInputOrderActionField::default();
            data_map_clone = inord_datas.data_map.clone();
        } else {
            rsp_cancel = inord_datas.rsp_cancel.lock().unwrap().clone();
            data_map_clone = Arc::new(DashMap::new());
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            if is_err_in_cancel {
                show_cancel_msg_box(
                    &app,
                    1,
                    "撤单失败".into(),
                    slint::format!(
                        "交易所报单编号:{}\n错误信息:[{}, {}]",
                        rsp_cancel.OrderSysID,
                        rsp_cancel.ec,
                        rsp_cancel.em
                    ),
                );
                return;
            }

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            data_map_clone.iter().for_each(|ord| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                let exchid = {
                    match ord.ExchangeID.as_str() {
                        "SSE" => 1,
                        "SZSE" => 2,
                        _ => -1,
                    }
                };
                let ti = TIConvert::ctp_opt_order_to_ti(exchid, ord.OrderPriceType, ord.TimeCondition, ord.VolumeCondition);

                // 资金账号
                items.push(ord.InvestorID.clone().into());

                // 交易所
                items.push(ord.ExchangeID.clone().into());

                // 交易编码
                items.push(ord.ClientID.clone().into());

                // 发生时间
                items.push(ord.InsertTime.clone().into());

                // 合约编码
                items.push(ord.InstrumentID.clone().into());

                // 买买方向
                items.push(TIConvert::ctp_side(ord.Direction).into());

                // 开平标志
                items.push(
                    TIConvert::ctp_offsetflag({
                        let ret = ord.CombOffsetFlag.parse::<i32>();
                        if let Ok(of) = ret {
                            of + 48
                        } else {
                            -1
                        }
                    })
                    .into(),
                );

                // 数量
                items.push(slint::format!("{}", ord.VolumeTotalOriginal));

                // 价格
                items.push(slint::format!("{:.*}", Config::PRICE_DIGITS, ord.LimitPrice));

                // 报单状态
                items.push(TIConvert::order_status(ord.LastStatus).into());

                // 交易所报单编号
                items.push(ord.OrderSysID.clone().into());

                // 本地报单编号
                items.push(ord.OrderLocalID.clone().into());

                // 报单价格条件
                items.push(TIConvert::pricetype(ti.0).into());

                // 有效期类型
                items.push(TIConvert::timeinforce(ti.1).into());

                // 成交量类型
                items.push(TIConvert::volume_condition(ti.2).into());

                // 投资者类型
                items.push(TIConvert::ownertype(ord.TIOwnerType).into());

                // 触发条件
                items.push(TIConvert::ctp_trigcondition(ord.ContingentCondition).into());

                // 最小成交量
                items.push(slint::format!("{}", ord.MinVolume));

                // 用户代码
                items.push(ord.UserID.clone().into());

                // 备兑标志
                items.push(
                    TIConvert::ctp_hedgeflag2({
                        let ret = ord.CombHedgeFlag.parse::<i32>();
                        if let Ok(of) = ret {
                            of + 48
                        } else {
                            -1
                        }
                    })
                    .into(),
                );

                // 序号(用于排序)
                items.push(slint::format!("{:>13}", ord.SequenceNo));

                row_data.push(items.into());
            });

            // 按时间列降序显示
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(20 as usize).unwrap();
                let c_b = r_b.row_data(20 as usize).unwrap();
                c_b.cmp(&c_a)
            }));

            app.global::<OptTrd_InOrder>().set_row_data(sort_model.into());
        });
    }
}
