import { Button, LineEdit, Icons, Label } from "@zstk/lib.slint";
import { WndRec } from "../widgets/wndrec.slint";
import { StkTrd_CreditContract } from "struct.slint";
import { AppCom, WidgetColor, TradeColor } from "../struct.slint";

export component CreditReturnPosPage {
    WndRec {
        title: @tr("现券还券");
        can_close: StkTrd_CreditContract.repos_ctrls_enabled;

        btn_close_clicked => {
            StkTrd_CreditContract.show_return_pos = false;
        }

        init => {
            StkTrd_CreditContract.repos_req_ret = "";
        }

        VerticalLayout {
            spacing: 10px;
            padding: 10px;

            HorizontalLayout {
                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "资金账号";
                }

                LineEdit {
                    width: 150px;
                    read_only: true;
                    text: StkTrd_CreditContract.repos_accid;
                }
            }

            HorizontalLayout {
                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "证券代码";
                }

                LineEdit {
                    enabled: StkTrd_CreditContract.repos_ctrls_enabled;
                    placeholder_text: "请输入";
                    text <=> StkTrd_CreditContract.repos_insid;
                    action_icon: Icons.close;

                    action => {
                        self.text = "";
                    }

                    changed text => {
                        StkTrd_CreditContract.repos_req_ret = "";
                    }
                }
            }

            HorizontalLayout {
                Label {
                    width: 60px;
                    height: 26px;
                    vertical_alignment: center;
                    text: "还券数量";
                }

                LineEdit {
                    enabled: StkTrd_CreditContract.repos_ctrls_enabled;
                    placeholder_text: "请输入";
                    text <=> StkTrd_CreditContract.repos_pos;
                    action_icon: Icons.close;

                    action => {
                        self.text = "";
                    }

                    changed text => {
                        StkTrd_CreditContract.repos_req_ret = "";
                    }
                }
            }

            Rectangle {
                height: 2px;
            }

            HorizontalLayout {
                Label {
                    height: 26px;
                    vertical_alignment: center;
                    color: TradeColor.dir_sell;
                    text <=> StkTrd_CreditContract.repos_req_ret;
                }

                Rectangle {
                    horizontal_stretch: 1;
                }

                Button {
                    width: 50px;
                    height: 25px;
                    background: WidgetColor.btn_background;
                    enabled: StkTrd_CreditContract.repos_ctrls_enabled;
                    text: "还券";
                    clicked => {
                        if AppCom.check_td_session_timeout() {
                             return ;
                        }

                        StkTrd_CreditContract.repos_ctrls_enabled = false;
                        StkTrd_CreditContract.repos_clicked();
                    }
                }
            }
        }
    }
}
