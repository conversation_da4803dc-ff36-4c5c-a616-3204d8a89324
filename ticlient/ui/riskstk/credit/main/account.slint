import { Theme, TableView, TVColumnHeader, TVVisible, Fonts, Label, Radius, Icons, ComboBox } from "@zstk/lib.slint";
import { ColWidth, NotifyColor, AppCom, AppInerArgs } from "../../../struct.slint";
import { StkRskCredit_Account, StkRskCredit_Com } from "../struct.slint";

export component AccountPage {

    preferred_width: 100%;
    preferred_height: 100%;

    in property <bool> is_used_for_main: true;  // 是否用于主界面(同样也可以用于按资金账号显示的详情界面)

    private property <Point> right_up_point;

    private property <length> header_exchid_width: AppInerArgs.show_detail ? ColWidth.exchid : 0.5px;
    private property <TVVisible> header_exchid_visible: AppInerArgs.show_detail ? TVVisible.Visible : TVVisible.Invisible;

    private property <TVColumnHeader> header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 2,
        columns: [
            /*  0 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: header_exchid_width, visible: header_exchid_visible },
            /*  1 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },

            /*  2 */{ title: @tr("总资产"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  3 */{ title: @tr("可用资金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },

            /*  4 */{ title: @tr("总负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  5 */{ title: @tr("保证金可用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            /*  6 */{ title: @tr("维持担保比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },

            /*  7 */{ title: @tr("当日盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /*  8 */{ title: @tr("资金收支"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /*  9 */{ title: @tr("资金冻结"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 10 */{ title: @tr("手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 11 */{ title: @tr("冻结手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 12 */{ title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 13 */{ title: @tr("平仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 14 */{ title: @tr("入金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 15 */{ title: @tr("出金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 16 */{ title: @tr("证券市值"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },

            /* 17 */{ title: @tr("融资负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 18 */{ title: @tr("融券负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 19 */{ title: @tr("两融冻结保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 20 */{ title: @tr("两融冻结费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 21 */{ title: @tr("未成交充抵保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_130 },
            /* 22 */{ title: @tr("充抵保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 23 */{ title: @tr("融资盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 24 */{ title: @tr("融券盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 25 */{ title: @tr("融券卖出金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 26 */{ title: @tr("融资保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 27 */{ title: @tr("融券保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 28 */{ title: @tr("两融利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 29 */{ title: @tr("两融费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },

            /* 30 */{ title: @tr("上日可用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
        ]
    };

    VerticalLayout {
        if is_used_for_main : HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "资金账号";
                vertical_alignment: center;
            }
            ComboBox {
                width: 130px;
                height: AppCom.widgets_height;
                drop_down_list: false;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_Account.filter_accid;
                model <=> StkRskCredit_Com.accid_model;

                edited(by_selected) => {
                    StkRskCredit_Account.filter_changed();
                }

                selected => {
                    StkRskCredit_Account.filter_changed();
                }
            }

            Rectangle {}
        }

        TableView {
            sortable: true;
            column_header: header;
            row_count: StkRskCredit_Account.row_data.length;

            // 默认以维保比例(6, 但6使用的是不可见的第31列)进行排序
            sort_canceled => {
                StkRskCredit_Account.sort_asc_column_index = 31;
                StkRskCredit_Account.sort_dec_column_index = -1;
                StkRskCredit_Account.sort_ascending(StkRskCredit_Account.sort_asc_column_index);
            }
            sort_ascending(index) => {
                StkRskCredit_Account.sort_asc_column_index = 6 == index ? 31 : index;
                StkRskCredit_Account.sort_dec_column_index = -1;
                StkRskCredit_Account.sort_ascending(StkRskCredit_Account.sort_asc_column_index);
                true
            }
            sort_descending(index) => {
                StkRskCredit_Account.sort_asc_column_index = -1;
                StkRskCredit_Account.sort_dec_column_index = 6 == index ? 31 : index;
                StkRskCredit_Account.sort_descending(StkRskCredit_Account.sort_dec_column_index);
                true
            }

            get_cell_data(row_index, column_index) => {
                StkRskCredit_Account.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                // 入金
                if 14 == column_index {
                    return NotifyColor.normal;
                }

                // 出金
                if 15 == column_index {
                    return NotifyColor.caution;
                }

                // 这几列数据中小数含有逗号, ui处理不了, 交给回调处理
                if 6 == column_index {
                    return StkRskCredit_Account.get_row_data_color(row_index, 10000, data);
                }
                if 3 == column_index        // 可用资金
                    || 7 == column_index    // 当日盈亏
                    || 8 == column_index    // 资金收支
                    || 12 == column_index   // 持仓盈亏
                    || 13 == column_index   // 平仓盈亏
                    || 5 == column_index    // 保证金可用
                    || 23 == column_index   // 融资浮盈
                    || 24 == column_index   // 融券浮盈
                {
                    return StkRskCredit_Account.get_row_data_color(row_index, column_index, data);
                }

                if 4 == column_index        // 总负债
                    || 17 == column_index   // 融资负债
                    || 18 == column_index   // 融券负债
                {
                    return StkRskCredit_Account.get_row_data_color(row_index, 10001, data);
                }

                NotifyColor.default
            }

            cell_double_clicked(row_index, column_index) => {
                StkRskCredit_Com.accountid = StkRskCredit_Account.row_data[row_index][1];
                StkRskCredit_Com.trd_accountid_changed(StkRskCredit_Com.accountid);
            }

            cell_pointer_event(row_index, column_index, event, point) => {
                if PointerEventKind.up == event.kind {
                    if PointerEventButton.left == event.button {
                        StkRskCredit_Account.sel_accid = StkRskCredit_Account.row_data[row_index][1];
                        if "" != StkRskCredit_Account.sel_accid {
                            self.select_full_row = true;
                            self.set_current_row(row_index);
                        }
                    } else if PointerEventButton.right == event.button {
                        StkRskCredit_Account.sel_accid = StkRskCredit_Account.row_data[row_index][1];
                        if "" != StkRskCredit_Account.sel_accid {
                            self.select_full_row = true;
                            self.set_current_row(row_index);

                            right_up_point = point;
                        }
                    }
                }
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_Account.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_Account.row_data[row_index]);
                }
            }
        }
    }
}