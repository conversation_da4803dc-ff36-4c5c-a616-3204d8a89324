import { Fonts, TableView, Label, ComboBox, LineEdit, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../../../struct.slint";
import { StkRskCredit_CreditConcentration } from "../struct.slint";

export component CreditConcentrationPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "组名称";
                vertical_alignment: center;
            }
            ComboBox {
                width: 120px;
                height: AppCom.widgets_height;
                placeholder_text: "组名称";
                drop_down_list: false;
                current_text <=> StkRskCredit_CreditConcentration.filter_group_name;
                model <=> StkRskCredit_CreditConcentration.group_name_model;

                edited => {
                    StkRskCredit_CreditConcentration.filter_changed();
                }
            }

            Label {
                text: "类型";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "集中度类型";
                current_text <=> StkRskCredit_CreditConcentration.filter_sub_str;
                model: [
                    { text: "" },
                    { text: "单一证券", tag: 1 },
                    { text: "全组", tag: 2 },
                ];

                selected => {
                    StkRskCredit_CreditConcentration.filter_sub_index = self.current_value.tag;
                    if StkRskCredit_CreditConcentration.filter_sub_index == 2 {
                        StkRskCredit_CreditConcentration.filter_insid = "";
                    }
                    StkRskCredit_CreditConcentration.filter_changed();
                }
            }

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            ComboBox {
                width: 90px;
                height: AppCom.widgets_height;
                placeholder_text: "交易所编码";
                current_text <=> StkRskCredit_CreditConcentration.filter_exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkRskCredit_CreditConcentration.filter_changed();
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }
            LineEdit {
                width: 85px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                enabled: 2 != StkRskCredit_CreditConcentration.filter_sub_index;
                text <=> StkRskCredit_CreditConcentration.filter_insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }

                changed text => {
                    if 2 != StkRskCredit_CreditConcentration.filter_sub_index {
                        StkRskCredit_CreditConcentration.filter_changed();
                    }
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 7,
                columns: [
                    /*  0 */{ title: @tr("组编码"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_60 },
                    /*  1 */{ title: @tr("组名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    /*  2 */{ title: @tr("类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    /*  3 */{ title: @tr("资金账号"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    /*  4 */{ title: @tr("交易所 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid, tips: "全组集中度时为空" },
                    /*  5 */{ title: @tr("股东代码 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid, tips: "全组集中度时为空" },
                    /*  6 */{ title: @tr("证券代码 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100, tips: "全组集中度时为空" },
                    /*  7 */{ title: @tr("市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "持仓市值 + 在途市值" },
                    /*  8 */{ title: @tr("总资产 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "可用资金 + 总持仓市值 + 总在途市值" },
                    /*  9 */{ title: @tr("维持担保比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_140 },
                    /* 10 */{ title: @tr("集中度上限"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    /* 11 */{ title: @tr("集中度比例"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    /* 12 */{ title: @tr("是否异常"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    /* 13 */{ title: @tr("证券市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "单一证券或组内证券的持仓市值" },
                    /* 14 */{ title: @tr("总证券市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "所有证券的持仓市值" },
                    /* 15 */{ title: @tr("未成交市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "单一证券或组内证券的未成交市值" },
                    /* 16 */{ title: @tr("总未成交市值 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120, tips: "所有证券的未成交市值" },
                ]
            };
            row_count: StkRskCredit_CreditConcentration.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_CreditConcentration.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkRskCredit_CreditConcentration.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_CreditConcentration.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_CreditConcentration.row_data[row_index]);
                }
            }
        }
    }
}
