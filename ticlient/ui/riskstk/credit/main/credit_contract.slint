import { <PERSON>, <PERSON><PERSON>s, TableView, Label, ComboBox, LineEdit, <PERSON>ton, Radius, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom, NotifyColor } from "../../../struct.slint";
import { StkRskCredit_CreditContract, StkRskCredit_Com } from "../struct.slint";

export component CreditContractPage {

    preferred_width: 100%;
    preferred_height: 100%;

    in property <bool> is_used_for_main: true;  // 是否用于主界面(同样也可以用于按资金账号显示的详情界面)

    private property <Point> right_up_point;
    private property <int> sel_return_type;     // 1. 融资; 2. 融券

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            if is_used_for_main: Label {
                text: "资金账号";
                vertical_alignment: center;
            }
            if is_used_for_main: ComboBox {
                width: 130px;
                height: AppCom.widgets_height;
                drop_down_list: false;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_CreditContract.filter_accid;
                model <=> StkRskCredit_Com.accid_model;

                edited(by_selected) => {
                    StkRskCredit_CreditContract.filter_changed();
                }

                selected => {
                    StkRskCredit_CreditContract.filter_changed();
                }
            }

            Label {
                text: "合约类型";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_CreditContract.filter_side_str;
                model: [
                    { text: "", tag: 0, },
                    { text: "融资买入", tag: 3, },
                    { text: "融券卖出", tag: 4, },
                ];

                selected => {
                    StkRskCredit_CreditContract.filter_side = self.current_value.tag;
                    StkRskCredit_CreditContract.filter_changed();
                }
            }

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            ComboBox {
                width: 80px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_CreditContract.filter_exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkRskCredit_CreditContract.filter_changed();
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }
            LineEdit {
                width: 80px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text <=> StkRskCredit_CreditContract.filter_insid;
                action_icon: Icons.close;

                edited => {
                    StkRskCredit_CreditContract.filter_changed();
                }

                action => {
                    self.text = "";
                    StkRskCredit_CreditContract.filter_changed();
                }
            }

            Label {
                text: "委托编号";
                vertical_alignment: center;
            }
            LineEdit {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text <=> StkRskCredit_CreditContract.filter_ordsysid;
                action_icon: Icons.close;

                edited => {
                    StkRskCredit_CreditContract.filter_changed();
                }

                action => {
                    self.text = "";
                    StkRskCredit_CreditContract.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 7,
                columns: [
                    /*  0 */{ title: @tr("开仓日期"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    /*  1 */{ title: @tr("到期日期"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },
                    /*  2 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    /*  3 */{ title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    /*  4 */{ title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    /*  5 */{ title: @tr("合约类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_80 },

                    /*  6 */{ title: @tr("委托编号 ⓘ"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid, tips: "历史合约 : 导入的合约编码 \n当日合约 : 交易所报单编码" },

                    /*  7 */{ title: @tr("合约数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /*  8 */{ title: @tr("合约金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /*  9 */{ title: @tr("合约费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    /* 10 */{ title: @tr("合约利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },

                    /* 11 */{ title: @tr("已还数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 12 */{ title: @tr("已还金额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110, tips: "已还本金 + 已还费用" },
                    /* 13 */{ title: @tr("已还利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },

                    /* 14 */{ title: @tr("当日已还数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 15 */{ title: @tr("当日已还本金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 16 */{ title: @tr("当日已还费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 17 */{ title: @tr("当日已还利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_105 },

                    /* 18 */{ title: @tr("未还数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 19 */{ title: @tr("未还金额 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110, tips: "未还本金 + 未还费用" },
                    /* 20 */{ title: @tr("未还利息"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    /* 21 */{ title: @tr("合约负债"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },

                    /* 22 */{ title: @tr("最新价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    /* 23 */{ title: @tr("折算率"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    /* 24 */{ title: @tr("保证金率 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80, tips: "融资买入 : 融资保证金率\n融券卖出 : 融券保证金率" },

                    /* 25 */{ title: @tr("融资折算未还量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 26 */{ title: @tr("融资盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 27 */{ title: @tr("融资保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                    /* 28 */{ title: @tr("融券卖出金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 29 */{ title: @tr("融券盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                    /* 30 */{ title: @tr("融券保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                    /* 31 */{ title: @tr("委托价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.price },
                    /* 32 */{ title: @tr("委托数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    /* 33 */{ title: @tr("委托金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                    /* 34 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    /* 35 */{ title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },

                ]
            };
            row_count: StkRskCredit_CreditContract.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_CreditContract.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkRskCredit_CreditContract.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_CreditContract.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_CreditContract.row_data[row_index]);
                }
            }
        }
    }
}
