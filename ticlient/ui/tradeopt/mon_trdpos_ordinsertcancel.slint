import { Font<PERSON>, Label, ComboBox, TableView, ListViewItem } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";

export global OptTrd_MonTrdPosOrdInsertCancel {
    in property <[[string]]> row_data;
    in property <string> greaterthannum;

    in_out property <string> exchid;
    in_out property <string> clientid;
    in_out property <string> productid;

    in_out property <[ListViewItem]> clientid_model;
    in_out property <[ListViewItem]> productid_model;

    callback filter_changed();
    pure callback get_row_data_color(int, int, string) -> brush;

    public function get_cell_data(row_index: int, column_index: int) -> string {
        if row_index < row_data.length && column_index < 12 {
            row_data[row_index][column_index]
        }
        else{
            ""
        }
    }

    public function clientid_model_len() -> int {
        clientid_model.length
    }

    public function productid_model_len() -> int {
        productid_model.length
    }
}

export component MonTrdPosOrdInsertCancelPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_MonTrdPosOrdInsertCancel.exchid;
                model: CommonModel.exch_opt_model;

                selected => {
                    OptTrd_MonTrdPosOrdInsertCancel.filter_changed();
                }
            }

            Label {
                text: "交易编码";
                vertical_alignment: center;
            }
            ComboBox {
                width: 130px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> OptTrd_MonTrdPosOrdInsertCancel.clientid;
                model: OptTrd_MonTrdPosOrdInsertCancel.clientid_model;
                max_popup_height: 160px;

                edited => {
                    OptTrd_MonTrdPosOrdInsertCancel.filter_changed();
                }

                selected => {
                    OptTrd_MonTrdPosOrdInsertCancel.filter_changed();
                }
            }

            Label {
                text: "品种编码";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> OptTrd_MonTrdPosOrdInsertCancel.productid;
                model: OptTrd_MonTrdPosOrdInsertCancel.productid_model;
                max_popup_height: 160px;

                edited => {
                    OptTrd_MonTrdPosOrdInsertCancel.filter_changed();
                }

                selected => {
                    OptTrd_MonTrdPosOrdInsertCancel.filter_changed();
                }
            }

            Rectangle { }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 4,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("品种编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insid },

                    { title: @tr("成交量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("持仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("成交持仓比"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_90 },
                    { title: @tr("成交持仓比异常"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_120 },

                    { title: @tr("大于{}万报单笔数", OptTrd_MonTrdPosOrdInsertCancel.greaterthannum), alignment: TextHorizontalAlignment.right, width: ColWidth.len_130 },
                    { title: @tr("大于{}万撤单笔数", OptTrd_MonTrdPosOrdInsertCancel.greaterthannum), alignment: TextHorizontalAlignment.right, width: ColWidth.len_130 },
                    { title: @tr("撤单比率"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    { title: @tr("总报单笔数"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("总撤单笔数"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },

                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                ]
            };
            row_count: OptTrd_MonTrdPosOrdInsertCancel.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_MonTrdPosOrdInsertCancel.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                OptTrd_MonTrdPosOrdInsertCancel.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_MonTrdPosOrdInsertCancel.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_MonTrdPosOrdInsertCancel.row_data[row_index]);
                }
            }
        }
    }
}
