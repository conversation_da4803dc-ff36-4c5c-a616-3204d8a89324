import { Font<PERSON>, Label, ComboBox, TableView, ListViewItem } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";

export global FutTrd_MonLimitPosition {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> clientid;
    in_out property <string> productid;
    in_out property <string> insid;

    in_out property <[ListViewItem]> clientid_model;
    in_out property <[ListViewItem]> productid_model;
    in_out property <[ListViewItem]> insid_model;

    callback filter_changed();
    pure callback get_row_data_color(int, int, string) -> brush;

    public function clientid_model_len() -> int {
        clientid_model.length
    }

    public function productid_model_len() -> int {
        productid_model.length
    }

    public function insid_model_len() -> int {
        insid_model.length
    }
}

export component MonLimitPositionPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> FutTrd_MonLimitPosition.exchid;
                model: CommonModel.exch_fut_model;

                selected => {
                    FutTrd_MonLimitPosition.filter_changed();
                }
            }

            Label {
                text: "交易编码";
                vertical_alignment: center;
            }

            ComboBox {
                width: 130px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> FutTrd_MonLimitPosition.clientid;
                model: FutTrd_MonLimitPosition.clientid_model;
                max_popup_height: 160px;

                edited => {
                    FutTrd_MonLimitPosition.filter_changed();
                }

                selected => {
                    FutTrd_MonLimitPosition.filter_changed();
                }
            }

            Label {
                text: "品种编码";
                vertical_alignment: center;
            }

            ComboBox {
                width: 100px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> FutTrd_MonLimitPosition.productid;
                model: FutTrd_MonLimitPosition.productid_model;
                max_popup_height: 160px;

                edited => {
                    FutTrd_MonLimitPosition.filter_changed();
                }

                selected => {
                    FutTrd_MonLimitPosition.filter_changed();
                }
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }

            ComboBox {
                width: 130px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> FutTrd_MonLimitPosition.insid;
                model: FutTrd_MonLimitPosition.insid_model;
                max_popup_height: 160px;

                edited => {
                    FutTrd_MonLimitPosition.filter_changed();
                }

                selected => {
                    FutTrd_MonLimitPosition.filter_changed();
                }
            }

            Rectangle { }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("品种编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.fut_insid },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.fut_insid },
                    { title: @tr("多头持仓"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("空头持仓"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                ]
            };
            row_count: FutTrd_MonLimitPosition.row_data.length;

            get_cell_data(row_index, column_index) => {
                FutTrd_MonLimitPosition.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                FutTrd_MonLimitPosition.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(FutTrd_MonLimitPosition.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(FutTrd_MonLimitPosition.row_data[row_index]);
                }
            }
        }
    }
}