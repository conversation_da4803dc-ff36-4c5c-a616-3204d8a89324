import { But<PERSON>, <PERSON>mbo<PERSON><PERSON>, <PERSON>, Icons, ListViewItem } from "@zstk/lib.slint";
import { WidgetColor } from "../struct.slint";

export component PageItem {

    in property <[ListViewItem]> page_index_model;
    in property <bool> top_popwnd: true;
    in property <bool> enabled: true;

    in property <int> unit;          // 单位. 0:条; 1:组
    in property <int> item_total;    // 总条数
    in property <int> page_total;    // 总页数

    in_out property <int> page_index;   // 当前页索引
    in_out property <int> page_size;    // 页大小

    private property <length> widget_len: 22px;

    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);

    public function refresh_page_info() {

        i_cbx_pages_index.select(page_index);
        if 50 == page_size {
            i_cbx_page_size.select(0);
        }
        if 100 == page_size {
            i_cbx_page_size.select(1);
        }
        if 200 == page_size {
            i_cbx_page_size.select(2);
        }
    }

    HorizontalLayout {
        padding: 4px;
        spacing: 5px;

        i_btn_first := Button {
            width: widget_len;
            round: true;
            has_border: false;
            icon: Icons.page_first;
            background: WidgetColor.btn_background;
            enabled: root.enabled && 0 != page_index;

            clicked => {
                i_cbx_pages_index.select(0);
            }
        }

        i_btn_prev := Button {
            width: widget_len;
            round: true;
            has_border: false;
            icon: Icons.page_pre;
            background: WidgetColor.btn_background;
            enabled: root.enabled && page_index > 0;

            clicked => {
                i_cbx_pages_index.select(page_index - 1);
            }
        }

        i_cbx_pages_index := ComboBox {
            width: 100px;
            top_popup: root.top_popwnd;
            model: root.page_index_model;
            enabled: root.enabled && page_total > 1;
            current_text: page_index >= 0 && page_index < page_total ? page_index_model[page_index].text : "";

            selected(index) => {
                if index != root.page_index {
                    root.page_index = index;
                    page_index_changed(index);
                }
            }
        }

        i_btn_next := Button {
            width: widget_len;
            round: true;
            has_border: false;
            icon: Icons.page_next;
            background: WidgetColor.btn_background;
            enabled: root.enabled && page_index < page_total - 1;

            clicked => {
                i_cbx_pages_index.select(page_index + 1);
            }
        }

        i_btn_end := Button {
            width: widget_len;
            round: true;
            has_border: false;
            icon: Icons.page_last;
            background: WidgetColor.btn_background;
            enabled: root.enabled && page_index != page_total - 1;

            clicked => {
                i_cbx_pages_index.select(page_total - 1);
            }
        }

        i_cbx_page_size := ComboBox {
            width: 100px;
            enabled: root.enabled;
            top_popup: root.top_popwnd;

            model: [
                { text: @tr("50{}/页", 0 == unit ? "条" : "组") },
                { text: @tr("100{}/页", 0 == unit ? "条" : "组") },
                { text: @tr("200{}/页", 0 == unit ? "条" : "组") }
            ];

            selected(index) => {
                if 0 == index && 50 != page_size {
                    page_size = 50;
                    page_size_changed(page_size);
                }

                if 1 == index && 100 != page_size {
                    page_size = 100;
                    page_size_changed(page_size);
                }

                if 2 == index && 200 != page_size {
                    page_size = 200;
                    page_size_changed(page_size);
                }
            }
        }

        Label {
            vertical_alignment: center;
            text: @tr("    共 {} {}数据. 当前第 {} 页, 共 {} 页", item_total, 0 == unit ? "条" : "组", 1 + page_index, page_total);
        }
    }
}