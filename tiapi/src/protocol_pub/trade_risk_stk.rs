use serde::Deserialize;

/// 成交信息/自成交详情消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct TradeStkField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 成交编号
    pub TradeID: String,

    /// 本地报单编号
    pub LocalOrderNo: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 买卖方向
    pub Side: i32,

    /// 成交价格
    pub TradePrice: f64,

    /// 成交数量
    pub TradeVolume: i32,

    /// 成交时间
    pub TransTime: i32,
}

/// 非交易业务成交信息
#[derive(Default)]
pub struct BusinessTradeStkField {
    /// 程序编码
    pub AID: i32,

    /// 消息序号
    pub MsgSeqNum: i32,

    /// 业务序号
    pub BizSeqNum: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 合约编码
    pub DstInstrumentID: String,

    /// 原始报单交易用户代码
    pub UserID: String,

    /// 成交编号
    pub TradeID: String,

    /// 本地报单编号
    pub LocalOrderNo: i32,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 业务类型
    pub BusinessType: i32,

    /// 买卖方向
    pub Side: i32,

    /// 成交价格
    pub TradePrice: f64,

    /// 成交数量
    pub TradeVolume: i32,

    /// 本次成交后申报余额数量
    pub LeavesVolume: i32,

    /// 成交时间
    pub TransTime: i32,
}

/// 成交请求查询消息
#[derive(Default)]
pub struct ReqQryTradeStkField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 成交编号
    pub TradeID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 成交响应查询消息
#[derive(Default)]
pub struct RspQryTradeStkField {
    /// 成交消息
    pub array: Vec<TradeStkField>,
}

/// 非交易业务成交请求查询消息
#[derive(Default)]
pub struct ReqQryBusinessTradeStkField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 成交编号
    pub TradeID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}

/// 非交易业务成交响应查询消息
#[derive(Default)]
pub struct RspQryBusinessTradeStkField {
    /// 非交易业务成交消息
    pub array: Vec<BusinessTradeStkField>,
}
