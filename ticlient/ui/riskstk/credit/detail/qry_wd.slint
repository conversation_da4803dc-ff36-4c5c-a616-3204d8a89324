import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Button, ComboBox, TableView, ListViewItem, TVVisible } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom, NotifyColor, AppInerArgs } from "../../../struct.slint";
import { PageItem } from "../../../widgets/page.slint";
import { TimeSelect, TimeItem } from "../../../widgets/time.slint";
import { StkRskCredit_QryWithdrawDeposit } from "../struct.slint";

export component QryWithdrawDepositPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <length> header_exchid_width: AppInerArgs.show_detail ? ColWidth.exchid : 0.5px;
    private property <TVVisible> header_exchid_visible: AppInerArgs.show_detail ? TVVisible.Visible : TVVisible.Invisible;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkRskCredit_QryWithdrawDeposit.exchid;
        i_ts_start.time = StkRskCredit_QryWithdrawDeposit.starttime;
        i_ts_end.time = StkRskCredit_QryWithdrawDeposit.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            if AppInerArgs.show_detail: Label {
                text: "交易所";
                vertical_alignment: center;
            }
            i_cbx_exch := ComboBox {
                width: AppInerArgs.show_detail ? 70px : 0px;
                height: AppCom.widgets_height;
                visible: AppInerArgs.show_detail;
                placeholder_text: "请选择";
                current_text: StkRskCredit_QryWithdrawDeposit.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "处理时间";
                vertical_alignment: center;
            }
            HorizontalLayout {
                i_ts_start := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryWithdrawDeposit.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    height: AppCom.widgets_height;
                    time: StkRskCredit_QryWithdrawDeposit.endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                height: AppCom.widgets_height;
                background: WidgetColor.btn_background;

                clicked => {
                    StkRskCredit_QryWithdrawDeposit.exchid = i_cbx_exch.current_text;
                    StkRskCredit_QryWithdrawDeposit.starttime = i_ts_start.time;
                    StkRskCredit_QryWithdrawDeposit.endtime = i_ts_end.time;
                    StkRskCredit_QryWithdrawDeposit.starttime_int = i_ts_start.time_int;
                    StkRskCredit_QryWithdrawDeposit.endtime_int = i_ts_end.time_int;

                    StkRskCredit_QryWithdrawDeposit.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: header_exchid_width, visible: header_exchid_visible },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("入金金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("出金金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("处理时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                ]
            };
            row_count: StkRskCredit_QryWithdrawDeposit.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_QryWithdrawDeposit.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                if 1 == column_index && data.to_float() > 0.0 {
                    return NotifyColor.normal;
                }

                if 2 == column_index && data.to_float() > 0.0 {
                    return NotifyColor.caution;
                }

                NotifyColor.default
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_QryWithdrawDeposit.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_QryWithdrawDeposit.row_data[row_index]);
                }
            }
        }

        if StkRskCredit_QryWithdrawDeposit.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkRskCredit_QryWithdrawDeposit.page_index_model;
            item_total <=> StkRskCredit_QryWithdrawDeposit.item_total;
            page_total <=> StkRskCredit_QryWithdrawDeposit.page_total;
            page_index <=> StkRskCredit_QryWithdrawDeposit.page_index;
            page_size <=> StkRskCredit_QryWithdrawDeposit.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryWithdrawDeposit.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkRskCredit_QryWithdrawDeposit.page_size_changed(size);
            }
        }
    }
}
