pub(crate) struct Linux {}

use std::process::Command;

use crate::com::{self, CollectInfo, IpMac};

impl Linux {
    /// 获取采集信息
    pub fn get_collect_info() -> Result<CollectInfo, String> {
        // 终端类型(1)
        let terminal_type = com::get_terminal_type().unwrap();

        // 信息采集时间(19)
        let collect_time = com::get_collect_time();

        // IP(39)/MAC(12)
        let ret = Linux::get_ip_mac();
        if let Err(err) = ret {
            return Err(std::format!("get_ip_mac failed. {err}"));
        }
        let ipmac_vec = ret.unwrap();
        let mut ip1 = String::new();
        let mut mac1 = String::new();
        let mut ip2 = String::new();
        let mut mac2 = String::new();
        for ipmac in &ipmac_vec {
            if ip1.is_empty() {
                ip1 = {
                    if !ipmac.ipv4.is_empty() {
                        ipmac.ipv4.clone()
                    } else {
                        com::sub_str(&ipmac.ipv6, 39).to_owned()
                    }
                };
                mac1 = ipmac.mac.clone().replace(':', "");
                continue;
            }

            if ip2.is_empty() {
                ip2 = {
                    if !ipmac.ipv4.is_empty() {
                        ipmac.ipv4.clone()
                    } else {
                        com::sub_str(&ipmac.ipv6, 39).to_owned()
                    }
                };
                mac2 = ipmac.mac.clone().replace(':', "");
                break;
            }
        }

        // 设备名称(9)
        let ret = Linux::get_device_name();
        if let Err(err) = ret {
            return Err(std::format!("get_device_name failed. {err}"));
        }
        let device_name = ret.unwrap();
        let device_name = com::sub_str(&com::replace_at_2_space(&device_name), 9).to_owned();

        // 操作系统版本(5)
        let ret = Linux::get_os_ver();
        if let Err(err) = ret {
            return Err(std::format!("get_os_ver failed. {err}"));
        }
        let os_ver = ret.unwrap();
        let os_ver = com::sub_str(&com::replace_at_2_space(&os_ver), 5).to_owned();

        // 获取硬盘序列号(16)
        let ret = Linux::get_disk_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_os_ver failed. {err}"));
        }
        let disk_sn = {
            let disk_sn_arr = ret.unwrap();
            if !disk_sn_arr.is_empty() {
                disk_sn_arr[0].to_owned()
            } else {
                "".to_owned()
            }
        };
        let disk_sn = com::sub_str(&com::replace_at_2_space(&disk_sn), 16).to_owned();

        // 获取CPU序列号(16)
        let ret = Linux::get_cpu_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_cpu_serial_number failed. {err}"));
        }
        let cpu_sn = ret.unwrap();
        let cpu_sn = com::sub_str(&com::replace_at_2_space(&cpu_sn), 16).to_owned();

        // 获取BIOS序列号(10)
        let ret = Linux::get_bios_serial_number();
        if let Err(err) = ret {
            return Err(std::format!("get_bios_serial_number failed. {err}"));
        }
        let bios_sn = ret.unwrap();
        let bios_sn = com::sub_str(&com::replace_at_2_space(&bios_sn), 10).to_owned();

        // 终端类型@信息采集时间@私网IP1@私网IP2@网卡Mac地址1@网卡Mac地址2@设备名称@操作系统版本@硬盘序列号@CPU序列号@BIOS序列号
        let collect_info = std::format!("{terminal_type}@{collect_time}@{ip1}@{ip2}@{mac1}@{mac2}@{device_name}@{os_ver}@{disk_sn}@{cpu_sn}@{bios_sn}");

        #[cfg(debug_assertions)]
        {
            println!("collect_info(linux): {}", collect_info);
        }

        // 对采集信息进行加密
        let ret = com::encrypt(&collect_info);
        if let Err(err) = ret {
            return Err(std::format!("encrypt failed. {err}"));
        }

        Ok(ret.unwrap())
    }
}

impl Linux {
    /// 获取默认网卡名
    fn get_default_route_interface() -> Result<String, String> {
        let output = Command::new("ip")
            .args(&["route", "show", "default"])
            .output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = stdout_str.lines().collect();

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            // default via *********** dev eth0 proto dhcp src *********** metric 100
            if let Some(posidx) = line.find("default via") {
                if 0 == posidx {
                    let p1 = line.find("dev "); // dev xxxx：表示数据包应通过的出接口
                    let p2 = line.find("proto "); // proto 路由的来源

                    if p1.is_some() && p2.is_some() {
                        let p1 = p1.unwrap() + 4;
                        let p2 = p2.unwrap();

                        return Ok(line[p1..p2].trim().to_owned());
                    }
                }
            }
        }

        Err(format!("get empty"))
    }

    /// 获取IP/MAC地址
    /// 第一个地址为默认路由的地址
    pub(super) fn get_ip_mac() -> Result<Vec<IpMac>, String> {
        let ret = Linux::get_default_route_interface();
        if let Err(err) = ret {
            return Err(format!("get_default_route_interface failed. {}", err));
        }
        let default_interface = ret.unwrap();

        // 获取所有网络接口
        let interfaces = pnet::datalink::interfaces();

        let mut ipmac_vec = vec![];
        for interface in interfaces {
            if interface.is_up() && !interface.is_loopback() {
                let isdefault = default_interface == interface.name;

                let mac = interface.mac;
                if mac.is_none() {
                    continue;
                }
                let mac = mac.unwrap();

                let mut ipmac = IpMac::default();
                ipmac.interface = interface.name;
                ipmac.mac = mac.to_string();

                for addr in interface.ips.clone() {
                    if ipmac.ipv4.is_empty() && addr.is_ipv4() {
                        ipmac.ipv4 = addr.ip().to_string();
                    }

                    if ipmac.ipv6.is_empty() && addr.is_ipv6() {
                        ipmac.ipv6 = addr.ip().to_string();
                    }

                    // 一块网卡最多只需要获取一个IPV4和一个IPV6地址即可
                    if !ipmac.ipv4.is_empty() && !ipmac.ipv6.is_empty() {
                        break;
                    }
                }

                if isdefault {
                    ipmac_vec.insert(0, ipmac);
                } else {
                    ipmac_vec.push(ipmac);
                }
            }
        }

        if !ipmac_vec.is_empty() {
            return Ok(ipmac_vec);
        }

        return Err("empty".to_owned());
    }

    /// 获取设备名
    fn get_device_name() -> Result<String, String> {
        let output = Command::new("hostname").output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = stdout_str.lines().collect();

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if !line.is_empty() {
                return Ok(line.to_owned());
            }
        }

        Ok("".to_owned())
    }

    /// 获取操作系统内核版本
    fn get_os_ver() -> Result<String, String> {
        let output = Command::new("uname").args(&["-r"]).output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = stdout_str.lines().collect();

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if !line.is_empty() {
                return Ok(line.to_owned());
            }
        }

        Ok("".to_owned())
    }

    /// 获取硬盘序列号
    fn get_disk_serial_number() -> Result<Vec<String>, String> {
        let output = Command::new("lsblk")
            .args(&["-d", "-n", "-o", "SERIAL", "-e", "2,11"])
            .output();
        if let Err(err) = output {
            return Err(err.to_string());
        }
        let output = output.unwrap();
        if !output.status.success() {
            return Err(format!("exec order failed"));
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = stdout_str.lines().collect();

        let mut ret = vec![];

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if !line.is_empty() {
                ret.push(line.to_owned());
            }
        }

        Ok(ret)
    }

    /// 获取CPU序列号
    fn get_cpu_serial_number() -> Result<String, String> {
        let output = Command::new("dmidecode")
            .args(&["-t", "4", "|", "grep", "ID"])
            .output();
        if let Err(_err) = output {
            // dmidecode可能需要安装, 且在执行时需要root权限
            return Ok("".to_owned());
        }
        let output = output.unwrap();
        if !output.status.success() {
            // dmidecode可能需要安装, 且在执行时需要root权限
            return Ok("".to_owned());
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = stdout_str.lines().collect();

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if !line.is_empty() {
                if let Some(posidx) = line.find("ID:") {
                    let id = line[posidx + 3..].trim().to_owned().replace(' ', "");
                    return Ok(id);
                }
            }
        }

        Ok("".to_owned())
    }

    /// 获取BIOS序列号
    fn get_bios_serial_number() -> Result<String, String> {
        let output = Command::new("cat")
            .args(&["/sys/class/dmi/id/product_serial"])
            .output();
        if let Err(_err) = output {
            // 可能没有权限或没有文件
            return Ok("".to_owned());
        }
        let output = output.unwrap();
        if !output.status.success() {
            // 可能没有权限或没有文件
            return Ok("".to_owned());
        }

        let stdout_str = String::from_utf8_lossy(&output.stdout);
        let lines: Vec<&str> = stdout_str.lines().collect();

        let mut inx = 0;
        while inx < lines.len() {
            let line = lines[inx].trim();
            inx += 1;

            if !line.is_empty() && line.find(" ").is_none() {
                // 可能没有提供序列号提示出错
                return Ok(line.to_owned());
            }
        }

        Ok("".to_owned())
    }
}

#[cfg(test)]
mod tests {
    use crate::linux::Linux;

    #[test]
    fn it_works() {
        let _ = Linux::get_ip_mac();

        let ret = Linux::get_collect_info();
        assert_eq!(true, ret.is_ok());

        if let Ok(col) = ret {
            println!("exinfo:{}\ntiinfo:{}", col.exinfo, col.tiinfo);
        }
    }
}
