use crate::{
    protocol_pub::{
        account_trade_ctp::{
            CtpAccountField, CtpReqFundTransferField, CtpReqQryFundTransferField, CtpReqQryTradingAccountExField,
            CtpReqQryTradingAccountField, CtpReqQryTradingCodeField, CtpReqReFundTransferField, CtpRspFundTransferField,
            CtpRspQryFundTransferField, CtpTradingCodeField, CtpWithdrawDepositField,
        },
        app::*,
        instrument_trade_ctp::{
            CtpReqQryFJYInstrumentField, CtpReqQryInstrumentField, CtpRspQryFJY<PERSON>nstrumentField, CtpRspQryInstrumentField,
        },
        login_trade_ctp::{CtpLoginRetField, CtpReqLoginField},
        monitor_trade_ctp::*,
        order_trade_ctp::{CtpBusinessOrderField, CtpOrderField},
        position_trade_ctp::{
            CtpCreditConcentrationField, CtpCreditContractField, CtpCreditLimitField, CtpCreditReturnDtlField,
            CtpCreditTcAmtField, CtpCreditTcPosField, CtpPositionField, CtpPositionTransField, CtpPositionTransferField,
            CtpReqPositionTransferField, CtpReqQryPositionField, CtpReqQryPositionTransferField, CtpReqRePositionTransferField,
            CtpRspPositionTransferField,
        },
        trade_req_ctp::*,
        trade_trade_ctp::{CtpBusinessTradeField, CtpTradeField},
    },
    serverinfo::ServerInfo,
};

/// 现货交易回调类
pub trait ITradeStkSpi: Send + Sync + super::SpiDowncast {
    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {}

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {}

    /// 委托消息
    fn on_rtn_order(&self, ord: CtpOrderField) {}

    /// 成交消息
    fn on_rtn_trade(&self, trd: CtpTradeField) {}

    /// 非交易业务委托消息
    fn on_rtn_businessorder(&self, ord: CtpBusinessOrderField) {}

    /// 非交易业务成交消息
    fn on_rtn_businesstrade(&self, trd: CtpBusinessTradeField) {}

    /// 出入金消息
    fn on_rtn_wd(&self, wd: CtpWithdrawDepositField) {}

    /// 股份划转消息
    fn on_rtn_positiontrans(&self, pt: CtpPositionTransField) {}

    /// 查询交易编码响应
    fn on_rsp_qry_tradingcode(&self, tc: CtpTradingCodeField, reqid: i32, islast: bool) {}

    /// 查询合约响应
    fn on_rsp_qry_instrument(&self, ins: CtpRspQryInstrumentField, reqid: i32, islast: bool) {}

    /// 查询非交易合约响应
    fn on_rsp_qry_fjyinstrument(&self, ins: CtpRspQryFJYInstrumentField, reqid: i32, islast: bool) {}

    /// 查询资金响应消息
    fn on_rsp_qry_tradingaccount(&self, acc: CtpAccountField, reqid: i32, islast: bool) {}

    /// 查询资金(分交易所)响应消息
    fn on_rsp_qry_tradingaccountex(&self, acc: CtpAccountField, reqid: i32, islast: bool) {}

    /// 查询持仓响应消息
    fn on_rsp_qry_investorposition(&self, pos: CtpPositionField, reqid: i32, islast: bool) {}

    /// 报单录入响应消息
    fn on_rsp_inputorder(&self, rsp: CtpRspInputOrderField, reqid: i32, islast: bool) {}

    /// 报单操作响应
    fn on_rsp_inputorderaction(&self, rsp: CtpRspInputOrderActionField, reqid: i32, islast: bool) {}

    /// 非交易业务报单录入响应消息
    fn on_rsp_businessorder(&self, rsp: CtpRspBusinessOrderField, reqid: i32, islast: bool) {}

    /// 非交易业务报单操作响应
    fn on_rsp_businessorderaction(&self, rsp: CtpRspBusinessOrderActionField, reqid: i32, islast: bool) {}

    /// 自成交
    fn on_rtn_tradeself(&self, trdslf: CtpTradeSelfField) {}

    /// 自成交详情
    fn on_rtn_tradeselfdtl(&self, trd: CtpTradeSelfDtlField) {}

    /// 成交持仓比例
    fn on_rtn_tradeposition(&self, tp: CtpTradePositionField) {}

    /// 报撤单
    fn on_rtn_orderinsertcancel(&self, oic: CtpOrderInsertCancelField) {}

    /// 可转债监控
    fn on_rtn_convertbondsmon(&self, cbm: CtpConvertBondsMonField) {}

    /// 成交监控
    fn on_rtn_trademon(&self, tm: CtpTradeMonField) {}

    /// 信用授信额度
    fn on_rtn_creditlimit(&self, cl: CtpCreditLimitField) {}

    /// 信用资金头寸
    fn on_rtn_credittcamt(&self, amt: CtpCreditTcAmtField) {}

    /// 信用股份头寸
    fn on_rtn_credittcpos(&self, pos: CtpCreditTcPosField) {}

    /// 信用合约
    fn on_rtn_creditcontract(&self, cc: CtpCreditContractField) {}

    /// 信用集中度
    fn on_rtn_creditconcentration(&self, cc: CtpCreditConcentrationField) {}

    /// 信用合约归还明细
    fn on_rtn_creditretdtl(&self, rtd: CtpCreditReturnDtlField) {}
}

/// 现货交易API
#[async_trait::async_trait]
pub trait TradeStkApi: Send + Sync {
    /// 初始化
    async fn init(&mut self);

    /// 释放资源
    async fn release(&mut self);

    /// 设置服务端信息(请求登录前必须设置)
    async fn set_svrinfo(&mut self, si: &ServerInfo);

    /// 请求登录<br>
    /// 同步接口. 连接->登录<br>
    /// 返回值说明. 成功发送登录请求前的错误均放在Err中, 成功发送请求后无论登录成功与否都放在OK中
    async fn req_login(&self, req: &CtpReqLoginField) -> crate::Result<CtpLoginRetField>;

    /// 请求查询交易编码
    async fn req_qry_tradingcode(&self, reqid: i32, req: &CtpReqQryTradingCodeField) -> crate::Result<()>;

    /// 请求查询合约
    async fn req_qry_instrument(&self, reqid: i32, req: &CtpReqQryInstrumentField) -> crate::Result<()>;

    /// 请求查询非交易合约<br>
    /// 服务端还不支持按条件查询
    async fn req_qry_fjy_instrument(&self, reqid: i32, req: &CtpReqQryFJYInstrumentField) -> crate::Result<()>;

    /// 请求查询资金账户
    async fn req_qry_tradingaccount(&self, reqid: i32, req: &CtpReqQryTradingAccountField) -> crate::Result<()>;

    /// 请求查询资金账户(分交易所)
    async fn req_qry_tradingaccountex(&self, reqid: i32, req: &CtpReqQryTradingAccountExField) -> crate::Result<()>;

    /// 请求查询持仓
    async fn req_qry_investorposition(&self, reqid: i32, req: &CtpReqQryPositionField) -> crate::Result<()>;

    /// 请求报单
    async fn req_orderinput(&self, reqid: i32, req: &CtpReqInputOrderField) -> crate::Result<()>;

    /// 请求撤单
    async fn req_orderaction(&self, reqid: i32, req: &CtpReqInputOrderActionField) -> crate::Result<()>;

    /// 请求非交易业务报单
    async fn req_fjy_orderinput(&self, reqid: i32, req: &CtpReqBusinessOrderField) -> crate::Result<()>;

    /// 请求非交易业务撤单
    async fn req_fjy_orderaction(&self, reqid: i32, req: &CtpReqBusinessOrderActionField) -> crate::Result<()>;

    /// 请求资金划转(同步函数)
    async fn req_fundtrans(&self, req: &CtpReqFundTransferField, rsp: &mut CtpRspFundTransferField) -> crate::Result<()>;

    /// 请求资金继续划转(同步函数)
    async fn req_re_fundtrans(
        &self,
        req: &CtpReqReFundTransferField,
        rsp: &mut Vec<CtpRspQryFundTransferField>,
    ) -> crate::Result<()>;

    /// 请求查询资金划转记录(同步函数)
    async fn req_qry_fundtrans(
        &self,
        req: &CtpReqQryFundTransferField,
        rsp: &mut Vec<CtpRspQryFundTransferField>,
    ) -> crate::Result<()>;

    /// 请求股份划转(同步函数)
    async fn req_positiontrans(
        &self,
        req: &CtpReqPositionTransferField,
        rsp: &mut CtpRspPositionTransferField,
    ) -> crate::Result<()>;

    /// 请求股份继续划转(同步函数)
    async fn req_re_positiontrans(
        &self,
        req: &CtpReqRePositionTransferField,
        rsp: &mut Vec<CtpPositionTransferField>,
    ) -> crate::Result<()>;

    /// 请求查询股份划转记录(同步函数)
    async fn req_qry_positiontrans(
        &self,
        req: &CtpReqQryPositionTransferField,
        rsp: &mut Vec<CtpPositionTransferField>,
    ) -> crate::Result<()>;
}
