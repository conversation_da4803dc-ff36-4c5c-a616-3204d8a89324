use std::io::{Read, Write};

/// 历史字符串
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ialEq, Eq)]
pub struct HisText {
    /// 字符串1
    pub text1: String,

    /// 字符串2
    pub text2: String,
}

/// 简易的编码
pub struct SimpleCode {}
impl SimpleCode {
    /// 编码
    pub fn ecb_encode(bytes: &mut Vec<u8>) {
        let len = bytes.len();
        if len > 1 {
            bytes[0] = bytes[0] ^ bytes[len - 1];
            for i in 1..len {
                bytes[i] = bytes[i - 1] ^ bytes[i];
            }
        }
    }

    /// 解码
    pub fn ecb_decode(bytes: &mut Vec<u8>) {
        let len = bytes.len();
        if len > 1 {
            let mut i = len - 1;
            while i > 0 {
                bytes[i] = bytes[i] ^ bytes[i - 1];
                i -= 1;
            }
            bytes[0] = bytes[len - 1] ^ bytes[0];
        }
    }
}

/// 编码
pub struct TICode {}
impl TICode {
    /// 将数据写入文件
    pub fn w(htvec: &Vec<HisText>, path: &str) {
        if htvec.is_empty() || path.is_empty() {
            return;
        }

        let mut htbytes = String::from("6678").into_bytes();

        for ht in htvec {
            let mut bytes = std::format!("{}|{}", ht.text1, ht.text2).into_bytes();
            SimpleCode::ecb_encode(&mut bytes);

            let tmp = std::format!("{:4o}", bytes.len()).into_bytes();
            htbytes.extend(tmp);
            htbytes.extend(bytes);
        }

        SimpleCode::ecb_encode(&mut htbytes);

        let file = std::fs::File::create(path);
        if file.is_err() {
            return;
        }
        let mut file = file.unwrap();
        let _ = file.write_all(&htbytes);
    }

    /// 从文件读取数据
    pub fn r(path: &str) -> Option<Vec<HisText>> {
        if path.is_empty() {
            return None;
        }

        let file = std::fs::File::open(path);
        if file.is_err() {
            return None;
        }
        let mut file = file.unwrap();
        let mut htbytes = Vec::new();
        let ret = file.read_to_end(&mut htbytes);
        if ret.is_err() {
            return None;
        }

        SimpleCode::ecb_decode(&mut htbytes);

        let tag_len = 4;
        if htbytes.len() < tag_len || 54 != htbytes[0] || 54 != htbytes[1] || 55 != htbytes[2] || 56 != htbytes[3] {
            return None;
        }

        let mut ht_vec = vec![];

        htbytes = htbytes[tag_len..].to_owned();
        while htbytes.len() > tag_len {
            let htlen = htbytes[0..tag_len].to_owned();
            let htlen = String::from_utf8(htlen);
            if htlen.is_err() {
                break;
            }
            let htlen = i32::from_str_radix(htlen.unwrap().trim(), 8);
            if htlen.is_err() {
                break;
            }
            let htlen = htlen.unwrap() as usize;

            let mut ht = htbytes[tag_len..tag_len + htlen].to_owned();
            SimpleCode::ecb_decode(&mut ht);
            let ht = String::from_utf8(ht);
            if ht.is_err() {
                break;
            }
            let ht = ht.unwrap();
            let ht: Vec<&str> = ht.split('|').collect();
            if 2 == ht.len() {
                let tmp = HisText {
                    text1: ht[0].to_owned(),
                    text2: ht[1].to_owned(),
                };
                ht_vec.push(tmp);
            }

            htbytes = htbytes[tag_len + htlen..].to_owned();
        }

        if ht_vec.is_empty() {
            return None;
        }

        Some(ht_vec)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn t_simplecode() {
        let src = "1234567890asdf~!@#$%^&*()_+-=;':\"./<>@@我产#$国***...23".to_owned();
        let mut bytes = src.clone().into_bytes();

        println!("{:?}", bytes);
        SimpleCode::ecb_encode(&mut bytes);
        SimpleCode::ecb_decode(&mut bytes);
        println!("{:?}", bytes);

        let dst = String::from_utf8(bytes).unwrap();

        assert_eq!(src, dst);
    }

    fn t_ticode() {
        let mut htvec = vec![];
        for i in 0..100 {
            htvec.push(HisText {
                text1: format!("1uxsddddda99_{}", i),
                text2: format!("dsa&&速??*&^华%$#@8..12(((;;pp{}", i),
            })
        }

        let path = "hu";

        TICode::w(&htvec, path);

        if let Some(rhtvec) = TICode::r(path) {
            for ht in &rhtvec {
                println!("{:?}", ht);
            }

            assert_eq!(htvec, rhtvec);
        } else {
            assert!(false);
        }
    }

    #[test]
    fn it_works() {
        t_simplecode();
        t_ticode();
    }
}
