use serde::Deserialize;
use num_enum::TryFromPrimitive;

/// 系统消息
#[derive(TryFromPrimitive, PartialEq)]
#[repr(i32)]
pub(crate) enum EAppMessageType {
    TI_AMT_Min = 100,

    TI_AMT_Heartbeat,          // 心跳
    TI_AMT_TDServerError,      // 交易服务端异常(重启或心跳中断等)
    TIMT_App_TDSessionTimeout, // 登录到交易服务端的Session超时

    TI_AMT_Max,
}

/// 系统消息
#[derive(Default, Deserialize)]
#[serde[default]]
pub(crate) struct PTRtnApp {
    /// 消息类型
    pub at: i32,

    /// 消息
    pub msg: String,
}
