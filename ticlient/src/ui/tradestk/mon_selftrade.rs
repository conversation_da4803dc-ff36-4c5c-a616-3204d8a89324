use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc,
    },
};

use crate::{
    apiproc::tradestkbuf::CtpTradeSelfMap,
    common::{
        global::{CFG, TRADE_STK_BUF},
        ticonvert::TIConvert,
    },
    slintui::*,
};
use slint::*;

/// 监控 - 自成交
pub(super) struct MonSelfTrade {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CtpTradeSelfMap>,
}

impl MonSelfTrade {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CtpTradeSelfMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let mon = app.global::<StkTrd_MonSelfTrade>();

        let app_weak = app.as_weak();
        mon.on_get_row_data_color(move |row_index, column_index, data| {
            MonSelfTrade::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        log::trace!("Monitor self trade init completed");
    }
}

impl MonSelfTrade {
    /// 查询自成交信息
    pub fn qry_selftrade(&self) {
        let ret = { TRADE_STK_BUF.get().unwrap().read().unwrap().pop_selftrade() };
        if ret.is_empty() {
            return;
        }

        for (key, oic) in ret {
            self.data_map.insert(key, oic);
        }
        self.data_changed.store(true, Ordering::Relaxed);
    }
}

impl MonSelfTrade {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 是否异常
        if 4 == column_index {
            if "是" == data.as_str() {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }
}

impl MonSelfTrade {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                self.data_map.clone()
            } else {
                Arc::new(CtpTradeSelfMap::new())
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            let max_selftrade_vol = { CFG.get().unwrap().read().unwrap().stk_mon.TrdSelfMonitor.TradeVol };

            data_map_clone.iter().for_each(|st| {
                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 资金账户
                items.push(st.AccountID.clone().into());

                // 交易所
                items.push(TIConvert::exch_name(st.ExchID).into());

                // 交易编码
                items.push(st.ClientID.clone().into());

                // 合约编码
                items.push(st.InstrumentID.clone().into());

                // 是否异常
                items.push((if st.TradeVol >= max_selftrade_vol { "是" } else { "否" }).into());

                // 自成交数量
                items.push(slint::format!("{}", st.TradeVol));

                // 自成交笔数
                items.push(slint::format!("{}", st.TradeNum));

                // 发生时间
                items.push(TIConvert::transtime(st.TransTime).into());

                // Ratio
                let ratio = { ********* + st.TradeVol };
                items.push(ratio.to_string().into());

                row_data.push(items.into());
            });

            // 根据 ratio 按降序排序
            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(8).unwrap();
                let c_b = r_b.row_data(8).unwrap();
                c_b.cmp(&c_a)
            }));

            app.global::<StkTrd_MonSelfTrade>().set_row_data(sort_model.into());
        });
    }
}
