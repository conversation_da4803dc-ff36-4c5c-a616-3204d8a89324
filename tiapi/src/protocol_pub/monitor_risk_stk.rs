/// 报撤单消息
#[derive(Default)]
pub struct OrderInsertCancelStkField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 报单笔数
    pub InsertNum: i32,

    /// 撤单笔数
    pub CancelNum: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 成交持仓比
#[derive(Default)]
pub struct TradePositionStkField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 品种编码
    pub ProductID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 成交量
    pub TradeVolume: i32,

    /// 持仓量
    pub Position: i32,

    /// 成交量/持仓量
    pub Ratio: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,

    /// 是否异常
    pub IsAbnormal: bool,
}

/// 自成交
#[derive(Default)]
pub struct TradeSelfStkField {
    /// 程序编码
    pub AID: i32,

    /// 统计类型. 1:按合约2:按标的(此时InstrumentID填标的品种编码)
    pub TSType: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 自成交笔数
    pub TradeNum: i32,

    /// 自成交数量
    pub TradeVol: i32,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 可转债监控信息
#[derive(Default)]
pub struct ConvertBondsMonStkField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 持仓量
    pub Position: i32,

    /// 当日最大持仓量
    pub MaxPosition: i32,

    /// 买入数量
    pub BuyVol: i32,

    /// 买入金额
    pub BuyAmount: f64,

    /// 卖出数量
    pub SellVol: i32,

    /// 卖出金额
    pub SellAmount: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}

/// 成交监控信息
#[derive(Default)]
pub struct TradeMonStkField {
    /// 程序编码
    pub AID: i32,

    /// 交易所编码
    pub ExchID: i32,

    /// 资金帐号
    pub AccountID: String,

    /// 账号类型
    pub AccountType: i32,

    /// 交易编码
    pub ClientID: String,

    /// 合约编码
    pub InstrumentID: String,

    /// 已处理的引起本条信息变化的序号
    pub BizSeqNum: i32,

    /// 发生时间
    pub TransTime: i32,

    /// 买入数量
    pub BuyVol: i32,

    /// 买入金额
    pub BuyAmount: f64,

    /// 卖出数量
    pub SellVol: i32,

    /// 卖出金额
    pub SellAmount: f64,

    /**********下面字段为非协议内容用于计算展示等**********/
    /// 标识消息唯一性
    pub Key: String,
}
