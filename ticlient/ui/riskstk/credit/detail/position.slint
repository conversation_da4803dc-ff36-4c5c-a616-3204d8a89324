import { Theme, Fonts, TableView, TVColumnHeader, Label, ComboBox, LineEdit, Button, Radius, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../../../struct.slint";
import { ExportButton } from "../../../widgets/export.slint";
import { StkRskCredit_Position } from "../struct.slint";

export component PositionPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <TVColumnHeader> header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 2,
        columns: [
            /*  0 */{ title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
            /*  1 */{ title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
            /*  2 */{ title: @tr("持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日持仓量 + 当日买入 - 当日卖出" },
            /*  3 */{ title: @tr("当前可用 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "T + 0 : 持仓量 - 冻结持仓 + 划转量 \nT + 1 : 上日持仓量 - 当日卖出 - 冻结持仓 + 划转量" },
            /*  4 */{ title: @tr("上日持仓量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "上日结算持仓量, 盘中该值不会变动" },
            /*  5 */{ title: @tr("当日买入"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  6 */{ title: @tr("当日卖出"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  7 */{ title: @tr("划转量 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume, tips: "值大于 0 : 从主席划入 TITD\n值小于 0 : 从 TITD 划入主席" },
            /*  8 */{ title: @tr("冻结持仓"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /*  9 */{ title: @tr("持仓均价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            /* 10 */{ title: @tr("持仓成本"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 11 */{ title: @tr("最新价"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            /* 12 */{ title: @tr("持仓市值"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 13 */{ title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
            /* 14 */{ title: @tr("现券还券量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
            /* 15 */{ title: @tr("融资已还折算量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 16 */{ title: @tr("融资未还折算量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 17 */{ title: @tr("折算率"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
            /* 18 */{ title: @tr("充抵保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
            /* 19 */{ title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
            /* 20 */{ title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },
        ]
    };

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_Position.filter_exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkRskCredit_Position.filter_changed();
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }
            LineEdit {
                width: 80px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text <=> StkRskCredit_Position.filter_insid;
                action_icon: Icons.close;

                edited => {
                    StkRskCredit_Position.filter_changed();
                }

                action => {
                    self.text = "";
                    StkRskCredit_Position.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            sortable: true;
            column_header: header;
            row_count: StkRskCredit_Position.row_data.length;

            sort_canceled => {
                StkRskCredit_Position.sort_asc_column_index = 1;
                StkRskCredit_Position.sort_dec_column_index = -1;
                StkRskCredit_Position.sort_ascending(StkRskCredit_Position.sort_asc_column_index);
            }

            sort_ascending(index) => {
                StkRskCredit_Position.sort_asc_column_index = index;
                StkRskCredit_Position.sort_dec_column_index = -1;
                StkRskCredit_Position.sort_ascending(index);
                true
            }

            sort_descending(index) => {
                StkRskCredit_Position.sort_asc_column_index = -1;
                StkRskCredit_Position.sort_dec_column_index = index;
                StkRskCredit_Position.sort_descending(index);
                true
            }

            get_cell_data(row_index, column_index) => {
                StkRskCredit_Position.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_Position.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_Position.row_data[row_index]);
                }
            }
        }
    }
}
