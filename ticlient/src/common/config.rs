use super::tistruct::*;
use std::collections::HashMap;

/// 配置 - 共用
#[derive(Default)]
pub struct ConfigCom {
    /// 服务端类型
    pub SvrType: EServerType,

    /// 服务端具体类型
    pub SvrTypeDtl: i32,

    /// 服务端编译时间
    pub SvrBuildDate: i32,

    /// 服务端返回的其他信息(通过查询服务端类型接口里的tc字段返回,仅部分服务端支持)
    pub SvrOther: i32,

    /// 客户端类型
    pub ClientType: EClientType,

    /// 路径配置
    pub FPath: FilePathField,

    /// 当前选择的服务器配置
    pub SIF: ServerInfoField,

    /// 配置文件中的服务器配置
    pub SIFMap: HashMap<String, ServerInfoField>,

    /// 日志配置
    pub LogF: LogInfoFiled,

    /// 风控服务器配置
    pub SvrInfo: tiapi::serverinfo::ServerInfo,

    /// 交易服务器配置(仅在风控中使用交易功能时使用)
    pub TrdSrvInfo: tiapi::serverinfo::ServerInfo,

    /// 行情服务器配置
    pub MdSrvInfo: mdapi::serverinfo::MDServerInfo,

    /// 显示实时消息的条数
    pub RealMsgCount: ShowRealMsgCountField,

    /// 值的显示格式
    pub ValueFormat: ShowValueFormatFiled,

    /// 监控数据显示格式
    pub MonShowFormat: MonitorShowFormatField,

    /// 使用交易功能时是否提示
    pub ReqTradeTip: RequestTradeTipField,

    /// 主题
    pub Theme: ThemeField,
}

/// 配置 - 现货监控
#[derive(Default)]
pub struct ConfigStkMon {
    /// 可转债监控设置
    pub CovrtBondMonitor: ConvertBondsMonitorField,

    /// 报撤单设置
    pub OrdInsertCancelMonitor: OrderInsertCancelMonitorField,

    /// 自成交设置
    pub TrdSelfMonitor: TradeSelfMonitorField,
}

/// 配置 - 个股期权监控
#[derive(Default)]
pub struct ConfigOptMon {
    /// 成交持仓比例设置
    pub TrdPosMonitor: TradePositionMonitorField,

    /// 报撤单设置
    pub OrdInsertCancel: OrderInsertCancelMonitorField,

    /// 自成交设置
    pub TrdSelfMonitor: TradeSelfMonitorField,
}

/// 配置 - 期货监控
#[derive(Default)]
pub struct ConfigFutMon {
    /// 自成交设置
    pub TrdSelfMonitor: TradeSelfMonitorField,
}

/// 运行后生成的信息
#[derive(Default)]
pub struct ConfigRun {
    /// 登录成功后获取的交易日
    pub TradingDay: i32,

    /// 登录用户信息
    pub LoginUser: LoginUserInfoField,

    /// 交易登录成功后的信息
    pub CtpLoginVec: Vec<tiapi::protocol_pub::login_trade_ctp::CtpRspLoginField>,
}

/// 配置
#[derive(Default)]
pub struct Config {
    /// 公共
    pub com: ConfigCom,

    /// 运行
    pub run: ConfigRun,

    /// 现货监控
    pub stk_mon: ConfigStkMon,

    /// 期货监控
    pub fut_mon: ConfigFutMon,

    /// 个股期权监控
    pub opt_mon: ConfigOptMon,
}

impl Config {
    /// 发布
    pub const PUB_COMP: EPubCompany = EPubCompany::Ti;
}

impl Config {
    /// 无效价格分界点
    pub const INVALID_PRICE: f64 = 0.000001;

    /// 表格中资金统一保留2位小数
    pub const AMT_DIGITS: usize = 2;

    /// 表格中价格统一保留3位小数 - 证券
    pub const STK_PRICE_DIGITS: usize = 3;

    /// 表格中价格统一保留4位小数 - 期货期权
    pub const PRICE_DIGITS: usize = 4;
}
