import { Splitter, Label } from "@zstk/lib.slint";

import { AccountPage } from "./main/account.slint";
import { LeftMiddlePage } from "./main/left_middle.slint";
import { LeftBottomPage } from "./main/left_bottom.slint";

import { RightTopPage } from "./main/right_top.slint";
import { RightMiddlePage } from "./main/right_middle.slint";
import { RightBottomPage } from "./main/right_bottom.slint";

import { TopPage } from "./main/top.slint";
import { MiddlePage } from "./main/middle.slint";
import { BottomPage } from "./main/bottom.slint";

export component RiskStkCreditPage {

    preferred_width: 100%;
    preferred_height: 100%;

    property <length> splitter_size: 2px;

    VerticalLayout {
        i_rec_right_top := Rectangle {
            height: 30%;

            TopPage {}
        }

        i_splitter_ver_mon_1 := Splitter {
            is_vertical: false;
            height: splitter_size;
            ratio: 0.3;
            panel1_min_size: 150px;
            panel2_min_size: 300px;
            max_size <=> parent.height;
            panel1_size <=> i_rec_right_top.height;

            changed ratio => {
                i_rec_right_top.height = parent.height * self.ratio;
                i_rec_right_bottom_container.y = i_rec_right_top.height + self.height;
                i_rec_right_bottom_container.height = self.panel2_size;
            }
        }

        i_rec_right_bottom_container := Rectangle {

            VerticalLayout {
                i_rec_right_mid := Rectangle {
                    height: 50%;

                    MiddlePage {}
                }

                i_splitter_ver_mon_2 := Splitter {
                    is_vertical: false;
                    height: splitter_size;
                    ratio: 0.5;
                    panel1_min_size: 150px;
                    panel2_min_size: 150px;
                    max_size <=> parent.height;
                    panel1_size <=> i_rec_right_mid.height;

                    changed ratio => {
                        i_rec_right_mid.height = parent.height * self.ratio;
                        i_rec_right_tottom.y = i_rec_right_mid.height + self.height;
                        i_rec_right_tottom.height = self.panel2_size;
                    }
                }

                i_rec_right_tottom := Rectangle {

                    BottomPage {
                    }
                }
            }
        }
    }

    // HorizontalLayout {
    //     i_rec_left_container := Rectangle {
    //         width: 60%;

    //         VerticalLayout {
    //             i_rec_left_top := Rectangle {
    //                 height: 30%;

    //                 AccountPage {
    //                 }
    //             }

    //             i_splitter_ver_left := Splitter {
    //                 is_vertical: false;
    //                 height: splitter_size;
    //                 ratio: 0.3;
    //                 panel1_min_size: 100px;
    //                 panel2_min_size: i_splitter_ver_mid.min_size;
    //                 max_size <=> parent.height;
    //                 panel1_size <=> i_rec_left_top.height;

    //                 changed ratio => {
    //                     i_rec_left_top.height = parent.height * self.ratio;
    //                     i_rec_left_bottom_container.y = i_rec_left_top.height + self.height;
    //                     i_rec_left_bottom_container.height = self.panel2_size;
    //                 }
    //             }

    //             i_rec_left_bottom_container := Rectangle {

    //                 VerticalLayout {
    //                     i_rec_left_mid := Rectangle {
    //                         height: 50%;

    //                         LeftMiddlePage {
    //                         }
    //                     }

    //                     i_splitter_ver_mid := Splitter {
    //                         is_vertical: false;
    //                         height: splitter_size;
    //                         ratio: 0.5;
    //                         max_size <=> parent.height;
    //                         panel1_size <=> i_rec_left_mid.height;

    //                         changed ratio => {
    //                             i_rec_left_mid.height = parent.height * self.ratio;
    //                             i_rec_left_tottom.y = i_rec_left_mid.height + self.height;
    //                             i_rec_left_tottom.height = self.panel2_size;
    //                         }
    //                     }

    //                     i_rec_left_tottom := Rectangle {

    //                         LeftBottomPage {
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     Splitter {
    //         width: splitter_size;
    //         ratio: 0.6;
    //         max_size <=> parent.width;
    //         panel1_size <=> i_rec_left_container.width;

    //         changed ratio => {
    //             i_rec_left_container.width = self.ratio * parent.width;
    //             i_rec_right_container.x = i_rec_left_container.width + self.width;
    //             i_rec_right_container.width = self.panel2_size;
    //         }
    //     }

    //     i_rec_right_container := Rectangle {
    //         VerticalLayout {
    //             i_rec_right_top := Rectangle {
    //                 height: 30%;

    //                 RightTopPage {}
    //             }

    //             i_splitter_ver_mon_1 := Splitter {
    //                 is_vertical: false;
    //                 height: splitter_size;
    //                 ratio: 0.3;
    //                 panel2_min_size: i_splitter_ver_mon_2.min_size;
    //                 max_size <=> parent.height;
    //                 panel1_size <=> i_rec_right_top.height;

    //                 changed ratio => {
    //                     i_rec_right_top.height = parent.height * self.ratio;
    //                     i_rec_right_bottom_container.y = i_rec_right_top.height + self.height;
    //                     i_rec_right_bottom_container.height = self.panel2_size;
    //                 }
    //             }

    //             i_rec_right_bottom_container := Rectangle {

    //                 VerticalLayout {
    //                     i_rec_right_mid := Rectangle {
    //                         height: 50%;

    //                         RightMiddlePage {}
    //                     }

    //                     i_splitter_ver_mon_2 := Splitter {
    //                         is_vertical: false;
    //                         height: splitter_size;
    //                         ratio: 0.5;
    //                         max_size <=> parent.height;
    //                         panel1_size <=> i_rec_right_mid.height;

    //                         changed ratio => {
    //                             i_rec_right_mid.height = parent.height * self.ratio;
    //                             i_rec_right_tottom.y = i_rec_right_mid.height + self.height;
    //                             i_rec_right_tottom.height = self.panel2_size;
    //                         }
    //                     }

    //                     i_rec_right_tottom := Rectangle {

    //                         RightBottomPage {
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }
}

export component _RiskStkCreditPage {

    preferred_width: 100%;
    preferred_height: 100%;

    property <length> splitter_size: 2px;

    HorizontalLayout {
        i_rec_left_container := Rectangle {
            width: 60%;

            VerticalLayout {
                i_rec_left_top := Rectangle {
                    height: 30%;

                    AccountPage {
                    }
                }

                i_splitter_ver_left := Splitter {
                    is_vertical: false;
                    height: splitter_size;
                    ratio: 0.3;
                    panel1_min_size: 100px;
                    panel2_min_size: i_splitter_ver_mid.min_size;
                    max_size <=> parent.height;
                    panel1_size <=> i_rec_left_top.height;

                    changed ratio => {
                        i_rec_left_top.height = parent.height * self.ratio;
                        i_rec_left_bottom_container.y = i_rec_left_top.height + self.height;
                        i_rec_left_bottom_container.height = self.panel2_size;
                    }
                }

                i_rec_left_bottom_container := Rectangle {

                    VerticalLayout {
                        i_rec_left_mid := Rectangle {
                            height: 50%;

                            LeftMiddlePage {
                            }
                        }

                        i_splitter_ver_mid := Splitter {
                            is_vertical: false;
                            height: splitter_size;
                            ratio: 0.5;
                            max_size <=> parent.height;
                            panel1_size <=> i_rec_left_mid.height;

                            changed ratio => {
                                i_rec_left_mid.height = parent.height * self.ratio;
                                i_rec_left_tottom.y = i_rec_left_mid.height + self.height;
                                i_rec_left_tottom.height = self.panel2_size;
                            }
                        }

                        i_rec_left_tottom := Rectangle {

                            LeftBottomPage {
                            }
                        }
                    }
                }
            }
        }

        Splitter {
            width: splitter_size;
            ratio: 0.6;
            max_size <=> parent.width;
            panel1_size <=> i_rec_left_container.width;

            changed ratio => {
                i_rec_left_container.width = self.ratio * parent.width;
                i_rec_right_container.x = i_rec_left_container.width + self.width;
                i_rec_right_container.width = self.panel2_size;
            }
        }

        i_rec_right_container := Rectangle {
            VerticalLayout {
                i_rec_right_top := Rectangle {
                    height: 30%;

                    RightTopPage {}
                }

                i_splitter_ver_mon_1 := Splitter {
                    is_vertical: false;
                    height: splitter_size;
                    ratio: 0.3;
                    panel2_min_size: i_splitter_ver_mon_2.min_size;
                    max_size <=> parent.height;
                    panel1_size <=> i_rec_right_top.height;

                    changed ratio => {
                        i_rec_right_top.height = parent.height * self.ratio;
                        i_rec_right_bottom_container.y = i_rec_right_top.height + self.height;
                        i_rec_right_bottom_container.height = self.panel2_size;
                    }
                }

                i_rec_right_bottom_container := Rectangle {

                    VerticalLayout {
                        i_rec_right_mid := Rectangle {
                            height: 50%;

                            RightMiddlePage {}
                        }

                        i_splitter_ver_mon_2 := Splitter {
                            is_vertical: false;
                            height: splitter_size;
                            ratio: 0.5;
                            max_size <=> parent.height;
                            panel1_size <=> i_rec_right_mid.height;

                            changed ratio => {
                                i_rec_right_mid.height = parent.height * self.ratio;
                                i_rec_right_tottom.y = i_rec_right_mid.height + self.height;
                                i_rec_right_tottom.height = self.panel2_size;
                            }
                        }

                        i_rec_right_tottom := Rectangle {

                            RightBottomPage {
                            }
                        }
                    }
                }
            }
        }
    }
}