use serde::Deserialize;

/// 成交消息
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpTradeField {
    // CThostFtdcTradeField
    pub BrokerID: String,       // 经纪公司代码
    pub InvestorID: String,     // 投资者代码
    pub InstrumentID: String,   // 合约代码
    pub OrderRef: String,       // 报单引用
    pub UserID: String,         // 用户代码
    pub ExchangeID: String,     // 交易所代码
    pub TradeID: String,        // 成交编号
    pub Direction: i32,         // 买卖方向
    pub OrderSysID: String,     // 报单编号
    pub ParticipantID: String,  // 会员代码
    pub ClientID: String,       // 客户代码
    pub TradingRole: i32,       // 交易角色
    pub ExchangeInstID: String, // 合约在交易所的代码
    pub OffsetFlag: i32,        // 开平标志
    pub HedgeFlag: i32,         // 投机套保标志
    pub Price: f64,             // 价格
    pub Volume: i32,            // 数量
    pub TradeDate: String,      // 成交时期
    pub TradeTime: String,      // 成交时间
    pub TradeType: i32,         // 成交类型
    pub PriceSource: i32,       // 成交价来源
    pub TraderID: String,       // 交易所交易员代码
    pub OrderLocalID: String,   // 本地报单编号
    pub ClearingPartID: String, // 结算会员编号
    pub BusinessUnit: String,   // 业务单元
    pub SequenceNo: i32,        // 序号
    pub TradingDay: String,     // 交易日
    pub SettlementID: i32,      // 结算编号
    pub BrokerOrderSeq: i32,    // 经纪公司报单编号
    pub TradeSource: i32,       // 成交来源
    pub InvestUnitID: String,   // 投资单元代码
    pub OwnerType: String,      // 所有者类型
    pub ComTradeID: String,     // 组合编号
    pub LoginPBU: String,       // 登陆PBU
}

/// 成交请求查询消息
#[derive(Default)]
pub struct CtpReqQryTradeField {
    /// 交易所代码
    pub ExchID: String,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 成交编号
    pub TradeID: String,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: String,
    pub TransTime0_Int: i32,

    /// 结束时间
    pub TransTime1: String,
    pub TransTime1_Int: i32,
}

/// 成交响应查询消息
#[derive(Default)]
pub struct CtpRspQryTradeField {
    /// 成交消息
    pub array: Vec<CtpTradeField>,
}

/// 非交易业务成交
#[derive(Default, Debug, Clone, Deserialize)]
#[serde[default]]
pub struct CtpBusinessTradeField {
    pub FrontNo: i32,   // 交易端前置
    pub SessionNo: i32, // 交易端SessionNO
    pub RequestNo: i32, // 请求编号

    pub MsgSeqNum: i32, // 消息序号(按资金账户-消息类型从1递增)
    pub BizSeqNum: i32, // 业务序号(按资金账户从1递增)

    pub ExchID: i32,       // 交易所编码
    pub AccountID: String, // 资金帐号
    pub AccountType: i32,  // 账号类型
    pub ClientID: String,  // 交易编码

    pub InstrumentID: String,    // 合约编码
    pub DstInstrumentID: String, // 合约编码
    pub UserID: String,          // 原始报单交易用户代码

    pub TradeID: String,    // 成交编号
    pub LocalOrderNo: i32,  // 本地报单编号
    pub OrderSysID: String, // 交易所报单编号

    pub BusinessType: i32, // 业务类型
    pub Side: i32,         // 买卖方向

    pub TradePrice: f64,   // 成交价格
    pub TradeVolume: i32,  // 成交数量
    pub LeavesVolume: i32, // 本次成交后申报余额数量

    pub TransTime: i32, // 成交时间
}

/// 非交易业务成交请求查询消息
#[derive(Default)]
pub struct CtpReqQryBusinessTradeField {
    /// 交易所代码
    pub ExchID: i32,

    /// 账号编码
    pub AccountID: String,

    /// 合约代码
    pub InstrumentID: String,

    /// 成交编号
    pub TradeID: String,

    /// 交易所报单编号
    pub OrderSysID: String,

    /// 开始时间
    pub TransTime0: i32,

    /// 结束时间
    pub TransTime1: i32,
}