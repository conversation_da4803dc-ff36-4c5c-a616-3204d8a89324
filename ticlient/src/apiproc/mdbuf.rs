#![allow(unused_variables)]

use std::sync::Arc;

use mdapi::protocol::{
    ptapp::AppHeartbeat,
    ptmd::{MdSubscribeField, PTRtnMarketDataL1},
};

pub trait IOnRtnMarketDataL1: Send + Sync {
    fn rtn(&self, md: PTRtnMarketDataL1) {}
}

pub struct MdBuf {
    is_login: bool,
    rtn_md_callback: Option<Arc<dyn Fn(PTRtnMarketDataL1) + Send + Sync>>,
    on_sub_failed_callback: Option<Arc<dyn Fn(String) + Send + Sync>>,
    on_unsub_failed_callback: Option<Arc<dyn Fn(String) + Send + Sync>>,

    rtn_callback: Option<Vec<Box<dyn IOnRtnMarketDataL1>>>,
}

impl MdBuf {
    pub fn new() -> Self {
        Self {
            is_login: false,

            rtn_md_callback: None,
            on_sub_failed_callback: None,
            on_unsub_failed_callback: None,

            rtn_callback: None,
        }
    }

    // 注册L1行情回调
    pub fn register_rtn_md_l1_callback(&mut self, callback: Box<dyn IOnRtnMarketDataL1>) {
        if self.rtn_callback.is_none() {
            self.rtn_callback = Some(Vec::new());
        }
        self.rtn_callback.as_mut().unwrap().push(callback);
    }

    // 注册行情回调
    pub fn register_rtn_md_callback<F>(&mut self, callback: F)
    where
        F: Fn(PTRtnMarketDataL1) + Send + Sync + 'static,
    {
        self.rtn_md_callback = Some(Arc::new(callback));
    }

    // 注册行情订阅失败回调
    pub fn register_on_subscribe_failed_callback<F>(&mut self, callback: F)
    where
        F: Fn(String) + Send + Sync + 'static,
    {
        self.on_sub_failed_callback = Some(Arc::new(callback));
    }

    // 注册行情退订阅失败回调
    pub fn register_on_un_subscribe_failed_callback<F>(&mut self, callback: F)
    where
        F: Fn(String) + Send + Sync + 'static,
    {
        self.on_unsub_failed_callback = Some(Arc::new(callback));
    }
}

impl MdBuf {
    /// 断开连接
    pub fn on_rtn_disconnect(&mut self) {
        self.is_login = false;
        log::warn!("md: on_rtn_disconnect");
    }

    /// 心跳消息
    pub fn on_rtn_heartbeat(&mut self, heartbeat: AppHeartbeat) {}

    /// 请求行情登录响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    pub fn on_rsp_login(&mut self, ec: i32, em: &String) {
        if 0 == ec {
            self.is_login = true;
        } else {
            self.is_login = false;
            log::warn!("md: on_rsp_login, failed. [{}, {}]", ec, em);
        }
    }

    /// 请求行情登出响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    pub fn on_rsp_logout(&mut self, ec: i32, em: &String) {
        self.is_login = false;
    }

    /// 请求行情订阅响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`sub`: 行情订阅字段
    pub fn on_rsp_md_subscribe(&mut self, ec: i32, em: &String, sub: &MdSubscribeField) {
        if 0 != ec {
            if let Some(cb) = self.on_sub_failed_callback.as_ref() {
                cb(format!("请求订阅合约[{}]的行情失败\n{}", sub.IID, em));
            }
        }
    }

    /// 请求行情退订阅响应
    /// <br>`ec`: 错误码
    /// <br>`em`: 错误信息
    /// <br>`sub`: 行情退订阅字段
    pub fn on_rsp_md_un_subscribe(&mut self, ec: i32, em: &String, sub: &MdSubscribeField) {
        if 0 != ec {
            if let Some(cb) = self.on_unsub_failed_callback.as_ref() {
                cb(format!("请求退订阅合约[{}]的行情失败\n{}", sub.IID, em));
            }
        }
    }

    /// L1行情推送
    /// <br>`md`: L1行情
    pub fn on_rtn_market_data_l1(&mut self, md: PTRtnMarketDataL1) {
        if let Some(cbarr) = self.rtn_callback.as_ref() {
            cbarr.iter().for_each(|cb| {
                cb.rtn(md.clone());
            })
        }

        // if let Some(cb) = self.rtn_md_callback.as_ref() {
        //     cb(md);
        // }
    }
}
