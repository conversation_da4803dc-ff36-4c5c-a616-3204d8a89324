import { Theme, TableView, TVColumnHeader, TVCellType, Fonts, Label, Radius, Icons, ComboBox, Button } from "@zstk/lib.slint";
import { ColWidth, NotifyColor, WidgetColor, AppCom } from "../../../struct.slint";
import { TimeSelect, TimeItem } from "../../../widgets/time.slint";
import { StkRskCredit_Com } from "../struct.slint";

export component QryCreditTcPosPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <Point> right_up_point;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    private property <TVColumnHeader> header : {
        height: AppCom.table_header_height,
        background: Colors.transparent,
        font: Fonts.normal,
        fix_head_count: 2,
        fix_tail_count: 1,
        columns: [
            { title: @tr("处理时间"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("股东代码"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("证券代码"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("转入数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("转出数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("交易所"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            { title: @tr("资金账户"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
        ]
    };

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                //current_text: StkTrd_QryWithdrawDeposit.exchid;
                //model: CommonModel.exch_stk_model;
            }

            Label {
                text: "处理时间";
                vertical_alignment: center;
            }

            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            sortable: true;
            column_header: header;
            // row_count: StkRskCredit_Account.row_data.length;

            sort_canceled => {
                // StkRskCredit_Account.sort_asc_column_index = 0;
                // StkRskCredit_Account.sort_dec_column_index = -1;
                // StkRskCredit_Account.sort_ascending(StkRskCredit_Account.sort_asc_column_index);
            }

            sort_ascending(index) => {
                // StkRskCredit_Account.sort_ascending(index);
                // StkRskCredit_Account.sort_asc_column_index = index;
                // StkRskCredit_Account.sort_dec_column_index = -1;
                true
            }

            sort_descending(index) => {
                // StkRskCredit_Account.sort_descending(index);
                // StkRskCredit_Account.sort_asc_column_index = -1;
                // StkRskCredit_Account.sort_dec_column_index = index;
                true
            }

            get_cell_data(row_index, column_index) => {
                ""
                // StkRskCredit_Account.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                // 入金
                if 11 == column_index {
                    return NotifyColor.normal;
                }

                // 出金
                if 12 == column_index {
                    return NotifyColor.caution;
                }

                // return StkRskCredit_Account.get_row_data_color(row_index, column_index, data);

                NotifyColor.default
            }

            cell_double_clicked(row_index, column_index) => {
            }

            cell_pointer_event(row_index, column_index, event, point) => {
            }

            cell_key_copy_pressed(row_index, column_index) => {
            }
        }
    }
}