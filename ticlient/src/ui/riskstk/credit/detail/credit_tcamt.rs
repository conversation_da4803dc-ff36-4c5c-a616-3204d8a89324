use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::CreditTcAmtMap,
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

/// 资金头寸数据
struct CreditTcAmtDatas {
    filter_changed: AtomicBool,
}
impl CreditTcAmtDatas {
    pub fn new() -> Self {
        Self {
            filter_changed: AtomicBool::new(false),
        }
    }
}
static CREDIT_TC_AMT_DATAS: OnceLock<CreditTcAmtDatas> = OnceLock::new();

/// 资金头寸
pub(in crate::ui::riskstk::credit) struct CreditTcAmt {
    /// 显示按交易所的明细
    show_detail_amt: AtomicBool,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CreditTcAmtMap>,

    /// 数据
    data_sumary_map: Arc<CreditTcAmtMap>,
}

/// 构建与初始化
impl CreditTcAmt {
    pub fn new() -> Self {
        Self {
            show_detail_amt: AtomicBool::new(false),
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CreditTcAmtMap::new()),
            data_sumary_map: Arc::new(CreditTcAmtMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let _ = CREDIT_TC_AMT_DATAS.set(CreditTcAmtDatas::new());

        self.show_detail_amt
            .store(app.global::<AppInerArgs>().get_show_detail(), Ordering::Relaxed);

        let app_tc = app.global::<StkRskCredit_CreditTcAmt>();

        let app_weak = app.as_weak();
        app_tc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditTcAmt::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_tc.on_filter_changed(move || CreditTcAmt::on_filter_changed(&app_weak));

        log::trace!("Credit tc amt init completed");
    }
}

/// 查询
impl CreditTcAmt {
    /// 查询资金头寸
    pub fn qry_credit_tcamt(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_credit_tcamt() };
        if ret.is_empty() {
            return;
        }

        let show_detail_amt = self.show_detail_amt.load(Ordering::Relaxed);

        for (key, tc) in ret {
            if let Some(mut exist) = self.data_map.get_mut(&key) {
                if !show_detail_amt {
                    let distrib = tc.Distrib - exist.Distrib;
                    let used = tc.Used - exist.Used;
                    let frozen = tc.Frozen - exist.Frozen;
                    let in_ = tc.In - exist.In;
                    let out = tc.Out - exist.Out;

                    // 根据先添加汇总,再添加明细的规则,这里肯定存在
                    let sum_key = std::format!("{}{}", tc.TcType, tc.AccID);
                    let mut sum = self.data_sumary_map.get_mut(&sum_key).unwrap();
                    sum.Distrib += distrib;
                    sum.Used += used;
                    sum.Frozen += frozen;
                    sum.In += in_;
                    sum.Out += out;
                }

                exist.Distrib = tc.Distrib;
                exist.Used = tc.Used;
                exist.Frozen = tc.Frozen;
                exist.In = tc.In;
                exist.Out = tc.Out;
            } else {
                if !show_detail_amt {
                    let sum_key = std::format!("{}{}", tc.TcType, tc.AccID);
                    if let Some(mut sum) = self.data_sumary_map.get_mut(&sum_key) {
                        sum.Distrib += tc.Distrib;
                        sum.Used += tc.Used;
                        sum.Frozen += tc.Frozen;
                        sum.In += tc.In;
                        sum.Out += tc.Out;
                    } else {
                        let mut new_tc = tc.clone();
                        new_tc.ExchID = 0;
                        self.data_sumary_map.insert(sum_key, new_tc);
                    }
                }

                self.data_map.insert(key, tc);
            }
        }

        self.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditTcAmt {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(
        app_weak: &Weak<RiskStkCreditDetailPage>,
        row_index: i32,
        column_index: i32,
        data: SharedString,
    ) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 可用头寸
        if 4 == column_index {
            let data = data.replace(",", "").parse::<f64>().unwrap_or_default();
            if data < 0.0 {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<RiskStkCreditDetailPage>) {
        CREDIT_TC_AMT_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

/// 更新UI
impl CreditTcAmt {
    // 更新
    pub async fn update(&self, app_weak: Weak<RiskStkCreditDetailPage>) {
        let tc_datas = CREDIT_TC_AMT_DATAS.get().unwrap();

        let show_detail_amt = self.show_detail_amt.load(Ordering::Relaxed);

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                tc_datas.filter_changed.store(false, Ordering::Relaxed);

                if show_detail_amt {
                    self.data_map.clone()
                } else {
                    self.data_sumary_map.clone()
                }
            } else {
                if tc_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    if show_detail_amt {
                        self.data_map.clone()
                    } else {
                        self.data_sumary_map.clone()
                    }
                } else {
                    Arc::new(CreditTcAmtMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            let app_tc = app.global::<StkRskCredit_CreditTcAmt>();

            let filter_accid = app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned();
            if filter_accid.is_empty() {
                app_tc.set_row_data(row_data.into());
                return;
            }

            let filter_tctype = app_tc.get_filter_tctype();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|tc| {
                if !tc.AccID.is_empty() {
                    if filter_accid != tc.AccID {
                        return;
                    }
                }

                if filter_tctype > 0 {
                    if tc.TcType != filter_tctype {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 头寸类型
                items.push(
                    match tc.TcType {
                        1 => "普通头寸",
                        2 => "专项头寸",
                        _ => "未知头寸",
                    }
                    .into(),
                );

                // 资金账户
                items.push(slint::format!("{}", tc.AccID));

                // 交易所
                items.push(TIConvert::exch_name(tc.ExchID).into());

                // 授权头寸
                items.push(TIConvert::format_f(tc.Distrib, Config::AMT_DIGITS).into());

                // 可用头寸
                items.push(TIConvert::format_f(tc.Distrib - tc.Used - tc.Frozen + tc.In - tc.Out, Config::AMT_DIGITS).into());

                // 使用头寸
                items.push(TIConvert::format_f(tc.Used, Config::AMT_DIGITS).into());

                // 冻结头寸
                items.push(TIConvert::format_f(tc.Frozen, Config::AMT_DIGITS).into());

                // 转入头寸
                items.push(TIConvert::format_f(tc.In, Config::AMT_DIGITS).into());

                // 转出头寸
                items.push(TIConvert::format_f(tc.Out, Config::AMT_DIGITS).into());

                row_data.push(items.into());
            });

            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(1 as usize).unwrap();
                let c_b = r_b.row_data(1 as usize).unwrap();
                c_a.cmp(&c_b)
            }));

            app_tc.set_row_data(sort_model.into());
        });
    }
}
