import { <PERSON><PERSON><PERSON>, TableView, ListViewItem, ComboBox, Label } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";

export global FutTrd_MonSelfTrade {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> clientid;
    in_out property <string> insid;

    in_out property <[ListViewItem]> clientid_model;
    in_out property <[ListViewItem]> insid_model;

    callback filter_changed();
    pure callback get_row_data_color(int, int, string) -> brush;

    public function clientid_model_len() -> int {
        clientid_model.length
    }

    public function insid_model_len() -> int {
        insid_model.length
    }
}

export component MonSelfTradePage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> FutTrd_MonSelfTrade.exchid;
                model: CommonModel.exch_fut_model;

                selected => {
                    FutTrd_MonSelfTrade.filter_changed();
                }
            }

            Label {
                text: "交易编码";
                vertical_alignment: center;
            }

            ComboBox {
                width: 130px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> FutTrd_MonSelfTrade.clientid;
                model: FutTrd_MonSelfTrade.clientid_model;
                max_popup_height: 160px;

                edited => {
                    FutTrd_MonSelfTrade.filter_changed();
                }

                selected => {
                    FutTrd_MonSelfTrade.filter_changed();
                }
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }

            ComboBox {
                width: 130px;
                drop_down_list: false;
                placeholder_text: "请输入";
                current_text <=> FutTrd_MonSelfTrade.insid;
                model: FutTrd_MonSelfTrade.insid_model;
                max_popup_height: 160px;

                edited => {
                    FutTrd_MonSelfTrade.filter_changed();
                }

                selected => {
                    FutTrd_MonSelfTrade.filter_changed();
                }
            }

            Rectangle { }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.fut_insid },
                    { title: @tr("是否异常"), alignment: TextHorizontalAlignment.center, width: ColWidth.monstatus },
                    { title: @tr("自成交数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("自成交笔数"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                ]
            };
            row_count: FutTrd_MonSelfTrade.row_data.length;

            get_cell_data(row_index, column_index) => {
                FutTrd_MonSelfTrade.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                FutTrd_MonSelfTrade.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(FutTrd_MonSelfTrade.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(FutTrd_MonSelfTrade.row_data[row_index]);
                }
            }
        }
    }
}
