use std::rc::Rc;

use crate::{
    common::{global::INS, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::instrument::InstrumentField;

/// 查询 - 合约
pub(in crate::ui::riskstk::credit) struct QueryInstrument {}

impl QueryInstrument {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let qry = app.global::<StkRskCredit_QryInstrument>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryInstrument::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryInstrument::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryInstrument::on_page_size_changed(&app_weak, size));

        log::trace!("Query instrument init completed");
    }
}

impl QueryInstrument {
    /// 获取一行数据
    fn get_row_items(ins: &InstrumentField, is_credit: bool) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        let tp1 = if 0 == ins.tp1 { "否" } else { "是" };
        let producttype = TIConvert::producttype(ins.producttype);
        let ptw = ins.ptw as usize;

        // 交易所
        items.push(ins.exchname.clone().into());

        // 证券代码
        items.push(ins.insid.clone().into());

        // 证券名称
        items.push(ins.symbol.clone().into());

        // 类型
        items.push(producttype.into());

        // T+1
        items.push(tp1.into());

        // 买单位数量
        items.push(TIConvert::format_i(ins.buyordtick).into());

        // 卖单位数量
        items.push(TIConvert::format_i(ins.sellordtick).into());

        // 最小变动价
        items.push(TIConvert::format_f(ins.pricetick, ptw).into());

        // 昨收盘价
        items.push(TIConvert::format_f(ins.prestlprice, ptw).into());

        // 涨停板价格
        items.push(TIConvert::format_f(ins.uplmtprice, ptw).into());

        // 跌停板价格
        items.push(TIConvert::format_f(ins.lowlmtprice, ptw).into());

        // 市价最大下单量
        items.push(TIConvert::format_i(ins.maxmktvol).into());

        // 市价最小下单量
        items.push(TIConvert::format_i(ins.minmktvol).into());

        // 限价最大下单量
        items.push(TIConvert::format_i(ins.maxlmtvol).into());

        // 限价最小下单量
        items.push(TIConvert::format_i(ins.minlmtvol).into());

        // 融资标的
        items.push(slint::format!("{}", if 1 == ins.ifn { "是" } else { "否" }));

        // 融券标的
        items.push(slint::format!("{}", if 1 == ins.isc { "是" } else { "否" }));

        // 折算率
        items.push(slint::format!("{}", ins.colldiskratio));

        // 融资保证金比例
        items.push(slint::format!("{}", ins.fimarginratio));

        // 融券保证金比例
        items.push(slint::format!("{}", ins.slmarginratio));

        // 允许融资
        items.push(slint::format!("{}", if 1 == ins.enablefi { "是" } else { "否" }));

        // 允许融券
        items.push(slint::format!("{}", if 1 == ins.ebablesl { "是" } else { "否" }));

        // 允许做抵押物
        items.push(slint::format!("{}", if 1 == ins.enabledb { "是" } else { "否" }));

        items
    }

    // 查询
    fn on_query(app_weak: &Weak<RiskStkCreditDetailPage>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let is_credit = app.global::<StkTrd_Com>().get_iscredit();
        let app_qryins = app.global::<StkRskCredit_QryInstrument>();

        // 清空上一次结果
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qryins.set_row_data(row_data.into());

        let exchid = {
            let eid = app_qryins.get_exchid();
            match eid.as_str() {
                "SSE" => 1,
                "SZSE" => 2,
                "BSE" => 9,
                _ => 0,
            }
        };
        let insid = app_qryins.get_insid();

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qryins.get_page_index()
            }
        };
        let page_size = app_qryins.get_page_size();

        let ret = {
            INS.get()
                .unwrap()
                .read()
                .unwrap()
                .qry_ins(exchid, &insid, page_index, page_size)
        };
        if 0 == ret.0 {
            // show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
            println!("查询结果为空");
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qryins.set_page_index_model(page_index_data.into());
            app_qryins.set_item_total(ret.0);
            app_qryins.set_page_index(0);
            app_qryins.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|ins| {
            let items = QueryInstrument::get_row_items(&ins, is_credit);
            row_data.push(items.into());
        });

        app_qryins.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<RiskStkCreditDetailPage>) {
        QueryInstrument::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<RiskStkCreditDetailPage>, page_index: i32) {
        QueryInstrument::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<RiskStkCreditDetailPage>, page_size: i32) {
        QueryInstrument::on_query(app_weak, true);
    }
}
