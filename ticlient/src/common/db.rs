use std::{fs, process::Command};

use rusqlite::{params, Connection};

#[cfg(feature = "risk")]
use tiapi::protocol_pub::{
    order_risk_stk::{OrderStkField, ReqQryOrderStkField},
    trade_risk_stk::{ReqQryTradeStkField, TradeStkField},
};

#[cfg(feature = "trade")]
use tiapi::protocol_pub::{
    order_trade_ctp::{CtpOrderField, CtpQuoteField, CtpReqQryOrderField, CtpReqQryQuoteField},
    trade_trade_ctp::{CtpReqQryTradeField, CtpTradeField},
};

use super::tistruct::EClientType;

#[cfg(target_os = "windows")]
use ::std::os::windows::process::CommandExt;

pub struct DbSqlite {
    dbpath: String,
    conn: Option<Connection>,
}

impl DbSqlite {
    /// new
    pub fn new() -> Self {
        Self {
            dbpath: String::new(),
            conn: None,
        }
    }

    /// 初始化
    pub fn init(&mut self, dbdir: &str, clitetype: EClientType) -> bool {
        // 删除历史数据库文件(如程序异常退出时遗留的文件)
        {
            // 在windows系统下forfiles命令只认以\为路径的路径名
            let dbdir = {
                if cfg!(target_os = "windows") {
                    let mut tmpdir = String::new();
                    let sub_arr: Vec<&str> = dbdir.split('/').collect();
                    sub_arr.iter().for_each(|sub| {
                        tmpdir.push_str(sub);
                        tmpdir.push_str("\\");
                    });
                    tmpdir
                } else {
                    dbdir.to_owned()
                }
            };

            let (command, args) = if cfg!(target_os = "macos") || cfg!(target_os = "linux") {
                ("find", vec![dbdir.as_str(), "-type", "f", "-mtime", "+0", "-delete"])
            } else if cfg!(target_os = "windows") {
                (
                    "forfiles",
                    vec!["/P", dbdir.as_str(), "/S", "/D", "-1", "/C", "cmd /c del /F /Q @path"],
                )
            } else {
                log::warn!("Unsupported operating system for database cleanup");
                ("", vec![])
            };

            #[cfg(target_os = "windows")]
            let ret = Command::new(command).creation_flags(0x08000000).args(&args).output();

            #[cfg(any(target_os = "macos", target_os = "linux"))]
            let ret = Command::new(command).args(&args).output();

            match ret {
                Ok(output) if output.status.success() => {
                    log::info!("Successfully cleaned up database files");
                }
                Ok(_) => {
                    log::warn!("Failed to execute database cleanup command"); // 注意:windows下如果没有找到满足条件的文件也会返回错误
                }
                Err(err) => {
                    log::warn!("Failed to delete database files: {}", err);
                }
            }
        }

        // 创建数据库文件
        self.dbpath = std::format!("{}/{}.db", dbdir, chrono::Local::now().format("%Y%m%d_%H%M%S%3f"));

        let cnn = Connection::open(self.dbpath.clone());
        if cnn.is_err() {
            log::warn!("open db({}) failed", self.dbpath);
            return false;
        }
        self.conn = Some(cnn.unwrap());

        // 创建表
        if EClientType::FutTrd == clitetype
            || EClientType::StkOptTrd == clitetype
            || EClientType::StkTrd == clitetype
            || EClientType::StkCreditTrd == clitetype
        {
            #[cfg(feature = "trade")]
            self.create_trd_table();
        } else {
            #[cfg(feature = "risk")]
            self.create_rsk_table();
        }

        return true;
    }

    /// 释放
    pub fn release(&mut self) {
        if self.conn.is_some() {
            self.conn = None;
            let _ = fs::remove_file(self.dbpath.clone());
        }
    }
}

impl DbSqlite {
    /// 创建交易端需要的表
    #[cfg(feature = "trade")]
    fn create_trd_table(&self) {
        if self.conn.is_none() {
            return;
        }

        self.create_trd_order();
        self.create_trd_trade();
        self.create_trd_quote();
    }

    /// 创建委托表
    #[cfg(feature = "trade")]
    fn create_trd_order(&self) {
        let sql = r###"CREATE TABLE "trd_rtn_order" (
            "InvestorID" TEXT DEFAULT NULL,
            "ExchangeID" TEXT DEFAULT NULL,
            "ClientID" TEXT DEFAULT NULL,
            "InsertTime" TEXT DEFAULT NULL,
            "InstrumentID" TEXT DEFAULT NULL,
            "Volume" INTEGER DEFAULT 0,
            "LimitPrice" REAL DEFAULT 0,
            "Direction" INTEGER DEFAULT 0,
            "LastStatus" INTEGER DEFAULT 0,
            "OrderSysID" TEXT DEFAULT NULL,
            "OrderLocalID" TEXT DEFAULT NULL,
            "OrderPriceType" INTEGER DEFAULT NULL,
            "TimeCondition" INTEGER DEFAULT 0,
            "VolumeTotal" INTEGER DEFAULT 0,
            "UserID" TEXT DEFAULT NULL,
            "CombOffsetFlag" TEXT DEFAULT '',
            "CombHedgeFlag" TEXT DEFAULT '',
            "VolumeCondition" INTEGER DEFAULT 0,
            "ContingentCondition" INTEGER DEFAULT 0,
            "TIOwnerType" INTEGER DEFAULT 0,
            "MinVolume" INTEGER DEFAULT 0,
            "SettlementID" INTEGER DEFAULT 0,
            "PKey" TEXT NOT NULL,
            PRIMARY KEY ("PKey")
            ); "###;

        let idx1 = r###"CREATE INDEX "idx_trd_rtn_order_qry"
            ON "trd_rtn_order" (
            "ExchangeID",
            "InstrumentID",
            "LastStatus",
            "OrderSysID"
            );"###;

        let idx2 = r###"CREATE INDEX "idx_trd_rtn_order_time"
            ON "trd_rtn_order" (
            "InsertTime" DESC
            );"###;

        let conn: &Connection = self.conn.as_ref().unwrap();

        let _ = conn.execute("DROP TABLE IF EXISTS trd_rtn_order;", ());
        let _ = conn.execute(sql, ());
        let _ = conn.execute(idx1, ());
        let _ = conn.execute(idx2, ());
    }

    /// 创建成交表
    #[cfg(feature = "trade")]
    fn create_trd_trade(&self) {
        let sql = r###"CREATE TABLE "trd_rtn_trade" (
            "InvestorID" TEXT DEFAULT NULL,
            "ExchangeID" TEXT DEFAULT NULL,
            "ClientID" TEXT DEFAULT NULL,
            "TradeTime" TEXT DEFAULT NULL,
            "InstrumentID" TEXT DEFAULT NULL,
            "Direction" INTEGER DEFAULT 0,
            "OffsetFlag" INTEGER DEFAULT 0,
            "Volume" INTEGER DEFAULT 0,
            "Price" REAL DEFAULT 0,
            "TradeID" TEXT DEFAULT NULL,
            "OrderSysID" TEXT DEFAULT NULL,
            "OrderLocalID" TEXT DEFAULT NULL,
            "HedgeFlag" HedgeFlag DEFAULT 0,
            "SequenceNo" INTEGER DEFAULT 0,
            PRIMARY KEY ("InvestorID", "SequenceNo")
            ); "###;

        let idx1 = r###"CREATE INDEX "idx_trd_rtn_trade_qry"
            ON "trd_rtn_trade" (
            "ExchangeID",
            "InstrumentID",
            "TradeID",
            "OrderSysID"
            );"###;

        let idx2 = r###"CREATE INDEX "idx_trd_rtn_trade_time"
            ON "trd_rtn_trade" (
            "TradeTime" DESC
            );"###;

        let conn: &Connection = self.conn.as_ref().unwrap();

        let _ = conn.execute("DROP TABLE IF EXISTS trd_rtn_trade;", ());
        let _ = conn.execute(sql, ());
        let _ = conn.execute(idx1, ());
        let _ = conn.execute(idx2, ());
    }

    /// 创建报价表
    #[cfg(feature = "trade")]
    fn create_trd_quote(&self) {
        let sql = r###"CREATE TABLE "trd_rtn_quote" (
            "InvestorID" TEXT DEFAULT NULL,
            "ExchangeID" TEXT DEFAULT NULL,
            "ClientID" TEXT DEFAULT NULL,
            "InsertTime" TEXT DEFAULT NULL,
            "InstrumentID" TEXT DEFAULT NULL,
            "LastStatus" INTEGER DEFAULT 0,
            "QuoteSysID" TEXT DEFAULT NULL,
            "QuoteLocalID" TEXT DEFAULT NULL,
            "BidLastStatus" INTEGER DEFAULT 0,
            "BidOrderSysID" TEXT DEFAULT NULL,
            "BidOffsetFlag" INTEGER DEFAULT 0,
            "BidPrice" REAL DEFAULT 0,
            "BidVolume" INTEGER DEFAULT 0,
            "AskLastStatus" INTEGER DEFAULT 0,
            "AskOrderSysID" TEXT DEFAULT NULL,
            "AskOffsetFlag" INTEGER DEFAULT 0,
            "AskPrice" REAL DEFAULT 0,
            "AskVolume" INTEGER DEFAULT 0,
            "UserID" TEXT DEFAULT NULL,
            "PKey" TEXT NOT NULL,
            PRIMARY KEY ("PKey")
            ); "###;

        let idx1 = r###"CREATE INDEX "idx_trd_rtn_quote_qry"
            ON "trd_rtn_quote" (
            "ExchangeID",
            "InstrumentID",
            "LastStatus",
            "QuoteSysID"
            );"###;

        let idx2 = r###"CREATE INDEX "idx_trd_rtn_quote_time"
            ON "trd_rtn_quote" (
            "InsertTime" DESC
            );"###;

        let conn: &Connection = self.conn.as_ref().unwrap();

        let _ = conn.execute("DROP TABLE IF EXISTS trd_rtn_quote;", ());
        let _ = conn.execute(sql, ());
        let _ = conn.execute(idx1, ());
        let _ = conn.execute(idx2, ());
    }

    /// 写委托数据
    #[cfg(feature = "trade")]
    pub fn write_trd_order(&mut self, order_map: dashmap::DashMap<String, CtpOrderField>) {
        if order_map.is_empty() || self.conn.is_none() {
            return;
        }

        let conn = self.conn.as_mut().unwrap();
        let trans = conn.transaction();
        if let Err(err) = trans {
            log::debug!("write_trd_order: create transaction failed. {}", err.to_string());
            return;
        }
        let trans = trans.unwrap();

        order_map.iter().for_each(|ord| {
            let ret = trans.execute(
                r###"INSERT INTO trd_rtn_order (
                    InvestorID, ExchangeID, ClientID, InsertTime, InstrumentID, Volume, LimitPrice, Direction, LastStatus,
                    OrderSysID, OrderLocalID, OrderPriceType, TimeCondition, VolumeTotal, UserID, CombOffsetFlag,
                    CombHedgeFlag, VolumeCondition, ContingentCondition, TIOwnerType, MinVolume, SettlementID, PKey
                    ) VALUES (
                    ?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18, ?19, ?20, ?21, ?22, ?23
                    );"###,
                params![
                    &ord.InvestorID,
                    &ord.ExchangeID,
                    &ord.ClientID,
                    &ord.InsertTime,
                    &ord.InstrumentID,
                    ord.VolumeTotalOriginal,
                    ord.LimitPrice,
                    ord.Direction,
                    ord.LastStatus,
                    &ord.OrderSysID,
                    &ord.OrderLocalID,
                    ord.OrderPriceType,
                    ord.TimeCondition,
                    ord.VolumeTotal,
                    &ord.UserID,
                    &ord.CombOffsetFlag,
                    &ord.CombHedgeFlag,
                    ord.VolumeCondition,
                    ord.ContingentCondition,
                    ord.TIOwnerType,
                    ord.MinVolume,
                    ord.SettlementID,
                    &ord.Key,
                ],
            );

            if ret.is_err() {
                // 这里错误一般是由于主键冲突引起的, 尝试更新. 由于slqite的 ON CONFLICT 不支持条件更新, 所以这里分两步
                let ret = trans.execute(
                    "UPDATE trd_rtn_order SET InsertTime=?1, LastStatus=?2, VolumeTotal=?3, OrderLocalID=?4, UserID=?5 WHERE PKey=?6 and LastStatus<=?7 ;",
                    params![&ord.InsertTime, ord.LastStatus, ord.VolumeTotal, &ord.OrderLocalID, &ord.UserID, &ord.Key, ord.LastStatus],
                );
                if let Err(err) = ret {
                    log::debug!("write_trd_order: update failed. {}", err.to_string());
                }
            }
        });

        if let Err(err) = trans.commit() {
            log::debug!("write_trd_order: transaction commit failed. {}", err.to_string());
        }
    }

    /// 查询委托
    #[cfg(feature = "trade")]
    pub fn qry_trd_order(&self, req: &CtpReqQryOrderField, page_index: i32, page_size: i32) -> (i32, Vec<CtpOrderField>) {
        log::debug!(
            "qry_trd_order: exch:{}, insid:{}, ordstatus:{}, ordsysid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.OrdStatus,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime0_Int,
            req.TransTime1,
            req.TransTime1_Int,
            page_index,
            page_size
        );

        let page_offset = page_index * page_size;

        let mut sql = String::new();
        let mut params: Vec<&dyn rusqlite::ToSql> = Vec::new();

        // 构建sql语句与参数
        {
            sql.push_str(
                "WITH cte AS (
                            SELECT *, ROW_NUMBER() OVER (ORDER BY InsertTime DESC)
                            FROM trd_rtn_order
                            WHERE 1 = 1",
            );

            if !req.ExchID.is_empty() {
                sql.push_str(" AND ExchangeID = ?");
                params.push(&req.ExchID);
            }

            if !req.AccountID.is_empty() {
                sql.push_str(" AND InvestorID LIKE ?");
                params.push(&req.AccountID);
            }

            if !req.InstrumentID.is_empty() {
                sql.push_str(" AND InstrumentID LIKE ?");
                params.push(&req.InstrumentID);
            }

            if 0 != req.OrdStatus {
                sql.push_str(" AND LastStatus = ?");
                params.push(&req.OrdStatus);
            }

            if !req.OrderSysID.is_empty() {
                sql.push_str(" AND OrderSysID LIKE ?");
                params.push(&req.OrderSysID);
            }

            if 0 != req.TransTime0_Int {
                sql.push_str(" AND InsertTime > ?");
                params.push(&req.TransTime0);
            }

            if 235959 != req.TransTime1_Int {
                sql.push_str(" AND InsertTime < ?");
                params.push(&req.TransTime1);
            }

            sql.push_str(
                "
                        ),
                        cte2 AS (
                            SELECT COUNT(*) AS total_count
                            FROM cte
                        )
                        SELECT total_count, cte.*
                        FROM cte, cte2 LIMIT ? OFFSET ?",
            );
            params.push(&page_size);
            params.push(&page_offset);
        }

        let mut total_count = 0;
        let mut ret_vec = Vec::new();

        let conn: &Connection = self.conn.as_ref().unwrap();
        let stmt = conn.prepare(&sql);
        if stmt.is_err() {
            log::debug!("qry_trd_order: create stmt failed. {}", stmt.err().unwrap().to_string());
            return (total_count, ret_vec);
        }
        let mut stmt = stmt.unwrap();

        let ret_iter = stmt.query_map(rusqlite::params_from_iter(params.iter()), |row| {
            if 0 == total_count {
                total_count = row.get(0)?;
            }

            Ok(CtpOrderField {
                InvestorID: row.get(1)?,
                ExchangeID: row.get(2)?,
                ClientID: row.get(3)?,
                InsertTime: row.get(4)?,
                InstrumentID: row.get(5)?,
                VolumeTotalOriginal: row.get(6)?,
                LimitPrice: row.get(7)?,
                Direction: row.get(8)?,
                LastStatus: row.get(9)?,
                OrderSysID: row.get(10)?,
                OrderLocalID: row.get(11)?,
                OrderPriceType: row.get(12)?,
                TimeCondition: row.get(13)?,
                VolumeTotal: row.get(14)?,
                UserID: row.get(15)?,
                CombOffsetFlag: row.get(16)?,
                CombHedgeFlag: row.get(17)?,
                VolumeCondition: row.get(18)?,
                ContingentCondition: row.get(19)?,
                TIOwnerType: row.get(20)?,
                MinVolume: row.get(21)?,
                SettlementID: row.get(22)?,
                ..Default::default()
            })
        });
        if let Ok(it) = ret_iter {
            ret_vec = it.into_iter().map(|v| v.unwrap()).collect();
        } else {
            log::debug!("qry_trd_order: stmt query failed. {}", ret_iter.err().unwrap().to_string());
        }

        (total_count, ret_vec)
    }

    /// 写成交数据
    #[cfg(feature = "trade")]
    pub fn write_trd_trade(&mut self, trade_vec: Vec<CtpTradeField>) {
        if trade_vec.is_empty() || self.conn.is_none() {
            return;
        }

        let conn = self.conn.as_mut().unwrap();
        let trans = conn.transaction();
        if let Err(err) = trans {
            log::debug!("write_trd_trade: create transaction failed. {}", err.to_string());
            return;
        }
        let trans = trans.unwrap();

        trade_vec.iter().for_each(|trd| {
            let ret = trans.execute(
                r###"INSERT INTO trd_rtn_trade (
                    InvestorID, ExchangeID, ClientID, TradeTime, InstrumentID, Direction, OffsetFlag, Volume, Price,
                    TradeID, OrderSysID, OrderLocalID, HedgeFlag, SequenceNo
                    ) VALUES (
                    ?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14);"###,
                params![
                    &trd.InvestorID,
                    &trd.ExchangeID,
                    &trd.ClientID,
                    &trd.TradeTime,
                    &trd.InstrumentID,
                    trd.Direction,
                    trd.OffsetFlag,
                    trd.Volume,
                    trd.Price,
                    &trd.TradeID,
                    &trd.OrderSysID,
                    &trd.OrderLocalID,
                    trd.HedgeFlag,
                    trd.SequenceNo
                ],
            );
            if let Err(err) = ret {
                // 在重传时,序号相同可能会导致主键重复而插入失败
                log::debug!("write_trd_trade: insert failed. {}", err.to_string());
            }
        });

        if let Err(err) = trans.commit() {
            log::debug!("write_trd_trade: transaction commit failed. {}", err.to_string());
        }
    }

    /// 查询成交
    #[cfg(feature = "trade")]
    pub fn qry_trd_trade(&self, req: &CtpReqQryTradeField, page_index: i32, page_size: i32) -> (i32, Vec<CtpTradeField>) {
        log::debug!(
            "qry_trd_trade: exch:{}, insid:{}, tradeid:{}, ordsysid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.TradeID,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime0_Int,
            req.TransTime1,
            req.TransTime1_Int,
            page_index,
            page_size
        );

        let page_offset = page_index * page_size;

        let mut sql = String::new();
        let mut params: Vec<&dyn rusqlite::ToSql> = Vec::new();

        // 构建sql语句与参数
        {
            sql.push_str(
                "WITH cte AS (
                            SELECT *, ROW_NUMBER() OVER (ORDER BY TradeTime DESC)
                            FROM trd_rtn_trade
                            WHERE 1 = 1",
            );

            if !req.ExchID.is_empty() {
                sql.push_str(" AND ExchangeID = ?");
                params.push(&req.ExchID);
            }

            if !req.AccountID.is_empty() {
                sql.push_str(" AND InvestorID LIKE ?");
                params.push(&req.AccountID);
            }

            if !req.InstrumentID.is_empty() {
                sql.push_str(" AND InstrumentID LIKE ?");
                params.push(&req.InstrumentID);
            }

            if !req.TradeID.is_empty() {
                sql.push_str(" AND TradeID LIKE ?");
                params.push(&req.TradeID);
            }

            if !req.OrderSysID.is_empty() {
                sql.push_str(" AND OrderSysID LIKE ?");
                params.push(&req.OrderSysID);
            }

            if 0 != req.TransTime0_Int {
                sql.push_str(" AND TradeTime > ?");
                params.push(&req.TransTime0);
            }

            if 235959 != req.TransTime1_Int {
                sql.push_str(" AND TradeTime < ?");
                params.push(&req.TransTime1);
            }

            sql.push_str(
                "
                        ),
                        cte2 AS (
                            SELECT COUNT(*) AS total_count
                            FROM cte
                        )
                        SELECT total_count, cte.*
                        FROM cte, cte2 LIMIT ? OFFSET ?",
            );
            params.push(&page_size);
            params.push(&page_offset);
        }

        let mut total_count = 0;
        let mut ret_vec = Vec::new();

        let conn: &Connection = self.conn.as_ref().unwrap();
        let stmt = conn.prepare(&sql);
        if stmt.is_err() {
            log::debug!("qry_trd_trade: create stmt failed. {}", stmt.err().unwrap().to_string());
            return (total_count, ret_vec);
        }
        let mut stmt = stmt.unwrap();

        let ret_iter = stmt.query_map(rusqlite::params_from_iter(params.iter()), |row| {
            if 0 == total_count {
                total_count = row.get(0)?;
            }

            Ok(CtpTradeField {
                InvestorID: row.get(1)?,
                ExchangeID: row.get(2)?,
                ClientID: row.get(3)?,
                TradeTime: row.get(4)?,
                InstrumentID: row.get(5)?,
                Direction: row.get(6)?,
                OffsetFlag: row.get(7)?,
                Volume: row.get(8)?,
                Price: row.get(9)?,
                TradeID: row.get(10)?,
                OrderSysID: row.get(11)?,
                OrderLocalID: row.get(12)?,
                HedgeFlag: row.get(13)?,
                ..Default::default()
            })
        });
        if let Ok(it) = ret_iter {
            ret_vec = it.into_iter().map(|v| v.unwrap()).collect();
        } else {
            log::debug!("qry_trd_trade: stmt query failed. {}", ret_iter.err().unwrap().to_string());
        }

        (total_count, ret_vec)
    }

    /// 写报价数据
    #[cfg(feature = "trade")]
    pub fn write_trd_quote(&mut self, quote_map: dashmap::DashMap<String, CtpQuoteField>) {
        if quote_map.is_empty() || self.conn.is_none() {
            return;
        }

        let conn = self.conn.as_mut().unwrap();
        let trans = conn.transaction();
        if let Err(err) = trans {
            log::debug!("write_trd_quote: create transaction failed. {}", err.to_string());
            return;
        }
        let trans = trans.unwrap();

        quote_map.iter().for_each(|quote| {
            let ret = trans.execute(
                r###"INSERT INTO trd_rtn_quote (
                    InvestorID, ExchangeID, ClientID, InsertTime, InstrumentID, LastStatus, QuoteSysID, QuoteLocalID,
                    BidLastStatus, BidOrderSysID, BidOffsetFlag, BidPrice, BidVolume,
                    AskLastStatus, AskOrderSysID, AskOffsetFlag, AskPrice, AskVolume,
                    UserID, PKey
                    ) VALUES (
                    ?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18, ?19, ?20
                    );"###,
                params![
                    &quote.InvestorID,
                    &quote.ExchangeID,
                    &quote.ClientID,
                    &quote.InsertTime,
                    &quote.InstrumentID,
                    &quote.LastStatus,
                    &quote.QuoteSysID,
                    &quote.QuoteLocalID,
                    &quote.BidLastStatus,
                    &quote.BidOrderSysID,
                    &quote.BidOffsetFlag,
                    &quote.BidPrice,
                    &quote.BidVolume,
                    &quote.AskLastStatus,
                    &quote.AskOrderSysID,
                    &quote.AskOffsetFlag,
                    &quote.AskPrice,
                    &quote.AskVolume,
                    &quote.UserID,
                    &quote.Key,
                ],
            );

            if ret.is_err() {
                // 这里错误一般是由于主键冲突引起的, 尝试更新. 由于slqite的 ON CONFLICT 不支持条件更新, 所以这里分两步
                let ret = trans.execute(
                    "UPDATE trd_rtn_quote SET InsertTime=?1, LastStatus=?2, BidLastStatus=?3, AskLastStatus=?4 WHERE PKey=?5 and LastStatus<=?6 ;",
                    params![&quote.InsertTime, quote.LastStatus, quote.BidLastStatus, quote.AskLastStatus, &quote.Key, quote.LastStatus],
                );
                if let Err(err) = ret {
                    log::debug!("write_trd_quote: update failed. {}", err.to_string());
                }
            }
        });

        if let Err(err) = trans.commit() {
            log::debug!("write_trd_quote: transaction commit failed. {}", err.to_string());
        }
    }

    /// 查询报价
    #[cfg(feature = "trade")]
    pub fn qry_trd_quote(&self, req: &CtpReqQryQuoteField, page_index: i32, page_size: i32) -> (i32, Vec<CtpQuoteField>) {
        log::debug!(
            "qry_trd_quote: exch:{}, insid:{}, ordstatus:{}, quotesysid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
            req.ExchID,
            req.InstrumentID,
            req.OrdStatus,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime0_Int,
            req.TransTime1,
            req.TransTime1_Int,
            page_index,
            page_size
        );

        let page_offset = page_index * page_size;

        let mut sql = String::new();
        let mut params: Vec<&dyn rusqlite::ToSql> = Vec::new();

        // 构建sql语句与参数
        {
            sql.push_str(
                "WITH cte AS (
                        SELECT *, ROW_NUMBER() OVER (ORDER BY InsertTime DESC)
                        FROM trd_rtn_quote
                        WHERE 1 = 1",
            );

            if !req.ExchID.is_empty() {
                sql.push_str(" AND ExchangeID = ?");
                params.push(&req.ExchID);
            }

            if !req.AccountID.is_empty() {
                sql.push_str(" AND InvestorID LIKE ?");
                params.push(&req.AccountID);
            }

            if !req.InstrumentID.is_empty() {
                sql.push_str(" AND InstrumentID LIKE ?");
                params.push(&req.InstrumentID);
            }

            if 0 != req.OrdStatus {
                sql.push_str(" AND LastStatus = ?");
                params.push(&req.OrdStatus);
            }

            if !req.OrderSysID.is_empty() {
                sql.push_str(" AND QuoteSysID LIKE ?");
                params.push(&req.OrderSysID);
            }

            if 0 != req.TransTime0_Int {
                sql.push_str(" AND InsertTime > ?");
                params.push(&req.TransTime0);
            }

            if 235959 != req.TransTime1_Int {
                sql.push_str(" AND InsertTime < ?");
                params.push(&req.TransTime1);
            }

            sql.push_str(
                "
                    ),
                    cte2 AS (
                        SELECT COUNT(*) AS total_count
                        FROM cte
                    )
                    SELECT total_count, cte.*
                    FROM cte, cte2 LIMIT ? OFFSET ?",
            );
            params.push(&page_size);
            params.push(&page_offset);
        }

        let mut total_count = 0;
        let mut ret_vec = Vec::new();

        let conn: &Connection = self.conn.as_ref().unwrap();
        let stmt = conn.prepare(&sql);
        if stmt.is_err() {
            log::debug!("qry_trd_quote: create stmt failed. {}", stmt.err().unwrap().to_string());
            return (total_count, ret_vec);
        }
        let mut stmt = stmt.unwrap();

        let ret_iter = stmt.query_map(rusqlite::params_from_iter(params.iter()), |row| {
            if 0 == total_count {
                total_count = row.get(0)?;
            }

            Ok(CtpQuoteField {
                InvestorID: row.get(1)?,
                ExchangeID: row.get(2)?,
                ClientID: row.get(3)?,
                InsertTime: row.get(4)?,
                InstrumentID: row.get(5)?,
                LastStatus: row.get(6)?,
                QuoteSysID: row.get(7)?,
                QuoteLocalID: row.get(8)?,
                BidLastStatus: row.get(9)?,
                BidOrderSysID: row.get(10)?,
                BidOffsetFlag: row.get(11)?,
                BidPrice: row.get(12)?,
                BidVolume: row.get(13)?,
                AskLastStatus: row.get(14)?,
                AskOrderSysID: row.get(15)?,
                AskOffsetFlag: row.get(16)?,
                AskPrice: row.get(17)?,
                AskVolume: row.get(18)?,
                UserID: row.get(19)?,
                ..Default::default()
            })
        });
        if let Ok(it) = ret_iter {
            ret_vec = it.into_iter().map(|v| v.unwrap()).collect();
        } else {
            log::debug!("qry_trd_quote: stmt query failed. {}", ret_iter.err().unwrap().to_string());
        }

        (total_count, ret_vec)
    }
}

impl DbSqlite {
    /// 创建风控端需要的表
    #[cfg(feature = "risk")]
    fn create_rsk_table(&self) {
        if self.conn.is_none() {
            return;
        }

        self.create_rsk_order_stk();
        self.create_rsk_trade_stk();
    }

    /// 创建现货委托表
    #[cfg(feature = "risk")]
    fn create_rsk_order_stk(&self) {
        let sql = r###"CREATE TABLE "rsk_rtn_order_stk" (
            "AID" INTEGER DEFAULT 0,
            "MsgSeqNum" INTEGER DEFAULT 0,
            "BizSeqNum" INTEGER DEFAULT 0,
            "ExchID" INTEGER DEFAULT 0,
            "AccountID" TEXT DEFAULT NULL,
            "ClientID" TEXT DEFAULT NULL,
            "InstrumentID" TEXT DEFAULT NULL,
            "UserID" TEXT DEFAULT NULL,
            "OrdStatus" INTEGER DEFAULT 0,
            "LocalOrderNo" INTEGER DEFAULT 0,
            "OrderSysID" TEXT DEFAULT NULL,
            "Volume" INTEGER DEFAULT 0,
            "Price" REAL DEFAULT 0,
            "Side" INTEGER DEFAULT 0,
            "PriceType" INTEGER DEFAULT 0,
            "TimeInForce" INTEGER DEFAULT 0,
            "OwnerType" INTEGER DEFAULT 0,
            "LeavesVolume" INTEGER DEFAULT 0,
            "CancelVolume" INTEGER DEFAULT 0,
            "TransTime" INTEGER DEFAULT 0,
            "PKey" TEXT NOT NULL,
            PRIMARY KEY ("PKey")
            ); "###;

        let idx1 = r###"CREATE INDEX "idx_rsk_rtn_order_stk_qry"
            ON "rsk_rtn_order_stk" (
            "ExchID",
            "AccountID",
            "InstrumentID",
            "OrdStatus",
            "OrderSysID"
            );"###;

        let idx2 = r###"CREATE INDEX "idx_rsk_rtn_trade_stk_qry_time"
            ON "rsk_rtn_order_stk" (
            "TransTime" DESC
            );"###;

        let conn: &Connection = self.conn.as_ref().unwrap();

        let _ = conn.execute("DROP TABLE IF EXISTS rsk_rtn_order_stk;", ());
        let _ = conn.execute(sql, ());
        let _ = conn.execute(idx1, ());
        let _ = conn.execute(idx2, ());
    }

    /// 创建现货成交表
    #[cfg(feature = "risk")]
    fn create_rsk_trade_stk(&self) {
        let sql = r###"CREATE TABLE "rsk_rtn_trade_stk" (
            "AID" INTEGER DEFAULT 0,
            "MsgSeqNum" INTEGER DEFAULT 0,
            "BizSeqNum" INTEGER DEFAULT 0,
            "ExchID" INTEGER DEFAULT 0,
            "AccountID" TEXT DEFAULT NULL,
            "ClientID" TEXT DEFAULT NULL,
            "InstrumentID" TEXT DEFAULT NULL,
            "TradeID" TEXT DEFAULT NULL,
            "LocalOrderNo" INTEGER DEFAULT 0,
            "OrderSysID" TEXT DEFAULT NULL,
            "Side" INTEGER DEFAULT 0,
            "TradePrice" REAL DEFAULT 0,
            "TradeVolume" INTEGER DEFAULT 0,
            "TransTime" INTEGER DEFAULT 0,
            PRIMARY KEY ("AccountID", "BizSeqNum")
            ); "###;

        let idx1 = r###"CREATE INDEX "idx_rsk_rtn_trade_stk_qry"
            ON "rsk_rtn_trade_stk" (
            "ExchID",
            "AccountID",
            "TradeID",
            "OrderSysID",
            "InstrumentID",
            );"###;

        let idx2 = r###"CREATE INDEX "idx_rsk_rtn_trade_stk_qry_time"
            ON "rsk_rtn_trade_stk" (
            "TransTime" DESC
            );"###;

        let conn: &Connection = self.conn.as_ref().unwrap();

        let _ = conn.execute("DROP TABLE IF EXISTS rsk_rtn_trade_stk;", ());
        let _ = conn.execute(sql, ());
        let _ = conn.execute(idx1, ());
        let _ = conn.execute(idx2, ());
    }

    /// 写现货委托数据
    #[cfg(feature = "risk")]
    pub fn write_rsk_order_stk(&mut self, order_map: dashmap::DashMap<String, OrderStkField>) {
        if order_map.is_empty() || self.conn.is_none() {
            return;
        }

        let conn = self.conn.as_mut().unwrap();
        let trans = conn.transaction();
        if let Err(err) = trans {
            log::debug!("write_rsk_order_stk: create transaction failed. {}", err.to_string());
            return;
        }
        let trans = trans.unwrap();

        order_map.iter().for_each(|ord| {
            let ret = trans.execute(
                r###"INSERT INTO rsk_rtn_order_stk (
                    AID, MsgSeqNum, BizSeqNum, ExchID, AccountID, ClientID, InstrumentID, UserID, OrdStatus,
                    LocalOrderNo, OrderSysID, Volume, Price, Side, PriceType, TimeInForce, OwnerType, LeavesVolume,
                    CancelVolume, TransTime, PKey
                    ) VALUES (
                    ?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18, ?19, ?20, ?21);"###,
                params![
                    &ord.AID,
                    &ord.MsgSeqNum,
                    &ord.BizSeqNum,
                    &ord.ExchID,
                    &ord.AccountID,
                    &ord.ClientID,
                    &ord.InstrumentID,
                    &ord.UserID,
                    &ord.OrdStatus,
                    &ord.LocalOrderNo,
                    &ord.OrderSysID,
                    &ord.Volume,
                    &ord.Price,
                    &ord.Side,
                    &ord.PriceType,
                    &ord.TimeInForce,
                    &ord.OwnerType,
                    &ord.LeavesVolume,
                    &ord.CancelVolume,
                    &ord.TransTime,
                    &ord.Key
                ],
            );

            if let Err(err) = ret {
                // 这里错误一般是由于主键冲突引起的, 尝试更新. 由于slqite的 ON CONFLICT 不支持条件更新, 所以这里分两步
                let ret = trans.execute(
                    "UPDATE rsk_rtn_order_stk SET TransTime=?1, OrdStatus=?2, LeavesVolume=?3, CancelVolume=?4, UserID=?5 WHERE PKey=?6 and OrdStatus<=?7 ;",
                    params![&ord.TransTime, ord.OrdStatus, ord.LeavesVolume, ord.CancelVolume, &ord.UserID, &ord.Key, ord.OrdStatus],
                );
                if let Err(err) = ret {
                    log::debug!("write_trd_order: update failed. {}", err.to_string());
                }
            }
        });

        if let Err(err) = trans.commit() {
            log::debug!("write_rsk_order_stk: transaction commit failed. {}", err.to_string());
        }
    }

    /// 查询现货委托
    #[cfg(feature = "risk")]
    pub fn qry_rsk_order_stk(&self, req: &ReqQryOrderStkField, page_index: i32, page_size: i32) -> (i32, Vec<OrderStkField>) {
        log::debug!(
            "qry_rsk_order_stk: exch:{}, accid:{}, insid:{}, ordstatus:{}, ordsysid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.InstrumentID,
            req.OrdStatus,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime0_Str,
            req.TransTime1,
            req.TransTime1_Str,
            page_index,
            page_size
        );

        let page_offset = page_index * page_size;

        let mut sql = String::new();
        let mut params: Vec<&dyn rusqlite::ToSql> = Vec::new();

        // 构建sql语句与参数
        {
            sql.push_str(
                "WITH cte AS (
                            SELECT *, ROW_NUMBER() OVER (ORDER BY TransTime DESC)
                            FROM rsk_rtn_order_stk
                            WHERE 1 = 1",
            );

            if 0 != req.ExchID {
                sql.push_str(" AND ExchID = ?");
                params.push(&req.ExchID);
            }

            if !req.AccountID.is_empty() {
                sql.push_str(" AND AccountID = ?");
                params.push(&req.AccountID);
            }

            if !req.InstrumentID.is_empty() {
                sql.push_str(" AND InstrumentID LIKE ?");
                params.push(&req.InstrumentID);
            }

            if 0 != req.OrdStatus {
                sql.push_str(" AND OrdStatus = ?");
                params.push(&req.OrdStatus);
            }

            if !req.OrderSysID.is_empty() {
                sql.push_str(" AND OrderSysID LIKE ?");
                params.push(&req.OrderSysID);
            }

            if 0 != req.TransTime0 {
                sql.push_str(" AND TransTime > ?");
                params.push(&req.TransTime0);
            }

            if 235959 != req.TransTime1 {
                sql.push_str(" AND TransTime < ?");
                params.push(&req.TransTime1);
            }

            sql.push_str(
                "
                        ),
                        cte2 AS (
                            SELECT COUNT(*) AS total_count
                            FROM cte
                        )
                        SELECT total_count, cte.*
                        FROM cte, cte2 LIMIT ? OFFSET ?",
            );
            params.push(&page_size);
            params.push(&page_offset);
        }

        let mut total_count = 0;
        let mut ret_vec = Vec::new();

        let conn: &Connection = self.conn.as_ref().unwrap();
        let stmt = conn.prepare(&sql);
        if stmt.is_err() {
            log::debug!("qry_rsk_order_stk: create stmt failed. {}", stmt.err().unwrap().to_string());
            return (total_count, ret_vec);
        }
        let mut stmt = stmt.unwrap();

        let ret_iter = stmt.query_map(rusqlite::params_from_iter(params.iter()), |row| {
            if 0 == total_count {
                total_count = row.get(0)?;
            }

            Ok(OrderStkField {
                AID: row.get(1)?,
                MsgSeqNum: row.get(2)?,
                BizSeqNum: row.get(3)?,
                ExchID: row.get(4)?,
                AccountID: row.get(5)?,
                ClientID: row.get(6)?,
                InstrumentID: row.get(7)?,
                UserID: row.get(8)?,
                OrdStatus: row.get(9)?,
                LocalOrderNo: row.get(10)?,
                OrderSysID: row.get(11)?,
                Volume: row.get(12)?,
                Price: row.get(13)?,
                Side: row.get(14)?,
                PriceType: row.get(15)?,
                TimeInForce: row.get(16)?,
                OwnerType: row.get(17)?,
                LeavesVolume: row.get(18)?,
                CancelVolume: row.get(19)?,
                TransTime: row.get(20)?,
                ..Default::default()
            })
        });
        if let Ok(it) = ret_iter {
            ret_vec = it.into_iter().map(|v| v.unwrap()).collect();
        } else {
            log::debug!(
                "qry_rsk_order_stk: stmt query failed. {}",
                ret_iter.err().unwrap().to_string()
            );
        }

        (total_count, ret_vec)
    }

    /// 写现货成交数据
    #[cfg(feature = "risk")]
    pub fn write_rsk_trade_stk(&mut self, trade_vec: Vec<TradeStkField>) {
        if trade_vec.is_empty() || self.conn.is_none() {
            return;
        }

        let conn = self.conn.as_mut().unwrap();
        let trans = conn.transaction();
        if let Err(err) = trans {
            log::debug!("write_rsk_trade_stk: create transaction failed. {}", err.to_string());
            return;
        }
        let trans = trans.unwrap();

        trade_vec.iter().for_each(|trd| {
            let ret = trans.execute(
                r###"INSERT INTO rsk_rtn_trade_stk (
                    AID, MsgSeqNum, BizSeqNum, ExchID, AccountID, ClientID, InstrumentID, TradeID, LocalOrderNo,
                    OrderSysID, Side, TradePrice, TradeVolume, TransTime
                    ) VALUES (
                    ?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14);"###,
                params![
                    &trd.AID,
                    &trd.MsgSeqNum,
                    &trd.BizSeqNum,
                    &trd.ExchID,
                    &trd.AccountID,
                    &trd.ClientID,
                    &trd.InstrumentID,
                    &trd.TradeID,
                    &trd.LocalOrderNo,
                    &trd.OrderSysID,
                    &trd.Side,
                    &trd.TradePrice,
                    &trd.TradeVolume,
                    &trd.TransTime,
                ],
            );
            if let Err(err) = ret {
                // 在重传时,序号相同可能会导致主键重复而插入失败
                log::debug!("write_rsk_trade_stk: insert failed. {}", err.to_string());
            }
        });

        if let Err(err) = trans.commit() {
            log::debug!("write_rsk_trade_stk: transaction commit failed. {}", err.to_string());
        }
    }

    /// 查询现货成交
    #[cfg(feature = "risk")]
    pub fn qry_rsk_trade_stk(&self, req: &ReqQryTradeStkField, page_index: i32, page_size: i32) -> (i32, Vec<TradeStkField>) {
        log::debug!(
            "qry_rsk_trade_stk: exch:{}, accid:{}, insid:{}, tradeid:{}, ordsysid:{}, start:{}({}), end:{}({}), pageindex:{}, pagesize:{}",
            req.ExchID,
            req.AccountID,
            req.InstrumentID,
            req.TradeID,
            req.OrderSysID,
            req.TransTime0,
            req.TransTime0_Str,
            req.TransTime1,
            req.TransTime1_Str,
            page_index,
            page_size
        );

        let page_offset = page_index * page_size;

        let mut sql = String::new();
        let mut params: Vec<&dyn rusqlite::ToSql> = Vec::new();

        // 构建sql语句与参数
        {
            sql.push_str(
                "WITH cte AS (
                            SELECT *, ROW_NUMBER() OVER (ORDER BY TransTime DESC)
                            FROM rsk_rtn_trade_stk
                            WHERE 1 = 1",
            );

            if 0 != req.ExchID {
                sql.push_str(" AND ExchID = ?");
                params.push(&req.ExchID);
            }

            if !req.AccountID.is_empty() {
                sql.push_str(" AND AccountID = ?");
                params.push(&req.AccountID);
            }

            if !req.InstrumentID.is_empty() {
                sql.push_str(" AND InstrumentID LIKE ?");
                params.push(&req.InstrumentID);
            }

            if !req.TradeID.is_empty() {
                sql.push_str(" AND TradeID LIKE ?");
                params.push(&req.TradeID);
            }

            if !req.OrderSysID.is_empty() {
                sql.push_str(" AND OrderSysID LIKE ?");
                params.push(&req.OrderSysID);
            }

            if 0 != req.TransTime0 {
                sql.push_str(" AND TransTime > ?");
                params.push(&req.TransTime0);
            }

            if 235959 != req.TransTime1 {
                sql.push_str(" AND TransTime < ?");
                params.push(&req.TransTime1);
            }

            sql.push_str(
                "
                        ),
                        cte2 AS (
                            SELECT COUNT(*) AS total_count
                            FROM cte
                        )
                        SELECT total_count, cte.*
                        FROM cte, cte2 LIMIT ? OFFSET ?",
            );
            params.push(&page_size);
            params.push(&page_offset);
        }

        let mut total_count = 0;
        let mut ret_vec = Vec::new();

        let conn: &Connection = self.conn.as_ref().unwrap();
        let stmt = conn.prepare(&sql);
        if stmt.is_err() {
            log::debug!("qry_rsk_trade_stk: create stmt failed. {}", stmt.err().unwrap().to_string());
            return (total_count, ret_vec);
        }
        let mut stmt = stmt.unwrap();

        let ret_iter = stmt.query_map(rusqlite::params_from_iter(params.iter()), |row| {
            if 0 == total_count {
                total_count = row.get(0)?;
            }

            Ok(TradeStkField {
                AID: row.get(1)?,
                MsgSeqNum: row.get(2)?,
                BizSeqNum: row.get(3)?,
                ExchID: row.get(4)?,
                AccountID: row.get(5)?,
                ClientID: row.get(6)?,
                InstrumentID: row.get(7)?,
                TradeID: row.get(8)?,
                LocalOrderNo: row.get(9)?,
                OrderSysID: row.get(10)?,
                Side: row.get(11)?,
                TradePrice: row.get(12)?,
                TradeVolume: row.get(13)?,
                TransTime: row.get(14)?,
                ..Default::default()
            })
        });
        if let Ok(it) = ret_iter {
            ret_vec = it.into_iter().map(|v| v.unwrap()).collect();
        } else {
            log::debug!(
                "qry_rsk_trade_stk: stmt query failed. {}",
                ret_iter.err().unwrap().to_string()
            );
        }

        (total_count, ret_vec)
    }
}
