use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex, OnceLock,
    },
};

use crate::{
    common::{
        config::Config,
        global::{CFG, INS, MD_API, TOKIO_RT, TRADE_OPT_API},
        instrument::Instrument,
        ticonvert::TIConvert,
        titype::TIType,
    },
    slintui::*,
};

use slint::*;
use tiapi::protocol_pub::trade_req_ctp::{
    CtpReqInputOrderField, CtpRspInputOrderField,
    TradeType::{EContingentCondition, EDirection, EForceCloseReason, EOrderPriceType, ETimeCondition, EVolumeCondition},
};

use super::tradeopt::show_input_msg_box;

/// 报单录入数据
struct InputOrderDatas {
    /******************************************************************************************************************/
    /// 在报单过程中是否发生错误(用于更新界面时弹出错误提示)
    is_err_inputorder: AtomicBool,

    /// 报单响应数据
    rsp_inputorder: Arc<Mutex<CtpRspInputOrderField>>,

    /// 输入的合约是否有改变
    input_instid_changed: AtomicBool,
}
impl InputOrderDatas {
    pub fn new() -> Self {
        Self {
            is_err_inputorder: AtomicBool::new(false),
            rsp_inputorder: Arc::new(Mutex::new(CtpRspInputOrderField::default())),
            input_instid_changed: AtomicBool::new(false),
        }
    }
}
static INPUTORDER_DATAS: OnceLock<InputOrderDatas> = OnceLock::new();

/// 报单
pub(super) struct InputOrder {}

/// 构建与初始化
impl InputOrder {
    /// new
    pub fn new() -> Self {
        Self {}
    }

    /// 初始化
    pub fn init(&self, app: &crate::slintui::App) {
        // 初始化报单录入数据
        let _ = INPUTORDER_DATAS.set(InputOrderDatas::new());

        let app_inputord = app.global::<OptTrd_InputOrder>();

        // 添加合约
        let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
        let all_insid = { INS.get().unwrap().read().unwrap().get_opt_id_all_with_name() };
        all_insid.iter().for_each(|it| {
            items.push(crate::slintui::LineItem {
                text: slint::format!("{}", it.key()),
                remark: slint::format!("{}", it.value().1),
                ..Default::default()
            });
        });
        let sort_model = Rc::new(items.sort_by(move |r_a, r_b| r_a.text.cmp(&r_b.text)));
        app_inputord.set_insid_model(sort_model.clone().into());
        app_inputord.set_all_insid_model(sort_model.into());

        // 注册市场改变事件
        let app_weak = app.as_weak();
        app_inputord.on_exchid_changed(move || InputOrder::on_exchid_changed(&app_weak));

        // 注册合约改变事件
        let app_weak = app.as_weak();
        app_inputord.on_insid_text_changed(move || InputOrder::on_insid_text_changed(&app_weak));

        // 注册价格改变事件
        let app_weak = app.as_weak();
        app_inputord.on_price_text_changed(move || InputOrder::on_price_text_changed(&app_weak));

        // 注册数量改变事件
        let app_weak = app.as_weak();
        app_inputord.on_volume_text_changed(move || InputOrder::on_volume_text_changed(&app_weak));

        // 注册用户选择了持仓事件
        let app_weak = app.as_weak();
        app_inputord.on_sel_position(move |accid, exchid, insid, dir, vol, covered| {
            InputOrder::on_sel_position(&app_weak, accid, exchid, insid, dir, vol, covered)
        });

        // 注册买卖方向改变的事件
        let app_weak = app.as_weak();
        app_inputord.on_dir_changed(move |is_buy| InputOrder::on_dir_changed(&app_weak, is_buy));

        // 注册报单价格上下调整时的事件
        let app_weak = app.as_weak();
        app_inputord.on_price_updown_changed(move |upflag| InputOrder::on_price_updown_changed(&app_weak, upflag));

        // 注册报单数量上下调整时的事件
        let app_weak = app.as_weak();
        app_inputord.on_volume_updown_changed(move |upflag| InputOrder::on_volume_updown_changed(&app_weak, upflag));

        // 注册请求提示按钮点击事件
        let app_weak = app.as_weak();
        app_inputord.on_req_tip_toggled(move |checked| InputOrder::on_req_tip_toggled(&app_weak, checked));

        // 注册确定按钮点击事件
        let app_weak = app.as_weak();
        app_inputord.on_ok_clicked(move |need_confirm| InputOrder::on_ok_clicked(&app_weak, need_confirm));

        log::trace!("Input order init completed");
    }

    /// 报单响应
    pub fn on_rsp_inputorder(&self, rsp: CtpRspInputOrderField) {
        let inputord_datas = INPUTORDER_DATAS.get().unwrap();
        *inputord_datas.rsp_inputorder.lock().unwrap() = rsp;
        inputord_datas.is_err_inputorder.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl InputOrder {
    /// 点击行情详情的价格
    pub fn on_price_clicked(app_weak: &Weak<App>, exchid: SharedString, insid: SharedString, price: SharedString, ptype: i32) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        if exchid != app_inputord.get_exchid() {
            app_inputord.set_exchid(exchid);
            InputOrder::on_exchid_changed(app_weak);
        }

        if insid != app_inputord.get_insid() {
            app_inputord.set_insid(insid);
            InputOrder::on_insid_text_changed(app_weak);
        }

        if "--" != price.as_str() && price != app_inputord.get_price() {
            app_inputord.set_price(price);
            InputOrder::on_price_text_changed(app_weak);
        }
    }

    /// 市场有变动
    fn on_exchid_changed(app_weak: &Weak<App>) {}

    /// 合约编码有变动
    fn on_insid_text_changed(app_weak: &Weak<App>) -> bool {
        // 更新可选择合约列表
        let inputord_datas = INPUTORDER_DATAS.get().unwrap();
        inputord_datas.input_instid_changed.store(true, Ordering::Relaxed);

        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        let insid = app_inputord.get_insid();
        let ins = { INS.get().unwrap().read().unwrap().get_ins_by_insid(&insid) };

        // 合约名称
        let mut insname: String = match insid.is_empty() {
            true => "".to_owned(),
            false => "无效合约".to_owned(),
        };

        // 报价模式
        let mut price_type_model = 0;

        if let Some(ins) = ins {
            app_inputord.set_insid_has_error(false);

            insname = if !ins.symbol.is_empty() {
                ins.symbol.clone()
            } else {
                ins.name.clone()
            };

            // 报价模式
            match ins.exchid {
                TIType::EXCH_SSE => price_type_model = 1,
                TIType::EXCH_SZSE => price_type_model = 2,
                _ => {}
            };

            // 设置交易所编码
            let eid = app_inputord.get_exchid();
            if eid.as_str() != ins.exchname.as_str() {
                app_inputord.set_exchid(ins.exchname.clone().into());
                app_inputord.invoke_exchid_changed();
            }

            // 设置价格调整的参数
            app_inputord.set_price_dig(ins.ptw);
            app_inputord.set_price_step(ins.pricetick as f32);

            // 订阅行情
            let app_mddtl = app.global::<MarketDataDtl>();
            let cur_key = std::format!("{}{}", ins.exchname, ins.insid);
            let pre_key = app_mddtl.get_pre_key().to_string();
            if cur_key != pre_key {
                let insname = if !ins.symbol.is_empty() {
                    ins.symbol.clone()
                } else {
                    ins.name.clone()
                };

                app_mddtl.invoke_reset_by_sel_ins(
                    ins.exchname.clone().into(),
                    ins.insid.clone().into(),
                    insname.into(),
                    TIConvert::format_f(ins.uplmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.lowlmtprice, ins.ptw as usize).into(),
                    TIConvert::format_f(ins.prestlprice, ins.ptw as usize).into(),
                );

                let sub_insid = ins.insid.clone();
                TOKIO_RT.get().unwrap().spawn(async move {
                    let _ = MD_API.get().unwrap().read().await.req_subscribe(&sub_insid).await;
                });
            }
        } else {
            app_inputord.set_exchid("".into());
            app_inputord.set_insid_has_error(true);
        }

        if price_type_model != app_inputord.get_price_type_model() {
            app_inputord.set_price_type_model(price_type_model);
            app_inputord.set_price_type(0);
            app_inputord.set_price_type_str("普通限价".into());
        }

        if *insname != *app_inputord.get_insname() {
            app_inputord.set_insname(insname.into());
        }

        true
    }

    /// 报单价格有变动
    fn on_price_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        let price = app_inputord.get_price().replace(",", "");
        if price.is_empty() {
            app_inputord.set_price_has_error(true);
            return false;
        }

        if price.trim().parse::<f64>().unwrap_or_default() <= Config::INVALID_PRICE {
            app_inputord.set_price_has_error(true);
            return false;
        }

        app_inputord.set_price_has_error(false);

        true
    }

    /// 报单数量有变动
    fn on_volume_text_changed(app_weak: &Weak<App>) -> bool {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        let volume = app_inputord.get_volume().replace(",", "");
        if volume.is_empty() {
            app_inputord.set_volume_has_error(true);
            return false;
        }

        if volume.trim().parse::<i32>().unwrap_or_default() <= 0 {
            app_inputord.set_volume_has_error(true);
            return false;
        }

        app_inputord.set_volume_has_error(false);

        true
    }

    // 用户选择了持仓
    fn on_sel_position(
        app_weak: &Weak<App>,
        accid: SharedString,
        exchid: SharedString,
        insid: SharedString,
        dir: SharedString,
        vol: SharedString,
        covered: SharedString,
    ) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        app_inputord.set_accountid(accid); // 设置资金账号
        app_inputord.set_offset(1); // 平仓

        // 设置买卖方向
        if "多" == dir.as_str() {
            app_inputord.set_is_buy(false);
        } else {
            app_inputord.set_is_buy(true);
        }

        // 设置备兑标志
        if "非备兑" == covered.as_str() {
            app_inputord.set_covered(2);
        } else {
            app_inputord.set_covered(1);
        }

        // 设置市场
        if exchid != app_inputord.get_exchid() {
            app_inputord.set_exchid(exchid);
            app_inputord.invoke_exchid_changed();
        }

        // 设置合约
        if insid != app_inputord.get_insid() {
            // 重置价格
            app_inputord.set_price("".into());
            app_inputord.set_price_has_error(true);

            app_inputord.set_insid(insid);
            app_inputord.invoke_insid_text_changed();
        }

        // 设置数量
        app_inputord.set_volume(vol);
    }

    /// 买卖方向改变的事件
    fn on_dir_changed(app_weak: &Weak<App>, is_buy: bool) {
        if is_buy {
            let app = app_weak.unwrap();
            let app_inputord = app.global::<OptTrd_InputOrder>();
            app_inputord.set_volume("1".into()); // 买入时数量默认为最小报单量
        }
    }

    // 报单价格上下调整时的事件
    fn on_price_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        if app_inputord.get_price_has_error() {
            return;
        }

        let price_str = app_inputord.get_price().replace(",", "");
        let mut price = price_str.trim().parse::<f64>().unwrap_or_default();
        if price <= Config::INVALID_PRICE {
            return;
        }

        if upflag {
            price += app_inputord.get_price_step() as f64;
        } else {
            price -= app_inputord.get_price_step() as f64;
            if price <= Config::INVALID_PRICE {
                return;
            }
        }

        app_inputord.set_price(slint::format!("{:.*}", app_inputord.get_price_dig() as usize, price));
    }

    // 报单数量上下调整时的事件
    fn on_volume_updown_changed(app_weak: &Weak<App>, upflag: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        if app_inputord.get_volume_has_error() {
            app_inputord.set_volume("1".into());
            app_inputord.set_volume_has_error(false);
            return;
        }

        let volume_str = app_inputord.get_volume().replace(",", "");
        let mut volume = volume_str.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            return;
        }

        if upflag {
            volume += 1;
        } else {
            volume -= 1;
            if volume <= 0 {
                return;
            }
        }

        app_inputord.set_volume(slint::format!("{}", volume));
    }

    /// 请求提示按钮点击事件
    fn on_req_tip_toggled(app_weak: &Weak<App>, checked: bool) {
        let app = app_weak.unwrap();
        let app_set = app.global::<OptTrd_Setting>();
        app_set.set_trdreqtip_inputorder(checked);
        CFG.get().unwrap().write().unwrap().com.ReqTradeTip.InputOrder = checked;
    }

    /// 确定按钮点击事件
    fn on_ok_clicked(app_weak: &Weak<App>, need_confirm: bool) {
        let app = app_weak.unwrap();
        let app_inputord = app.global::<OptTrd_InputOrder>();

        // 获取参数
        let accid = app_inputord.get_accountid();
        let is_buy = app_inputord.get_is_buy();
        let offset = app_inputord.get_offset();
        let covered = app_inputord.get_covered();
        let insid = Instrument::parse_id(&app_inputord.get_insid()).0;
        let exchid = app_inputord.get_exchid();
        let price_type_str = app_inputord.get_price_type_str();
        let price = app_inputord.get_price().replace(",", "");
        let volume = app_inputord.get_volume().replace(",", "");
        let yumai: bool = app_inputord.get_is_yumai();

        // 输入参数检查
        {
            if accid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "账户不能为空".into());
                return;
            }

            if insid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约信息失败".into());
                return;
            }

            if exchid.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "获取合约所在交易所失败".into());
                return;
            }

            if price_type_str.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "报单价格类型不能为空".into());
                return;
            }

            if price.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "价格不能为空".into());
                return;
            }

            if volume.is_empty() {
                show_input_msg_box(&app, 1, Default::default(), "数量不能为空".into());
                return;
            }
        }

        let price = price.trim().parse::<f64>().unwrap_or_default();
        if price <= Config::INVALID_PRICE {
            show_input_msg_box(&app, 1, Default::default(), "请输入有效的价格".into());
            return;
        }

        let volume = volume.trim().parse::<i32>().unwrap_or_default();
        if volume <= 0 {
            show_input_msg_box(&app, 1, Default::default(), "请输入有效的数量".into());
            return;
        }

        let price_type = InputOrder::get_order_price_type(&exchid, &price_type_str);
        if -1 == price_type.0 {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!(
                    "解析报单价格条件失败\n\n交易所编码:{}, 报单价格条件:{}]",
                    exchid,
                    app_inputord.get_price_type()
                ),
            );
            return;
        }

        if need_confirm && { CFG.get().unwrap().read().unwrap().com.ReqTradeTip.InputOrder } {
            show_input_msg_box(
                &app,
                100,
                "报单录入确认".into(),
                slint::format!(
                    "资金账户:{}\n\n交易所:{}    合约:{}\n\n[{}][{}][{}] {} 张, 价格:{}\n\n报单价格条件:{}",
                    accid,
                    exchid,
                    insid,
                    if 1 == covered { "备兑" } else { "非备兑" },
                    if is_buy { "买入" } else { "卖出" },
                    if 0 == offset { "开仓" } else { "平仓" },
                    volume,
                    price,
                    price_type_str
                ),
            );

            return;
        }

        let req = CtpReqInputOrderField {
            CombOffsetFlag: offset.to_string(),
            CombHedgeFlag: "1".to_owned(),
            InvestUnitID: std::format!("{}", if 1 == covered { "C" } else { "" }),
            ContingentCondition: EContingentCondition::Immediately.into(),
            ForceCloseReason: EForceCloseReason::NotForceClose.into(),
            InvestorID: accid.to_string(),
            InstrumentID: insid.to_string(),
            ExchangeID: exchid.to_string(),
            OrderPriceType: price_type.0,
            TimeCondition: price_type.1,
            VolumeCondition: price_type.2,
            LimitPrice: price,
            VolumeTotalOriginal: volume,
            Direction: if is_buy { EDirection::Buy } else { EDirection::Sell }.into(),
            BusinessUnit: if yumai { "9".to_owned() } else { "".into() },
            ..Default::default()
        };
        if let Err(err) = TRADE_OPT_API.get().unwrap().read().unwrap().req_orderinput(0, &req) {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("请求报单录入失败\n\n错误信息: {}", err),
            );
        }
    }
}

impl InputOrder {
    /// 获取报单价格条件
    fn get_order_price_type(exchid: &str, pricetype: &str) -> (i32, i32, i32) {
        if "SSE" == exchid {
            match pricetype {
                "普通限价" => {
                    return (
                        EOrderPriceType::LimitPrice.into(),
                        ETimeCondition::GFD.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "市价即时成交剩余转限价" => {
                    return (
                        EOrderPriceType::BestPrice.into(),
                        ETimeCondition::GFD.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "市价即时成交剩余撤销" => {
                    return (
                        EOrderPriceType::BestPrice.into(),
                        ETimeCondition::IOC.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "限价全部成交或撤销" => {
                    return (
                        EOrderPriceType::LimitPrice.into(),
                        ETimeCondition::IOC.into(),
                        EVolumeCondition::CV.into(),
                    );
                }
                "市价全部成交或撤销" => {
                    return (
                        EOrderPriceType::BestPrice.into(),
                        ETimeCondition::IOC.into(),
                        EVolumeCondition::CV.into(),
                    );
                }
                _ => {}
            }
        } else if "SZSE" == exchid {
            match pricetype {
                "普通限价" => {
                    return (
                        EOrderPriceType::LimitPrice.into(),
                        ETimeCondition::GFD.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "对手方最优价" => {
                    return (
                        EOrderPriceType::BestPrice.into(),
                        ETimeCondition::GFD.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "本方最优剩余转限价" => {
                    return (
                        EOrderPriceType::BestPriceThisSide.into(),
                        ETimeCondition::GFD.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "最优5档即时成交剩余撤销" => {
                    return (
                        EOrderPriceType::FiveLevelPrice.into(),
                        ETimeCondition::IOC.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "市价即时成交剩余撤销" => {
                    return (
                        EOrderPriceType::BestPrice.into(),
                        ETimeCondition::IOC.into(),
                        EVolumeCondition::AV.into(),
                    );
                }
                "市价全部成交或撤销" => {
                    return (
                        EOrderPriceType::BestPrice.into(),
                        ETimeCondition::IOC.into(),
                        EVolumeCondition::CV.into(),
                    );
                }
                "限价全部成交或撤销" => {
                    return (
                        EOrderPriceType::LimitPrice.into(),
                        ETimeCondition::IOC.into(),
                        EVolumeCondition::CV.into(),
                    );
                }
                _ => {}
            }
        }

        (-1, -1, -1)
    }
}

/// 更新UI
impl InputOrder {
    // 更新错误信息
    async fn update_errmsg(&self, app_weak: &Weak<App>) {
        let inputord_datas = INPUTORDER_DATAS.get().unwrap();

        let is_err_inputorder = inputord_datas
            .is_err_inputorder
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok();
        if !is_err_inputorder {
            return;
        }

        let rsp = { inputord_datas.rsp_inputorder.lock().unwrap().clone() };

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            show_input_msg_box(
                &app,
                1,
                Default::default(),
                slint::format!("报单录入响应失败\n\n错误码:{}\n错误信息:{}", rsp.ec, rsp.em),
            );
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        // 更新报单响应错误信息
        self.update_errmsg(&app_weak).await;
    }

    /// 更新输入
    pub async fn update_input(&self, app_weak: Weak<App>) {
        let inputord_datas = INPUTORDER_DATAS.get().unwrap();
        if inputord_datas
            .input_instid_changed
            .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
            .is_ok()
        {
            let _ = app_weak.upgrade_in_event_loop(move |app| {
                let app_inputord = app.global::<OptTrd_InputOrder>();

                let insid = app_inputord.get_insid();

                if insid.len() > 8 + 5 {
                    // 目前个股期权单合约的编码长度还没有超过8位的
                    let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());
                    app_inputord.set_insid_model(items.into());
                    return;
                }

                let all_insid_data = app_inputord.get_all_insid_model();

                if insid.is_empty() {
                    app_inputord.set_insid_model(all_insid_data);
                    return;
                }

                let items: Rc<VecModel<crate::slintui::LineItem>> = Rc::new(VecModel::default());

                let mut find_flag = false;
                for i in 0..all_insid_data.row_count() {
                    let li = all_insid_data.row_data(i).unwrap();
                    if li.text.starts_with(insid.as_str()) {
                        items.push(li);
                        find_flag = true;
                    } else {
                        if find_flag {
                            break;
                        }
                    }
                }

                app_inputord.set_insid_model(items.into());
            });
        }
    }
}
