use serde::Serialize;

use crate::{
    api::tradestkapi::{ITradeStkSpi, TradeStkApi},
    protocol::{
        app::*,
        json,
        login::{LoginStatus, PTCtpRspLogin, PTWSReqLogin, PTWSRspLogin},
        ptstruct::{EMessageType, PTMsgBase, PTReqBase},
    },
    protocol_pub::{
        account_trade_ctp::*, app::*, instrument_trade_ctp::*, login_trade_ctp::*, monitor_trade_ctp::*, order_trade_ctp::*,
        position_trade_ctp::*, trade_req_ctp::*, trade_trade_ctp::*,
    },
    request::HttpClient,
    serverinfo::ServerInfo,
    ws::WsClient,
};

use std::{
    sync::{Arc, RwLock},
    thread,
};
use tokio::sync::Mutex;

/// 现货交易API实现
pub(crate) struct TradeStkApiImpl {
    /// 服务端信息
    svrinfo: ServerInfo,

    /// 回调类
    spi: Arc<dyn ITradeStkSpi>,

    /// 请求客户端
    req: HttpClient,

    /// Ws客户端(仅在登录时使用一次,其他时间段都在解析消息的线程中使用)
    ws: Arc<Mutex<WsClient>>,

    /// 消息解析线程
    parse_thread: Option<thread::JoinHandle<()>>,

    /// 登录成功后的SessionID
    login_status: Arc<RwLock<LoginStatus>>,
}

impl TradeStkApiImpl {
    pub fn new<T: ITradeStkSpi + 'static>(spi: T) -> Self {
        Self {
            svrinfo: ServerInfo::default(),
            spi: Arc::new(spi),
            req: HttpClient::new(),
            ws: Arc::new(Mutex::new(WsClient::new())),
            parse_thread: None,
            login_status: Arc::new(RwLock::new(LoginStatus::default())),
        }
    }

    /// 是否已经登录
    fn is_login(&self) -> bool {
        LoginStatus::SUCCESS == self.login_status.read().unwrap().status
    }

    /// 登录状态
    fn login_status(&self) -> (i32, String) {
        let ls = self.login_status.read().unwrap();
        (ls.status, ls.em.clone())
    }

    /// 设置SID
    fn set_login_id(&self, uid: &str, sid: &str) {
        let mut ls = self.login_status.write().unwrap();
        ls.set_uid(uid);
        ls.set_sid(sid);
    }

    /// 获取登录状态
    fn get_login_id(&self) -> (String, String) {
        let ls = self.login_status.read().unwrap();
        (ls.uid.clone(), ls.sid.clone())
    }

    /// 获取序列化后的JSON字符串
    fn get_serialize_json<T1, T2>(&self, reqbase: &T1, req: &T2) -> crate::Result<String>
    where
        T1: Serialize,
        T2: Serialize,
    {
        let ret = json::to_string(reqbase);
        if ret.is_err() {
            return Err("Serialize reqbase failed".to_owned());
        }
        let basejson = ret.ok().unwrap();

        let ret = json::to_string(req);
        if ret.is_err() {
            return Err("Serialize req failed".to_owned());
        }
        let mut reqbody = ret.ok().unwrap();
        if reqbody.eq("{}") {
            reqbody = basejson;
        } else {
            reqbody.insert(1, ',');
            reqbody.insert_str(1, &basejson[1..basejson.len() - 1]);
        }
        Ok(reqbody)
    }

    /// 发送请求到服务端
    ///
    /// reqid: 请求编号<br>
    /// mt: 消息类型<br>
    /// req: 请求消息<br>
    async fn send_req_to_server<T: serde::Serialize>(&self, mt: EMessageType, reqid: i32, req: &T) -> crate::Result<()> {
        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = super::comfun::get_trd_req_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(&self.svrinfo.get_req_url(), mt, &reqbody)
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&ret.ok().unwrap());
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        Ok(())
    }
}

#[async_trait::async_trait]
impl TradeStkApi for TradeStkApiImpl {
    /// 初始化
    async fn init(&mut self) {
        let spi: Arc<dyn ITradeStkSpi> = self.spi.clone();
        let login_status: Arc<RwLock<LoginStatus>> = self.login_status.clone();

        let (sender, receiver) = crossbeam_channel::unbounded();

        // 启动消息解析线程
        self.parse_thread = Some(std::thread::spawn(move || {
            TradeStkApiImpl::parse_run(spi, receiver, login_status);
        }));

        self.ws.lock().await.set_msg_sender(sender);
    }

    /// 释放资源
    async fn release(&mut self) {
        log::info!("api release");

        self.ws.lock().await.release();

        if let Some(t) = self.parse_thread.take() {
            let _ = t.join();
        }
    }

    /// 设置服务端信息(请求登录前必须设置)
    async fn set_svrinfo(&mut self, si: &ServerInfo) {
        self.svrinfo = si.clone();
        self.req.set_encon(1 == self.svrinfo.encon);
        self.ws.lock().await.set_svrinfo(&self.svrinfo);
    }

    /// 请求登录
    async fn req_login(&self, req: &CtpReqLoginField) -> crate::Result<CtpLoginRetField> {
        // 检查当前是否已经登录
        if self.is_login() {
            return Err("Already login".to_string());
        }

        // 1. 请求http登录

        // 序列化 CtpReqLoginField
        let ret = json::to_string(&req);
        if ret.is_err() {
            return Err(ret.unwrap_err());
        }
        let reqbody = ret.unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(&self.svrinfo.get_req_url(), EMessageType::TIMT_Ctp_Req_Login, &reqbody)
            .await?;

        // 反序列化结登录结果
        let ret: Result<PTCtpRspLogin, _> = json::from_str(&ret);
        if ret.is_err() {
            return Err(format!("Parse response failed"));
        }
        let rsp = ret.ok().unwrap();

        // 赋值返回的登录结果
        let mut loginret = CtpLoginRetField {
            ec: rsp.ec,
            em: rsp.em.clone(),
            func: rsp.func,
            cnt: rsp.cnt,
            arr: vec![],
        };
        rsp.arr.clone_into(&mut loginret.arr);
        if 0 != rsp.ec {
            return Ok(loginret);
        }

        // 检查节点是否成功. 只要有一个成功则认为登录成功
        loginret.ec = loginret.cnt;
        for lr in &loginret.arr {
            if 0 == lr.ec {
                loginret.ec -= 1;
            }
        }
        if loginret.cnt == loginret.ec {
            // 所有结节都登录失败则认为整个登录都失败
            loginret.ec = -1;

            if 0 != loginret.cnt {
                loginret.em = "All TDSvrID login failed".to_owned();
                return Ok(loginret);
            } else {
                // cnt 为0这种情况只有在服务端为 Debug 模式且定义了不登录 TITD 服务的宏时才会出现，此时就是为了终端进行界面的调试
                #[cfg(not(debug_assertions))]
                {
                    loginret.em = "Not set TDSvrID to login".to_owned();
                    return Ok(loginret);
                }
            }
        }
        loginret.ec = 0;

        // 2. WS 登录

        // 序列化登录协议
        let mut wslogin = PTWSReqLogin::default();
        wslogin.userid = req.UserID.clone();
        wslogin.sessionid = rsp.sid.clone();
        let ret = json::to_string(&wslogin);
        if ret.is_err() {
            loginret.ec = -1;
            loginret.em = ret.unwrap_err();
            return Ok(loginret);
        }

        // 发送请求
        let reqbody = ret.unwrap();
        let ret = self.ws.lock().await.req_login(&reqbody).await;
        if ret.is_err() {
            loginret.ec = -1;
            loginret.em = format!("Login to ws failed. {}", ret.err().unwrap());
            return Ok(loginret);
        }

        // 等待10S
        let start = std::time::Instant::now();
        loop {
            let duration = start.elapsed();
            if duration.as_secs() >= 10 {
                break;
            }

            let ret = self.login_status();
            if LoginStatus::SUCCESS == ret.0 {
                break;
            }

            if LoginStatus::FAILED == ret.0 {
                loginret.ec = -1;
                loginret.em = format!("Login to ws failed. {}", ret.1);
                return Ok(loginret);
            }

            thread::sleep(std::time::Duration::from_millis(100));
        }

        if self.is_login() {
            self.set_login_id(&req.UserID, &rsp.sid);
        } else {
            loginret.ec = -1;
            loginret.em = format!("Login to ws timeout");
            return Ok(loginret);
        }

        return Ok(loginret);
    }

    /// 请求资金划转(同步函数)
    async fn req_fundtrans(&self, req: &CtpReqFundTransferField, rsp: &mut CtpRspFundTransferField) -> crate::Result<()> {
        // 参数检查
        if req.AccountID.is_empty() {
            return Err("AccountID is empty".to_owned());
        }
        if req.Amount <= 0f64 || std::ops::Sub::sub(0.005, req.Amount) > 0.000001 {
            return Err("Amount must be grater than 0.005".to_owned());
        }
        if 0 != req.InSys && 1 != req.InSys && 2 != req.InSys && 9 != req.InSys {
            return Err("Error InSys".to_owned());
        }
        if 0 != req.OutSys && 1 != req.OutSys && 2 != req.OutSys && 9 != req.OutSys {
            return Err("Error OutSys".to_owned());
        }
        if req.InSys == req.OutSys {
            return Err("InSys cannot be equal to OutSys".to_owned());
        }

        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid: 0,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = self.get_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(
                &self.svrinfo.get_req_url(),
                EMessageType::TIMT_Ctp_Ex_Req_FundTransfer,
                &reqbody,
            )
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        let rspmsg = ret.ok().unwrap();

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp errmsg failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        // 解析 CtpRspFundTransferField
        let ret: Result<CtpRspFundTransferField, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        *rsp = ret.ok().unwrap();

        // 解析 pbtitdwebaccountupdrsp
        let ret: Result<Vec<pbtitdwebaccountupdrsp>, _> = json::from_str(&rsp.Rst);
        if ret.is_err() {
            return Err("parse rsp Rst failed".to_owned());
        }
        rsp.Arr = ret.ok().unwrap();

        Ok(())
    }

    /// 请求资金继续划转(同步函数)
    async fn req_re_fundtrans(
        &self,
        req: &CtpReqReFundTransferField,
        rsp: &mut Vec<CtpRspQryFundTransferField>,
    ) -> crate::Result<()> {
        // 参数检查
        if req.AccountID.is_empty() {
            return Err("AccountID is empty".to_owned());
        }
        if req.TransNo.is_empty() {
            return Err("TransNo is empty".to_owned());
        }
        if req.TradeDate <= ******** || req.TradeDate >= ******** {
            return Err("TradeDate error".to_owned());
        }

        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid: 0,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = self.get_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(
                &self.svrinfo.get_req_url(),
                EMessageType::TIMT_Ctp_Ex_Req_AFundTransfer,
                &reqbody,
            )
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        let rspmsg = ret.ok().unwrap();

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp errmsg failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        // 解析 CtpRspReFundTransferField
        let ret: Result<CtpRspReFundTransferField, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        let qft = ret.ok().unwrap();

        // 解析 pbtitdwebaccountupdqryrsp
        let ret: Result<Vec<CtpRspQryFundTransferField>, _> = json::from_str(&qft.Rst);
        if ret.is_err() {
            return Err("parse rsp Rst failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        for r in ret {
            rsp.push(r);
        }

        Ok(())
    }

    /// 请求查询资金划转(同步函数)
    async fn req_qry_fundtrans(
        &self,
        req: &CtpReqQryFundTransferField,
        rsp: &mut Vec<CtpRspQryFundTransferField>,
    ) -> crate::Result<()> {
        // 参数检查
        if req.AccountID.is_empty() {
            return Err("AccountID is empty".to_owned());
        }
        if 100 != req.TransStatus && -1 != req.TransStatus && 0 != req.TransStatus && 1 != req.TransStatus {
            return Err("TransStatus is error".to_owned());
        }

        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid: 0,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = self.get_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(
                &self.svrinfo.get_req_url(),
                EMessageType::TIMT_Ctp_Ex_Req_QryFundTransfer,
                &reqbody,
            )
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        let rspmsg = ret.ok().unwrap();

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp errmsg failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        // 解析 CtpRspQryFundTransfer
        let ret: Result<CtpRspQryFundTransfer, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        let qft = ret.ok().unwrap();

        // 解析 pbtitdwebaccountupdrsp
        let ret: Result<Vec<Vec<CtpRspQryFundTransferField>>, _> = json::from_str(&qft.Rst);
        if ret.is_err() {
            return Err("parse rsp Rst failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        for r0 in ret {
            for r1 in r0 {
                rsp.push(r1);
            }
        }

        Ok(())
    }

    /// 请求股份划转(同步函数)
    async fn req_positiontrans(
        &self,
        req: &CtpReqPositionTransferField,
        rsp: &mut CtpRspPositionTransferField,
    ) -> crate::Result<()> {
        // 参数检查
        if req.AccountID.is_empty() {
            return Err("AccountID is empty".to_owned());
        }
        if req.ClientID.is_empty() {
            return Err("ClientID is empty".to_owned());
        }
        if 1 != req.ExchID && 2 != req.ExchID && 9 != req.ExchID {
            return Err("ExchID error".to_owned());
        }
        if req.InstrumentID.trim().is_empty() {
            return Err("InstrumentID is empty".to_owned());
        }
        if req.Volume <= 0 {
            return Err("Volume error".to_owned());
        }
        if 1 != req.Dir && 2 != req.Dir {
            return Err("Trans direction error".to_owned());
        }

        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid: 0,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = self.get_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(
                &self.svrinfo.get_req_url(),
                EMessageType::TIMT_Ctp_Ex_Req_PositionTransfer,
                &reqbody,
            )
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        let rspmsg = ret.ok().unwrap();

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp errmsg failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        // 解析 CtpRspPositionTransferField
        let ret: Result<CtpRspPositionTransferField, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        *rsp = ret.ok().unwrap();

        // 解析 CtpPositionTransferField
        let ret: Result<Vec<CtpPositionTransferField>, _> = json::from_str(&rsp.Rst);
        if ret.is_err() {
            return Err("parse rsp Rst failed".to_owned());
        }
        rsp.Arr = ret.ok().unwrap();

        Ok(())
    }

    /// 请求股份继续划转(同步函数)
    async fn req_re_positiontrans(
        &self,
        req: &CtpReqRePositionTransferField,
        rsp: &mut Vec<CtpPositionTransferField>,
    ) -> crate::Result<()> {
        // 参数检查
        if req.AccountID.is_empty() {
            // return Err("AccountID is empty".to_owned());
        }
        if req.ClientID.is_empty() {
            return Err("ClientID is empty".to_owned());
        }
        if req.TransNo.is_empty() {
            return Err("TransNo is empty".to_owned());
        }
        if req.TradeDate <= ******** || req.TradeDate >= ******** {
            return Err("TradeDate error".to_owned());
        }

        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid: 0,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = self.get_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(
                &self.svrinfo.get_req_url(),
                EMessageType::TIMT_Ctp_Ex_Req_APositionTransfer,
                &reqbody,
            )
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        let rspmsg = ret.ok().unwrap();

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp errmsg failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        // 解析 CtpRspRePositionTransferField
        let ret: Result<CtpRspRePositionTransferField, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        let qft = ret.ok().unwrap();

        // 解析 CtpPositionTransferField
        let ret: Result<Vec<CtpPositionTransferField>, _> = json::from_str(&qft.Rst);
        if ret.is_err() {
            return Err("parse rsp Rst failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        for r in ret {
            rsp.push(r);
        }

        Ok(())
    }

    /// 请求查询股份划转记录(同步函数)
    async fn req_qry_positiontrans(
        &self,
        req: &CtpReqQryPositionTransferField,
        rsp: &mut Vec<CtpPositionTransferField>,
    ) -> crate::Result<()> {
        // 参数检查
        if req.ClientID.is_empty() {
            return Err("ClientID is empty".to_owned());
        }
        if 100 != req.TransStatus && -1 != req.TransStatus && 0 != req.TransStatus && 1 != req.TransStatus {
            return Err("TransStatus is error".to_owned());
        }

        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid: 0,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = self.get_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(
                &self.svrinfo.get_req_url(),
                EMessageType::TIMT_Ctp_Ex_Req_QryPositionTransfer,
                &reqbody,
            )
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        let rspmsg = ret.ok().unwrap();

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp errmsg failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        // 解析 CtpRspQryPositionTransferField
        let ret: Result<CtpRspQryPositionTransferField, _> = json::from_str(&rspmsg);
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        let qpt = ret.ok().unwrap();

        // 解析 CtpPositionTransferField
        let ret: Result<Vec<Vec<CtpPositionTransferField>>, _> = json::from_str(&qpt.Rst);
        if ret.is_err() {
            return Err("parse rsp Rst failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        for r0 in ret {
            for r1 in r0 {
                rsp.push(r1);
            }
        }

        Ok(())
    }

    /// 请求报单
    async fn req_orderinput(&self, reqid: i32, req: &CtpReqInputOrderField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Req_InputOrder, reqid, req)
            .await
    }

    /// 请求撤单
    async fn req_orderaction(&self, reqid: i32, req: &CtpReqInputOrderActionField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Req_InputOrderAction, reqid, req)
            .await
    }

    /// 请求非交易业务报单
    async fn req_fjy_orderinput(&self, reqid: i32, req: &CtpReqBusinessOrderField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Ex_Req_BusinessInsert, reqid, req)
            .await
    }

    /// 请求非交易业务撤单
    async fn req_fjy_orderaction(&self, reqid: i32, req: &CtpReqBusinessOrderActionField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Ex_Req_BusinessAction, reqid, req)
            .await
    }

    /// 请求查询交易编码
    async fn req_qry_tradingcode(&self, reqid: i32, req: &CtpReqQryTradingCodeField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Req_QryTradingCode, reqid, req)
            .await
    }

    /// 请求查询合约
    async fn req_qry_instrument(&self, reqid: i32, req: &CtpReqQryInstrumentField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Req_QryInstrument, reqid, req)
            .await
    }

    /// 请求查询非交易合约
    async fn req_qry_fjy_instrument(&self, reqid: i32, req: &CtpReqQryFJYInstrumentField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_EX_Req_QryFJYInstrument, reqid, req)
            .await
    }

    /// 请求查询资金账户
    async fn req_qry_tradingaccount(&self, reqid: i32, req: &CtpReqQryTradingAccountField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Req_QryTradingAccount, reqid, req)
            .await
    }

    /// 请求查询资金账户(分交易所)
    async fn req_qry_tradingaccountex(&self, reqid: i32, req: &CtpReqQryTradingAccountExField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Ex_Req_QryTradingAccount, reqid, req)
            .await
    }

    /// 请求查询持仓
    async fn req_qry_investorposition(&self, reqid: i32, req: &CtpReqQryPositionField) -> crate::Result<()> {
        self.send_req_to_server(EMessageType::TIMT_Ctp_Req_QryInvestorPosition, reqid, req)
            .await
    }
}

impl TradeStkApiImpl {
    /// 解析消息
    fn parse_run(
        spi: Arc<dyn ITradeStkSpi>,
        receiver: crossbeam_channel::Receiver<tokio_tungstenite::tungstenite::Message>,
        login_status: Arc<RwLock<LoginStatus>>,
    ) {
        log::info!("TradeStkApiImpl parse_run");

        loop {
            match receiver.recv() {
                Ok(msg) if msg.is_text() => {
                    match msg.to_text() {
                        Ok(raw_msg) => {
                            if let Ok(msgbase) = json::from_str::<PTMsgBase>(raw_msg) {
                                if let Ok(mt) = EMessageType::try_from(msgbase.mt) {
                                    match mt {
                                        EMessageType::TIMT_WS_Ctp_Rtn_Order => {
                                            TradeStkApiImpl::parse_rtn_order(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Rtn_Trade => {
                                            TradeStkApiImpl::parse_rtn_trade(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_BusinessOrder => {
                                            TradeStkApiImpl::parse_rtn_businessorder(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_BusinessTrade => {
                                            TradeStkApiImpl::parse_rtn_businesstrade(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_OrderInsertCancel => {
                                            TradeStkApiImpl::parse_rtn_orderinsertcancel(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_TradeSelf => {
                                            TradeStkApiImpl::parse_rtn_tradeself(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_TradeSelfDtl => {
                                            TradeStkApiImpl::parse_rtn_tradeselfdtl(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_TradePosition => {
                                            TradeStkApiImpl::parse_rtn_tradeposition(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_TradeMon => {
                                            TradeStkApiImpl::parse_rtn_trademon(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_ConvertBondsMon => {
                                            TradeStkApiImpl::parse_rtn_convertbondsmon(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_UpdateAccount => {
                                            TradeStkApiImpl::parse_rtn_wd(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_UpdatePosition => {
                                            TradeStkApiImpl::parse_rtn_positiontrans(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_CreditContract => {
                                            TradeStkApiImpl::parse_rtn_creditcontract(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_CreditLimit => {
                                            TradeStkApiImpl::parse_rtn_creditlimit(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_CreditTcAmt => {
                                            TradeStkApiImpl::parse_rtn_credittcamt(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_CreditTcPos => {
                                            TradeStkApiImpl::parse_rtn_credittcpos(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_CreditConcentration => {
                                            TradeStkApiImpl::parse_rtn_creditconcentration(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rtn_CreditReturnDtl => {
                                            TradeStkApiImpl::parse_rtn_creditretdtl(&spi, raw_msg);
                                        }

                                        EMessageType::TIMT_WS_Ctp_Rsp_InputOrder => {
                                            TradeStkApiImpl::parse_rsp_inputorder(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Rsp_InputOrderAction => {
                                            TradeStkApiImpl::parse_rsp_inputorderaction(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rsp_BusinessInsert => {
                                            TradeStkApiImpl::parse_rsp_businessinsert(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rsp_BusinessAction => {
                                            TradeStkApiImpl::parse_rsp_businessaction(&spi, &msgbase, raw_msg);
                                        }

                                        EMessageType::TIMT_WS_Ctp_Rsp_QryTradingAccount => {
                                            TradeStkApiImpl::parse_rsp_qrytradingaccount(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Ex_Rsp_QryTradingAccount => {
                                            TradeStkApiImpl::parse_rsp_qrytradingaccount_ex(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Rsp_QryInvestorPosition => {
                                            TradeStkApiImpl::parse_rsp_qryinvestorposition(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Rsp_QryTradingCode => {
                                            TradeStkApiImpl::parse_rsp_qry_tradingcode(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_Rsp_QryInstrument => {
                                            TradeStkApiImpl::parse_rsp_qry_instrument(&spi, &msgbase, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Ctp_EX_Rsp_QryFJYInstrument => {
                                            TradeStkApiImpl::parse_rsp_qry_fjyinstrument(&spi, &msgbase, raw_msg);
                                        }

                                        EMessageType::TIMT_WS_Com_Rtn_App => {
                                            TradeStkApiImpl::parse_rtn_app_msg(&spi, &msgbase, raw_msg);
                                        }

                                        EMessageType::TIMT_WS_Com_Rsp_Login => {
                                            let ret = TradeStkApiImpl::parse_ws_com_rsp_login(raw_msg);
                                            login_status.write().unwrap().set_login(ret.0, &ret.1);
                                        }

                                        // 在响应中处理相关错误
                                        EMessageType::TIMT_WS_Ctp_Rtn_Err_OrderInsert => {}
                                        EMessageType::TIMT_WS_Ctp_Rtn_Err_OrderAction => {}

                                        _ => log::warn!("Unhandled message. [{:?}]", raw_msg),
                                    }
                                } else {
                                    // 新的消息
                                }
                            } else {
                                // 格式错误
                            }
                        }
                        Err(err) => {
                            log::warn!("parse_run: ws Message to_text failed. {}", err.to_string());
                        }
                    }
                }
                Ok(msg) if msg.is_close() => {
                    log::warn!("parse_run: proc ws closed");
                    login_status.write().unwrap().set_login(LoginStatus::FAILED, "ws closed");
                    spi.on_rtn_disconnect();
                    break;
                }
                Err(err) => {
                    log::warn!("Error receiving message: {:?}", err);
                    break;
                }
                _ => {}
            }
        }

        log::info!("TradeStkApiImpl parse_run end");
    }

    /// 解析系统消息
    fn parse_rtn_app_msg(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<PTRtnApp, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            let ret = EAppMessageType::try_from(rtn.at);
            if let Ok(amt) = ret {
                if EAppMessageType::TI_AMT_Heartbeat == amt {
                    let ret: Result<AppHeartbeatField, _> = json::from_str(&rtn.msg);
                    if let Ok(rtn0) = ret {
                        spi.on_rtn_heartbeat(rtn0);
                    } else {
                        log::warn!("parse_rtn_app_msg: parse Heartbeat failed. {}", rtn.msg);
                    }
                } else if EAppMessageType::TI_AMT_TDServerError == amt {
                    let ret: Result<AppTDServerErrorField, _> = json::from_str(&rtn.msg);
                    if let Ok(rtn0) = ret {
                        spi.on_rtn_tdservererror(rtn0);
                    } else {
                        log::warn!("parse_rtn_app_msg: parse TDServerError failed. {}", rtn.msg);
                    }
                } else if EAppMessageType::TIMT_App_TDSessionTimeout == amt {
                    let ret: Result<AppTDSessionTimeoutField, _> = json::from_str(&rtn.msg);
                    if let Ok(rtn0) = ret {
                        spi.on_rtn_tdsessiontimeout(rtn0);
                    } else {
                        log::warn!("parse_rtn_app_msg: parse TDSessionTimeout failed. {}", rtn.msg);
                    }
                } else {
                    // 新类型消息
                }
            } else {
                log::warn!("parse_rtn_app_msg: parse at failed. {}", rtn.at);
            }
        } else {
            log::warn!("parse_rtn_app_msg: parse failed. {}", msg);
        }
    }

    /// 解析报单录入响应
    fn parse_rsp_inputorder(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpRspInputOrderField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_inputorder(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!("parse_rsp_inputorder failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析报单操作响应
    fn parse_rsp_inputorderaction(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpRspInputOrderActionField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_inputorderaction(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!("parse_rsp_inputorderaction failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析非交易业务报单录入响应
    fn parse_rsp_businessinsert(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let mut rsp = CtpRspBusinessOrderField::default();
        rsp.ec = msgbase.ec;
        rsp.em = msgbase.em.clone();
        spi.on_rsp_businessorder(rsp, msgbase.reqid, 1 == msgbase.lt);
    }

    /// 解析非交易业务报单操作响应
    fn parse_rsp_businessaction(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpRspBusinessOrderActionField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_businessorderaction(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!("parse_rsp_businessaction failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析投资者持仓响应
    fn parse_rsp_qryinvestorposition(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpPositionField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_qry_investorposition(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!(
                "parse_rsp_qryinvestorposition failed. err:{}, msg:[{}]",
                ret.err().unwrap(),
                msg
            );
        }
    }

    /// 解析查询资金账户响应
    fn parse_rsp_qrytradingaccount(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpAccountField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_qry_tradingaccount(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!(
                "parse_rsp_qrytradingaccount failed. err:{}, msg:[{}]",
                ret.err().unwrap(),
                msg
            );
        }
    }

    /// 解析查询资金账户响应
    fn parse_rsp_qrytradingaccount_ex(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpAccountField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_qry_tradingaccountex(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!(
                "parse_rsp_qrytradingaccount_ex failed. err:{}, msg:[{}]",
                ret.err().unwrap(),
                msg
            );
        }
    }

    /// 解析查询交易编码响应
    fn parse_rsp_qry_tradingcode(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpTradingCodeField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_qry_tradingcode(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!("parse_rsp_qry_tradingcode failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析查询合约响应
    fn parse_rsp_qry_instrument(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpRspQryInstrumentField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_qry_instrument(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!("parse_rsp_qry_instrument failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析查询非交易合约响应
    fn parse_rsp_qry_fjyinstrument(spi: &Arc<dyn ITradeStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CtpRspQryFJYInstrumentField, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            spi.on_rsp_qry_fjyinstrument(rsp, msgbase.reqid, 1 == msgbase.lt);
        } else {
            log::warn!(
                "parse_rsp_qry_fjyinstrument failed. err:{}, msg:[{}]",
                ret.err().unwrap(),
                msg
            );
        }
    }

    /// 解析报单推送
    fn parse_rtn_order(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpOrderField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_order(rtn);
        } else {
            log::warn!("parse_rtn_order failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析非交易业务报单推送
    fn parse_rtn_businessorder(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpBusinessOrderField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_businessorder(rtn);
        } else {
            log::warn!("parse_rtn_businessorder failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析成交推送
    fn parse_rtn_trade(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpTradeField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_trade(rtn);
        } else {
            log::warn!("parse_rtn_trade failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析非交易业务成交推送
    fn parse_rtn_businesstrade(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpBusinessTradeField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_businesstrade(rtn);
        } else {
            log::warn!("parse_rtn_businesstrade failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析报撤单推送
    fn parse_rtn_orderinsertcancel(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpOrderInsertCancelField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_orderinsertcancel(rtn);
        } else {
            log::warn!(
                "parse_rtn_orderinsertcancel failed. err:{}, msg:[{}]",
                ret.err().unwrap(),
                msg
            );
        }
    }

    /// 解析自成交推送
    fn parse_rtn_tradeself(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpTradeSelfField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_tradeself(rtn);
        } else {
            log::warn!("parse_rtn_tradeself failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析自成交详情推送
    fn parse_rtn_tradeselfdtl(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpTradeSelfDtlField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_tradeselfdtl(rtn);
        } else {
            log::warn!("parse_rtn_tradeselfdtl failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析成交持仓比推送
    fn parse_rtn_tradeposition(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpTradePositionField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_tradeposition(rtn);
        } else {
            log::warn!("parse_rtn_tradeposition failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析成交监控推送
    fn parse_rtn_trademon(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpTradeMonField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_trademon(rtn);
        } else {
            log::warn!("parse_rtn_trademon failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析可转债监控推送
    fn parse_rtn_convertbondsmon(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpConvertBondsMonField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_convertbondsmon(rtn);
        } else {
            log::warn!("parse_rtn_convertbondsmon failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析出入金推送
    fn parse_rtn_wd(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpWithdrawDepositField, _> = json::from_str(msg);
        if let Ok(wd) = ret {
            spi.on_rtn_wd(wd);
        } else {
            log::warn!("parse_rtn_wd failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析股份划转推送
    fn parse_rtn_positiontrans(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpPositionTransField, _> = json::from_str(msg);
        if let Ok(pt) = ret {
            spi.on_rtn_positiontrans(pt);
        } else {
            log::warn!("parse_rtn_positiontrans failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用交易的信用合约推送
    fn parse_rtn_creditcontract(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpCreditContractField, _> = json::from_str(msg);
        if let Ok(cc) = ret {
            spi.on_rtn_creditcontract(cc);
        } else {
            log::warn!("parse_rtn_creditcontract failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用交易的授信额度推送
    fn parse_rtn_creditlimit(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpCreditLimitField, _> = json::from_str(msg);
        if let Ok(cl) = ret {
            spi.on_rtn_creditlimit(cl);
        } else {
            log::warn!("parse_rtn_creditlimit failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用交易的资金头寸推送
    fn parse_rtn_credittcamt(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpCreditTcAmtField, _> = json::from_str(msg);
        if let Ok(amt) = ret {
            spi.on_rtn_credittcamt(amt);
        } else {
            log::warn!("parse_rtn_credittcamt failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用交易的股份头寸推送
    fn parse_rtn_credittcpos(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpCreditTcPosField, _> = json::from_str(msg);
        if let Ok(pos) = ret {
            spi.on_rtn_credittcpos(pos);
        } else {
            log::warn!("parse_rtn_credittcpos failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用交易的集中度推送
    fn parse_rtn_creditconcentration(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpCreditConcentrationField, _> = json::from_str(msg);
        if let Ok(cc) = ret {
            spi.on_rtn_creditconcentration(cc);
        } else {
            log::warn!(
                "parse_rtn_creditconcentration failed. err:{}, msg:[{}]",
                ret.err().unwrap(),
                msg
            );
        }
    }

    /// 解析信用交易的合约归还明细推送
    fn parse_rtn_creditretdtl(spi: &Arc<dyn ITradeStkSpi>, msg: &str) {
        let ret: Result<CtpCreditReturnDtlField, _> = json::from_str(msg);
        if let Ok(rtd) = ret {
            spi.on_rtn_creditretdtl(rtd);
        } else {
            log::warn!("parse_rtn_creditretdtl failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析WS端请求登录响应
    fn parse_ws_com_rsp_login(msg: &str) -> (i32, String) {
        let em;
        let ret: Result<PTWSRspLogin, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            if 0 == rsp.ec {
                return (LoginStatus::SUCCESS, "".to_owned());
            } else {
                log::warn!("parse_ws_com_rsp_login: login failed. err:{}, msg:[{}]", rsp.em, msg);
                em = rsp.em;
            }
        } else {
            em = ret.err().unwrap();
            log::warn!("parse_ws_com_rsp_login failed. err:{}, msg:[{}]", em, msg);
        }

        return (LoginStatus::FAILED, em);
    }
}
