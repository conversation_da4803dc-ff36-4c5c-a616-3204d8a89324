//! # lan
//!
//!  实现语言切换。目前只支持简体中文和英文。

use std::collections::HashMap;

/// 支持的语言
pub enum LanType {
    /// 简体中文
    Chinese,

    /// 英语
    English,
}

/// 专业术语标识
#[derive(PartialEq, Eq, Hash)]
pub enum LanTermID {
    /// 交易所
    ExchangeID,

    /// 品种编码
    ProductID,

    /// 合约编码(期货期权)
    InstrumentID,

    /// 合约编码(股票)
    SecurityID,

    /// 资金账号
    AccountID,

    /// 交易编码
    ClientID,
}

/// 控件标识
#[derive(PartialEq, Eq, Hash)]
pub enum LanCtrlID {
    /// 登录
    Login,

    /// 登出
    Logout,

    /// 确认
    Ok,

    /// 关闭
    Close,

    /// 是
    Yes,

    /// 否
    No,

    /// 设置
    Set,

    /// 导入
    Import,

    /// 导出
    Export,

    /// 查询
    Search,
}

/// 语言
pub struct Language {
    /// 类型
    lantype: LanType,

    /// 专业术语
    tern_texts: HashMap<LanTermID, (&'static str, &'static str)>,

    /// 控件
    ctrl_texts: HashMap<LanCtrlID, (&'static str, &'static str)>,
}

impl Language {
    /// 构造语言
    pub fn new(lt: LanType) -> Self {
        Language {
            lantype: lt,

            tern_texts: {
                let mut vn = HashMap::new();
                vn.insert(LanTermID::ExchangeID, ("交易所", "ExchangeID"));
                vn.insert(LanTermID::ProductID, ("品种编码", "ProductID"));
                vn.insert(LanTermID::InstrumentID, ("合约编码", "InstrumentID"));
                vn.insert(LanTermID::SecurityID, ("合约编码", "SecurityID"));
                vn.insert(LanTermID::AccountID, ("资金账号", "AccountID"));
                vn.insert(LanTermID::ClientID, ("交易编码", "ClientID"));

                vn
            },

            ctrl_texts: {
                let mut vn = HashMap::new();
                vn.insert(LanCtrlID::Login, ("登录", "Sign in"));
                vn.insert(LanCtrlID::Logout, ("登出", "Sign out"));

                vn.insert(LanCtrlID::Ok, ("确认", "Ok"));
                vn.insert(LanCtrlID::Close, ("关闭", "Close"));
                vn.insert(LanCtrlID::Yes, ("是", "Yes"));
                vn.insert(LanCtrlID::No, ("否", "No"));
                vn.insert(LanCtrlID::Set, ("设置", "Set"));

                vn.insert(LanCtrlID::Import, ("导入", "Import"));
                vn.insert(LanCtrlID::Export, ("导出", "Export"));

                vn.insert(LanCtrlID::Search, ("查询", "Search"));

                vn
            },
        }
    }

    /// 设置语言
    pub fn set_lan_type(&mut self, lt: LanType) {
        self.lantype = lt;
    }

    /// 根据专业术语标识获取文本
    pub fn get_text(&self, id: LanTermID) -> &'static str {
        let it = self.tern_texts.get(&id);
        if let Some(text) = it {
            match self.lantype {
                LanType::Chinese => text.0,
                LanType::English => text.1,
            }
        } else {
            "err-text"
        }
    }

    /// 根据控件标识获取文本
    pub fn get_ctrl_text(&self, id: LanCtrlID) -> &'static str {
        let it = self.ctrl_texts.get(&id);
        if let Some(text) = it {
            match self.lantype {
                LanType::Chinese => text.0,
                LanType::English => text.1,
            }
        } else {
            "err-text"
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let mut lan = Language::new(LanType::Chinese);

        assert_eq!("资金账号", lan.get_text(LanTermID::AccountID));
        assert_eq!("登录", lan.get_ctrl_text(LanCtrlID::Login));

        lan.set_lan_type(LanType::English);
        assert_eq!("Sign in", lan.get_ctrl_text(LanCtrlID::Login));
        assert_eq!("AccountID", lan.get_text(LanTermID::AccountID));
    }
}
