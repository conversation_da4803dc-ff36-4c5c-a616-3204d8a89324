import { ListViewItem } from "@zstk/lib.slint";
import { LineItem } from "../widgets/lineeditex.slint";

import { AppResult, TradeColor, MonTradePosition, MonOrderinsertcancel, MonTradeself } from "../struct.slint";

// 公共
export global OptTrd_Com {
    in_out property <string> accountid; // 资金账户
    in_out property <bool> ismarket;    // 是否做市商
    in_out property <string> transpwd;
}

// 选择的Tab的索引
export global OptTrd_TabSelIdx {
    in_out property <int> selidx;
    in_out property <int> in_subselidx;
    in_out property <int> rtn_subselidx;
    in_out property <int> mon_subselidx;

    in_out property <int> input_selidx;
}

// 资金划转
export struct FundTransField {
    accountid: string,
    password: string,
    outsys: int,
    insys: int,
    amount: string
}

// 资金信息
export global OptTrd_Account {
    in_out property <[[string]]> row_data;

    in_out property <int> sort_asc_column_index: 0;
    in_out property <int> sort_dec_column_index: -1;

    in_out property <string> sel_accid;
    in_out property <bool> show_fund_trans;

    callback sort_ascending(int);
    callback sort_descending(int);
    pure callback get_row_data_color(int, int, string) -> brush;

    callback req_fund_trans(/* ft */ FundTransField);
}

// 持仓信息
export global OptTrd_Position {
    in property <[[string]]> row_data;
    in property <[ListViewItem]> insid_model;

    in_out property <string> exchid;
    in_out property <string> insid;
    in_out property <string> posdir;
    in_out property <int> posdir_idx;

    callback insid_text_changed();
    callback filter_changed();
    callback export_clicked(/* type */ int);
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <int> sort_asc_column_index: -1;
    in_out property <int> sort_dec_column_index: -1;
    callback sort_ascending(int);
    callback sort_descending(int);

    public function get_row_data() -> [[string]] {
        row_data
    }

    // 备兑转换
    in_out property <bool> show_covered_convert;    // 是否显示备兑转换
    in_out property <int> show_covered_type;        // 备兑转换类型. 1:转备兑仓;2:转普通仓
    in_out property <string> sel_cc_accid;
    in_out property <string> sel_cc_exchid;
    in_out property <string> sel_cc_insid;
    in_out property <string> sel_cc_volume;
    in_out property <string> sel_cc_covered;
    callback pre_deal_covered_convert() -> bool;
    callback corvered_convert_clicked();
}

// 组合持仓
export global OptTrd_PositionComb {

    in property <[[string]]> row_data;

    in property <[ListViewItem]> combtradeid_model;
    in property <[ListViewItem]> insid_model;

    in_out property <string> exchid;
    in_out property <string> strategyid;
    in_out property <int> state: 1; // "组合"
    in_out property <string> combtradeid;
    in_out property <string> insid;

    callback insid_text_changed();
    callback combid_text_changed();
    callback filter_changed();
    callback export_clicked(/* type */ int);
    pure callback get_row_data_color(int, int, string) -> brush;

    in_out property <int> sort_asc_column_index: -1;
    in_out property <int> sort_dec_column_index: -1;
    callback sort_ascending(int);
    callback sort_descending(int);

    public function get_row_data() -> [[string]] {
        row_data
    }
}

// 录入提示
export global OptTrd_InputTip {
    in property <AppResult> app_ret;
}

// 撤销提示
export global OptTrd_CancelTip {
    in property <AppResult> app_ret;
}

// 报单录入
export global OptTrd_InputOrder {
    // 资金账号
    in_out property <string> accountid;

    // 是否买入
    in_out property <bool> is_buy: true;

    // 是否预埋单
    in_out property <bool> is_yumai: false;

    // 开平标志. 0:开仓; 1:平仓
    in_out property <int> offset: 0;

    // 备兑标志. 1:备兑; 2:非备兑
    in_out property <int> covered: 2;

    // 投保标志. 0:投机; 1:套保; 2:套利
    out property <int> hedge: 0;

    // 交易所编码
    in_out property <string> exchid;

    // 合约编码
    in_out property <string> insid;

    // 合约名称
    in_out property <string> insname: "";

    // 合约编码是否错误
    in_out property <bool> insid_has_error: true;

    // 报单价格条件
    in_out property <int> price_type: 0;
    in_out property <string> price_type_str: "普通限价";

    // 报单价格条件下拉列表. 0:默认; 1:SSE; 2:SZSE; ... 其他交易所做时再实现
    in_out property <int> price_type_model: 0;

    // 报单价格
    in_out property <string> price;

    // 报单价格是否错误
    in_out property <bool> price_has_error: true;

    // 报单数量
    in_out property <string> volume: "1";

    // 报单数量是否错误
    in_out property <bool> volume_has_error: false;

    // 最小成交数量
    out property <string> min_volume: "1";

    // 合约列表
    in_out property <[LineItem]> insid_model;
    in_out property <[LineItem]> all_insid_model;

    // 初始的报单价格条件
    out property <[ListViewItem]> init_price_type_model: [
        { text: "普通限价"}
    ];

    // SSE的报单价格条件
    out property <[ListViewItem]> sse_price_type_model: [
        { text: "普通限价"},
        { text: "市价即时成交剩余转限价"},
        { text: "市价即时成交剩余撤销"},
        { text: "限价全部成交或撤销"},
        { text: "市价全部成交或撤销"},
    ];

    // SZSE的报单价格条件
    out property <[ListViewItem]> szse_price_type_model: [
        { text: "普通限价"},
        { text: "对手方最优价"},
        { text: "本方最优剩余转限价"},
        { text: "最优5档即时成交剩余撤销"},
        { text: "市价即时成交剩余撤销"},
        { text: "市价全部成交或撤销"},
        { text: "限价全部成交或撤销"},
    ];

    // 市场有变化时的回调
    callback exchid_changed();

    // 合约编码有变动时的回调
    callback insid_text_changed() -> bool;

    // 报单价格有变动时的回调
    callback price_text_changed() -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 买卖方向有变动时的回调
    callback dir_changed(/* is_buy */ bool);

    // 价格上下调整时的回调
    callback price_updown_changed(/** upflag */ bool);

    // 数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 用户选择了持仓
    callback sel_position(/** accid */ string, /** exchid */ string, /** insid */ string, /** dir */ string, /** vol */ string, /** covered */ string);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // 价格的小数位数
    in property <int> price_dig: 4;

    // 价格加减的步长
    in property <float> price_step: 0.0001;

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        is_buy ? TradeColor.dir_buy : TradeColor.dir_sell
    }
}

// 组合录入
export global OptTrd_InputOml {
    // 资金账号
    in_out property <string> accountid;

    // 是否组合
    in_out property <bool> is_comb: true;

    // 组合编码
    in_out property <string> combtradeid;

    // 第1腿信息
    in_out property <string> leg1_insid: "";
    in_out property <string> leg1_insname: "";
    in_out property <string> leg1_vol: "0";
    in_out property <string> leg1_dir: "权利";
    in_out property <int> leg1_dir_i32: 50;   // 50:权利仓; 51:义务仓
    in_out property <bool> leg1_has_error: true;
    in_out property <[LineItem]> leg1_insid_model;
    in_out property <[LineItem]> all_leg1_insid_model;

    // 第2腿信息
    in_out property <string> leg2_insid: "";
    in_out property <string> leg2_insname: "";
    in_out property <string> leg2_vol: "0";
    in_out property <string> leg2_dir: "义务";
    in_out property <string> leg2_placeholder_text: "请先输入第1腿合约";
    in_out property <int> leg2_dir_i32: 51;   // 50:权利仓; 51:义务仓
    in_out property <bool> leg2_has_error: true;
    in_out property <[LineItem]> leg2_insid_model;

    // 组合数量
    in_out property <string> volume: "1";

    // 组合数量是否错误
    in_out property <bool> volume_has_error: false;

    // 组合策略
    in_out property <string> strategyid: "CNSJC - 认购牛市价差策略";
    in_out property <int> strategyid_idx: 0;

    // 组合策略列表
    out property <[ListViewItem]> strategyid_model: [
        { text: "CNSJC - 认购牛市价差策略"},
        { text: "CXSJC - 认购熊市价差策略"},
        { text: "PNSJC - 认沽牛市价差策略"},
        { text: "PXSJC - 认沽熊市价差策略"},
        { text: "KS - 跨式策略"},
        { text: "KKS - 宽跨式策略"},
    ];

    // 组合策略有变动时的回调
    callback strategyid_changed() -> bool;

    // 第1腿合约编码变动时的回调
    callback leg1_insid_changed() -> bool;

    // 第2腿合约编码变动时的回调
    callback leg2_insid_changed() -> bool;

    // 组合数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 组合数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 用户选择了持仓
    callback sel_position(/** accid */ string, /** exchid */ string, /** insid */ string, /** dir */ string);

    // 用户选择了组合持仓
    callback sel_comb_position();

    // 清空第1腿合约信息
    public function clear_leg1_info() {
        leg1_insid = "";
        leg1_insname = "";
        leg1_vol = "0";
        leg1_has_error = true;
        leg1_insid_model = [];
    }

    // 清空第2腿合约信息
    public function clear_leg2_info() {
        leg2_insid = "";
        leg2_insname = "";
        leg2_vol = "0";
        leg2_placeholder_text = "请先输入第1腿合约";
        leg2_has_error = true;
        leg2_insid_model = [];
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 获取组合方向颜色
    pure public function get_comb_color() -> color {
        is_comb ? TradeColor.dir_buy : TradeColor.dir_sell
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下为组合预览相关

    in-out property <bool> show_oml_preview;    // 是否显示组合预览
    in_out property <int> op_strategyid_idx: 0; // 组合策略
    in_out property <string> op_strategyid: "CNSJC - 认购牛市价差策略";

    // 组合预览的第1腿信息
    in_out property <string> op_leg1_insid: "";
    in_out property <string> op_leg1_insname: "";
    in_out property <string> op_leg1_dir: "权利";
    in_out property <int> op_leg1_dir_i32: 50;   // 50:权利仓; 51:义务仓
    in_out property <bool> op_leg1_has_error: true;
    in_out property <[LineItem]> op_leg1_insid_model;
    in_out property <[LineItem]> all_op_leg1_insid_model;

    // 组合预览的第2腿信息
    in_out property <string> op_leg2_insid: "";
    in_out property <string> op_leg2_insname: "";
    in_out property <string> op_leg2_dir: "义务";
    in_out property <int> op_leg2_dir_i32: 50;   // 50:权利仓; 51:义务仓
    in_out property <bool> op_leg2_has_error: true;
    in_out property <[LineItem]> op_leg2_insid_model;
    in_out property <string> op_leg2_placeholder_text: "请先输入第1腿合约";

    // 第1腿合约编码变动时的回调
    callback op_leg1_insid_changed() -> bool;

    // 第2腿合约编码变动时的回调
    callback op_leg2_insid_changed() -> bool;

    // 清空第2腿合约信息
    public function clear_op_leg2_info() {
        op_leg2_insid = "";
        op_leg2_insname = "";
        op_leg2_has_error = true;
        op_leg2_insid_model = [];
        op_leg2_placeholder_text = "请先输入第1腿合约";
    }
}

// 行权录入
export global OptTrd_InputExercise {
    // 资金账号
    in_out property <string> accountid;

    // 投保标志. 0:投机; 1:套保; 2:套利
    in_out property <int> hedge: 0;

    // 行权类型. 0:普通行权; 1:组合行权
    in_out property <int> ex_type: 0;

    // 交易所编码
    in_out property <string> exchid;

    // 看涨期权合约(普通行权时使用本字段)
    in_out property <string> call_insid;

    // 看涨期权合约名称
    in_out property <string> call_insname;

    // 看涨期权合约编码是否错误
    in_out property <bool> call_insid_has_error: true;

    // 看跌期权合约
    in_out property <string> put_insid;

    // 看跌期权合约名称
    in_out property <string> put_insname;

    // 看跌期权合约编码是否错误
    in_out property <bool> put_insid_has_error: true;

    // 行权数量
    in_out property <string> volume: "1";

    // 行权数量是否错误
    in_out property <bool> volume_has_error: false;

    // 期权合约列表
    in_out property <[LineItem]> all_insid_model;

    // 看涨期权合约列表
    in_out property <[LineItem]> call_insid_model;
    in_out property <[LineItem]> all_call_insid_model;

    // 看跌期权合约列表
    in_out property <[LineItem]> put_insid_model;
    in_out property <[LineItem]> all_put_insid_model;

    // 行权类型
    out property <[ListViewItem]> ex_type_model: [
        { text: "普通行权"},
        { text: "组合行权"},
    ];

    // 看涨期权合约有变动时的回调
    callback call_insid_txt_changed() -> bool;

    // 看跌期权合约有变动时的回调
    callback put_insid_txt_changed() -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed() -> bool;

    // 数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 用户选择了持仓
    callback sel_position(/** accid */ string, /** exchid */ string, /** insid */ string, /** vol */ string);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        TradeColor.dir_buy
    }
}

// 备兑解锁仓
export global OptTrd_InputStockLock {
    // 资金账号
    in_out property <string> accountid;

    // 是否锁定
    in_out property <bool> is_lock: true;

    // 交易所编码
    in_out property <string> exchid;

    // 合约编码
    in_out property <string> insid;

    // 合约名称
    in_out property <string> insname: "";

    // 合约编码是否错误
    in_out property <bool> insid_has_error: true;

    // 数量
    in_out property <string> volume: "10000";

    // 数量是否错误
    in_out property <bool> volume_has_error: false;

    // 合约列表
    in_out property <[ListViewItem]> insid_model;

    // 合约编码有变动时的回调
    callback insid_text_changed() -> bool;

    // 数量有变动
    callback volume_text_changed() -> bool;

    // 数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // 以下 public function 仅供UI内部调用

    // 获取买卖方向颜色
    pure public function get_dir_color() -> color {
        is_lock ? TradeColor.dir_buy : TradeColor.dir_sell
    }
}

// 报价录入
export global OptTrd_InputQuote {
    // 资金账号
    in_out property <string> accountid;

    // 交易所
    in_out property <string> exchid;

    // 合约编码
    in_out property <string> insid;

    // 合约名称
    in_out property <string> insname: "";

    // 合约编码是否错误
    in_out property <bool> insid_has_error: true;

    // 合约列表
    in_out property <[LineItem]> insid_model;
    in_out property <[LineItem]> all_insid_model;

    // 买 - 方向
    in_out property <string> bid_offset_txt: "开仓";

    // 买 - 方向
    in_out property <int> bid_offset: 0;

    // 买 - 价格
    in_out property <string> bid_price;

    // 买 - 价格是否错误
    in_out property <bool> bid_price_has_error: true;

    // 买 - 数量
    in_out property <string> bid_volume: "1";

    // 买 - 数量是否错误
    in_out property <bool> bid_volume_has_error: false;

    // 卖 - 方向
    in_out property <string> ask_offset_txt: "开仓";

    // 卖 - 方向
    in_out property <int> ask_offset: 0;

    // 卖 - 价格
    in_out property <string> ask_price;

    // 卖 - 价格是否错误
    in_out property <bool> ask_price_has_error: true;

    // 卖 - 数量
    in_out property <string> ask_volume: "1";

    // 卖 - 数量是否错误
    in_out property <bool> ask_volume_has_error: false;

    // 开平方向
    out property <[ListViewItem]> offset_model: [
        { text: "开仓"},
        { text: "平仓"},
    ];

    // 合约编码有变动时的回调
    callback insid_text_changed() -> bool;

    // 报单价格有变动时的回调
    callback price_text_changed(/* is_bid */ bool) -> bool;

    // 报单数量有变动时的回调
    callback volume_text_changed(/* is_bid */ bool) -> bool;

    // 数量上下调整时的回调
    callback volume_updown_changed(/** upflag */ bool, /* is_bid */ bool);

    // 价格上下调整时的回调
    callback bid_price_updown_changed(/** upflag */ bool);

    // 价格上下调整时的回调
    callback ask_price_updown_changed(/** upflag */ bool);

    // 按下了确定键
    callback ok_clicked(/* need_confirm */ bool);

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    // 价格的小数位数
    in property <int> price_dig: 4;

    // 价格加减的步长
    in property <float> price_step: 0.0001;
}

// 撤单结构
struct CancelOrderField {
    exchid: string,
    accountid: string,
    ordersysid: string,
    bs: string,
}

// 撤行权结构
struct CancelExerciseField {
    exchid: string,
    accountid: string,
    ordersysid: string,
}

// 撤单
export global OptTrd_InOrder {
    // 在途订单
    in property <[[string]]> row_data;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_order_clieked(/* ord */ CancelOrderField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_order_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤买/卖单
    // buy_sell: 0:buy; other:sell
    callback cancel_order_bs_clieked(/* buy_sell */ int,  /* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelOrderField> cancel_order;
    public function get_cancel_order(row_index: int) -> CancelOrderField {
        if row_index >= row_data.length {
            cancel_order.exchid = "";
            cancel_order.accountid = "";
            cancel_order.ordersysid = "";
            return cancel_order;
        }

        cancel_order.exchid = row_data[row_index][1];
        cancel_order.accountid = row_data[row_index][0];
        cancel_order.ordersysid = row_data[row_index][10];
        cancel_order.bs = row_data[row_index][5];
        cancel_order
    }

    // 撤选中单的撤单信息
    in_out property <CancelOrderField> sel_cancel_order;
}

// 撤报价
export global OptTrd_InQuote {
    // 在途报价订单
    in property <[[string]]> row_data;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_order_clieked(/* ord */ CancelOrderField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_order_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelOrderField> cancel_order;
    public function get_cancel_order(row_index: int) -> CancelOrderField {
        if row_index >= row_data.length {
            cancel_order.exchid = "";
            cancel_order.accountid = "";
            cancel_order.ordersysid = "";
            return cancel_order;
        }

        cancel_order.exchid = row_data[row_index][1];
        cancel_order.accountid = row_data[row_index][0];
        cancel_order.ordersysid = row_data[row_index][6];
        cancel_order
    }

    // 撤选中单的撤单信息
    in_out property <CancelOrderField> sel_cancel_order;
}

// 撤行权
export global OptTrd_InExercise {
    // 在途行权
    in property <[[string]]> row_data;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_exec_clieked(/* exec */ CancelExerciseField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_exec_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelExerciseField> cancel_exec;
    public function get_cancel_exec(row_index: int) -> CancelExerciseField {
        if row_index >= row_data.length {
            cancel_exec.exchid = "";
            cancel_exec.accountid = "";
            cancel_exec.ordersysid = "";
            return cancel_exec;
        }

        cancel_exec.exchid = row_data[row_index][1];
        cancel_exec.accountid = row_data[row_index][0];
        cancel_exec.ordersysid = row_data[row_index][7];
        cancel_exec
    }

    // 撤选中单的撤单信息
    in_out property <CancelExerciseField> sel_cancel_exec;
}

// 撤组合行权
export global OptTrd_InExerciseComb {
    // 在途组合行权
    in property <[[string]]> row_data;

    // 按下了提示键
    callback req_tip_toggled(/* checked */ bool);

    // 撤选中单
    callback cancel_exec_clieked(/* exec */ CancelExerciseField, /* need_confirm */ bool);

    // 撤全部撤
    callback cancel_exec_all_clieked(/* row_cnt */ int, /* need_confirm */ bool);

    // 撤所有单获取的撤单信息
    private property<CancelExerciseField> cancel_exec;
    public function get_cancel_exec(row_index: int) -> CancelExerciseField {
        if row_index >= row_data.length {
            cancel_exec.exchid = "";
            cancel_exec.accountid = "";
            cancel_exec.ordersysid = "";
            return cancel_exec;
        }

        cancel_exec.exchid = row_data[row_index][1];
        cancel_exec.accountid = row_data[row_index][0];
        cancel_exec.ordersysid = row_data[row_index][8];
        cancel_exec
    }

    // 撤选中单的撤单信息
    in_out property <CancelExerciseField> sel_cancel_exec;
}

// 设置
export global OptTrd_Setting {
    // 当前选中的项
    in_out property <{ parent: int, item: int }> sel_item: { parent: 0, item: 0 };

    // 标题
    in_out property <string> set_txt;

    // 交易请求确认 - 报单
    in_out property <bool> trdreqtip_inputorder: true;
    callback trdreqtip_inputorder_changed();

    // 交易请求确认 - 撤单
    in_out property <bool> trdreqtip_ordercancel: true;
    callback trdreqtip_ordercancel_changed();

    // 交易请求确认 - 组合
    in_out property <bool> trdreqtip_combinsert: true;
    callback trdreqtip_combinsert_changed();

    // 交易请求确认 - 解组合
    in_out property <bool> trdreqtip_combcancecl: true;
    callback trdreqtip_combcancecl_changed();

    // 交易请求确认 - 行权
    in_out property <bool> trdreqtip_inputexec: true;
    callback trdreqtip_inputexec_changed();

    // 交易请求确认 - 撤行权
    in_out property <bool> trdreqtip_execcancel: true;
    callback trdreqtip_execcancel_changed();

    // 交易请求确认 - 备兑解锁仓
    in_out property <bool> trdreqtip_inputstklock: true;
    callback trdreqtip_inputstklock_changed();

    // 交易请求确认 - 报价
    in_out property <bool> trdreqtip_inputquote: true;
    callback trdreqtip_inputquote_changed();

    // 交易请求确认 - 撤报价
    in_out property <bool> trdreqtip_quotecancel: true;
    callback trdreqtip_quotecancel_changed();

    // 异常监控 - 成交持仓比
    in_out property <MonTradePosition> mon_tradeposition;
    in_out property <string> mon_tradeposition_err_tips;
    in_out property <int> mon_tradeposition_err_status;
    callback mon_tradeposition_changed();
    callback reset_mon_tradeposition();

    // 异常监控 - 报撤单
    in_out property <MonOrderinsertcancel> mon_orderinsertcancel;
    in_out property <string> mon_orderinsertcancel_err_tips;
    in_out property <int> mon_orderinsertcancel_err_status;
    callback mon_orderinsertcancel_changed();
    callback reset_mon_orderinsertcancel();

    // 异常监控 - 自成交
    in_out property <MonTradeself> mon_tradeself;
    in_out property <string> mon_tradeself_err_tips;
    in_out property <int> mon_tradeself_err_status;
    callback mon_tradeself_changed();
    callback reset_mon_tradeself();
}
