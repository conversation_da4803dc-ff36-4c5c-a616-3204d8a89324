import { <PERSON>, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { TimeSelect, TimeItem } from "../widgets/time.slint";

export global OptTrd_MonSelfTradeDtl {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> insid;
    in_out property <string> tradeid;
    in_out property <string> ordersysid;
    in_out property <int> starttime_int;
    in_out property <int> endtime_int;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);
}

export component MonSelfTradeDtlPage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = OptTrd_MonSelfTradeDtl.exchid;
        i_le_insid.text = OptTrd_MonSelfTradeDtl.insid;
        i_le_tradeid.text = OptTrd_MonSelfTradeDtl.tradeid;
        i_le_ordsysid.text = OptTrd_MonSelfTradeDtl.ordersysid;
        i_ts_start.time = OptTrd_MonSelfTradeDtl.starttime;
        i_ts_end.time = OptTrd_MonSelfTradeDtl.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> OptTrd_MonSelfTradeDtl.exchid;
                model: CommonModel.exch_opt_model;
            }

            Label {
                text: "合约编码";
                vertical_alignment: center;
            }
            i_le_insid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: OptTrd_MonSelfTradeDtl.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "成交编号";
                vertical_alignment: center;
            }
            i_le_tradeid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: OptTrd_MonSelfTradeDtl.tradeid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "报单编码";
                vertical_alignment: center;
            }
            i_le_ordsysid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: OptTrd_MonSelfTradeDtl.ordersysid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "发生时间";
                vertical_alignment: center;
            }
            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: OptTrd_MonSelfTradeDtl.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: OptTrd_MonSelfTradeDtl.endtime;
                }
            }

            Button {
                text: "查询";
                background: WidgetColor.btn_background;
                width: 80px;

                clicked => {
                    OptTrd_MonSelfTradeDtl.exchid = i_cbx_exch.current_text;
                    OptTrd_MonSelfTradeDtl.insid = i_le_insid.text;
                    OptTrd_MonSelfTradeDtl.tradeid = i_le_tradeid.text;
                    OptTrd_MonSelfTradeDtl.ordersysid = i_le_ordsysid.text;
                    OptTrd_MonSelfTradeDtl.starttime = i_ts_start.time;
                    OptTrd_MonSelfTradeDtl.endtime = i_ts_end.time;
                    OptTrd_MonSelfTradeDtl.starttime_int = i_ts_start.time_int;
                    OptTrd_MonSelfTradeDtl.endtime_int = i_ts_end.time_int;

                    OptTrd_MonSelfTradeDtl.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 5,
                columns: [
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("交易编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("发生时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("合约编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.opt_insid },
                    { title: @tr("买卖方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.buyside },
                    { title: @tr("开平标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.openclose },
                    { title: @tr("成交数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_vol },
                    { title: @tr("成交价格"), alignment: TextHorizontalAlignment.right, width: ColWidth.ord_price },
                    { title: @tr("成交编码"), alignment: TextHorizontalAlignment.center, width: ColWidth.tradeid },
                    { title: @tr("交易所报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_sysid },
                    { title: @tr("本地报单编号"), alignment: TextHorizontalAlignment.center, width: ColWidth.ord_localid },
                    { title: @tr("备兑标志"), alignment: TextHorizontalAlignment.center, width: ColWidth.hedge },
                ]
            };
            row_count: OptTrd_MonSelfTradeDtl.row_data.length;

            get_cell_data(row_index, column_index) => {
                OptTrd_MonSelfTradeDtl.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(OptTrd_MonSelfTradeDtl.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(OptTrd_MonSelfTradeDtl.row_data[row_index]);
                }
            }
        }

        if OptTrd_MonSelfTradeDtl.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> OptTrd_MonSelfTradeDtl.page_index_model;
            item_total <=> OptTrd_MonSelfTradeDtl.item_total;
            page_total <=> OptTrd_MonSelfTradeDtl.page_total;
            page_index <=> OptTrd_MonSelfTradeDtl.page_index;
            page_size <=> OptTrd_MonSelfTradeDtl.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                OptTrd_MonSelfTradeDtl.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                OptTrd_MonSelfTradeDtl.page_size_changed(size);
            }
        }
    }
}
