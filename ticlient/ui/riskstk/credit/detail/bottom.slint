import { Theme, TabView, TabBarItem, Label } from "@zstk/lib.slint";

import { QryOrderPage } from "qry_order.slint";
import { QryTradePage } from "qry_trade.slint";
import { QryWithdrawDepositPage } from "qry_wd.slint";
import { QryPositionTransPage } from "qry_postrans.slint";
import { QryFundTransDtlPage } from "../../../tradestk/qry_fundtransdtl.slint";
import { QryPositionTransDtlPage } from "../../../tradestk/qry_postransdtl.slint";
import { QryInstrumentPage } from "qry_instrument.slint";
import { QryCreditReAmtDtlPage } from "qry_credit_reamt_detail.slint";
import { QryCreditLimitPage } from "qry_credit_limit.slint";
import { QryCreditTcAmtPage } from "qry_credit_tc_amt.slint";
import { QryCreditTcPosPage } from "qry_credit_tc_pos.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";

export component BottomPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [

        { text: "委托" },
        { text: "成交" },

        { text: "融资还款明细" },

        { text: "出入金" },
        { text: "股份划转" },

        { text: "授信额度" },
        { text: "资金头寸" },
        //{ text: "股份头寸" },

        { text: "证券信息" },

        // { text: "资金划转记录" },
        // { text: "股份划转记录" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.letf_bottom_selidx;
        }

        if 0 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryOrderPage { } }
        if 1 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryTradePage { } }
        if 2 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryCreditReAmtDtlPage { } }

        if 3 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryWithdrawDepositPage { } }
        if 4 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryPositionTransPage { } }

        if 5 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryCreditLimitPage {} }
        if 6 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryCreditTcAmtPage {} }
        // if 7 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryCreditTcPosPage {} }

        if 7 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryInstrumentPage { } }
        // if 9 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryFundTransDtlPage { } }
        // if 10 == StkRsk_TabSelIdx.letf_bottom_selidx: Rectangle { QryPositionTransDtlPage { } }

    }
}
