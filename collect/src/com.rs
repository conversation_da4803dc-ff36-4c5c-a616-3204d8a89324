use base64::Engine;

use rsa::{pkcs8::DecodePublicKey, Pkcs1v15Encrypt, RsaPublicKey};

/// IP/MAC
#[derive(Default, Debug, Clone)]
pub struct IpMac {
    pub interface: String,
    pub ipv4: String,
    pub ipv6: String,
    pub mac: String,
}

/// 看穿式信息采集
pub struct CollectInfo {
    /// 加密后的公司信息
    pub tiinfo: String,

    /// 加密后的交易所信息
    pub exinfo: String,
}

/// 获取指定长度的子串
pub(crate) fn sub_str(src: &str, len: usize) -> &str {
    if src.len() > len {
        let mut len = len;
        while len > 0 {
            if let Some(slice) = src.get(0..len) {
                return slice;
            }

            len -= 1;
        }

        return "";
    }
    src
}

/// 将字符串中的@符号替换为空格
pub(crate) fn replace_at_2_space(src: &str) -> String {
    src.replace('@', " ")
}

/// 获取终端类型
pub(crate) fn get_terminal_type() -> Option<&'static str> {
    if cfg!(target_os = "windows") {
        return Some("1");
    }

    if cfg!(target_os = "linux") {
        return Some("2");
    }

    if cfg!(target_os = "macos") {
        return Some("3");
    }

    None
}

/// 获取采集时间(东8区时间 yyyy-MM-dd HH:MM:SS)
pub(crate) fn get_collect_time() -> String {
    let eastern_offset = chrono::FixedOffset::east_opt(8 * 3600).unwrap(); // 8小时偏移量，即东8区
    std::format!(
        "{}",
        chrono::Utc::now().with_timezone(&eastern_offset).format("%Y-%m-%d %H:%M:%S")
    )
}

/// 对明文采集信息进行加密(0:tiinfo; 1:exinfo)
pub(crate) fn encrypt(collect_info: &str) -> Result<CollectInfo, String> {
    let ti_key = r"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAs4x3WzEXD3ElRIzAfI9t
qRDD8FdlLpTqLMh2u5OZdYShZgOx1xysmZ4ZZCiSp44/6MWULJ3N1ktfAs7DCaeK
0SGEeMf+B3UzYVC0vWgNv8wnPc0f/ATCWgsqDgbHAx/9eDL5ho1lXuXxo4tvtOb6
6mB/L+ceKrE+VZkG9n/YgFCiNO0MyYe3aCagoJMblVjyZfnXkMMfY6n9sU9zk76E
mz+316NOACFUEM95sGVO/saM81+OwkSDIGp7wE366FkpEmzeeVlUOdlWHM7SYjSs
4MubX5VzebsLAozc2quwF1MOBn9eRcBXXbaU9EWQOgzkhGQjRsFAi/EFVbQdr9UI
PwIDAQAB
-----END PUBLIC KEY-----
";
    let ex_key = r"-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAi03b6P+4KkAXZI5OELZC
ZzpRL4sGusHGshTFg1afstQDNTeO6kbiPyibKU5GUkSoqho6CHcf5HsaPfK1FHwJ
c+2xMda7EHzZOG93PhcqawPbCOi1zP5qMsGToeeQk0M2c/HXm71aEel4TLXEuQtH
eE8bfYnsDoXHwpJ1EWAceeLf7t8bQGtUegqjGrU3uohntKntPpIC6SvTPwyY7rNS
yi7rt3hr0iRmhJEA7aBpBoxQ3x6nM5PZucHBp3NUFIi3a4d65WrHMS0YHFnW4KS7
ahcn+T57rJjPuSnIG/jkdFMDPf60VbDIEjN0Ejjx5rz1gRYEzkyoGcfazzVpZiwB
mQIDAQAB
-----END PUBLIC KEY-----
";

    let plain_tetx = collect_info.as_bytes();
    let mut collect = CollectInfo {
        tiinfo: "".to_owned(),
        exinfo: "".to_owned(),
    };

    // ti encrypt
    {
        let mut rng = rand::thread_rng();
        let ret = RsaPublicKey::from_public_key_pem(&ti_key);
        if let Err(err) = ret {
            return Err(std::format!("parse ti pubkey failed. {:?}", err));
        }
        let pub_key = ret.unwrap();

        let ret = pub_key.encrypt(&mut rng, Pkcs1v15Encrypt, &plain_tetx);
        if let Err(err) = ret {
            return Err(std::format!("ti encrypt failed. {:?}", err));
        }
        let encrypt_text = ret.unwrap();

        collect.tiinfo = base64::engine::general_purpose::STANDARD.encode(encrypt_text);
    }

    // ex encrypt
    {
        let mut rng = rand::thread_rng();
        let ret = RsaPublicKey::from_public_key_pem(&ex_key);
        if let Err(err) = ret {
            return Err(std::format!("parse ex pubkey failed. {:?}", err));
        }
        let pub_key = ret.unwrap();

        let ret = pub_key.encrypt(&mut rng, Pkcs1v15Encrypt, &plain_tetx);
        if let Err(err) = ret {
            return Err(std::format!("ex encrypt failed. {:?}", err));
        }
        let encrypt_text = ret.unwrap();

        collect.exinfo = base64::engine::general_purpose::STANDARD.encode(encrypt_text);
    }

    Ok(collect)
}
