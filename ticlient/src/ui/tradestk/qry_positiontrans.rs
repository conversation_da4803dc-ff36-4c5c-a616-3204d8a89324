use std::rc::Rc;

use crate::{
    common::{global::TRADE_STK_BUF, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::position_trade_ctp::CtpReqQryPositionTransField;

/// 查询 - 股份划转
pub(super) struct QueryPositionTrans {}

impl QueryPositionTrans {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let qry = app.global::<StkTrd_QryPosTrans>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryPositionTrans::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryPositionTrans::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryPositionTrans::on_page_size_changed(&app_weak, size));

        log::trace!("Query position trans init completed");
    }
}

impl QueryPositionTrans {
    /// 查询事件
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qry_pt = app.global::<StkTrd_QryPosTrans>();

        let req = CtpReqQryPositionTransField {
            ExchID: TIConvert::exch_id(&app_qry_pt.get_exchid()),
            AccountID: "".into(),
            InstrumentID: app_qry_pt.get_insid().into(),
            TransTime0: app_qry_pt.get_starttime_int(),
            TransTime1: app_qry_pt.get_endtime_int(),
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qry_pt.get_page_index()
            }
        };
        let page_size = app_qry_pt.get_page_size();

        let ret = {
            TRADE_STK_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_positrans(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qry_pt.set_page_index_model(page_index_data.into());
            app_qry_pt.set_item_total(ret.0);
            app_qry_pt.set_page_index(0);
            app_qry_pt.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|pt| {
            let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

            let transfalg = {
                if 49 == pt.TransFlag {
                    "主席 -> TITD"
                } else if 50 == pt.TransFlag {
                    "TITD -> 主席"
                } else {
                    "未知"
                }
            };

            // 处理时间
            items.push(TIConvert::transtime(pt.DealTime).into());

            // 请求序号
            items.push(slint::format!("{}", pt.RequestNo));

            // 响应序号
            items.push(slint::format!("{}", pt.RspSeqNo));

            // 股东编码
            items.push(pt.ClientID.clone().into());

            // 合约编码
            items.push(pt.InstrumentID.clone().into());

            // 划转方向
            items.push(slint::format!("{}", transfalg));

            // 划转数量
            items.push(slint::format!("{}", pt.Volume));

            // 交易所
            items.push(slint::format!("{}", TIConvert::exch_name(pt.ExchID)));

            // 资金账号
            items.push(pt.AccountID.clone().into());

            row_data.push(items.into());
        });

        app_qry_pt.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryPositionTrans::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryPositionTrans::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryPositionTrans::on_query(app_weak, true);
    }
}
