import { Theme, TabView } from "@zstk/lib.slint";

import { MonOrderInsertCancelPage } from "mon_orderinsertcancel.slint";
import { MonLimitPositionPage } from "mon_limitposition.slint";
import { MonLimitOpenPage } from "mon_limitopen.slint";
import { MonSelfTradePage } from "mon_selftrade.slint";
import { MonSelfTradeDtlPage } from "mon_selftradedtl.slint";

import { FutTrd_TabSelIdx } from "struct.slint";

export component MonitorPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: [
                { text: "报撤单" },
                { text: "限仓" },
                { text: "开仓" },
                { text: "自成交" },
                { text: "自成交详情" },
            ];

            current_index <=> FutTrd_TabSelIdx.mon_subselidx;
        }

        if 0 == FutTrd_TabSelIdx.mon_subselidx: MonOrderInsertCancelPage { }

        if 1 == FutTrd_TabSelIdx.mon_subselidx: MonLimitPositionPage { }

        if 2 == FutTrd_TabSelIdx.mon_subselidx: MonLimitOpenPage { }

        if 3 == FutTrd_TabSelIdx.mon_subselidx: MonSelfTradePage { }

        if 4 == FutTrd_TabSelIdx.mon_subselidx: MonSelfTradeDtlPage { }
    }
}
