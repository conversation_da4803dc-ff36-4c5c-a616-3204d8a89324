import { Fonts, TableView } from "@zstk/lib.slint";
import { ColWidth, AppCom } from "../struct.slint";

export global StkTrd_MonConvertBonds {
    in property <[[string]]> row_data;

    pure callback get_row_data_color(int, int, string) -> brush;
}

export component MonConvertBondsPage {

    preferred_width: 100%;
    preferred_height: 100%;

    TableView {
        height: 100%;
        width: 100%;

        column_header: {
            height: AppCom.table_header_height,
            background: Colors.transparent,
            font: Fonts.normal,
            fix_head_count: 4,
            columns: [
                { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                { title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                { title: @tr("是否异常"), alignment: TextHorizontalAlignment.center, width: ColWidth.monstatus },

                { title: @tr("买入金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                { title: @tr("卖出金额"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                { title: @tr("比率"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },

                { title: @tr("买入数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                { title: @tr("卖出数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                { title: @tr("最大持仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },
                { title: @tr("当前持仓量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_110 },

                { title: @tr("发生时间"), alignment: TextHorizontalAlignment.right, width: ColWidth.time },
            ]
        };
        row_count: StkTrd_MonConvertBonds.row_data.length;

        get_cell_data(row_index, column_index) => {
            StkTrd_MonConvertBonds.row_data[row_index][column_index]
        }

        get_cell_data_color(row_index, column_index, data) => {
            StkTrd_MonConvertBonds.get_row_data_color(row_index, column_index, data)
        }

        cell_key_copy_pressed(row_index, column_index) => {
            if column_index >= 0 {
                AppCom.copy_str_to_clipboard(StkTrd_MonConvertBonds.row_data[row_index][column_index]);
            } else {
                AppCom.copy_arr_to_clipboard(StkTrd_MonConvertBonds.row_data[row_index]);
            }
        }
    }
}
