import { F<PERSON>s, TableView, Label, ComboBox, LineEdit, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../../../struct.slint";
import { StkRskCredit_CreditTcPos } from "../struct.slint";

export component CreditTcPosPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "头寸类型";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_CreditTcPos.filter_tctype_str;
                model: [
                    { text: "", tag: 0, },
                    { text: "普通头寸", tag: 1, },
                    { text: "专项头寸", tag: 2, },
                ];

                selected => {
                    StkRskCredit_CreditTcPos.filter_tctype = self.current_value.tag;
                    StkRskCredit_CreditTcPos.filter_changed();
                }
            }

            Label {
                text: "交易所";
                vertical_alignment: center;
            }
            ComboBox {
                width: 100px;
                height: AppCom.widgets_height;
                placeholder_text: "请选择";
                current_text <=> StkRskCredit_CreditTcPos.filter_exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkRskCredit_CreditTcPos.filter_changed();
                }
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }
            LineEdit {
                width: 80px;
                height: AppCom.widgets_height;
                placeholder_text: "请输入";
                text <=> StkRskCredit_CreditTcPos.filter_insid;
                action_icon: Icons.close;

                edited => {
                    StkRskCredit_CreditTcPos.filter_changed();
                }

                action => {
                    self.text = "";
                    StkRskCredit_CreditTcPos.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                fix_head_count: 5,
                columns: [
                    { title: @tr("头寸类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("授权数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("可用数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("占用数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("冻结数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("转入数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("转出数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.volume },
                    { title: @tr("证券名称"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insname },
                ]
            };
            row_count: StkRskCredit_CreditTcPos.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkRskCredit_CreditTcPos.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkRskCredit_CreditTcPos.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkRskCredit_CreditTcPos.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkRskCredit_CreditTcPos.row_data[row_index]);
                }
            }
        }
    }
}
