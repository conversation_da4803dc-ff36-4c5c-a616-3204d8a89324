use std::sync::{Arc, RwLock};

use tiapi::protocol_pub::{
    account_trade_ctp, app, exercise_trade_ctp, instrument_trade_ctp, monitor_trade_ctp, order_trade_ctp, position_trade_ctp,
    trade_req_ctp, trade_trade_ctp,
};

use super::tradefutbuf::TradeFutBuf;

pub struct TradeFutSpi {
    buf: Option<Arc<RwLock<TradeFutBuf>>>,
}

impl TradeFutSpi {
    pub fn new() -> Self {
        Self { buf: None }
    }

    pub fn register_buf(&mut self, buf: Arc<RwLock<TradeFutBuf>>) {
        self.buf = Some(buf);
    }
}

impl tiapi::api::tradefutapi::ITradeFutSpi for TradeFutSpi {
    /// 断开连接
    fn on_rtn_disconnect(&self) {
        log::warn!("on_rtn_disconnect");

        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_disconnect();
        }
    }

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: app::AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: app::AppTDServerErrorField) {
        log::warn!(
            "on_rtn_tdservererror. {}, {}, {}, {}, {}",
            tderr.AID,
            tderr.SID,
            tderr.Msg,
            tderr.Type,
            tderr.Time,
        );

        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tdservererror(tderr);
        }
    }

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: app::AppTDSessionTimeoutField) {
        log::warn!(
            "on_rtn_tdsessiontimeout. {}, {}, {}, {}",
            tdst.AID,
            tdst.SID,
            tdst.Msg,
            tdst.Time
        );

        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tdsessiontimeout(tdst);
        }
    }

    /// 委托消息
    fn on_rtn_order(&self, ord: order_trade_ctp::CtpOrderField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_order(ord);
        }
    }

    /// 成交消息
    fn on_rtn_trade(&self, trd: trade_trade_ctp::CtpTradeField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_trade(trd);
        }
    }

    /// 行权消息
    fn on_rtn_execorder(&self, eo: exercise_trade_ctp::CtpExecOrderField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_execorder(eo);
        }
    }

    /// 报价消息
    fn on_rtn_quote(&self, quote: order_trade_ctp::CtpQuoteField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_quote(quote);
        }
    }

    /// 自成交
    fn on_rtn_tradeself(&self, trdslf: monitor_trade_ctp::CtpTradeSelfField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tradeself(trdslf);
        }
    }

    /// 自成交详情
    fn on_rtn_tradeselfdtl(&self, trdslf: monitor_trade_ctp::CtpTradeSelfDtlField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_tradeselfdtl(trdslf);
        }
    }

    /// 报撤单
    fn on_rtn_orderinsertcancel(&self, ordinsertcancel: monitor_trade_ctp::CtpFutOrderInsertCancelField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_orderinsertcancel(ordinsertcancel);
        }
    }

    /// 限持仓
    fn on_rtn_limitposition(&self, lp: monitor_trade_ctp::CtpFutLimitPositionField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_limitposition(lp);
        }
    }

    /// 限开仓
    fn on_rtn_limitopen(&self, lo: monitor_trade_ctp::CtpFutLimitOpenField) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rtn_limitopen(lo);
        }
    }

    /// 报单录入响应消息
    fn on_rsp_inputorder(&self, rsp: trade_req_ctp::CtpRspInputOrderField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputorder(rsp, reqid, islast);
        }
    }

    /// 报单操作响应
    fn on_rsp_inputorderaction(&self, rsp: trade_req_ctp::CtpRspInputOrderActionField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputorderaction(rsp, reqid, islast);
        }
    }

    /// 行权响应
    fn on_rsp_inputexecorder(&self, rsp: trade_req_ctp::CtpRspInputExecOrderField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputexecorder(rsp, reqid, islast);
        }
    }

    /// 行权操作响应
    fn on_rsp_inputexecorderaction(&self, rsp: trade_req_ctp::CtpRspInputExecOrderActionField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputexecorderaction(rsp, reqid, islast);
        }
    }

    /// 报价录入响应消息
    fn on_rsp_inputquote(&self, rsp: trade_req_ctp::CtpRspInputQuoteField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputquote(rsp, reqid, islast);
        }
    }

    /// 报价操作响应
    fn on_rsp_inputquoteaction(&self, rsp: trade_req_ctp::CtpRspInputQuoteActionField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_inputquoteaction(rsp, reqid, islast);
        }
    }

    /// 查询合约响应
    fn on_rsp_qry_instrument(&self, ins: instrument_trade_ctp::CtpRspQryInstrumentField, reqid: i32, islast: bool) {
        if let Some(buf) = self.buf.as_ref() {
            buf.read().unwrap().on_rsp_qry_instrument(ins, reqid, islast);
        }
    }

    /// 查询资金响应消息
    fn on_rsp_qry_tradingaccount(&self, acc: account_trade_ctp::CtpAccountField, reqid: i32, islast: bool) {
        if !acc.AccountID.is_empty() {
            if let Some(buf) = self.buf.as_ref() {
                buf.read().unwrap().on_rsp_qry_tradingaccount(acc, reqid, islast);
            }
        }
    }

    /// 查询资金(分交易所)响应消息
    fn on_rsp_qry_tradingaccountex(&self, acc: account_trade_ctp::CtpAccountField, reqid: i32, islast: bool) {
        if !acc.AccountID.is_empty() {
            if let Some(buf) = self.buf.as_ref() {
                buf.read().unwrap().on_rsp_qry_tradingaccount(acc, reqid, islast);
            }
        }
    }

    /// 查询持仓响应消息
    fn on_rsp_qry_investorposition(&self, pos: position_trade_ctp::CtpPositionField, reqid: i32, islast: bool) {
        if !pos.InvestorID.is_empty() {
            if let Some(buf) = self.buf.as_ref() {
                buf.read().unwrap().on_rsp_qry_investorposition(pos, reqid, islast);
            }
        }
    }
}
