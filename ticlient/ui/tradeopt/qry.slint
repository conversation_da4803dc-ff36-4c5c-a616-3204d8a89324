import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { QryOrderPage } from "qry_order.slint";
import { QryTradePage } from "qry_trade.slint";
import { QryOmlPage } from "qry_oml.slint";
import { QryExercisePage } from "qry_exercise.slint";
import { QryExerciseCombPage } from "qry_exercise_comb.slint";
import { QryQuotePage } from "qry_quote.slint";
import { QryFundTransDtlPage } from "qry_fundtransdtl.slint";
import { QryInstrumentPage } from "qry_instrument.slint";
import { QryWithdrawDepositPage } from "qry_wd.slint";

import { OptTrd_Com, OptTrd_TabSelIdx } from "struct.slint";

export component QueryPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { /* 0 */ text: "委托" },
        { /* 1 */ text: "成交" },
        { /* 2 */ text: "组合" },
        { /* 3 */ text: "行权" },
        { /* 4 */ text: "组合行权" },
        { /* 5 */ text: "出入金" },
        { /* 6 */ text: "合约信息" },
        { /* 7 */ text: "资金划转记录" },
    ];

    private property <[TabBarItem]> tabview_market_items: [
        { /* 0 */ text: "委托" },
        { /* 1 */ text: "成交" },
        { /* 2 */ text: "组合" },
        { /* 3 */ text: "行权" },
        { /* 4 */ text: "组合行权" },
        { /* 5 */ text: "报价" },
        { /* 6 */ text: "出入金" },
        { /* 7 */ text: "合约信息" },
        { /* 8 */ text: "资金划转记录" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: OptTrd_Com.ismarket ? tabview_market_items : tabview_items;
            current_index <=> OptTrd_TabSelIdx.rtn_subselidx;
        }

        if 0 == OptTrd_TabSelIdx.rtn_subselidx: QryOrderPage { }

        if 1 == OptTrd_TabSelIdx.rtn_subselidx: QryTradePage { }

        if 2 == OptTrd_TabSelIdx.rtn_subselidx: QryOmlPage { }

        if 3 == OptTrd_TabSelIdx.rtn_subselidx: QryExercisePage { }

        if 4 == OptTrd_TabSelIdx.rtn_subselidx: QryExerciseCombPage { }

        if 5 == OptTrd_TabSelIdx.rtn_subselidx && !OptTrd_Com.ismarket: QryWithdrawDepositPage { }
        if 5 == OptTrd_TabSelIdx.rtn_subselidx && OptTrd_Com.ismarket: QryQuotePage { }

        if 6 == OptTrd_TabSelIdx.rtn_subselidx && !OptTrd_Com.ismarket: QryInstrumentPage { }
        if 6 == OptTrd_TabSelIdx.rtn_subselidx && OptTrd_Com.ismarket: QryWithdrawDepositPage { }

        if 7 == OptTrd_TabSelIdx.rtn_subselidx && !OptTrd_Com.ismarket: QryFundTransDtlPage { }
        if 7 == OptTrd_TabSelIdx.rtn_subselidx && OptTrd_Com.ismarket: QryInstrumentPage { }

        if 8 == OptTrd_TabSelIdx.rtn_subselidx && OptTrd_Com.ismarket: QryFundTransDtlPage { }
    }
}
