use crate::protocol_pub::login_trade_ctp::CtpRspLoginField;
use serde::{Deserialize, Serialize};

#[derive(Default)]
pub(crate) struct LoginStatus {
    /// 登录状态: -1:失败; 1:成功
    pub status: i32,

    /// session id
    pub sid: String,

    /// user id
    pub uid: String,

    /// 登录失败后的错误信息
    pub em: String,
}
impl LoginStatus {
    /// 登录成功
    pub const SUCCESS: i32 = 1;

    /// 登录失败
    pub const FAILED: i32 = -1;
}
impl LoginStatus {
    /// 设置登录结果
    pub fn set_login(&mut self, status: i32, em: &str) {
        self.status = status;
        self.em = em.to_owned();
        if LoginStatus::SUCCESS != status {
            self.set_sid("");
            self.set_uid("");
        }
    }

    /// 设置成功后的sid
    pub fn set_sid(&mut self, sid: &str) {
        self.sid = sid.to_owned();
    }

    /// 设置成功后的uid
    pub fn set_uid(&mut self, uid: &str) {
        self.uid = uid.to_owned();
    }
}

/// HTTP请求登录响应(CTP)
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct PTCtpRspLogin {
    pub ec: i32,
    pub em: String,
    pub cnt: i32,
    pub func: i32,
    pub sid: String,                // SessionID
    pub arr: Vec<CtpRspLoginField>, // 最多5处
}

/// WS请求登录
#[derive(Default, Serialize)]
pub(crate) struct PTWSReqLogin {
    /// 用户名
    #[serde(skip_serializing_if = "String::is_empty")]
    pub userid: String,

    /// MAC地址
    #[serde(skip_serializing_if = "String::is_empty")]
    pub macaddress: String,

    /// Session ID
    #[serde(skip_serializing_if = "String::is_empty")]
    pub sessionid: String,
}

/// WS请求登录响应
#[derive(Default, Deserialize)]
#[serde[default]]
pub(crate) struct PTWSRspLogin {
    /// 错误码
    pub ec: i32,

    /// 错误信息
    pub em: String,
}
