use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::CreditGaChaMap,
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

/// 轧差数据
struct CreditGaChaDatas {
    /// 过滤数据的按钮是否点击过
    filter_clicked: AtomicBool,
}
impl CreditGaChaDatas {
    pub fn new() -> Self {
        Self {
            filter_clicked: AtomicBool::new(false),
        }
    }
}
static CREDIT_GACHA_DATAS: OnceLock<CreditGaChaDatas> = OnceLock::new();

/// 信用交易 - 资金轧差
pub(in crate::ui::riskstk::credit) struct CreditGaCha {
    /// 显示明细
    show_detail_gacha: AtomicBool,

    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CreditGaChaMap>,
}

impl CreditGaCha {
    pub fn new() -> Self {
        Self {
            show_detail_gacha: AtomicBool::new(false),
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CreditGaChaMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let _ = CREDIT_GACHA_DATAS.set(CreditGaChaDatas::new());

        let app_acc = app.global::<StkRskCredit_GaCha>();

        self.show_detail_gacha
            .store(app.global::<AppInerArgs>().get_show_detail(), Ordering::Relaxed);

        let app_weak = app.as_weak();
        app_acc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditGaCha::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_acc.on_sort_ascending(move |column_index| CreditGaCha::on_sort_ascending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_sort_descending(move |column_index| CreditGaCha::on_sort_descending(&app_weak, column_index));

        let app_weak = app.as_weak();
        app_acc.on_filter_changed(move || CreditGaCha::on_filter_changed(&app_weak));

        log::trace!("CreditGaCha init completed");
    }
}

impl CreditGaCha {
    /// 请求查询轧差信息
    pub fn qry_credit_gacha(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_credit_gacha() };
        if ret.is_empty() {
            return;
        }

        let show_detail_gacha = self.show_detail_gacha.load(Ordering::Relaxed);

        let mut update_false = false;
        for (key, acc) in ret {
            if show_detail_gacha {
                if 0 == acc.ExchID {
                    continue;
                }
            } else {
                if 0 != acc.ExchID {
                    continue;
                }
            }

            update_false = true;
            self.data_map.insert(key, acc);
        }

        if update_false {
            self.data_changed.store(true, Ordering::Relaxed);
        }
    }
}

impl CreditGaCha {
    /// 过滤
    fn on_filter_changed(app_weak: &Weak<App>) {
        CREDIT_GACHA_DATAS
            .get()
            .unwrap()
            .filter_clicked
            .store(true, Ordering::Relaxed);
    }

    /// 升序
    fn on_sort_ascending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<StkRskCredit_GaCha>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_a_num.total_cmp(&c_b_num)
            } else {
                c_a.cmp(&c_b)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 降序
    fn on_sort_descending(app_weak: &Weak<App>, column_index: i32) {
        let app = app_weak.unwrap();
        let app_acc = app.global::<StkRskCredit_GaCha>();
        let row_data = app_acc.get_row_data();
        let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
            let c_a = r_a.row_data(column_index as usize).unwrap();
            let c_b = r_b.row_data(column_index as usize).unwrap();

            let c_a_num = c_a.replace(",", "").parse::<f64>();
            if let Ok(c_a_num) = c_a_num {
                let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                c_b_num.total_cmp(&c_a_num)
            } else {
                c_b.cmp(&c_a)
            }
        }));
        app_acc.set_row_data(sort_model.into());
    }

    /// 获取单元格字体颜色
    fn on_get_row_data_color(app_weak: &Weak<App>, row_index: i32, column_index: i32, data: SharedString) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        if let Ok(var) = data.replace(",", "").parse::<f64>() {
            if 2 == column_index {
                if var > 0. {
                    return nc.get_error();
                }
                return nc.get_normal();
            }

            if 6 == column_index {
                if var >= 0. {
                    return nc.get_normal();
                }
                return nc.get_error();
            }
        }

        nc.get_default()
    }
}

impl CreditGaCha {
    /// 更新
    pub async fn update(&self, app_weak: Weak<App>) {
        let acc_gacha_datas = CREDIT_GACHA_DATAS.get().unwrap();

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                acc_gacha_datas.filter_clicked.store(false, Ordering::Relaxed);
                self.data_map.clone()
            } else {
                if acc_gacha_datas
                    .filter_clicked
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    self.data_map.clone()
                } else {
                    Arc::new(CreditGaChaMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

            let app_acc = app.global::<StkRskCredit_GaCha>();

            let filter_accid = app_acc.get_filter_accid().as_str().to_owned();

            data_map_clone.iter().for_each(|acc| {
                if !filter_accid.is_empty() {
                    if !acc.AccountID.contains(&filter_accid) {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 交易所
                items.push(TIConvert::exch_name(acc.ExchID).into());

                // 资金账号
                items.push(acc.AccountID.clone().into());

                // 总负债
                items.push(TIConvert::format_f(acc.XYAllDebt, Config::AMT_DIGITS).into());

                // 初始负债
                items.push(TIConvert::format_f(acc.XYInitDebt, Config::AMT_DIGITS).into());

                // 当日已还
                items.push(TIConvert::format_f(acc.XYInDebt, Config::AMT_DIGITS).into());

                // 当日借出
                items.push(TIConvert::format_f(acc.XYOutDebt, Config::AMT_DIGITS).into());

                // 差额
                items.push(TIConvert::format_f(acc.XYInDebt - acc.XYOutDebt, Config::AMT_DIGITS).into());

                row_data.push(items.into());
            });

            let app_acc = app.global::<StkRskCredit_GaCha>();

            let asc_column_index = app_acc.get_sort_asc_column_index();
            if asc_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(asc_column_index as usize).unwrap();
                    let c_b = r_b.row_data(asc_column_index as usize).unwrap();

                    let c_a_num = c_a.parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.parse::<f64>().unwrap_or_default();
                        c_a_num.total_cmp(&c_b_num)
                    } else {
                        c_a.cmp(&c_b)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            let dec_column_index = app_acc.get_sort_dec_column_index();
            if dec_column_index >= 0 {
                let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                    let c_a = r_a.row_data(dec_column_index as usize).unwrap();
                    let c_b = r_b.row_data(dec_column_index as usize).unwrap();

                    let c_a_num = c_a.replace(",", "").parse::<f64>();
                    if let Ok(c_a_num) = c_a_num {
                        let c_b_num = c_b.replace(",", "").parse::<f64>().unwrap_or_default();
                        c_b_num.total_cmp(&c_a_num)
                    } else {
                        c_b.cmp(&c_a)
                    }
                }));
                app_acc.set_row_data(sort_model.into());
                return;
            }

            app_acc.set_row_data(row_data.into());
        });
    }
}
