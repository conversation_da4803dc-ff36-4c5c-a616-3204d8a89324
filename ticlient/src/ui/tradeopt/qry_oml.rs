use std::{mem::swap, rc::Rc};

use crate::{
    common::{
        config::Config,
        ctptype::CTPType,
        global::{INS, TRADE_OPT_BUF},
        openselfile,
        ticonvert::TIConvert,
        titype::TIType,
    },
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::order_trade_ctp::{CtpOmlField, CtpReqQryOmlField};

/// 查询 - 组合
pub(super) struct QueryOml {}

impl QueryOml {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let app_qryoml = app.global::<OptTrd_QryOml>();

        let app_weak = app.as_weak();
        app_qryoml.on_qry_clieked(move || QueryOml::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        app_qryoml.on_page_index_changed(move |index| QueryOml::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        app_qryoml.on_page_size_changed(move |size| QueryOml::on_page_size_changed(&app_weak, size));

        let app_weak = app.as_weak();
        app_qryoml.on_export_clicked(move |etype| QueryOml::on_export_clicked(&app_weak, etype));

        log::trace!("Query oml init completed");
    }
}

impl QueryOml {
    /// 获取一行数据
    fn get_row_items(oml: &CtpOmlField) -> Rc<VecModel<SharedString>> {
        let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

        let mut leginsid1; // 第1腿合约
        let mut leginsid2; // 第2腿合约
        let mut legside1 = 0; // 第1腿合约方向
        let mut legside2 = 0; // 第2腿合约方向
        let mut strategy = 0; // 组合策略
        if let Some(tmp) = oml.InstrumentID.split_once('&') {
            leginsid1 = tmp.0.to_owned();
            leginsid2 = tmp.1.to_owned();

            let ins = {
                let ins = INS.get().unwrap().read().unwrap();
                let ins1 = ins.get_ins_with_exchname(&oml.ExchangeID, &leginsid1);
                let ins2 = ins.get_ins_with_exchname(&oml.ExchangeID, &leginsid2);
                (ins1, ins2)
            };
            if ins.0.is_some() && ins.1.is_some() {
                let ins1 = ins.0.unwrap();
                let ins2 = ins.1.unwrap();

                if CTPType::SIDE_BUY == oml.Direction {
                    if TIType::OPTIONS_CALL == ins1.optionstype {
                        strategy = TIType::STRATEGY_CNSJC;
                        legside1 = TIType::LEGSIDE_LONG_L;
                        legside2 = TIType::LEGSIDE_SHORT_S;
                    } else {
                        strategy = TIType::STRATEGY_PXSJC;
                        legside1 = TIType::LEGSIDE_LONG_L;
                        legside2 = TIType::LEGSIDE_SHORT_S;
                    }
                } else if CTPType::SIDE_SELL == oml.Direction {
                    if TIType::OPTIONS_PUT == ins1.optionstype {
                        strategy = TIType::STRATEGY_PNSJC;
                        legside1 = TIType::LEGSIDE_LONG_L;
                        legside2 = TIType::LEGSIDE_SHORT_S;
                    } else {
                        if TIType::OPTIONS_CALL == ins1.optionstype && TIType::OPTIONS_CALL == ins2.optionstype {
                            strategy = TIType::STRATEGY_CXSJC;
                            legside1 = TIType::LEGSIDE_LONG_L;
                            legside2 = TIType::LEGSIDE_SHORT_S;
                        } else {
                            let mut delta = ins1.strikeprice - ins2.strikeprice;
                            if delta < 0.0 {
                                delta *= -1.0;
                            }

                            if delta < Config::INVALID_PRICE {
                                strategy = TIType::STRATEGY_KS;
                                legside1 = TIType::LEGSIDE_SHORT_S;
                                legside2 = TIType::LEGSIDE_SHORT_S;
                            } else {
                                strategy = TIType::STRATEGY_KKS;
                                legside1 = TIType::LEGSIDE_SHORT_S;
                                legside2 = TIType::LEGSIDE_SHORT_S;
                            }
                        }
                    }
                }

                // 调整合约顺序(CXSJC, PXSJC在CTP中正好与交易所的规则相反)
                if TIType::STRATEGY_CXSJC == strategy || TIType::STRATEGY_PNSJC == strategy {
                    swap(&mut leginsid1, &mut leginsid2);
                }
            }
        } else {
            leginsid1 = oml.InstrumentID.clone();
            leginsid2 = "".into();
            legside1 = TIType::LEGSIDE_SHORT_S;
            legside2 = -1;
            strategy = if oml.HedgeFlag == CTPType::HEDGE_COVERED {
                TIType::STRATEGY_ZXJ
            } else {
                TIType::STRATEGY_ZBD
            };
        }

        // 资金账号
        items.push(oml.InvestorID.clone().into());

        // 交易所
        items.push(oml.ExchangeID.clone().into());

        // 交易编码
        items.push(oml.ClientID.clone().into());

        // 发生时间
        items.push(oml.ActionTime.clone().into());

        // 组合策略
        items.push(TIConvert::comb_strategy(strategy).into());

        // 第1腿合约编码
        items.push(leginsid1.into());

        // 第1腿方向
        items.push(TIConvert::leg_side(legside1).into());

        // 第2腿合约编码
        items.push(leginsid2.into());

        // 第2腿方向
        if -1 != legside2 {
            items.push(TIConvert::leg_side(legside2).into());
        } else {
            items.push("".into());
        }

        // 指令方向
        items.push(TIConvert::ctp_combdir(oml.CombDirection).into());

        // 数量
        items.push(slint::format!("{}", oml.Volume));

        // 交易所组合编号
        items.push(oml.ComTradeID.clone().into());

        // 本地申请组合编号
        items.push(oml.ActionLocalID.clone().into());

        // 用户代码
        items.push(oml.UserID.clone().into());

        // 备兑标志
        items.push(TIConvert::ctp_hedgeflag2(oml.HedgeFlag).into());

        items
    }

    // 查询
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qryoml = app.global::<OptTrd_QryOml>();

        // 清空上一次结果
        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
        app_qryoml.set_row_data(row_data.into());

        let st = app_qryoml.get_starttime();
        let et = app_qryoml.get_endtime();
        let combdir = app_qryoml.get_combdir();
        let req = CtpReqQryOmlField {
            ExchID: app_qryoml.get_exchid().into(),
            AccountID: "".into(),
            InstrumentID: app_qryoml.get_insid().into(),
            CombDir: TIConvert::ctp_combdir_id(&combdir),
            CombTradeID: app_qryoml.get_combid().into(),
            TransTime0: std::format!("{:02}:{:02}:{:02}", st.hour, st.minute, st.second),
            TransTime0_Int: st.hour * 10000 + st.minute * 100 + st.second,
            TransTime1: std::format!("{:02}:{:02}:{:02}", et.hour, et.minute, et.second),
            TransTime1_Int: et.hour * 10000 + et.minute * 100 + et.second,
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qryoml.get_page_index()
            }
        };
        let page_size = app_qryoml.get_page_size();

        let ret = {
            TRADE_OPT_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_oml(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qryoml.set_page_index_model(page_index_data.into());
            app_qryoml.set_item_total(ret.0);
            app_qryoml.set_page_index(0);
            app_qryoml.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|ord| {
            let items = QueryOml::get_row_items(&ord);
            row_data.push(items.into());
        });

        app_qryoml.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryOml::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryOml::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryOml::on_query(app_weak, true);
    }

    /// 导出
    fn on_export_clicked(app_weak: &Weak<App>, etype: i32) {
        let app = app_weak.unwrap();

        let default_name = std::format!("comb_{}.csv", chrono::Local::now().format("%Y%m%d_%H%M%S"));
        let path = match crate::get_expor_path(&app, &default_name) {
            Some(path) => path,
            None => return,
        };

        let writer = csv::Writer::from_path(path.clone());
        if writer.is_err() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出组合失败\n\n打开文件[{:?}失败]\n{:?}", path, writer.err().unwrap()),
            );
            return;
        }
        let mut writer = writer.unwrap();

        // 导出表头
        if let Err(err) = writer.write_record(&[
            "资金账户",
            "交易所",
            "交易编码",
            "发生时间",
            "组合策略",
            "第1腿合约编码",
            "第1腿方向",
            "第2腿合约编码",
            "第2腿方向",
            "指令方向",
            "数量",
            "交易所组合编码",
            "本地申请组合编码",
            "用户代码",
            "备兑标志",
        ]) {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出组合失败\n\n导出表头失败 \n{:?}", err),
            );
            return;
        }

        // 导出当前页
        if etype == 0 {
            let rowdatas = app.global::<OptTrd_QryOml>().invoke_get_row_data();
            rowdatas.iter().for_each(|rd| {
                let _ = writer.write_record(rd.iter());
            });
        }
        // 导出全部
        else {
            let ret = { TRADE_OPT_BUF.get().unwrap().read().unwrap().qry_oml_all() };
            ret.iter().for_each(|ord| {
                let items = QueryOml::get_row_items(&ord);
                let _ = writer.write_record(items.iter());
            });
        }

        if let Err(err) = writer.flush() {
            show_msg_box(
                &app,
                3,
                Default::default(),
                slint::format!("导出组合失败\n\n保存文件[{:?}]失败]\n{:?}", path, err),
            );
            return;
        }

        show_msg_box(&app, 1, Default::default(), slint::format!("导出组合成功"));
        let _ = openselfile::open_and_select_file(path.to_str().unwrap());
    }
}
