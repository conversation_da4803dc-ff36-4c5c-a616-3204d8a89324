use serde::Serialize;

use crate::protocol::json;

/// 获取交易请求序列化后的JSON字符串
pub(super) fn get_trd_req_serialize_json<T1, T2>(reqbase: &T1, req: &T2) -> crate::Result<String>
where
    T1: Serialize,
    T2: Serialize,
{
    let ret = json::to_string(reqbase);
    if ret.is_err() {
        return Err("Serialize reqbase failed".to_owned());
    }
    let basejson = ret.ok().unwrap();

    let ret = json::to_string(req);
    if ret.is_err() {
        return Err("Serialize req failed".to_owned());
    }
    let mut reqbody = ret.ok().unwrap();
    if reqbody.eq("{}") {
        reqbody = basejson;
    } else {
        reqbody.insert(1, ',');
        reqbody.insert_str(1, &basejson[1..basejson.len() - 1]);
    }
    Ok(reqbody)
}
