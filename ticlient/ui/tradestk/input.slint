import { TabView, TabBarItem } from "@zstk/lib.slint";
import { StkTrd_TabSelIdx, StkTrd_Com } from "struct.slint";

import { InputOrderPage } from "input_order.slint";
import { InputOrderCreditPage } from "input_order_credit.slint";
import { InputFjyOrderPage } from "input_fjy_order.slint";

export component InputPage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "普通交易" },
        { text: "非交易业务" },
    ];

    private property <[TabBarItem]> tabview_credit_items: [
        { text: "交易" },
    ];

    VerticalLayout {
        padding_left: 1px;
        spacing: 2px;

        TabView {
            height: 30px;
            items: StkTrd_Com.iscredit ? tabview_credit_items : tabview_items;
            current_index <=> StkTrd_TabSelIdx.input_selidx;
        }

        if 0 == StkTrd_TabSelIdx.input_selidx && !StkTrd_Com.iscredit: InputOrderPage { }
        if 1 == StkTrd_TabSelIdx.input_selidx: InputFjyOrderPage { }

        if 0 == StkTrd_TabSelIdx.input_selidx && StkTrd_Com.iscredit: InputOrderCreditPage { }
    }
}
