import { <PERSON>, <PERSON>, <PERSON><PERSON>, ComboBox, <PERSON><PERSON><PERSON>, Radius } from "@zstk/lib.slint";
import { AppCom, TradeColor } from "../struct.slint";
import { LineEditEx } from "../widgets/lineeditex.slint";
import { FutTrd_Com, FutTrd_InputExercise } from "struct.slint";

export component InputExercisePage {

    preferred_width: 100%;
    preferred_height: 100%;

    // 处理合约变动
    function do_show_insid_text_changed(is_call: bool) {

        if is_call {
            FutTrd_InputExercise.call_insid_txt_changed();
        } else {
            FutTrd_InputExercise.put_insid_txt_changed();
        }

        FutTrd_InputExercise.volume = 1;
        FutTrd_InputExercise.volume_has_error = false;
    }

    VerticalLayout {
        spacing: 2px;

        Rectangle {
            HorizontalLayout {
                spacing: 5px;

                i_input_order := Rectangle {
                    background: transparent;
                    width: 355px;

                    GridLayout {
                        spacing-vertical: 6px;
                        spacing-horizontal: 5px;
                        padding: 5px;

                        Row {
                            Label {
                                width: 60px;
                                text: "资金账户";
                                vertical-alignment: center;
                            }

                            Label {
                                text: FutTrd_Com.accountid;
                                vertical-alignment: center;
                            }
                        }

                        Row {

                            Label {
                                text: "投保标志";
                                vertical-alignment: center;
                            }

                            Rectangle {
                                clip: true;
                                height: 26px;
                                border_color: TradeColor.selected;
                                border_width: 1px;
                                border_radius: Radius.extra_small;

                                HorizontalLayout {

                                    Rectangle {
                                        background: 0 == FutTrd_InputExercise.hedge ? TradeColor.selected : transparent;

                                        Label {
                                            text: "投机";
                                            vertical_alignment: center;
                                            horizontal_alignment: center;
                                        }

                                        TouchArea {
                                            mouse_cursor: pointer;
                                            clicked => {
                                                FutTrd_InputExercise.hedge = 0;
                                            }
                                        }
                                    }

                                    Rectangle {
                                        width: 1px;
                                        border_color: TradeColor.selected;
                                        border_width: 1px;
                                    }

                                    Rectangle {
                                        background: 1 == FutTrd_InputExercise.hedge ? TradeColor.selected : transparent;

                                        Label {
                                            text: "套保";
                                            vertical_alignment: center;
                                            horizontal_alignment: center;
                                        }

                                        TouchArea {
                                            mouse_cursor: pointer;
                                            clicked => {
                                                FutTrd_InputExercise.hedge = 1;
                                            }
                                        }
                                    }

                                    Rectangle {
                                        width: 1px;
                                        border_color: TradeColor.selected;
                                        border_width: 1px;
                                    }

                                    Rectangle {
                                        background: 2 == FutTrd_InputExercise.hedge ? TradeColor.selected : transparent;

                                        Label {
                                            text: "套利";
                                            vertical_alignment: center;
                                            horizontal_alignment: center;
                                        }

                                        TouchArea {
                                            mouse_cursor: pointer;
                                            clicked => {
                                                FutTrd_InputExercise.hedge = 2;
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        Row {

                            Label {
                                text: "行权类型";
                                vertical-alignment: center;
                            }

                            i_cbx_extype := ComboBox {
                                placeholder_text: "请选择";
                                current_index <=> FutTrd_InputExercise.ex_type;
                                max_popup_height: 200px;
                                model: FutTrd_InputExercise.ex_type_model;
                                read_only: true;

                                init => {
                                    self.select(FutTrd_InputExercise.ex_type);
                                }

                                selected(idx) => {
                                    FutTrd_InputExercise.call_insid = "";
                                    FutTrd_InputExercise.call_insid_has_error = true;
                                    FutTrd_InputExercise.call_insid_txt_changed();

                                    FutTrd_InputExercise.put_insid = "";
                                    FutTrd_InputExercise.put_insid_has_error = true;
                                    FutTrd_InputExercise.put_insid_txt_changed();

                                    FutTrd_InputExercise.volume = 1;
                                    FutTrd_InputExercise.volume_has_error = false;
                                }
                            }
                        }

                        Row {

                            Label {
                                text: (0 == FutTrd_InputExercise.ex_type ? "行权合约" : "看涨合约");
                                vertical-alignment: center;
                            }

                            LineEditEx {
                                max_popup_height: 160px;
                                model <=> FutTrd_InputExercise.call_insid_model;
                                has_error <=> FutTrd_InputExercise.call_insid_has_error;
                                current_text <=> FutTrd_InputExercise.call_insid;
                                placeholder_text: "请输入";

                                edited => {
                                    do_show_insid_text_changed(true);
                                }
                            }
                        }

                        Row {

                            Label {
                                height: i_cbx_extype.current_index >= 1 ? 26px : 0px;
                                visible: i_cbx_extype.current_index >= 1;

                                text: "看跌合约";
                                vertical-alignment: center;
                            }

                            LineEditEx {
                                height: i_cbx_extype.current_index >= 1 ? 26px : 0px;
                                visible: i_cbx_extype.current_index >= 1;
                                max_popup_height: 160px;
                                model <=> FutTrd_InputExercise.put_insid_model;
                                has_error <=> FutTrd_InputExercise.put_insid_has_error;
                                current_text <=> FutTrd_InputExercise.put_insid;
                                placeholder_text: "请输入";

                                edited => {
                                    do_show_insid_text_changed(false);
                                }
                            }
                        }

                        Row {

                            Label {
                                text: "行权数量";
                                vertical-alignment: center;
                            }

                            SpinBox {
                                height: 26px;
                                placeholder_text: "请输入";
                                text <=> FutTrd_InputExercise.volume;
                                has_error: FutTrd_InputExercise.volume_has_error;

                                edited => {
                                    FutTrd_InputExercise.volume_text_changed();
                                }

                                up_down_clicked(upflag) => {
                                    FutTrd_InputExercise.volume_updown_changed(upflag);
                                }
                            }
                        }

                        Row {

                            Label {
                                text: "    重填";
                                vertical_alignment: center;
                                color: Theme.border_focus;

                                TouchArea {
                                    mouse_cursor: pointer;
                                    clicked => {
                                    FutTrd_InputExercise.call_insid = "";
                                    FutTrd_InputExercise.call_insid_has_error = true;
                                    FutTrd_InputExercise.call_insid_txt_changed();

                                    FutTrd_InputExercise.put_insid = "";
                                    FutTrd_InputExercise.put_insid_has_error = true;
                                    FutTrd_InputExercise.put_insid_txt_changed();

                                    FutTrd_InputExercise.volume = 1;
                                    FutTrd_InputExercise.volume_has_error = false;
                                    }
                                }
                            }

                            Button {
                                text: @tr("确定行权");
                                background: FutTrd_InputExercise.get_dir_color();

                                clicked => {
                                    if AppCom.check_td_session_timeout() {
                                        return ;
                                    }

                                    FutTrd_InputExercise.accountid = FutTrd_Com.accountid;
                                    FutTrd_InputExercise.ok_clicked(true);
                                }
                            }
                        }
                    }
                }

                Rectangle {
                    horizontal-stretch: 1;
                }
            }
        }

        Rectangle {
            vertical_stretch: 1;
        }
    }
}
