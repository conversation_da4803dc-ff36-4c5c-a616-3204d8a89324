use serde::Serialize;

use std::{
    sync::{<PERSON>, RwLock},
    thread,
};
use tokio::sync::Mutex;

use crate::{
    api::riskstkapi::{IRiskStkSpi, RiskStkApi},
    protocol::{
        app::{EAppMessageType, PTRtnApp},
        json,
        login::{LoginStatus, PTWSReqLogin, PTWSRspLogin},
        ptstruct::{EMessageType, PTMsgBase, PTReqBase},
    },
    protocol_pub::{
        account_risk_stk::{AccountsStkField, CreditLimitField, CreditTcAmtField, CreditTcPosField, WithdrawDepositStkField},
        app::{AppHeartbeatField, AppTDServerErrorField, AppTDSessionTimeoutField},
        instrument_risk::{ReqQryFJYInstrumentField, ReqQryInstrumentField, RspQryFJYInstrumentField, RspQry<PERSON>nstrumentField},
        login_risk::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>},
        order_risk_stk::OrderStkField,
        position_risk_stk::{
            CreditConcentrationField, CreditContractField, CreditReturnDtlField, PositionStkField, PositionTransStkField,
        },
        trade_risk_stk::TradeStkField,
    },
    request::HttpClient,
    serverinfo::ServerInfo,
    ws::WsClient,
};

/// 现货风控API实现
pub(crate) struct RiskStkApiImpl {
    /// 服务端信息
    svrinfo: ServerInfo,

    /// 回调类
    spi: Arc<dyn IRiskStkSpi>,

    /// 请求客户端
    req: HttpClient,

    /// Ws客户端(仅在登录时使用一次,其他时间段都在解析消息的线程中使用)
    ws: Arc<Mutex<WsClient>>,

    /// 消息解析线程
    parse_thread: Option<thread::JoinHandle<()>>,

    /// 登录成功后的SessionID
    login_status: Arc<RwLock<LoginStatus>>,
}

impl RiskStkApiImpl {
    pub fn new<T: IRiskStkSpi + 'static>(spi: T) -> Self {
        Self {
            svrinfo: ServerInfo::default(),
            spi: Arc::new(spi),
            req: HttpClient::new(),
            ws: Arc::new(Mutex::new(WsClient::new())),
            parse_thread: None,
            login_status: Arc::new(RwLock::new(LoginStatus::default())),
        }
    }

    /// 是否已经登录
    fn is_login(&self) -> bool {
        LoginStatus::SUCCESS == self.login_status.read().unwrap().status
    }

    /// 登录状态
    fn login_status(&self) -> (i32, String) {
        let ls = self.login_status.read().unwrap();
        (ls.status, ls.em.clone())
    }

    /// 设置SID
    fn set_login_id(&self, uid: &str, sid: &str) {
        let mut ls = self.login_status.write().unwrap();
        ls.set_uid(uid);
        ls.set_sid(sid);
    }

    /// 获取登录状态
    fn get_login_id(&self) -> (String, String) {
        let ls = self.login_status.read().unwrap();
        (ls.uid.clone(), ls.sid.clone())
    }

    /// 获取序列化后的JSON字符串
    fn get_serialize_json<T1, T2>(&self, reqbase: &T1, req: &T2) -> crate::Result<String>
    where
        T1: Serialize,
        T2: Serialize,
    {
        let ret = json::to_string(reqbase);
        if ret.is_err() {
            return Err("Serialize reqbase failed".to_owned());
        }
        let basejson = ret.ok().unwrap();

        let ret = json::to_string(req);
        if ret.is_err() {
            return Err("Serialize req failed".to_owned());
        }
        let mut reqbody = ret.ok().unwrap();
        if reqbody.eq("{}") {
            reqbody = basejson;
        } else {
            reqbody.insert(1, ',');
            reqbody.insert_str(1, &basejson[1..basejson.len() - 1]);
        }
        Ok(reqbody)
    }

    /// 发送请求到服务端
    ///
    /// reqid: 请求编号<br>
    /// mt: 消息类型<br>
    /// req: 请求消息<br>
    async fn send_req_to_server<T: serde::Serialize>(&self, mt: EMessageType, reqid: i32, req: &T) -> crate::Result<()> {
        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = super::comfun::get_trd_req_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(&self.svrinfo.get_req_url(), mt, &reqbody)
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        // 解析响应
        let ret: Result<PTMsgBase, _> = json::from_str(&ret.ok().unwrap());
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }
        let ret = ret.ok().unwrap();
        if 0 != ret.ec {
            return Err(ret.em);
        }

        Ok(())
    }

    /// 发送请求到服务端
    ///
    /// reqid: 请求编号<br>
    /// mt: 消息类型<br>
    /// req: 请求消息<br>
    async fn send_req_to_server_with_rsp<T: serde::Serialize>(
        &self,
        mt: EMessageType,
        reqid: i32,
        req: &T,
    ) -> crate::Result<String> {
        // 检测是否登录
        if !self.is_login() {
            return Err("Not login".to_owned());
        }

        // 构建请求消息体
        let loginstatus = self.get_login_id();
        let reqbase = PTReqBase {
            reqid,
            uid: loginstatus.0,
            sid: loginstatus.1,
        };
        let ret = super::comfun::get_trd_req_serialize_json(&reqbase, req);
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }
        let reqbody = ret.ok().unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(&self.svrinfo.get_req_url(), mt, &reqbody)
            .await;
        if ret.is_err() {
            return Err(ret.err().unwrap());
        }

        return Ok(ret.unwrap());
    }
}

#[async_trait::async_trait]
impl RiskStkApi for RiskStkApiImpl {
    /// 初始化
    async fn init(&mut self) {
        let spi = self.spi.clone();
        let login_status = self.login_status.clone();

        let (sender, receiver) = crossbeam_channel::unbounded();

        // 启动消息解析线程
        self.parse_thread = Some(std::thread::spawn(move || {
            RiskStkApiImpl::parse_run(spi, receiver, login_status);
        }));

        self.ws.lock().await.set_msg_sender(sender);
    }

    /// 释放资源
    async fn release(&mut self) {
        log::info!("api release");

        self.ws.lock().await.release();

        if let Some(t) = self.parse_thread.take() {
            let _ = t.join();
        }
    }

    /// 设置服务端信息(请求登录前必须设置)
    async fn set_svrinfo(&mut self, si: &ServerInfo) {
        self.svrinfo = si.clone();
        self.req.set_encon(1 == self.svrinfo.encon);
        self.ws.lock().await.set_svrinfo(&self.svrinfo);
    }

    /// 请求登录
    async fn req_login(&self, req: &ReqLoginField) -> crate::Result<RspLoginField> {
        // 检查当前是否已经登录
        if self.is_login() {
            return Err("Already login".to_string());
        }

        // 1. 请求http登录

        // 序列化
        let ret = json::to_string(&req);
        if ret.is_err() {
            return Err(ret.unwrap_err());
        }
        let reqbody = ret.unwrap();

        // 发送请求
        let ret = self
            .req
            .get_response_body_text(&self.svrinfo.get_req_url(), EMessageType::TIMT_Risk_Req_Login, &reqbody)
            .await?;

        // 反序列化结登录结果
        let ret: Result<RspLoginField, _> = json::from_str(&ret);
        if ret.is_err() {
            return Err(format!("Parse response failed"));
        }
        let mut rsp = ret.unwrap();

        if 0 != rsp.ec {
            return Ok(rsp);
        }

        // 2. WS 登录

        // 序列化登录协议
        let mut wslogin = PTWSReqLogin::default();
        wslogin.userid = req.uid.clone();
        wslogin.sessionid = rsp.sid.clone();
        wslogin.macaddress = req.macaddress.clone();
        let ret = json::to_string(&wslogin);
        if ret.is_err() {
            rsp.ec = -1;
            rsp.em = ret.unwrap_err();
            return Ok(rsp);
        }

        // 发送请求
        let reqbody = ret.unwrap();
        let ret = self.ws.lock().await.req_login(&reqbody).await;
        if ret.is_err() {
            rsp.ec = -1;
            rsp.em = format!("Login to ws failed. {}", ret.err().unwrap());
            return Ok(rsp);
        }

        // 等待10S
        let start = std::time::Instant::now();
        loop {
            let duration = start.elapsed();
            if duration.as_secs() >= 10 {
                break;
            }

            let ret = self.login_status();
            if LoginStatus::SUCCESS == ret.0 {
                break;
            }

            if LoginStatus::FAILED == ret.0 {
                rsp.ec = -1;
                rsp.em = format!("Login to ws failed. {}", ret.1);
                return Ok(rsp);
            }

            thread::sleep(std::time::Duration::from_millis(100));
        }

        if self.is_login() {
            self.set_login_id(&req.uid, &rsp.sid);
        } else {
            rsp.ec = -1;
            rsp.em = format!("Login to ws timeout");
            return Ok(rsp);
        }

        return Ok(rsp);
    }

    /// 请求查询合约
    async fn req_qry_instrument(&self, reqid: i32) -> crate::Result<()> {
        let req = ReqQryInstrumentField::default();
        let rsp = self
            .send_req_to_server_with_rsp(EMessageType::TIMT_Risk_Stk_Req_Qry_Instrument, reqid, &req)
            .await;

        if rsp.is_err() {
            return Err(rsp.err().unwrap());
        }

        let ret: Result<RspQryInstrumentField, _> = json::from_str(&rsp.unwrap());
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }

        for ins in ret.unwrap().array {
            self.spi.on_rsp_qry_instrument(reqid, ins);
        }

        Ok(())
    }

    /// 请求查询非交易业务合约
    async fn req_qry_fjy_instrument(&self, reqid: i32) -> crate::Result<()> {
        let req = ReqQryFJYInstrumentField::default();
        let rsp = self
            .send_req_to_server_with_rsp(EMessageType::TIMT_Risk_Stk_Req_Qry_FJYInstrument, reqid, &req)
            .await;

        if rsp.is_err() {
            return Err(rsp.err().unwrap());
        }

        let ret: Result<RspQryFJYInstrumentField, _> = json::from_str(&rsp.unwrap());
        if ret.is_err() {
            return Err("parse rsp failed".to_owned());
        }

        for ins in ret.unwrap().array {
            self.spi.on_rsp_qry_fly_instrument(reqid, ins);
        }

        Ok(())
    }
}

impl RiskStkApiImpl {
    /// 解析消息
    fn parse_run(
        spi: Arc<dyn IRiskStkSpi>,
        receiver: crossbeam_channel::Receiver<tokio_tungstenite::tungstenite::Message>,
        login_status: Arc<RwLock<LoginStatus>>,
    ) {
        log::info!("RiskStkApiImpl parse_run");

        loop {
            match receiver.recv() {
                Ok(msg) if msg.is_text() => {
                    match msg.to_text() {
                        Ok(raw_msg) => {
                            if let Ok(msgbase) = json::from_str::<PTMsgBase>(raw_msg) {
                                if let Ok(mt) = EMessageType::try_from(msgbase.mt) {
                                    match mt {
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Account => {
                                            RiskStkApiImpl::parse_rtn_account(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Position => {
                                            RiskStkApiImpl::parse_rtn_position(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Order => {
                                            RiskStkApiImpl::parse_rtn_order(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Trade => {
                                            RiskStkApiImpl::parse_rtn_trade(&spi, &msgbase, raw_msg)
                                        }

                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Credit_Contract => {
                                            RiskStkApiImpl::parse_rtn_credit_contract(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Credit_Limit => {
                                            RiskStkApiImpl::parse_rtn_credit_limit(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Credit_TcAmt => {
                                            RiskStkApiImpl::parse_rtn_credit_tcamt(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Credit_TcPos => {
                                            RiskStkApiImpl::parse_rtn_credit_tcpos(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Credit_Concentration => {
                                            RiskStkApiImpl::parse_rtn_credit_concentration(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_Credit_ReturnDtl => {
                                            RiskStkApiImpl::parse_rtn_credit_returndtl(&spi, &msgbase, raw_msg)
                                        }

                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_WithdrawDeposit => {
                                            RiskStkApiImpl::parse_rtn_withdraw_deposit(&spi, &msgbase, raw_msg)
                                        }
                                        EMessageType::TIMT_WS_Risk_Stk_Rtn_PositionTrans => {
                                            RiskStkApiImpl::parse_rtn_position_trans(&spi, &msgbase, raw_msg)
                                        }

                                        EMessageType::TIMT_WS_Com_Rsp_Login => {
                                            let ret = RiskStkApiImpl::parse_ws_com_rsp_login(raw_msg);
                                            log::info!("parse_ws_com_rsp_login: ret:[{:?}]", ret);
                                            login_status.write().unwrap().set_login(ret.0, &ret.1);
                                        }

                                        _ => {
                                            // log::warn!("Unhandled message. [{:?}]", raw_msg)
                                        }
                                    }
                                } else {
                                    // 新的消息
                                }
                            } else {
                                // 格式错误
                            }
                        }
                        Err(err) => {
                            log::warn!("parse_run: ws Message to_text failed. {}", err.to_string());
                        }
                    }
                }
                Ok(msg) if msg.is_close() => {
                    log::warn!("parse_run: proc ws closed");
                    login_status.write().unwrap().set_login(LoginStatus::FAILED, "ws closed");
                    spi.on_rtn_disconnect();
                    break;
                }
                Err(err) => {
                    log::warn!("Error receiving message: {:?}", err);
                    break;
                }
                _ => {}
            }
        }

        log::info!("RiskStkApiImpl parse_run end");
    }

    /// 解析信用合约回报
    fn parse_rtn_credit_contract(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CreditContractField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_credit_contract(rtn);
        } else {
            log::warn!("parse_rtn_credit_contract failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用授权额度
    fn parse_rtn_credit_limit(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CreditLimitField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_credit_limit(rtn);
        } else {
            log::warn!("parse_rtn_credit_limit failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用资金头寸
    fn parse_rtn_credit_tcamt(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CreditTcAmtField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_credit_tcamt(rtn);
        } else {
            log::warn!("parse_rtn_credit_tcamt failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信息股份头寸
    fn parse_rtn_credit_tcpos(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CreditTcPosField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_credit_tcpos(rtn);
        } else {
            log::warn!("parse_rtn_credit_tcpos failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析信用集中度
    fn parse_rtn_credit_concentration(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CreditConcentrationField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_credit_concentration(rtn);
        } else {
            log::warn!(
                "parse_rtn_credit_concentration failed. err:{}, msg:[{}]",
                ret.err().unwrap(),
                msg
            );
        }
    }

    /// 解析信用还款明细
    fn parse_rtn_credit_returndtl(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<CreditReturnDtlField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_credit_returndtl(rtn);
        } else {
            log::warn!("parse_rtn_credit_returndtl failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析委托回报
    fn parse_rtn_order(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<OrderStkField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_order(rtn);
        } else {
            log::warn!("parse_rtn_order failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析成交回报
    fn parse_rtn_trade(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<TradeStkField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_trade(rtn);
        } else {
            log::warn!("parse_rtn_trade failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析持仓回报
    fn parse_rtn_position(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<PositionStkField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_position(rtn);
        } else {
            log::warn!("parse_rtn_position failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 股份划转
    fn parse_rtn_position_trans(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<PositionTransStkField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_position_trans(rtn);
        } else {
            log::warn!("parse_rtn_position_trans failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析资金回报
    fn parse_rtn_account(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<AccountsStkField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_account(rtn);
        } else {
            log::warn!("parse_rtn_account failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 出入金
    fn parse_rtn_withdraw_deposit(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<WithdrawDepositStkField, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_withdrawdeposit(rtn);
        } else {
            log::warn!("parse_rtn_withdraw_deposit failed. err:{}, msg:[{}]", ret.err().unwrap(), msg);
        }
    }

    /// 解析WS端请求登录响应
    fn parse_ws_com_rsp_login(msg: &str) -> (i32, String) {
        let em;
        let ret: Result<PTWSRspLogin, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            if 0 == rsp.ec {
                return (LoginStatus::SUCCESS, "".to_owned());
            } else {
                log::warn!("parse_ws_com_rsp_login: login failed. err:{}, msg:[{}]", rsp.em, msg);
                em = rsp.em;
            }
        } else {
            em = ret.err().unwrap();
            log::warn!("parse_ws_com_rsp_login failed. err:{}, msg:[{}]", em, msg);
        }

        return (LoginStatus::FAILED, em);
    }

    /// 解析系统消息
    fn parse_rtn_app_msg(spi: &Arc<dyn IRiskStkSpi>, msgbase: &PTMsgBase, msg: &str) {
        let ret: Result<PTRtnApp, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            let ret = EAppMessageType::try_from(rtn.at);
            if let Ok(amt) = ret {
                if EAppMessageType::TI_AMT_Heartbeat == amt {
                    let ret: Result<AppHeartbeatField, _> = json::from_str(&rtn.msg);
                    if let Ok(rtn0) = ret {
                        spi.on_rtn_heartbeat(rtn0);
                    } else {
                        log::warn!("parse_rtn_app_msg: parse Heartbeat failed. {}", rtn.msg);
                    }
                } else if EAppMessageType::TI_AMT_TDServerError == amt {
                    let ret: Result<AppTDServerErrorField, _> = json::from_str(&rtn.msg);
                    if let Ok(rtn0) = ret {
                        spi.on_rtn_tdservererror(rtn0);
                    } else {
                        log::warn!("parse_rtn_app_msg: parse TDServerError failed. {}", rtn.msg);
                    }
                } else if EAppMessageType::TIMT_App_TDSessionTimeout == amt {
                    let ret: Result<AppTDSessionTimeoutField, _> = json::from_str(&rtn.msg);
                    if let Ok(rtn0) = ret {
                        spi.on_rtn_tdsessiontimeout(rtn0);
                    } else {
                        log::warn!("parse_rtn_app_msg: parse TDSessionTimeout failed. {}", rtn.msg);
                    }
                } else {
                    // 新类型消息
                }
            } else {
                log::warn!("parse_rtn_app_msg: parse at failed. {}", rtn.at);
            }
        } else {
            log::warn!("parse_rtn_app_msg: parse failed. {}", msg);
        }
    }
}
