import { Theme, Label } from "@zstk/lib.slint";
import { MarketDataDtl } from "mdstruct.slint";

export component MarketDataDtlPage {

    preferred_width: 100%;
    preferred_height: 100%;

    min_width: 280px;

    Rectangle {
        background: transparent;
        border_width: 1px;
        border_radius: 2px;
        border_color: Theme.border;

        VerticalLayout {
            padding-top: 3px;
            padding-bottom: 3px;
            padding_left: 5px;
            padding_right: 5px;
            spacing: 6px;

            // 合约信息
            HorizontalLayout {
                spacing: 10px;

                if 0 == MarketDataDtl.mdtype || "" != MarketDataDtl.insname: Label {
                    text: @tr("{}", MarketDataDtl.insname);
                    vertical_alignment: center;
                    horizontal_alignment: center;
                    color: i_lb_ins.color;
                    overflow: elide;
                    font_size: 1.2rem;
                }

                i_lb_ins := Label {
                    text: @tr("{}.{}", MarketDataDtl.insid, MarketDataDtl.exchid);
                    vertical_alignment: center;
                    horizontal_alignment: center;
                    color: MarketDataDtl.get_color(MarketDataDtl.last_price_falg);
                    overflow: elide;
                }

                Rectangle {
                    horizontal_stretch: 1;
                }
            }

            Rectangle {
                background: Theme.border_spliter;
                height: 1px;
                border_width: 1px;
            }

            // 最新价
            HorizontalLayout {
                spacing: 10px;
                Label {
                    text: @tr("{}", MarketDataDtl.last_price);
                    color: i_lb_ins.color;
                    vertical_alignment: center;
                    horizontal_alignment: left;

                    font_size: 2.2rem;
                    font_weight: 666;

                    TouchArea {
                        mouse_cursor: pointer;
                        clicked => {
                            MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.last_price, 1);
                        }
                    }
                }

                Label {
                    text: @tr("{}", MarketDataDtl.last_price_ud);
                    color: i_lb_ins.color;
                    vertical_alignment: center;
                }

                Label {
                    text: @tr("{}%", MarketDataDtl.last_price_rate);
                    color: i_lb_ins.color;
                    vertical_alignment: center;
                }

                Rectangle {
                    horizontal_stretch: 1;
                }
            }

            // 更新时间
            Label {
                text: @tr("{}", MarketDataDtl.update_time);
                vertical_alignment: top;
            }

            // 常规价格
            Rectangle {

                GridLayout {
                    spacing: 2px;

                    Row {
                        Label {
                            text: @tr("昨{}:", 0 == MarketDataDtl.mdtype ? "收" : "结");
                            vertical_alignment: center;
                        }

                        Label {
                            text: @tr("{}", MarketDataDtl.pre_stl_price);
                            vertical_alignment: center;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.pre_stl_price, 1);
                                }
                            }
                        }

                        Rectangle {
                            width: 10px;
                        }

                        Label {
                            text: @tr("今开:");
                            vertical_alignment: center;
                        }

                        Label {
                            text: @tr("{}", MarketDataDtl.open_price);
                            color: MarketDataDtl.get_color(MarketDataDtl.open_price_flag);
                            vertical_alignment: center;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.open_price, 1);
                                }
                            }
                        }
                    }

                    Row {
                        Label {
                            text: @tr("最低:");
                            vertical_alignment: center;
                        }

                        Label {
                            text: @tr("{}", MarketDataDtl.lowest_price);
                            color: MarketDataDtl.get_color(MarketDataDtl.lowest_price_flag);
                            vertical_alignment: center;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.lowest_price, 1);
                                }
                            }
                        }

                        Rectangle {
                            width: 10px;
                        }

                        Label {
                            text: @tr("最高:");
                            vertical_alignment: center;
                        }

                        Label {
                            text: @tr("{}", MarketDataDtl.highest_price);
                            color: MarketDataDtl.get_color(MarketDataDtl.highest_price_flag);
                            vertical_alignment: center;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.highest_price, 2);
                                }
                            }
                        }
                    }

                    Row {
                        Label {
                            text: @tr("跌停:");
                            vertical_alignment: center;
                        }

                        Label {
                            text: @tr("{}", MarketDataDtl.lower_price);
                            color: MarketDataDtl.get_color(3);
                            vertical_alignment: center;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.lower_price, 1);
                                }
                            }
                        }

                        Rectangle {
                            width: 10px;
                        }

                        Label {
                            text: @tr("涨停:");
                            vertical_alignment: center;
                        }

                        Label {
                            text: @tr("{}", MarketDataDtl.upper_price);
                            color: MarketDataDtl.get_color(1);
                            vertical_alignment: center;

                            TouchArea {
                                mouse_cursor: pointer;
                                clicked => {
                                    MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.upper_price, 2);
                                }
                            }
                        }
                    }
                }
            }

            // 五档价格
            VerticalLayout {
                padding_bottom: 3px;
                spacing: 1px;

                for i in MarketDataDtl.ask.length : HorizontalLayout {
                    Label {
                        text: @tr("卖{}", 5 - i);
                        vertical_alignment: center;
                    }

                    Label {
                        width: 80px;
                        text: @tr("{}", MarketDataDtl.ask[4-i].price);
                        overflow: elide;
                        vertical_alignment: center;
                        horizontal_alignment: left;
                        color: MarketDataDtl.get_color(MarketDataDtl.ask[4-i].falg);

                        TouchArea {
                            mouse_cursor: pointer;
                            clicked => {
                                MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.ask[4-i].price, 2);
                            }
                        }
                    }

                    Label {
                        width: 90px;
                        text: @tr("{}", MarketDataDtl.ask[4-i].volume);
                        overflow: elide;
                        vertical_alignment: center;
                        horizontal_alignment: right;
                    }
                }

                Rectangle {
                    background: Theme.border_spliter;
                    height: 1px;
                    border_width: 1px;
                }

                for i in MarketDataDtl.bid.length : HorizontalLayout {
                    Label {
                        text: @tr("买{}", 1 + i);
                        vertical_alignment: center;
                    }

                    Label {
                        width: 80px;
                        text: @tr("{}", MarketDataDtl.bid[i].price);
                        overflow: elide;
                        vertical_alignment: center;
                        horizontal_alignment: left;
                        color: MarketDataDtl.get_color(MarketDataDtl.bid[i].falg);

                        TouchArea {
                            mouse_cursor: pointer;
                            clicked => {
                                MarketDataDtl.price_clicked(MarketDataDtl.exchid, MarketDataDtl.insid, MarketDataDtl.bid[i].price, 1);
                            }
                        }
                    }

                    Label {
                        width: 90px;
                        text: @tr("{}", MarketDataDtl.bid[i].volume);
                        overflow: elide;
                        vertical_alignment: center;
                        horizontal_alignment: right;
                    }
                }
            }

            Rectangle {
                vertical_stretch: 1;
            }
        }
    }
}
