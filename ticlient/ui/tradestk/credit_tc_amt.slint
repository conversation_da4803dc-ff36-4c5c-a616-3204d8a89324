import { Fonts, TableView, Label, ComboBox } from "@zstk/lib.slint";
import { ColWidth, CommonModel, AppCom } from "../struct.slint";
import { ExportButton } from "../widgets/export.slint";
import { StkTrd_CreditTcAmt } from "struct.slint";

export component CreditTcAmtPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "头寸类型";
                vertical_alignment: center;
            }

            ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> StkTrd_CreditTcAmt.tctype_str;
                model: [
                    { text: "", tag: 0, },
                    { text: "普通头寸", tag: 1, },
                    { text: "专项头寸", tag: 2, },
                ];

                selected => {
                    StkTrd_CreditTcAmt.tctype = self.current_value.tag;
                    StkTrd_CreditTcAmt.filter_changed();
                }
            }

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text <=> StkTrd_CreditTcAmt.exchid;
                model: CommonModel.exch_stk_model;

                selected => {
                    StkTrd_CreditTcAmt.filter_changed();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }

            ExportButton {
                clicked(type) => {
                    StkTrd_CreditTcAmt.export_clicked(type);
                }
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("头寸类型"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("授权头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("可用头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("使用头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("冻结头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转入头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                    { title: @tr("转出头寸"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                ]
            };
            row_count: StkTrd_CreditTcAmt.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_CreditTcAmt.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                StkTrd_CreditTcAmt.get_row_data_color(row_index, column_index, data)
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_CreditTcAmt.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_CreditTcAmt.row_data[row_index]);
                }
            }
        }
    }
}
