import { Theme, TabView } from "@zstk/lib.slint";
import { StkTrd_TabSelIdx } from "struct.slint";
import { CreditLimitAmtPage } from "credit_limit_amt.slint";
import { CreditLimitPosPage } from "credit_limit_pos.slint";
import { CreditTcAmtPage } from "credit_tc_amt.slint";
import { CreditTcPosPage } from "credit_tc_pos.slint";
import { CreditConcentrationPage } from "credit_concentration.slint";

export component CreditCreditPage {

    preferred_width: 100%;
    preferred_height: 100%;

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: [
                { text: " 融资授信额度" },
                { text: "融券授信额度" },
                { text: "集中度" },
            ];

            current_index <=> StkTrd_TabSelIdx.credit_tc_subselidx;
        }

        if 0 == StkTrd_TabSelIdx.credit_tc_subselidx: CreditLimitAmtPage { }

        if 1 == StkTrd_TabSelIdx.credit_tc_subselidx: CreditLimitPosPage { }

        if 2 == StkTrd_TabSelIdx.credit_tc_subselidx: CreditConcentrationPage { }
    }
}
