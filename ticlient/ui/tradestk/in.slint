import { Theme, TabView } from "@zstk/lib.slint";

import { InOrderPage } from "in_order.slint";
import { InFjyOrderPage } from "in_fjy_order.slint";

import { StkTrd_Com, StkTrd_TabSelIdx } from "struct.slint";

export component InPage {

    preferred_width: 100%;
    preferred_height: 100%;

    if StkTrd_Com.iscredit : InOrderPage { }

    if !StkTrd_Com.iscredit : VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);

            items: [
                { text: "普通交易" },
                { text: "非交易业务" },
            ];

            current_index <=> StkTrd_TabSelIdx.in_subselidx;
        }

        if 0 == StkTrd_TabSelIdx.in_subselidx: InOrderPage { }

        if 1 == StkTrd_TabSelIdx.in_subselidx: InFjyOrderPage { }
    }
}
