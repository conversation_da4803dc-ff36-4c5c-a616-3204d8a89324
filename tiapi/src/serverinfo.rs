/// 服务端配置
#[derive(C<PERSON>, Debug)]
pub struct ServerInfo {
    /// 是否加密. 值为1时加密,其他值不加密. 注意:需要与服务端匹配使用
    pub encon: i32,

    /// 服务端地址
    pub host: String,

    /// 请求端口
    pub req_port: i32,
    /// 请求uri
    pub req_uri: String,

    /// 发布端口
    pub pub_port: i32,
    /// 发布uri
    pub pub_uri: String,
}

impl Default for ServerInfo {
    fn default() -> Self {
        Self {
            encon: 1,
            host: String::default(),
            req_port: 0,
            req_uri: String::default(),
            pub_port: 0,
            pub_uri: String::default(),
        }
    }
}

impl ServerInfo {
    /// 获取请求URL
    pub(crate) fn get_req_url(&self) -> String {
        let http = if 1 == self.encon { "https" } else { "http" };
        format!("{}://{}:{}{}", http, self.host, self.req_port, self.req_uri)
    }

    /// 获取请求URL
    pub(crate) fn get_ws_url(&self) -> String {
        let ws = if 1 == self.encon { "wss" } else { "ws" };
        format!("{}://{}:{}{}", ws, self.host, self.pub_port, self.pub_uri)
    }
}
