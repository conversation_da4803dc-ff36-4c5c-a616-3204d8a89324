import { Theme, But<PERSON>, LineEdit } from "@zstk/lib.slint";
import { AppCom, WidgetColor } from "struct.slint";

export component LockUI inherits Rectangle {
    preferred_width: 100%;
    preferred_height: 100%;
    background: Theme.background;

    forward-focus: i_passwd;

    VerticalLayout {
        Rectangle {
            vertical-stretch: 1;
        }

        HorizontalLayout {
            spacing: 10px;

            Rectangle {
                horizontal-stretch: 1;
            }

            i_passwd := LineEdit {
                height: 30px;
                width: 200px;
                input_type: password;
                placeholder_text: "请输入密码";

                edited => {
                    self.placeholder_text = "请输入密码";
                    self.has_error = false;
                    i_btn.background = WidgetColor.btn_background;
                }
            }

            i_btn := Button {
                text: "解锁";
                background: WidgetColor.btn_background;
                width: 60px;

                clicked => {
                    if AppCom.unlocked(i_passwd.text) {
                        AppCom.islocked = false;
                    } else {
                        i_passwd.text = "";
                        i_passwd.placeholder_text = "密码错误, 请重新输入";
                        i_passwd.has_error = true;
                        self.background = Theme.border_error;
                    }
                }
            }

            Rectangle {
                horizontal-stretch: 1;
            }
        }

        Rectangle {
            vertical-stretch: 1;
        }
    }
}
