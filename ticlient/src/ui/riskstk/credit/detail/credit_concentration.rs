use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::CreditConcentrationMap,
    common::{config::Config, global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

type StringSet = dashmap::DashSet<String>;

/// 集中度数据
struct CreditConcentrationDatas {
    filter_changed: AtomicBool,
}
impl CreditConcentrationDatas {
    pub fn new() -> Self {
        Self {
            filter_changed: AtomicBool::new(false),
        }
    }
}
static CREDIT_CONCENTRATION_DATAS: OnceLock<CreditConcentrationDatas> = OnceLock::new();

/// 集中度
pub(in crate::ui::riskstk::credit) struct CreditConcentration {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CreditConcentrationMap>,

    /// 所有的组名称列表
    gname_set: Arc<StringSet>,
}

/// 构建与初始化
impl CreditConcentration {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CreditConcentrationMap::new()),
            gname_set: Arc::new(StringSet::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let _ = CREDIT_CONCENTRATION_DATAS.set(CreditConcentrationDatas::new());

        let app_tc = app.global::<StkRskCredit_CreditConcentration>();

        let app_weak = app.as_weak();
        app_tc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditConcentration::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_tc.on_filter_changed(move || CreditConcentration::on_filter_changed(&app_weak));

        log::trace!("Credit concentration init completed");
    }
}

/// 查询
impl CreditConcentration {
    /// 查询集中度
    pub fn qry_credit_concentration(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_credit_concentration() };
        if ret.is_empty() {
            return;
        }

        for (key, cc) in ret {
            self.gname_set.insert(cc.GName.clone());
            self.data_map.insert(key, cc);
        }

        self.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditConcentration {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(
        app_weak: &Weak<RiskStkCreditDetailPage>,
        row_index: i32,
        column_index: i32,
        data: SharedString,
    ) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 维持担保比例
        if 9 == column_index {
            if "无负债" == data.as_str() {
                return nc.get_normal();
            }

            let var = data.replace(",", "").replace("%", "").parse::<f64>().unwrap_or(1000000.0);
            if var <= 300.0 {
                return nc.get_error();
            } else if var <= 1000.0 {
                return nc.get_caution();
            } else {
                return nc.get_normal();
            }
        }

        // 是否异常
        if 12 == column_index {
            if "是" == data.as_str() {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<RiskStkCreditDetailPage>) {
        CREDIT_CONCENTRATION_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

/// 更新UI
impl CreditConcentration {
    /// 更新过虑条件数据
    async fn update_filter_id(&self, app_weak: &Weak<RiskStkCreditDetailPage>) {
        let gname_set_clone = self.gname_set.clone();

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let app_cc = app.global::<StkRskCredit_CreditConcentration>();

            if gname_set_clone.len() != app_cc.invoke_group_name_model_len() as usize {
                let id_arr: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

                id_arr.push(ListViewItem {
                    text: "".into(),
                    ..Default::default()
                });

                gname_set_clone.iter().for_each(|id| {
                    id_arr.push(ListViewItem {
                        text: id.clone().into(),
                        ..Default::default()
                    });
                });

                app_cc.set_group_name_model(id_arr.into());
            }
        });
    }

    /// 更新集中度
    async fn update_credit_concent(&self, app_weak: &Weak<RiskStkCreditDetailPage>) {
        let cc_datas = CREDIT_CONCENTRATION_DATAS.get().unwrap();

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                cc_datas.filter_changed.store(false, Ordering::Relaxed);
                self.data_map.clone()
            } else {
                if cc_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    self.data_map.clone()
                } else {
                    Arc::new(CreditConcentrationMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            let app_cc = app.global::<StkRskCredit_CreditConcentration>();

            let filter_accid = app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned();
            if filter_accid.is_empty() {
                app_cc.set_row_data(row_data.into());
                return;
            }

            let filter_gname = app_cc.get_filter_group_name().to_string();
            let filter_subindex = app_cc.get_filter_sub_index();
            let filter_exchid = TIConvert::exch_id(app_cc.get_filter_exchid().as_str());
            let filter_insid = app_cc.get_filter_insid().to_string();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|cc| {
                if filter_accid != cc.AccID {
                    return;
                }

                if !filter_gname.is_empty() {
                    if !cc.GName.contains(&filter_gname) {
                        return;
                    }
                }

                if 0 != filter_subindex {
                    if cc.CType != filter_subindex {
                        return;
                    }
                }

                if 0 != filter_exchid {
                    if cc.ExchID != filter_exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !cc.InsID.contains(&filter_insid) {
                        return;
                    }
                }

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 组编码
                items.push(slint::format!("{}", cc.GID));

                // 组名称
                items.push(slint::format!("{}", cc.GName));

                // 类型
                items.push(
                    match cc.CType {
                        1 => "单一证券",
                        2 => "全组",
                        _ => "未知",
                    }
                    .into(),
                );

                // 资金账号
                items.push(slint::format!("{}", cc.AccID));

                // 交易所
                items.push(TIConvert::exch_name(cc.ExchID).into());

                // 股东代码
                items.push(slint::format!("{}", cc.CliID));

                // 证券代码
                items.push(slint::format!("{}", cc.InsID));

                // 市值
                items.push(TIConvert::format_f(cc.Value, Config::AMT_DIGITS).into());

                // 总资产
                items.push(TIConvert::format_f(cc.AValue, Config::AMT_DIGITS).into());

                // 维持担保比例
                items.push(if -1.0 != cc.MRatio {
                    slint::format!("{}%", TIConvert::format_f(cc.MRatio * 100.0, Config::AMT_DIGITS))
                } else {
                    "无负债".into()
                });

                // 集中度上限
                items.push(slint::format!(
                    "{}%",
                    TIConvert::format_f(cc.ULimit * 100.0, Config::AMT_DIGITS)
                ));

                // 集中度比例
                items.push(slint::format!("{}%", TIConvert::format_f(cc.Ratio * 100.0, 4)));

                // 是否异常
                let yc = if cc.Ratio >= cc.ULimit { "是" } else { "否" };
                items.push(slint::format!("{}", yc));

                // 持仓市值
                items.push(TIConvert::format_f(cc.BuyValue, Config::AMT_DIGITS).into());

                // 总持仓市值
                items.push(TIConvert::format_f(cc.ABuyValue, Config::AMT_DIGITS).into());

                // 在途市值
                items.push(TIConvert::format_f(cc.BIValue, Config::AMT_DIGITS).into());

                // 在途总市值
                items.push(TIConvert::format_f(cc.ABIValue, Config::AMT_DIGITS).into());

                // sort_col(仅用于排序)
                items.push(slint::format!(
                    "{}{}{}{}",
                    yc,
                    200000000 - cc.GID,
                    100000000 + cc.CType,
                    cc.InsID
                ));

                row_data.push(items.into());
            });

            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(17).unwrap();
                let c_b = r_b.row_data(17).unwrap();
                c_b.cmp(&c_a)
            }));

            app_cc.set_row_data(sort_model.into());
        });
    }

    /// 更新
    pub async fn update(&self, app_weak: Weak<RiskStkCreditDetailPage>) {
        // 更新过虑条件数据
        self.update_filter_id(&app_weak).await;

        // 更新集中度
        self.update_credit_concent(&app_weak).await;
    }
}
