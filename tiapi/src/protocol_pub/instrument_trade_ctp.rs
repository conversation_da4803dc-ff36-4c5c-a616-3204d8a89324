use super::instrument::{FJYInstrumentField, InstrumentField};
use serde::{Deserialize, Serialize};

/// 请求查询合约
#[derive(Default, Serialize)]
pub struct CtpReqQryInstrumentField {
    // CThostFtdcQryInstrumentField
    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub InstrumentID: String,

    /// 交易所代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeID: String,

    /// 合约在交易所的代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ExchangeInstID: String,

    /// 产品代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub ProductID: String,
}

/// 请求查询合约响应
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct CtpRspQryInstrumentField {
    // CThostFtdcInstrumentField
    pub InstrumentID: String,        // 合约代码
    pub ExchangeID: String,          // 交易所代码
    pub OldInstrumentName: String,   // 合约名称
    pub ExchangeInstID: String,      // 合约在交易所的代码
    pub ProductID: String,           // 产品代码
    pub ProductClass: i32,           // 产品类型
    pub DeliveryYear: i32,           // 交割年份
    pub DeliveryMonth: i32,          // 交割月
    pub MaxMarketOrderVolume: i32,   // 市价单最大下单量
    pub MinMarketOrderVolume: i32,   // 市价单最小下单量
    pub MaxLimitOrderVolume: i32,    // 限价单最大下单量
    pub MinLimitOrderVolume: i32,    // 限价单最小下单量
    pub VolumeMultiple: i32,         // 合约数量乘数
    pub PriceTick: f64,              // 最小变动价位
    pub CreateDate: String,          // 创建日
    pub OpenDate: String,            // 上市日
    pub ExpireDate: String,          // 到期日
    pub StartDelivDate: String,      // 开始交割日
    pub EndDelivDate: String,        // 结束交割日
    pub InstLifePhase: i32,          // 合约生命周期状态
    pub IsTrading: i32,              // 当前是否交易
    pub PositionType: i32,           // 持仓类型
    pub PositionDateType: i32,       // 持仓日期类型
    pub LongMarginRatio: f64,        // 多头保证金率
    pub ShortMarginRatio: f64,       // 空头保证金率
    pub MaxMarginSideAlgorithm: i32, // 是否使用大额单边保证金算法
    pub UnderlyingInstrID: String,   // 基础商品代码
    pub StrikePrice: f64,            // 执行价
    pub OptionsType: i32,            // 期权类型
    pub UnderlyingMultiple: i32,     // 合约基础商品乘数
    pub CombinationType: i32,        // 组合类型
    pub MinBuyVolume: i32,           // 最小买下单单位
    pub MinSellVolume: i32,          // 最小卖下单单位
    pub InstrumentCode: String,      // 合约标识码
    pub InstrumentName: String,      // 合约名称

    // 非ctp协议，扩展字段
    pub exchid: i32,                // 交易所编码
    pub producttype: i32,           // 产品类型
    pub prestlprice: f64,           // 昨结算价
    pub uplmtprice: f64,            // 涨停板价格
    pub lowlmtprice: f64,           // 跌停板价格

    pub tplus1: i32,                // 现货 - 是否为T+1交易
    pub buyordertick: i32,          // 现货 - 买单位数量
    pub sellordertick: i32,         // 现货 - 卖单位数量
    pub fairprice: f64,             // 现货 - 公允价

    pub isfinance: i32,             // 现货 - 融资融券 - 是否为融资标的
    pub issecurity: i32,            // 现货 - 融资融券 - 是否为融券标的
    pub coll_disc_ratio: f64,       // 现货 - 融资融券 - 担保品折算率
    pub fi_margin_ratio: f64,       // 现货 - 融资融券 - 融资保证金比例
    pub sl_margin_ratio: f64,       // 现货 - 融资融券 - 融券保证金比例
    pub enable_fi: i32,             // 现货 - 融资融券 - 是否允许融资
    pub enable_sl: i32,             // 现货 - 融资融券 - 是否允许融券
    pub enable_db: i32,             // 现货 - 融资融券 - 是否允许做抵押物
    pub static_pe: f64,             // 现货 - 融资融券 - 静态市盈率
    pub static_pe_flag: i32,        // 现货 - 融资融券 - 静态市盈率. 0:未超过; 1:超过300或为负
    pub db_ratio: f64,              // 现货 - 融资融券 - 担保物比例
}
impl CtpRspQryInstrumentField {
    /// 转化成公共的合约格式
    pub fn to_ins(&self) -> InstrumentField {
        let mut ins = InstrumentField::default();

        ins.exchid = self.exchid;
        ins.insid = self.InstrumentID.clone();
        ins.name = self.InstrumentCode.clone();
        ins.symbol = self.InstrumentName.clone();
        ins.productid = self.ProductID.clone();
        ins.underlyingid = self.UnderlyingInstrID.clone();
        ins.producttype = self.producttype;
        ins.multiple = self.VolumeMultiple;
        ins.delyear = self.DeliveryYear;
        ins.delmonth = self.DeliveryMonth;
        ins.admonth = 0;
        ins.tr = self.IsTrading;
        ins.createdate = self.CreateDate.clone();
        ins.opendate = self.OpenDate.clone();
        ins.expiredate = self.ExpireDate.clone();
        ins.startdeldate = self.StartDelivDate.clone();
        ins.enddeldate = self.EndDelivDate.clone();
        ins.basisprice = 0f64;
        ins.strikeprice = self.StrikePrice;
        ins.maxmktvol = self.MaxMarketOrderVolume;
        ins.minmktvol = self.MinMarketOrderVolume;
        ins.maxlmtvol = self.MaxLimitOrderVolume;
        ins.minlmtvol = self.MinLimitOrderVolume;
        ins.pricetick = self.PriceTick;
        ins.prestlprice = self.prestlprice;
        ins.uplmtprice = self.uplmtprice;
        ins.lowlmtprice = self.lowlmtprice;
        ins.tp1 = self.tplus1;
        ins.buyordtick = self.buyordertick;
        ins.sellordtick = self.sellordertick;
        ins.fairprice = self.fairprice;
        ins.ifn = self.isfinance;
        ins.isc = self.issecurity;
        ins.colldiskratio = self.coll_disc_ratio;
        ins.fimarginratio = self.fi_margin_ratio;
        ins.slmarginratio = self.sl_margin_ratio;
        ins.enablefi = self.enable_fi;
        ins.ebablesl = self.enable_sl;
        ins.enabledb = self.enable_db;
        ins.static_pe = self.static_pe;
        ins.static_pe_flag = self.static_pe_flag;
        ins.db_ratio = self.db_ratio;

        if 0 != self.OptionsType {
            if 49 == self.OptionsType {
                ins.optionstype = 67; // 'C'
            } else {
                ins.optionstype = 80; // 'P';
            }
        }

        ins.exchname = match ins.exchid {
            1 => "SSE",
            2 => "SZSE",
            3 => "CFFEX",
            4 => "SHFE",
            5 => "INE",
            6 => "DCE",
            7 => "CZCE",
            8 => "GFEX",
            9 => "BSE",
            0 => "",
            _ => "未知",
        }
        .to_owned();

        ins.ptw = {
            let mut ret = 0;
            let ptw = ins.pricetick.to_string();
            if let Some(idx) = ptw.find(".") {
                ret = ptw.len() - idx - 1;
            }
            if ret <= 4 {
                if 0 == ret {
                    ret = 2;
                }
                ret as i32
            } else {
                4
            }
        };

        ins
    }
}

/// 请求查询非交易业务合约
#[derive(Default, Serialize)]
pub struct CtpReqQryFJYInstrumentField {
    /// 交易所代码
    #[serde(skip_serializing_if = "comfun::is_zero_i32")]
    pub exchid: i32,
    /// 合约代码
    #[serde(skip_serializing_if = "String::is_empty")]
    pub instrumentid: String,
}

/// 请求查询非交易合约响应
#[derive(Default, Debug, Deserialize)]
#[serde[default]]
pub struct CtpRspQryFJYInstrumentField {
    pub exchid: i32,   // 交易所代码
    pub insid: String, // 合约代码
    pub name: String,  // 合约名称

    // SSE非交易业务字段
    pub sh_fjytype: String,     // 非交易业务类型
    pub sh_productid: String,   // 产品证券代码
    pub sh_productname: String, // 产品证券名称
    pub sh_startdate: String,   // 非交易订单输入开始日期
    pub sh_enddate: String,     // 非交易订单输入结束日期
    pub sh_tickvol: i32,        // 非交易订单整手数
    pub sh_minvol: i32,         // 非交易订单最小订单数量
    pub sh_maxvol: i32,         // 非交易订单的最大订单数量
    pub sh_price: f64,          // 非交易价格
    pub sh_ipouplmtprice: f64,  // IPO涨停板价格
    pub sh_ipolowlmtprice: f64, // IPO跌停板价格

                                // SZSE非交易业务字段
}
impl CtpRspQryFJYInstrumentField {
    /// 转化成公共的合约格式
    pub fn to_fjyins(&self) -> FJYInstrumentField {
        let mut ins = FJYInstrumentField::default();

        ins.exchid = self.exchid;
        ins.insid = self.insid.clone();
        ins.name = self.name.clone();

        ins.sh_fjytype = self.sh_fjytype.clone();
        ins.sh_productid = self.sh_productid.clone();
        ins.sh_productname = self.sh_productname.clone();
        ins.sh_startdate = self.sh_startdate.clone();
        ins.sh_enddate = self.sh_enddate.clone();
        ins.sh_tickvol = self.sh_tickvol;
        ins.sh_minvol = self.sh_minvol;
        ins.sh_maxvol = self.sh_maxvol;
        ins.sh_price = self.sh_price;
        ins.sh_ipouplmtprice = self.sh_ipouplmtprice;
        ins.sh_ipolowlmtprice = self.sh_ipolowlmtprice;

        ins.exchname = match ins.exchid {
            1 => "SSE",
            2 => "SZSE",
            9 => "BSE",
            _ => "",
        }
        .to_owned();

        ins
    }
}
