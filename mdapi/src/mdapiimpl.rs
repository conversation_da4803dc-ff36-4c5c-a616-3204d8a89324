use crate::{
    mdapi::{IMd<PERSON>pi, MdApi},
    protocol::{
        self,
        ptmd::{
            MdSubscribe<PERSON>ield, PTReqMdLogout, PTRspMdLogin, PTRspMdLogout, PTRspMdSubscribe, PTRspMdUnSubscribe, PTRtnMarketDataL1,
        },
        ptstruct::{EMessageType, PTMsgBase},
    },
    serverinfo,
    ws::WsClient,
};

use protocol::ptmd::{PTReqMdLogin, PTReqMdSubscribe, PTReqMdUnSubscribe};
use serverinfo::MDServerInfo;
use tokio::sync::Mutex;

use std::{
    sync::{Arc, RwLock},
    thread,
};

mod json {
    use serde::{Deserialize, Serialize};

    /// 将结构体序列化成字符串
    pub fn to_string<T: ?Sized + Serialize>(t: &T) -> crate::Result<String> {
        let ret = serde_json::to_string(&t);
        if ret.is_err() {
            return Err("Serialize failed".to_owned());
        }

        Ok(ret.unwrap())
    }

    /// 将字符串反序列化成结构体
    pub fn from_str<'a, T: Deserialize<'a>>(s: &'a str) -> crate::Result<T> {
        let ret = serde_json::from_str(s);
        if ret.is_err() {
            return Err("Deserialize failed".to_owned());
        }

        Ok(ret.unwrap())
    }
}

#[derive(Default)]
pub(crate) struct LoginStatus {
    pub is_login: bool,
    pub sid: String,
    pub uid: String,
}
impl LoginStatus {
    pub fn set_login(&mut self, status: bool) {
        self.is_login = status;
        if !status {
            self.set_sid("");
            self.set_uid("");
        }
    }

    pub fn set_sid(&mut self, sid: &str) {
        self.sid = sid.to_owned();
    }

    pub fn set_uid(&mut self, uid: &str) {
        self.uid = uid.to_owned();
    }
}

/// 行情API的实现
pub(crate) struct MdApiImpl {
    /// 服务端信息
    svrinfo: MDServerInfo,

    /// 回调类
    spi: Arc<dyn IMdSpi>,

    /// Ws客户端
    ws: Arc<Mutex<WsClient>>,

    /// 消息解析线程
    parse_thread: Option<thread::JoinHandle<()>>,

    /// 登录成功后的SessionID
    login_status: Arc<RwLock<LoginStatus>>,
}

impl MdApiImpl {
    /// new
    pub fn new<T: IMdSpi + 'static>(spi: T) -> Self {
        Self {
            svrinfo: MDServerInfo::default(),
            spi: Arc::new(spi),
            ws: Arc::new(Mutex::new(WsClient::new())),
            parse_thread: None,
            login_status: Arc::new(RwLock::new(LoginStatus::default())),
        }
    }

    /// 是否已经登录
    fn is_login(&self) -> bool {
        self.login_status.read().unwrap().is_login
    }

    /// 设置登录状态
    fn set_login_status(&self, ls: bool) {
        self.login_status.write().unwrap().set_login(ls);
    }

    /// 设置SID
    fn set_login_id(&self, uid: &str, sid: &str) {
        let mut ls = self.login_status.write().unwrap();
        ls.set_uid(uid);
        ls.set_sid(sid);
    }

    /// 获取登录状态
    fn get_login_id(&self) -> (String, String) {
        let ls = self.login_status.read().unwrap();
        (ls.uid.clone(), ls.sid.clone())
    }
}

#[async_trait::async_trait]
impl MdApi for MdApiImpl {
    async fn set_svrinfo(&mut self, si: &MDServerInfo) {
        self.svrinfo = si.clone();
        self.ws.lock().await.set_svrinfo(&si);
    }

    async fn init(&mut self) -> crate::Result<()> {
        let spi = self.spi.clone();
        let login_status = self.login_status.clone();

        let (sender, receiver) = crossbeam_channel::unbounded();

        // 启动消息解析线程
        self.parse_thread = Some(std::thread::spawn(move || {
            MdApiImpl::parse_run(spi, receiver, login_status);
        }));

        self.ws.lock().await.run(sender).await
    }

    async fn release(&mut self) {
        log::info!("md api release");

        self.ws.lock().await.release();

        if self.parse_thread.is_some() {
            self.parse_thread.take(); // 触发解析线程退出
        }

        thread::sleep(std::time::Duration::from_secs(1));
    }

    async fn req_login(&self, req: &PTReqMdLogin) -> crate::Result<()> {
        // 检查当前是否已经登录
        if self.is_login() {
            return Err("Already login".to_string());
        }

        let ret = json::to_string(&req);
        if ret.is_err() {
            return Err(ret.unwrap_err());
        }

        self.ws.lock().await.send_msg(ret.ok().unwrap())
    }

    async fn req_logout(&self) -> crate::Result<()> {
        if !self.is_login() {
            return Ok(());
        }

        let req = PTReqMdLogout::default();

        let ret = json::to_string(&req);
        if ret.is_err() {
            return Err(ret.unwrap_err());
        }

        self.ws.lock().await.send_msg(ret.ok().unwrap())
    }

    async fn req_subscribe(&self, iid: &str) -> crate::Result<()> {
        let mut req = PTReqMdSubscribe::default();
        req.MTH = 0;
        req.IID = iid.to_owned();

        let ret = json::to_string(&req);
        if ret.is_err() {
            return Err(ret.unwrap_err());
        }

        self.ws.lock().await.send_msg(ret.ok().unwrap())
    }

    async fn req_unsubscribe(&self, iid: &str) -> crate::Result<()> {
        let mut req = PTReqMdUnSubscribe::default();
        req.MTH = 0;
        req.IID = iid.to_owned();

        let ret = json::to_string(&req);
        if ret.is_err() {
            return Err(ret.unwrap_err());
        }

        self.ws.lock().await.send_msg(ret.ok().unwrap())
    }
}

impl MdApiImpl {
    /// 解析消息
    fn parse_run(
        spi: Arc<dyn IMdSpi>,
        receiver: crossbeam_channel::Receiver<tokio_tungstenite::tungstenite::Message>,
        login_status: Arc<RwLock<LoginStatus>>,
    ) {
        log::info!("MdApiImpl parse_run");

        loop {
            match receiver.recv() {
                Ok(msg) if msg.is_text() => {
                    match msg.to_text() {
                        Ok(raw_msg) => {
                            if let Ok(msgbase) = json::from_str::<PTMsgBase>(raw_msg) {
                                if let Ok(mt) = EMessageType::try_from(msgbase.mt) {
                                    match mt {
                                        EMessageType::TIMT_WS_Md_Rtn_L1 => {
                                            MdApiImpl::parse_ws_md_rtn_l1(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Com_Rtn_App => {
                                            MdApiImpl::parse_ws_com_rtn_app(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Md_Rsp_Subscribe => {
                                            MdApiImpl::parse_ws_md_rsp_subscribe(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Md_Rsp_UnSubscribe => {
                                            MdApiImpl::parse_ws_md_rsp_subscribe(&spi, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Com_Rsp_Login => {
                                            MdApiImpl::parse_ws_com_rsp_login(&spi, &login_status, raw_msg);
                                        }
                                        EMessageType::TIMT_WS_Com_Rsp_Logout => {
                                            MdApiImpl::parse_ws_com_rsp_logout(&spi, &login_status, raw_msg);
                                        }
                                        _ => log::warn!("Unhandled message. [{:?}]", raw_msg),
                                    }
                                } else {
                                    // 新的消息
                                }
                            } else {
                                // 格式错误
                            }
                        }
                        Err(err) => {
                            log::warn!("parse_run: ws Message to_text failed. {}", err.to_string());
                        }
                    }
                }
                Ok(msg) if msg.is_close() => {
                    log::warn!("parse_run: proc ws closed");
                    login_status.write().unwrap().set_login(false);
                    spi.on_rtn_disconnect();
                    break;
                }
                Err(err) => {
                    log::warn!("Error receiving message: {:?}", err);
                    break;
                }
                _ => {}
            }
        }

        log::info!("MdApiImpl parse_run end");
    }

    /// 解析L1行情
    fn parse_ws_md_rtn_l1(spi: &Arc<dyn IMdSpi>, msg: &str) {
        let ret: Result<PTRtnMarketDataL1, _> = json::from_str(msg);
        if let Ok(rtn) = ret {
            spi.on_rtn_market_data_l1(rtn);
        } else {
            log::warn!("parse_ws_md_rtn_l1: parse failed. {}", msg);
        }
    }

    /// 解析系统通知
    fn parse_ws_com_rtn_app(spi: &Arc<dyn IMdSpi>, msg: &str) {}

    /// 解析订阅行情响应
    fn parse_ws_md_rsp_subscribe(spi: &Arc<dyn IMdSpi>, msg: &str) {
        let ret: Result<PTRspMdSubscribe, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            let tmp = MdSubscribeField {
                IID: rsp.IID.clone(),
                MTH: rsp.MTH,
            };
            spi.on_rsp_md_subscribe(rsp.ec, &rsp.em, &tmp, rsp.reqid, rsp.lt);
        } else {
            log::warn!("parse_ws_md_rsp_subscribe: parse failed. {}", msg);
        }
    }

    /// 解析退订阅行情响应
    fn parse_ws_md_rsp_unsubscribe(spi: &Arc<dyn IMdSpi>, msg: &str) {
        let ret: Result<PTRspMdUnSubscribe, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            let tmp = MdSubscribeField {
                IID: rsp.IID.clone(),
                MTH: rsp.MTH,
            };
            spi.on_rsp_md_un_subscribe(rsp.ec, &rsp.em, &tmp, rsp.reqid, rsp.lt);
        } else {
            log::warn!("parse_ws_md_rsp_unsubscribe: parse failed. {}", msg);
        }
    }

    /// 解析WS端请求登录响应
    fn parse_ws_com_rsp_login(spi: &Arc<dyn IMdSpi>, login_status: &Arc<RwLock<LoginStatus>>, msg: &str) {
        let ret: Result<PTRspMdLogin, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            if 0 == rsp.ec {
                let mut ls = login_status.write().unwrap();
                ls.set_login(true);
                ls.set_sid(&rsp.uid);
                ls.set_uid(&rsp.sid);
            } else {
                log::warn!("parse_ws_com_rsp_login: login failed. {}", rsp.em);
            }

            spi.on_rsp_login(rsp.ec, &rsp.em, rsp.reqid, rsp.lt);
        } else {
            log::warn!("parse_ws_com_rsp_login: parse failed. {}", msg);
        }
    }

    /// 解析WS端请求登出响应
    fn parse_ws_com_rsp_logout(spi: &Arc<dyn IMdSpi>, login_status: &Arc<RwLock<LoginStatus>>, msg: &str) {
        let ret: Result<PTRspMdLogout, _> = json::from_str(msg);
        if let Ok(rsp) = ret {
            if 0 == rsp.ec {
                let mut ls = login_status.write().unwrap();
                ls.set_login(false);
            } else {
                log::warn!("parse_ws_com_rsp_logout: logout failed. {}", rsp.em);
            }

            spi.on_rsp_logout(rsp.ec, &rsp.em, rsp.reqid, rsp.lt);
        } else {
            log::warn!("parse_ws_com_rsp_logout: parse failed. {}", msg);
        }
    }
}
