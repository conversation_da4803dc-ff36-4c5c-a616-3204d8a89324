import { Theme, Splitter, Label } from "@zstk/lib.slint";

import { AppCom, AppInerArgs, EOS, ERenderer } from "../../struct.slint";
import { StkRskCredit_Com } from "struct.slint";

import { TopPage } from "./detail/top.slint";
import { MiddlePage } from "./detail/middle.slint";
import { BottomPage } from "./detail/bottom.slint";

export component RiskStkCreditDetailPage inherits Window {

    preferred_width: 1480px;
    preferred_height: 900px;
    title: @tr("EaseRisk - {}", StkRskCredit_Com.accountid);
    icon: @image_url("../../resource/logo.png");
    background: Theme.background;

    private property <length> splitter_size: 2px;
    private property<{family: string, size: length, weight: int}> default_font;

    public function set_font(os: EOS, renderer: ERenderer) {
        default_font = AppCom.get_default_font(os, renderer);
        root.default-font-family = default_font.family;
        root.default-font-size = default_font.size;
        root.default-font-weight = default_font.weight;
    }

    public function trd_accountid_changed(accid: string) {
        StkRskCredit_Com.accountid = accid;
    }

    VerticalLayout {
        i_rec_right_top := Rectangle {
            height: AppInerArgs.show_detail ? 20% : 12%;

            TopPage {}
        }

        i_splitter_ver_mon_1 := Splitter {
            is_vertical: false;
            height: splitter_size;
            ratio: AppInerArgs.show_detail ? 0.2 : 0.12;
            panel2_min_size: i_splitter_ver_mon_2.min_size;
            max_size <=> parent.height;
            panel1_size <=> i_rec_right_top.height;

            changed ratio => {
                i_rec_right_top.height = parent.height * self.ratio;
                i_rec_right_bottom_container.y = i_rec_right_top.height + self.height;
                i_rec_right_bottom_container.height = self.panel2_size;
            }
        }

        i_rec_right_bottom_container := Rectangle {

            VerticalLayout {
                i_rec_right_mid := Rectangle {
                    height: 50%;

                    MiddlePage {}
                }

                i_splitter_ver_mon_2 := Splitter {
                    is_vertical: false;
                    height: splitter_size;
                    ratio: 0.5;
                    max_size <=> parent.height;
                    panel1_size <=> i_rec_right_mid.height;

                    changed ratio => {
                        i_rec_right_mid.height = parent.height * self.ratio;
                        i_rec_right_tottom.y = i_rec_right_mid.height + self.height;
                        i_rec_right_tottom.height = self.panel2_size;
                    }
                }

                i_rec_right_tottom := Rectangle {

                    BottomPage {
                    }
                }
            }
        }
    }

    // HorizontalLayout {
    //     i_rec_left_container := Rectangle {
    //         width: 60%;

    //         VerticalLayout {
    //             i_rec_left_top := Rectangle {
    //                 height: 30%;

    //                 AccountPage {
    //                 }
    //             }

    //             i_splitter_ver_left := Splitter {
    //                 is_vertical: false;
    //                 height: splitter_size;
    //                 ratio: 0.3;
    //                 panel1_min_size: 100px;
    //                 panel2_min_size: i_splitter_ver_mid.min_size;
    //                 max_size <=> parent.height;
    //                 panel1_size <=> i_rec_left_top.height;

    //                 changed ratio => {
    //                     i_rec_left_top.height = parent.height * self.ratio;
    //                     i_rec_left_bottom_container.y = i_rec_left_top.height + self.height;
    //                     i_rec_left_bottom_container.height = self.panel2_size;
    //                 }
    //             }

    //             i_rec_left_bottom_container := Rectangle {

    //                 VerticalLayout {
    //                     i_rec_left_mid := Rectangle {
    //                         height: 50%;

    //                         LeftMiddlePage {
    //                         }
    //                     }

    //                     i_splitter_ver_mid := Splitter {
    //                         is_vertical: false;
    //                         height: splitter_size;
    //                         ratio: 0.5;
    //                         max_size <=> parent.height;
    //                         panel1_size <=> i_rec_left_mid.height;

    //                         changed ratio => {
    //                             i_rec_left_mid.height = parent.height * self.ratio;
    //                             i_rec_left_tottom.y = i_rec_left_mid.height + self.height;
    //                             i_rec_left_tottom.height = self.panel2_size;
    //                         }
    //                     }

    //                     i_rec_left_tottom := Rectangle {

    //                         LeftBottomPage {
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     Splitter {
    //         width: splitter_size;
    //         ratio: 0.6;
    //         max_size <=> parent.width;
    //         panel1_size <=> i_rec_left_container.width;

    //         changed ratio => {
    //             i_rec_left_container.width = self.ratio * parent.width;
    //             i_rec_right_container.x = i_rec_left_container.width + self.width;
    //             i_rec_right_container.width = self.panel2_size;
    //         }
    //     }

    //     i_rec_right_container := Rectangle {
    //         VerticalLayout {
    //             i_rec_right_top := Rectangle {
    //                 height: 30%;

    //                 RightTopPage {}
    //             }

    //             i_splitter_ver_mon_1 := Splitter {
    //                 is_vertical: false;
    //                 height: splitter_size;
    //                 ratio: 0.3;
    //                 panel2_min_size: i_splitter_ver_mon_2.min_size;
    //                 max_size <=> parent.height;
    //                 panel1_size <=> i_rec_right_top.height;

    //                 changed ratio => {
    //                     i_rec_right_top.height = parent.height * self.ratio;
    //                     i_rec_right_bottom_container.y = i_rec_right_top.height + self.height;
    //                     i_rec_right_bottom_container.height = self.panel2_size;
    //                 }
    //             }

    //             i_rec_right_bottom_container := Rectangle {

    //                 VerticalLayout {
    //                     i_rec_right_mid := Rectangle {
    //                         height: 50%;

    //                         RightMiddlePage {}
    //                     }

    //                     i_splitter_ver_mon_2 := Splitter {
    //                         is_vertical: false;
    //                         height: splitter_size;
    //                         ratio: 0.5;
    //                         max_size <=> parent.height;
    //                         panel1_size <=> i_rec_right_mid.height;

    //                         changed ratio => {
    //                             i_rec_right_mid.height = parent.height * self.ratio;
    //                             i_rec_right_tottom.y = i_rec_right_mid.height + self.height;
    //                             i_rec_right_tottom.height = self.panel2_size;
    //                         }
    //                     }

    //                     i_rec_right_tottom := Rectangle {

    //                         RightBottomPage {
    //                         }
    //                     }
    //                 }
    //             }
    //         }
    //     }
    // }
}
