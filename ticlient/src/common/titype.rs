/// TITD类型
pub struct TIType {}

// 交易所
impl TIType {
    /// 上交所
    pub const EXCH_SSE: i32 = 1;

    /// 深交所
    pub const EXCH_SZSE: i32 = 2;

    /// 中金所
    pub const EXCH_CFFEX: i32 = 3;

    /// 上期所
    pub const EXCH_SHFE: i32 = 4;

    /// 上期能源
    pub const EXCH_INE: i32 = 5;

    /// 大商所
    pub const EXCH_DCE: i32 = 6;

    /// 郑商所
    pub const EXCH_CZCE: i32 = 7;

    /// 广期所
    pub const EXCH_GFEX: i32 = 8;

    // 北交所
    pub const EXCH_BSE: i32 = 9;
}

// 开平标志
impl TIType {
    /// 开仓
    pub const OC_OPEN: i32 = 1;

    /// 平仓
    pub const OC_CLOSE: i32 = 2;

    /// 平今
    pub const OC_CLOSE_TODAY: i32 = 3;
}

// 持仓日期类型
impl TIType {
    /// 今日持仓
    pub const PSD_TODAY: i32 = 1;

    /// 历史持仓
    pub const PSD_HIS: i32 = 2;
}

// 持仓方向类型
impl TIType {
    /// 多头(买)
    pub const POSDIR_LONG: i32 = 1;

    /// 空头(卖)
    pub const POSDIR_SHORT: i32 = 2;
}

// 报单价格条件
impl TIType {
    /// 市价
    pub const PRICETYPE_MARKET: i32 = 1;

    /// 限价
    pub const PRICETYPE_LIMIT: i32 = 2;

    /// 本方最优剩余转限价
    pub const PRICETYPE_BESTPRICE: i32 = 3;

    /// 市价剩余转限价
    pub const PRICETYPE_MARKET2LIMIT: i32 = 4;

    /// 最优五档即时成交剩余撤销的市价订单
    pub const PRICETYPE_BEST1: i32 = 5;

    /// 最优五档即时成交剩余转限价的市价订单
    pub const PRICETYPE_BEST2: i32 = 6;

    /// 本方最优价格
    pub const PRICETYPE_BEST3: i32 = 7;

    /// 对手方最优价格
    pub const PRICETYPE_BEST4: i32 = 8;
}

// 订单有效时间类型
impl TIType {
    /// 当日有效
    pub const TIMEINFORCE_GFD: i32 = 1;

    /// 本节有效
    pub const TIMEINFORCE_GIS: i32 = 2;

    /// 即时成交剩余撤销
    pub const TIMEINFORCE_IOC: i32 = 3;

    /// 全部成交否则撤销
    pub const TIMEINFORCE_FOK: i32 = 4;
}

// 成交量类型
impl TIType {
    /// 任何数量
    pub const VC_AV: i32 = 1;

    /// 最小数量
    pub const VC_MV: i32 = 2;

    /// 全部数量
    pub const VC_CV: i32 = 3;
}

// 触发条件类型
impl TIType {
    /// 立即
    pub const CC_IMMEDIATELY: i32 = 1;

    /// 止损
    pub const CC_TOUCH: i32 = 2;
}

// 订单所有类型
impl TIType {
    /// 个人投资者发起
    pub const OWNER_PER: i32 = 1;

    /// 自撮合成交
    pub const OWNER_TS: i32 = 84;

    /// 交易所发起
    pub const OWNER_EX: i32 = 101;

    /// 经营机构（包括其风险管理部门）发起
    pub const OWNER_OP: i32 = 102;

    /// 机构投资者发起
    pub const OWNER_INSTITUTIONAL: i32 = 103;

    /// 自营交易发起
    pub const OWNER_PROPRIETARY: i32 = 104;

    /// 流动性服务提供商发起
    pub const OWNER_MAKER: i32 = 105;

    /// 结算结构发起
    pub const OWNER_CLEAR: i32 = 106;
}

// 买卖方向类型
impl TIType {
    /// 买
    pub const SIDE_BUY: i32 = 1;

    /// 卖
    pub const SIDE_SELL: i32 = 2;

    /// 融资买入
    pub const SIDE_BUY_CREDIT: i32 = 3;

    /// 融券卖出
    pub const SIDE_SELL_CREDIT: i32 = 4;

    /// 买券还券
    pub const SIDE_BUY_RETURN: i32 = 5;

    /// 卖券还款
    pub const SIDE_SELL_RETURN: i32 = 6;

    /// 现券还券
    pub const SIDE_RETURN_STOCK: i32 = 7;

    /// 直接还款
    pub const SIDE_RETURN_FUND: i32 = 8;
}

// 投机套保标志
impl TIType {
    /// 投机
    pub const HEDGE_SPECULATION: i32 = 1;

    /// 套利
    pub const HEDGE_ARBITRAGE: i32 = 2;

    /// 套保
    pub const HEDGE_HEDGE: i32 = 3;

    /// 做市商
    pub const HEDGE_MARKETMAKER: i32 = 4;
}

// 交易编码类型
impl TIType {
    /// 投机
    pub const CLIENT_SPECULATION: i32 = 1;

    /// 套利
    pub const CLIENT_ARBITRAGE: i32 = 2;

    /// 套保
    pub const CLIENT_HEDGE: i32 = 3;
}

// 账户类型
impl TIType {
    /// 现货账户
    pub const ACCOUNT_SK: i32 = 1;

    /// 衍生品账户
    pub const ACCOUNT_OF: i32 = 2;
}

// 订单状态类型
impl TIType {
    /// 已报入
    pub const ORDER_STATUS_SUCCESS: i32 = 1;

    /// 部分成交
    pub const ORDER_STATUS_TRADE: i32 = 2;

    /// 全部成交
    pub const ORDER_STATUS_ALL: i32 = 3;

    /// 已撤单
    pub const ORDER_STATUS_CANCEL: i32 = 4;

    /// 已拒绝
    pub const ORDER_STATUS_REJECT: i32 = 8;

    /// 部成部撤 'x'
    pub const ORDER_STATUS_TRADECANCEL: i32 = 120;
}

// 品种类型
impl TIType {
    /// 股票 'S'
    pub const PRODUCT_STK: i32 = 83;

    /// 期货 'F'
    pub const PRODUCT_FUT: i32 = 70;

    /// 期权 'O'
    pub const PRODUCT_OPT: i32 = 79;

    /// 基金 'E'
    pub const PRODUCT_EU: i32 = 69;

    /// 债券, 集合资产管理计划, 债券预发行, 定向可转债 'D'
    pub const PRODUCT_D: i32 = 68;

    /// 权证 'R'
    pub const PRODUCT_R: i32 = 82;

    /// 公募REITs 'C'
    pub const PRODUCT_CB: i32 = 67;

    /// 国债 'G'
    pub const PRODUCT_GFB: i32 = 71;
}

// 期权类型
impl TIType {
    /// 认购期权
    pub const OPTIONS_CALL: i32 = 67;

    /// 认沽期权
    pub const OPTIONS_PUT: i32 = 80;
}

// 期权行权类型
impl TIType {
    /// 欧式期权
    pub const EXCISE_E: i32 = 69;

    /// 美式期权
    pub const EXCISE_A: i32 = 65;
}

// 备兑标识
impl TIType {
    /// 备兑
    pub const COVERED: i32 = 1;

    /// 非备兑
    pub const COVERED_UN: i32 = 2;
}

// 组合策略
impl TIType {
    /// CNSJC
    pub const STRATEGY_CNSJC: i32 = 1;

    /// CXSJC
    pub const STRATEGY_CXSJC: i32 = 2;

    /// PNSJC
    pub const STRATEGY_PNSJC: i32 = 3;

    /// PXSJC
    pub const STRATEGY_PXSJC: i32 = 4;

    /// KS
    pub const STRATEGY_KS: i32 = 5;

    /// KKS
    pub const STRATEGY_KKS: i32 = 6;

    /// ZBD
    pub const STRATEGY_ZBD: i32 = 100;

    /// ZXJ
    pub const STRATEGY_ZXJ: i32 = 101;
}

// 组合与拆分组合
impl TIType {
    /// 组合 'B'
    pub const COMBED: i32 = 66;

    /// 拆分 'C'
    pub const COMBED_UN: i32 = 67;
}

// 单腿合约方向
impl TIType {
    /// 权力仓
    pub const LEGSIDE_LONG_0: i32 = 1;

    /// 义务仓
    pub const LEGSIDE_SHORT_1: i32 = 2;

    /// 权力仓 'L'
    pub const LEGSIDE_LONG_L: i32 = 76;

    /// 义务仓 'S'
    pub const LEGSIDE_SHORT_S: i32 = 83;
}

// 行权类型
impl TIType {
    /// 普通行权
    pub const EXECT: i32 = 0;

    /// 组合行权
    pub const EXECT_COMBED: i32 = 1;
}

// 非交易业务类型
impl TIType {
    /// 发行
    pub const BUSINESS_TYPE_IN: i32 = 1;

    /// 配股、科创板配售
    pub const BUSINESS_TYPE_R1: i32 = 2;

    /// 配转债
    pub const BUSINESS_TYPE_R4: i32 = 3;

    /// 要约预受
    pub const BUSINESS_TYPE_FS: i32 = 4;

    /// 要约撤销
    pub const BUSINESS_TYPE_FC: i32 = 5;

    /// 开放式基金申购
    pub const BUSINESS_TYPE_OC: i32 = 6;

    /// 开放式基金赎回
    pub const BUSINESS_TYPE_OR: i32 = 7;

    /// 开放式基金认购
    pub const BUSINESS_TYPE_OS: i32 = 8;

    /// 开放式基金转托管
    pub const BUSINESS_TYPE_OT: i32 = 9;

    /// 开放式基金分红设置
    pub const BUSINESS_TYPE_OD: i32 = 10;

    /// 开放式基金转换
    pub const BUSINESS_TYPE_OV: i32 = 11;

    /// 余券划转
    pub const BUSINESS_TYPE_ST: i32 = 12;

    /// 还券划转
    pub const BUSINESS_TYPE_SR: i32 = 13;

    /// 担保品划入
    pub const BUSINESS_TYPE_CI: i32 = 14;

    /// 担保品划出
    pub const BUSINESS_TYPE_CO: i32 = 15;

    /// 券源划入
    pub const BUSINESS_TYPE_SI: i32 = 16;

    /// 券源划出
    pub const BUSINESS_TYPE_SO: i32 = 17;

    /// 质押式回购(逆回购)
    pub const BUSINESS_TYPE_CRP: i32 = 18;
}

// 备兑解锁仓类型
impl TIType {
    /// 锁仓
    pub const COVERED_STOCK_LOCK: i32 = 1;

    /// 解锁仓
    pub const COVERED_STOCK_UNLOCK: i32 = 2;
}

// 信用授信额度类型
impl TIType {
    /// 融资授信额度
    pub const CREDIT_LIMIT_AMT: i32 = 1;

    /// 融券授信额度
    pub const CREDIT_LIMIT_POS: i32 = 2;
}

// 信用合约归还类型
impl TIType {
    /// 归还融资
    pub const CREDIT_RETURN_TYPE_AMT: i32 = 1;

    /// 归还融券
    pub const CREDIT_RETURN_TYPE_POS: i32 = 2;

    /// 归还融资类型 - 直接还款
    pub const CREDIT_RETURN_AMT_TYPE_DIRECT: i32 = 1;

    /// 归还融资类型 - 卖券还款
    pub const CREDIT_RETURN_AMT_TYPE_SELL: i32 = 2;

    /// 归还融资类型 - 平仓还款 - 无
    pub const CREDIT_RETURN_AMT_TYPE_CLOSE: i32 = 3;

    /// 归还融资类型明细 - 利息
    pub const CREDIT_RETURN_AMT_DTL_TYPE_INTEREST: i32 = 1;

    /// 归还融资类型明细 - 本金 - 无
    pub const CREDIT_RETURN_AMT_DTL_TYPE_AMT: i32 = 2;

    /// 归还融资类型明细 - 费用 - 无
    pub const CREDIT_RETURN_AMT_DTL_TYPE_COMMI: i32 = 3;

    /// 归还融资类型明细 - 罚息
    pub const CREDIT_RETURN_AMT_DTL_TYPE_FREEINT: i32 = 4;

    /// 归还融资类型明细 - 本金及费用
    pub const CREDIT_RETURN_AMT_DTL_TYPE_AMTCOMMI: i32 = 5;
}
