use std::{
    rc::Rc,
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, OnceLock,
    },
};

use crate::{
    apiproc::riskstkbuf::CreditTcPosMap,
    common::{global::RISK_STK_BUF, ticonvert::TIConvert},
    slintui::*,
};
use slint::*;

/// 股份头寸数据
struct CreditTcPosDatas {
    filter_changed: AtomicBool,
}
impl CreditTcPosDatas {
    pub fn new() -> Self {
        Self {
            filter_changed: AtomicBool::new(false),
        }
    }
}
static CREDIT_TC_POS_DATAS: OnceLock<CreditTcPosDatas> = OnceLock::new();

/// 股份头寸
pub(in crate::ui::riskstk::credit) struct CreditTcPos {
    /// 数据是否有变动
    data_changed: AtomicBool,

    /// 数据
    data_map: Arc<CreditTcPosMap>,
}

/// 构建与初始化
impl CreditTcPos {
    pub fn new() -> Self {
        Self {
            data_changed: AtomicBool::new(false),
            data_map: Arc::new(CreditTcPosMap::new()),
        }
    }

    pub fn init(&self, app: &crate::slintui::RiskStkCreditDetailPage) {
        let _ = CREDIT_TC_POS_DATAS.set(CreditTcPosDatas::new());

        let app_tc = app.global::<StkRskCredit_CreditTcPos>();

        let app_weak = app.as_weak();
        app_tc.on_get_row_data_color(move |row_index, column_index, data| {
            CreditTcPos::on_get_row_data_color(&app_weak, row_index, column_index, data)
        });

        let app_weak = app.as_weak();
        app_tc.on_filter_changed(move || CreditTcPos::on_filter_changed(&app_weak));

        log::trace!("Credit tc pos init completed");
    }
}

/// 查询
impl CreditTcPos {
    /// 查询股份头寸
    pub fn qry_credit_tcpos(&self) {
        let ret = { RISK_STK_BUF.get().unwrap().read().unwrap().pop_credit_tcpos() };
        if ret.is_empty() {
            return;
        }

        for (key, tc) in ret {
            self.data_map.insert(key, tc);
        }

        self.data_changed.store(true, Ordering::Relaxed);
    }
}

/// 事件
impl CreditTcPos {
    /// 获取单元格字体颜色
    fn on_get_row_data_color(
        app_weak: &Weak<RiskStkCreditDetailPage>,
        row_index: i32,
        column_index: i32,
        data: SharedString,
    ) -> Brush {
        let app = app_weak.unwrap();
        let nc = app.global::<NotifyColor>();

        // 可用数量
        if 6 == column_index {
            let data = data.replace(",", "").parse::<i32>().unwrap_or_default();
            if data < 0 {
                return nc.get_error();
            } else {
                return nc.get_normal();
            }
        }

        nc.get_default()
    }

    /// 过滤
    fn on_filter_changed(app_weak: &Weak<RiskStkCreditDetailPage>) {
        CREDIT_TC_POS_DATAS
            .get()
            .unwrap()
            .filter_changed
            .store(true, Ordering::Relaxed);
    }
}

/// 更新UI
impl CreditTcPos {
    // 更新
    pub async fn update(&self, app_weak: Weak<RiskStkCreditDetailPage>) {
        let tc_datas = CREDIT_TC_POS_DATAS.get().unwrap();

        let data_map_clone = {
            if self
                .data_changed
                .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                .is_ok()
            {
                tc_datas.filter_changed.store(false, Ordering::Relaxed);

                self.data_map.clone()
            } else {
                if tc_datas
                    .filter_changed
                    .compare_exchange(true, false, Ordering::Acquire, Ordering::Relaxed)
                    .is_ok()
                {
                    self.data_map.clone()
                } else {
                    Arc::new(CreditTcPosMap::new())
                }
            }
        };
        if data_map_clone.is_empty() {
            return;
        }

        let _ = app_weak.upgrade_in_event_loop(move |app| {
            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            let app_tc = app.global::<StkRskCredit_CreditTcPos>();

            let filter_accid = app.global::<StkRskCredit_Com>().get_accountid().as_str().to_owned();
            if filter_accid.is_empty() {
                app_tc.set_row_data(row_data.into());
                return;
            }

            let filter_tctype = app_tc.get_filter_tctype();
            let filter_exchid = app_tc.get_filter_exchid().to_string();
            let filter_insid = app_tc.get_filter_insid().to_string();

            let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());
            data_map_clone.iter().for_each(|tc| {
                if !tc.AccID.is_empty() {
                    if filter_accid != tc.AccID {
                        return;
                    }
                }

                if filter_tctype > 0 {
                    if tc.TcType != filter_tctype {
                        return;
                    }
                }

                let exchid = TIConvert::exch_name(tc.ExchID);
                if !filter_exchid.is_empty() {
                    if filter_exchid != exchid {
                        return;
                    }
                }

                if !filter_insid.is_empty() {
                    if !tc.InsID.contains(&filter_insid) {
                        return;
                    }
                }

                let mut insname = "".to_owned();
                {
                    if let Some(ins) = crate::common::global::INS
                        .get()
                        .unwrap()
                        .read()
                        .unwrap()
                        .get_ins(tc.ExchID, &tc.InsID)
                    {
                        insname = ins.name.clone()
                    }
                };

                let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

                // 头寸类型
                items.push(
                    match tc.TcType {
                        1 => "普通头寸",
                        2 => "专项头寸",
                        _ => "未知头寸",
                    }
                    .into(),
                );

                // 资金账户
                items.push(slint::format!("{}", tc.AccID));

                // 股东代码
                items.push(slint::format!("{}", tc.CliID));

                // 交易所
                items.push(exchid.into());

                // 证券代码
                items.push(slint::format!("{}", tc.InsID));

                // 授权数量
                items.push(TIConvert::format_i(tc.Distrib).into());

                // 可用数量
                items.push(TIConvert::format_i(tc.Distrib - tc.Used - tc.Frozen + tc.In - tc.Out).into());

                // 占用数量
                items.push(TIConvert::format_i(tc.Used).into());

                // 冻结数量
                items.push(TIConvert::format_i(tc.Frozen).into());

                // 转入数量
                items.push(TIConvert::format_i(tc.In).into());

                // 转出数量
                items.push(TIConvert::format_i(tc.Out).into());

                // 证券名称
                items.push(slint::format!("{}", insname));

                row_data.push(items.into());
            });

            let sort_model = Rc::new(row_data.sort_by(move |r_a, r_b| {
                let c_a = r_a.row_data(1 as usize).unwrap();
                let c_b = r_b.row_data(1 as usize).unwrap();
                c_a.cmp(&c_b)
            }));

            app_tc.set_row_data(sort_model.into());
        });
    }
}
