import { <PERSON>, Fonts, Label, LineEdit, Button, ComboBox, TableView, ListViewItem, Icons } from "@zstk/lib.slint";
import { ColWidth, CommonModel, WidgetColor, AppCom } from "../struct.slint";
import { PageItem } from "../widgets/page.slint";
import { TimeSelect, TimeItem } from "../widgets/time.slint";

export global StkTrd_QryPosTrans {
    in property <[[string]]> row_data;

    in_out property <string> exchid;
    in_out property <string> insid;
    in_out property <int> starttime_int;
    in_out property <int> endtime_int;
    in_out property <TimeItem> starttime;
    in_out property <TimeItem> endtime: {hour: 23, minute: 59, second: 59};

    in property <[ListViewItem]> page_index_model: [ { text: "1 / 1" } ];
    in property <int> item_total: 0;
    in property <int> page_total: 1;
    in_out property <int> page_index: 0;
    in_out property <int> page_size: 50;

    callback qry_clieked();
    callback page_index_changed(/* index */ int);
    callback page_size_changed(/* size */ int);
}

export component QryPositionTransPage {

    preferred_width: 100%;
    preferred_height: 100%;

    function reset_qry_condition_by_page_click() {
        i_cbx_exch.current_text = StkTrd_QryPosTrans.exchid;
        i_ts_start.time = StkTrd_QryPosTrans.starttime;
        i_ts_end.time = StkTrd_QryPosTrans.endtime;
    }

    VerticalLayout {
        HorizontalLayout {
            padding: 6px;
            spacing: 10px;
            height: 36px;

            Label {
                text: "交易所";
                vertical_alignment: center;
            }

            i_cbx_exch := ComboBox {
                width: 100px;
                placeholder_text: "请选择";
                current_text: StkTrd_QryPosTrans.exchid;
                model: CommonModel.exch_stk_model;
            }

            Label {
                text: "证券代码";
                vertical_alignment: center;
            }

            i_le_insid := LineEdit {
                width: 100px;
                placeholder_text: "请输入";
                text: StkTrd_QryPosTrans.insid;
                action_icon: Icons.close;

                action => {
                    self.text = "";
                }
            }

            Label {
                text: "处理时间";
                vertical_alignment: center;
            }

            HorizontalLayout {
                i_ts_start := TimeSelect {
                    time: StkTrd_QryPosTrans.starttime;
                }

                Label {
                    text: "~";
                    vertical_alignment: center;
                }

                i_ts_end := TimeSelect {
                    time: StkTrd_QryPosTrans.endtime;
                }
            }

            Button {
                text: "查询";
                width: 80px;
                background: WidgetColor.btn_background;

                clicked => {
                    StkTrd_QryPosTrans.exchid = i_cbx_exch.current_text;
                    StkTrd_QryPosTrans.insid = i_le_insid.text;
                    StkTrd_QryPosTrans.starttime = i_ts_start.time;
                    StkTrd_QryPosTrans.endtime = i_ts_end.time;
                    StkTrd_QryPosTrans.starttime_int = i_ts_start.time_int;
                    StkTrd_QryPosTrans.endtime_int = i_ts_end.time_int;

                    StkTrd_QryPosTrans.qry_clieked();
                }
            }

            Rectangle {
                horizontal_stretch: 1;
            }
        }

        TableView {
            column_header: {
                height: AppCom.table_header_height,
                background: Colors.transparent,
                font: Fonts.normal,
                columns: [
                    { title: @tr("处理时间"), alignment: TextHorizontalAlignment.center, width: ColWidth.time },
                    { title: @tr("请求序号"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    { title: @tr("响应序号"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_80 },
                    { title: @tr("股东代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.clientid },
                    { title: @tr("证券代码"), alignment: TextHorizontalAlignment.center, width: ColWidth.stk_insid },
                    { title: @tr("划转方向"), alignment: TextHorizontalAlignment.center, width: ColWidth.len_100 },
                    { title: @tr("划转数量"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                    { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                    { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                ]
            };
            row_count: StkTrd_QryPosTrans.row_data.length;

            get_cell_data(row_index, column_index) => {
                StkTrd_QryPosTrans.row_data[row_index][column_index]
            }

            get_cell_data_color(row_index, column_index, data) => {
                Theme.foreground
            }

            cell_key_copy_pressed(row_index, column_index) => {
                if column_index >= 0 {
                    AppCom.copy_str_to_clipboard(StkTrd_QryPosTrans.row_data[row_index][column_index]);
                } else {
                    AppCom.copy_arr_to_clipboard(StkTrd_QryPosTrans.row_data[row_index]);
                }
            }
        }

        if StkTrd_QryPosTrans.row_data.length > 0 : PageItem {
            height: 28px;
            page_index_model <=> StkTrd_QryPosTrans.page_index_model;
            item_total <=> StkTrd_QryPosTrans.item_total;
            page_total <=> StkTrd_QryPosTrans.page_total;
            page_index <=> StkTrd_QryPosTrans.page_index;
            page_size <=> StkTrd_QryPosTrans.page_size;

            init => {
                self.refresh_page_info();
            }

            page_index_changed(index) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryPosTrans.page_index_changed(index);
            }

            page_size_changed(size) => {
                reset_qry_condition_by_page_click();
                StkTrd_QryPosTrans.page_size_changed(size);
            }
        }
    }
}
