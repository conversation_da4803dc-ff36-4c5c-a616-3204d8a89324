use crate::{
    protocol_pub::{
        account_risk_stk::*,
        app::*,
        instrument::{FJYInstrumentField, InstrumentField},
        login_risk::{ReqLoginField, RspLoginField},
        monitor_risk_stk::*,
        order_risk_stk::*,
        position_risk_stk::{CreditConcentrationField, CreditContractField, CreditReturnDtlField, PositionStkField, PositionTransStkField},
        trade_risk_stk::*,
    },
    serverinfo::ServerInfo,
};

/// 现货风控回调类
pub trait IRiskStkSpi: Send + Sync {
    /// 连接成功
    fn on_rtn_connect(&self) {}

    /// 断开连接
    fn on_rtn_disconnect(&self) {}

    /// 心跳消息
    fn on_rtn_heartbeat(&self, heartbeat: AppHeartbeatField) {}

    /// 交易服务端异常消息
    fn on_rtn_tdservererror(&self, tderr: AppTDServerErrorField) {}

    /// 登录到交易服务端的Session超时
    fn on_rtn_tdsessiontimeout(&self, tdst: AppTDSessionTimeoutField) {}

    /// 资金消息
    fn on_rtn_account(&self, acc: AccountsStkField) {}

    /// 出入金消息
    fn on_rtn_withdrawdeposit(&self, wd: WithdrawDepositStkField) {}

    /// 持仓消息
    fn on_rtn_position(&self, position: PositionStkField) {}

    /// 股份划转
    fn on_rtn_position_trans(&self, position_trans: PositionTransStkField) {}

    /// 委托消息
    fn on_rtn_order(&self, ord: OrderStkField) {}

    /// 成交消息
    fn on_rtn_trade(&self, trd: TradeStkField) {}

    /// 非交易业务委托消息
    fn on_rtn_businessorder(&self, ord: BusinessOrderStkField) {}

    /// 非交易业务成交消息
    fn on_rtn_businesstrade(&self, trd: BusinessTradeStkField) {}

    /// 自成交详情消息
    fn on_rtn_tradeselfdtl(&self, trd: TradeStkField) {}

    /// 自成交消息
    fn on_rtn_tradeself(&self, trdself: TradeSelfStkField) {}

    /// 成交持仓比消息
    fn on_rtn_tradeposition(&self, tradepos: TradePositionStkField) {}

    /// 报撤单消息
    fn on_rtn_orderinsertcancel(&self, ordinsertcancel: OrderInsertCancelStkField) {}

    /// 可转债监控信息
    fn on_rtn_convertbondsmon(&self, convertbondsmon: ConvertBondsMonStkField) {}

    /// 成交监控信息
    fn on_rtn_trademon(&self, trademon: TradeMonStkField) {}

    /// 查询合约响应
    fn on_rsp_qry_instrument(&self, reqid: i32, ins: InstrumentField) {}

    /// 查询非交易业务合约响应
    fn on_rsp_qry_fly_instrument(&self, reqid: i32, ins: FJYInstrumentField) {}

    /// 信用合约消息
    fn on_rtn_credit_contract(&self, cc: CreditContractField) {}

    /// 信用授信额度消息
    fn on_rtn_credit_limit(&self, limit: CreditLimitField) {}

    /// 信用资金头寸消息
    fn on_rtn_credit_tcamt(&self, tcamt: CreditTcAmtField) {}

    /// 信用股份头寸消息
    fn on_rtn_credit_tcpos(&self, tcpos: CreditTcPosField) {}

    /// 信用集中度消息
    fn on_rtn_credit_concentration(&self, cc: CreditConcentrationField) {}

    /// 信用归还明细消息
    fn on_rtn_credit_returndtl(&self, revdtl: CreditReturnDtlField) {}
}

/// 现货风控API
#[async_trait::async_trait]
pub trait RiskStkApi: Send + Sync {
    /// 初始化
    async fn init(&mut self);

    /// 释放资源
    async fn release(&mut self);

    /// 设置服务端信息(请求登录前必须设置)
    async fn set_svrinfo(&mut self, si: &ServerInfo);

    /// 请求登录
    async fn req_login(&self, req: &ReqLoginField) -> crate::Result<RspLoginField>;

    /// 请求查询合约(实际为同步请求,查询成功通过on_rsp_qry_instrument返回)
    async fn req_qry_instrument(&self, reqid: i32) -> crate::Result<()>;

    /// 请求查询非交易业务合约(实际为同步请求,查询成功通过on_rsp_qry_fjy_instrument返回)
    async fn req_qry_fjy_instrument(&self, reqid: i32) -> crate::Result<()>;
}
