import { TableView, Fonts } from "@zstk/lib.slint";
import { ColWidth, AppCom } from "../struct.slint";
import { FutTrd_Com, FutTrd_Account } from "struct.slint";

export component AccountPage {

    private property <Point> right_up_point;

    preferred_width: 100%;
    preferred_height: 100%;

    TableView {
        height: 100%;
        width: 100%;
        sortable: true;

        column_header: {
            height: AppCom.table_header_height,
            background: Colors.transparent,
            font: Fonts.normal,
            fix_head_count: 2,
            columns: [
                { title: @tr("交易所"), alignment: TextHorizontalAlignment.center, width: ColWidth.exchid },
                { title: @tr("资金账户"), alignment: TextHorizontalAlignment.center, width: ColWidth.accountid },
                { title: @tr("动态权益"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                { title: @tr("市值权益"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                { title: @tr("可用资金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
                { title: @tr("占用保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("冻结保证金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("冻结资金 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100, tips: "冻结的权利金 + 行权冻结资金" },
                { title: @tr("冻结手续费 ⓘ"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100, tips: "冻结手续费 + 行权冻结手续费" },
                { title: @tr("权利金收支"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("手续费"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("申报费用"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("入金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("出金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("平仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("持仓盈亏"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_100 },
                { title: @tr("上次结算准备金"), alignment: TextHorizontalAlignment.right, width: ColWidth.len_120 },
            ]
        };
        row_header: { visible: false };
        row_count: FutTrd_Account.row_data.length;

        sort_canceled => {
            FutTrd_Account.sort_asc_column_index = 0;
            FutTrd_Account.sort_dec_column_index = -1;
            FutTrd_Account.sort_ascending(FutTrd_Account.sort_asc_column_index);
        }

        sort_ascending(index) => {
            FutTrd_Account.sort_ascending(index);
            FutTrd_Account.sort_asc_column_index = index;
            FutTrd_Account.sort_dec_column_index = -1;
            true
        }

        sort_descending(index) => {
            FutTrd_Account.sort_descending(index);
            FutTrd_Account.sort_asc_column_index = -1;
            FutTrd_Account.sort_dec_column_index = index;
            true
        }

        get_cell_data(row_index, column_index) => {
            FutTrd_Account.row_data[row_index][column_index]
        }

        get_cell_data_color(row_index, column_index, data) => {
            FutTrd_Account.get_row_data_color(row_index, column_index, data)
        }

        cell_double_clicked(row_index, column_index) => {
            FutTrd_Com.accountid = FutTrd_Account.row_data[row_index][1];
        }

        cell_pointer_event(row_index, column_index, event, point) => {
            if PointerEventButton.right == event.button && PointerEventKind.up == event.kind {
                right_up_point = point;
            }
        }

        cell_key_copy_pressed(row_index, column_index) => {
            if column_index >= 0 {
                AppCom.copy_str_to_clipboard(FutTrd_Account.row_data[row_index][column_index]);
            } else {
                AppCom.copy_arr_to_clipboard(FutTrd_Account.row_data[row_index]);
            }
        }
    }
}