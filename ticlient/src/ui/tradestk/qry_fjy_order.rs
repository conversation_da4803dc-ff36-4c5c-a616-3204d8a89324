use std::rc::Rc;

use crate::{
    common::{config::Config, global::TRADE_STK_BUF, ticonvert::TIConvert},
    show_msg_box,
    slintui::*,
};
use slint::*;
use tiapi::protocol_pub::order_trade_ctp::CtpReqQryBusinessOrderField;

/// 查询 - 非交易业务委托
pub(super) struct QueryFjyOrder {}

impl QueryFjyOrder {
    pub fn new() -> Self {
        Self {}
    }

    pub fn init(&self, app: &crate::slintui::App) {
        let qry = app.global::<StkTrd_QryFjyOrder>();

        let app_weak = app.as_weak();
        qry.on_qry_clieked(move || QueryFjyOrder::on_qry_clieked(&app_weak));

        let app_weak = app.as_weak();
        qry.on_page_index_changed(move |index| QueryFjyOrder::on_page_index_changed(&app_weak, index));

        let app_weak = app.as_weak();
        qry.on_page_size_changed(move |size| QueryFjyOrder::on_page_size_changed(&app_weak, size));

        log::trace!("Query fjy order init completed");
    }
}

impl QueryFjyOrder {
    /// 查询事件
    fn on_query(app_weak: &Weak<App>, is_qry_btn_clicked: bool) {
        let app = app_weak.unwrap();
        let app_qryfjyord = app.global::<StkTrd_QryFjyOrder>();

        let req = CtpReqQryBusinessOrderField {
            ExchID: TIConvert::exch_id(&app_qryfjyord.get_exchid()),
            AccountID: "".into(),
            InstrumentID: app_qryfjyord.get_insid().into(),
            OrdStatus: TIConvert::order_status_id(&app_qryfjyord.get_ordstatus()),
            OrderSysID: app_qryfjyord.get_ordersysid().into(),
            TransTime0: app_qryfjyord.get_starttime_int(),
            TransTime1: app_qryfjyord.get_endtime_int(),
        };

        let page_index = {
            if is_qry_btn_clicked {
                0
            } else {
                app_qryfjyord.get_page_index()
            }
        };
        let page_size = app_qryfjyord.get_page_size();

        let ret = {
            TRADE_STK_BUF
                .get()
                .unwrap()
                .read()
                .unwrap()
                .qry_fjy_order(&req, page_index, page_size)
        };
        if 0 == ret.0 {
            show_msg_box(&app, 1, Default::default(), "查询结果为空".into());
        }

        if is_qry_btn_clicked {
            let page_index_data: Rc<VecModel<ListViewItem>> = Rc::new(VecModel::default());

            let page_total = ret.0 / page_size + {
                if 0 == ret.0 % page_size {
                    0
                } else {
                    1
                }
            };
            for idx in 1..=page_total {
                page_index_data.push(ListViewItem {
                    text: slint::format!("{}/{}", idx, page_total),
                    ..Default::default()
                });
            }

            app_qryfjyord.set_page_index_model(page_index_data.into());
            app_qryfjyord.set_item_total(ret.0);
            app_qryfjyord.set_page_index(0);
            app_qryfjyord.set_page_total(page_total);
        }

        let row_data: Rc<VecModel<ModelRc<SharedString>>> = Rc::new(VecModel::default());

        ret.1.iter().for_each(|ord| {
            let items: Rc<VecModel<SharedString>> = Rc::new(VecModel::default());

            // 发生时间
            items.push(TIConvert::transtime(ord.TransTime).into());

            // 合约编码
            items.push(ord.InstrumentID.clone().into());

            // 数量
            items.push(slint::format!("{}", ord.Volume));

            // 价格
            items.push(slint::format!("{:.*}", Config::STK_PRICE_DIGITS, ord.Price));

            // 业务类型
            items.push(TIConvert::business_type(ord.BusinessType).into());

            // 买买方向
            items.push(TIConvert::side(ord.Side).into());

            // 报单状态
            items.push(TIConvert::order_status(ord.OrdStatus).into());

            // 交易所报单编号
            items.push(ord.OrderSysID.clone().into());

            // 本地报单编号
            items.push(slint::format!("{}", ord.LocalOrderNo));

            // 剩余数量
            items.push(slint::format!("{}", ord.LeavesVolume));

            // 用户代码
            items.push(ord.UserID.clone().into());

            // 目标合约
            items.push(ord.DstInstrumentID.clone().into());

            // 交易所
            items.push(slint::format!("{}", TIConvert::exch_name(ord.ExchID)));

            // 资金账号
            items.push(ord.AccountID.clone().into());

            row_data.push(items.into());
        });

        app_qryfjyord.set_row_data(row_data.into());
    }

    /// 查询事件
    fn on_qry_clieked(app_weak: &Weak<App>) {
        QueryFjyOrder::on_query(app_weak, true);
    }

    /// 页索引改变
    fn on_page_index_changed(app_weak: &Weak<App>, page_index: i32) {
        QueryFjyOrder::on_query(app_weak, false);
    }

    /// 页大小改变
    fn on_page_size_changed(app_weak: &Weak<App>, page_size: i32) {
        QueryFjyOrder::on_query(app_weak, true);
    }
}
