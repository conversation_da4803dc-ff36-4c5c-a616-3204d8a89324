pub fn is_zero_i32(v: &i32) -> bool {
    0 == *v
}
pub fn is_one_i32(v: &i32) -> bool {
    1 == *v
}
pub fn is_zero_f64(v: &f64) -> bool {
    0f64 == *v
}

pub fn default_zero_i32() -> i32 {
    0
}

pub fn default_zero_f64() -> f64 {
    0f64
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn it_works() {
        let i1 = 0;
        assert_eq!(is_zero_i32(&i1), true);
        assert_eq!(is_one_i32(&i1), false);

        let i1 = 1;
        assert_eq!(is_zero_i32(&i1), false);
        assert_eq!(is_one_i32(&i1), true);

        let f1 = 0f64;
        assert_eq!(is_zero_f64(&f1), true);

        let f1 = 1f64;
        assert_eq!(is_zero_f64(&f1), false);

        assert_eq!(default_zero_i32(), 0);
        assert_eq!(default_zero_f64(), 0f64);
    }
}
