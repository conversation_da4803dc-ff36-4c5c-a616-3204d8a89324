import { Theme, TabView, TabBarItem } from "@zstk/lib.slint";

import { PositionPage } from "./position.slint";
import { CreditContractPage } from "../main/credit_contract.slint";

import { CreditLimitAmtPage } from "./credit_limit_amt.slint";
import { CreditLimitPosPage } from "./credit_limit_pos.slint";
import { CreditTcAmtPage } from "./credit_tc_amt.slint";
import { CreditTcPosPage } from "./credit_tc_pos.slint";
import { CreditConcentrationPage } from "./credit_concentration.slint";

import { InOrderPage } from "../../../tradestk/in_order.slint";

import { StkRsk_TabSelIdx } from "../../struct.slint";

export component MiddlePage {

    preferred_width: 100%;
    preferred_height: 100%;

    private property <[TabBarItem]> tabview_items: [
        { text: "持仓" },
        { text: "合约" },
        { text: "融资授信额度" },
        { text: "融券授信额度" },
        { text: "资金头寸" },
        { text: "股份头寸" },
        { text: "集中度" },
        { text: "在途订单" },
    ];

    VerticalLayout {
        spacing: 2px;

        TabView {
            height: 30px;
            background: Theme.control_background.darker(0.1);
            items: tabview_items;
            current_index <=> StkRsk_TabSelIdx.letf_middle_selidx;
        }

        if 0 == StkRsk_TabSelIdx.letf_middle_selidx: PositionPage { }
        if 1 == StkRsk_TabSelIdx.letf_middle_selidx: CreditContractPage { is_used_for_main: false;}
        if 2 == StkRsk_TabSelIdx.letf_middle_selidx: CreditLimitAmtPage { }
        if 3 == StkRsk_TabSelIdx.letf_middle_selidx: CreditLimitPosPage { }
        if 4 == StkRsk_TabSelIdx.letf_middle_selidx: CreditTcAmtPage { }
        if 5 == StkRsk_TabSelIdx.letf_middle_selidx: CreditTcPosPage { }
        if 6 == StkRsk_TabSelIdx.letf_middle_selidx: CreditConcentrationPage { }
        if 7 == StkRsk_TabSelIdx.letf_middle_selidx: InOrderPage { }
    }
}
