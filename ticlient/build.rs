use std::collections::HashMap;
use std::error::Error;
use winres::WindowsResource;

fn gen_version() {
    // 读取Cargo.toml文件
    let manifest_path = std::env::var("CARGO_MANIFEST_DIR").unwrap();
    let cargo_toml_content = std::fs::read_to_string(format!("{}/Cargo.toml", manifest_path)).unwrap();
    let doc = cargo_toml_content.parse::<toml_edit::DocumentMut>().unwrap();

    // 从Cargo.toml中提取version
    let version = doc["package"]["version"].as_str().unwrap();
    let ver_arr: Vec<&str> = version.split('.').collect();

    // 从Cargo.toml中提取version
    let copyright = doc["package"]["metadata"]["bundle"]["copyright"].as_str().unwrap();

    // 生成包含版本号的Rust代码
    let tmp = ver_arr[2].parse::<i32>().unwrap();
    let code = format!(
        "pub const CRATE_VERSION: &str = \"V1.{}.{} {:04}\";\npub const CRATE_CLIENT_VER: &str = \"20{}{:04}\";\npub const CRATE_COPYRIGHT: &str = \"{}\";",
        ver_arr[0], ver_arr[1], tmp, ver_arr[1], tmp, copyright
    );

    // 写入到version.rs
    std::io::Write::write_all(&mut std::fs::File::create("src/version.rs").unwrap(), code.as_bytes()).unwrap();
}

// https://github.com/mxre/winres/pull/24
fn add_icon_to_bin_when_building_for_win(icon_path: &str) -> Result<(), Box<dyn Error>> {
    if std::env::var("CARGO_CFG_TARGET_FAMILY")? == "windows" {
        let mut res = WindowsResource::new();
        let target_env = std::env::var("CARGO_CFG_TARGET_ENV")?;
        match target_env.as_str() {
            "gnu" => res
                .set_ar_path("x86_64-w64-mingw32-ar")
                .set_windres_path("x86_64-w64-mingw32-windres")
                .set_toolkit_path(".")
                .set_icon(icon_path),
            "msvc" => res.set_icon(icon_path),
            _ => panic!("Unsupported env: {}", target_env),
        };
        res.compile()?;
    }

    Ok(())
}

fn set_required_features() {
    let features = std::env::var("CARGO_FEATURE_TRADE").or_else(|_| std::env::var("CARGO_FEATURE_RISK"));
    if features.is_err() {
        println!("cargo:rustc-cfg=feature=\"risk\"");
    }
}

fn set_required_features_for_mac() {
    if std::env::var("CARGO_CFG_TARGET_OS").as_deref() == Ok("macos") {
        println!("cargo:rustc-link-lib=framework=Cocoa");
    }
}

fn main() {
    set_required_features_for_mac();
    set_required_features();

    gen_version();
    add_icon_to_bin_when_building_for_win("ui/resource/logo.ico").unwrap();

    let mut libs_paths = HashMap::new();
    libs_paths.extend(zstk::import_paths());

    let config = slint_build::CompilerConfiguration::new()
        .with_style("fluent".to_owned())
        .with_library_paths(libs_paths.clone());

    slint_build::compile_with_config("ui/app.slint", config).unwrap();
}
